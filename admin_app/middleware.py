"""Middleware used exclusively for admin panel security headers."""

# --- Standard Library Imports ---
from typing import Callable

class AdminCSPMiddleware:
    """Set Content Security Policy headers for admin panel responses."""

    def __init__(self, get_response: Callable):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        if request.path.startswith('/admin-panel/'):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "font-src 'self'; "
                "connect-src 'self'"
            )
        return response

