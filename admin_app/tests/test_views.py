"""
Unit tests for admin_app views.

This module contains comprehensive unit tests for view functionality in the admin_app,
including authentication, permissions, and basic view responses.
"""

# Standard library imports
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

# Local imports
from admin_app.models import (
    StaticPage, BlogCategory, BlogPost, HomepageBlock, MediaFile,
    BulkActionLog, SystemHealthLog, SiteConfiguration, Announcement
)

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminViewsAuthenticationTest(TestCase):
    """Test authentication and permissions for admin views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_admin_dashboard_requires_authentication(self):
        """Test that admin dashboard requires authentication."""
        response = self.client.get(reverse('admin_app:admin_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_admin_dashboard_requires_staff_permission(self):
        """Test that admin dashboard requires staff permission."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_app:admin_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_admin_dashboard_accessible_to_staff(self):
        """Test that admin dashboard is accessible to staff users."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_app:admin_dashboard'))
        self.assertEqual(response.status_code, 200)

    def test_admin_login_view(self):
        """Test admin login view."""
        response = self.client.get(reverse('admin_app:admin_login'))
        self.assertEqual(response.status_code, 200)

    def test_admin_login_post_valid_credentials(self):
        """Test admin login with valid credentials."""
        response = self.client.post(reverse('admin_app:admin_login'), {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after login

    def test_admin_login_rejects_external_next(self):
        """Login should not redirect to external URLs."""
        url = reverse('admin_app:admin_login') + '?next=http://malicious.com'
        response = self.client.post(url, {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse('admin_app:admin_dashboard'))

    def test_admin_login_post_invalid_credentials(self):
        """Test admin login with invalid credentials."""
        response = self.client.post(reverse('admin_app:admin_login'), {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)  # Stay on login page

    def test_admin_logout_view(self):
        """Test admin logout view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_app:admin_logout'))
        self.assertEqual(response.status_code, 302)  # Redirect after logout


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminDashboardViewTest(TestCase):
    """Test admin dashboard view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')

    def test_admin_dashboard_context_data(self):
        """Test that admin dashboard provides correct context data."""
        response = self.client.get(reverse('admin_app:admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Check that context contains expected keys
        context_keys = [
            'total_users', 'total_customers', 'total_providers',
            'total_venues', 'total_bookings', 'total_reviews',
            'new_users_30d', 'new_bookings_30d', 'pending_providers',
            'recent_events', 'site_config'
        ]
        
        for key in context_keys:
            self.assertIn(key, response.context)

    def test_admin_dashboard_statistics_calculation(self):
        """Test that dashboard statistics are calculated correctly."""
        # Clear cache to ensure fresh data
        from django.core.cache import cache
        cache.clear()

        # Create some test users with proper roles
        User.objects.create_user(
            email='<EMAIL>',
            password='test123',
            role=User.CUSTOMER
        )
        User.objects.create_user(
            email='<EMAIL>',
            password='test123',
            role=User.CUSTOMER
        )

        # Verify users were created
        total_users_in_db = User.objects.count()
        self.assertGreaterEqual(total_users_in_db, 3)

        response = self.client.get(reverse('admin_app:admin_dashboard'))

        # Should include admin user + 2 test users = 3 total
        self.assertGreaterEqual(response.context['total_users'], 3)


@override_settings(SECURE_SSL_REDIRECT=False)
class StaticPageViewsTest(TestCase):
    """Test static page management views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')
        
        self.static_page = StaticPage.objects.create(
            title='Test Page',
            content='Test content',
            created_by=self.admin_user
        )

    def test_static_page_list_view(self):
        """Test static page list view."""
        response = self.client.get(reverse('admin_app:static_page_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Page')

    def test_static_page_detail_view(self):
        """Test static page detail view."""
        response = self.client.get(
            reverse('admin_app:static_page_detail', kwargs={'slug': self.static_page.slug})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Page')

    def test_static_page_create_view_get(self):
        """Test static page create view GET request."""
        response = self.client.get(reverse('admin_app:static_page_create'))
        self.assertEqual(response.status_code, 200)

    def test_static_page_create_view_post(self):
        """Test static page create view POST request."""
        response = self.client.post(reverse('admin_app:static_page_create'), {
            'title': 'New Page',
            'content': 'New page content',
            'status': 'draft'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        self.assertTrue(StaticPage.objects.filter(title='New Page').exists())

    def test_static_page_edit_view_get(self):
        """Test static page edit view GET request."""
        response = self.client.get(
            reverse('admin_app:static_page_edit', kwargs={'slug': self.static_page.slug})
        )
        self.assertEqual(response.status_code, 200)

    def test_static_page_edit_view_post(self):
        """Test static page edit view POST request."""
        response = self.client.post(
            reverse('admin_app:static_page_edit', kwargs={'slug': self.static_page.slug}),
            {
                'title': 'Updated Page',
                'content': 'Updated content',
                'status': 'published'
            }
        )
        self.assertEqual(response.status_code, 302)  # Redirect after update
        
        self.static_page.refresh_from_db()
        self.assertEqual(self.static_page.title, 'Updated Page')


@override_settings(SECURE_SSL_REDIRECT=False)
class BlogManagementViewsTest(TestCase):
    """Test blog management views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')
        
        self.category = BlogCategory.objects.create(name='Test Category')
        self.blog_post = BlogPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.admin_user,
            category=self.category
        )

    def test_blog_category_list_view(self):
        """Test blog category list view."""
        response = self.client.get(reverse('admin_app:blog_category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Category')

    def test_blog_post_list_view(self):
        """Test blog post list view."""
        response = self.client.get(reverse('admin_app:blog_post_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Post')

    def test_blog_category_create_view(self):
        """Test blog category create view."""
        response = self.client.post(reverse('admin_app:blog_category_create'), {
            'name': 'New Category',
            'description': 'New category description',
            'is_active': True
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        self.assertTrue(BlogCategory.objects.filter(name='New Category').exists())

    def test_blog_post_create_view(self):
        """Test blog post create view."""
        response = self.client.post(reverse('admin_app:blog_post_create'), {
            'title': 'New Post',
            'content': 'New post content',
            'category': self.category.id,
            'status': 'draft'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        self.assertTrue(BlogPost.objects.filter(title='New Post').exists())


@override_settings(SECURE_SSL_REDIRECT=False)
class HomepageBlockViewsTest(TestCase):
    """Test homepage block management views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')
        
        self.homepage_block = HomepageBlock.objects.create(
            block_type='hero',
            title='Test Block',
            updated_by=self.admin_user
        )

    def test_homepage_block_list_view(self):
        """Test homepage block list view."""
        response = self.client.get(reverse('admin_app:homepage_block_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Block')

    def test_homepage_block_create_view(self):
        """Test homepage block create view."""
        response = self.client.post(reverse('admin_app:homepage_block_create'), {
            'block_type': 'features',
            'title': 'New Block',
            'is_active': True,
            'display_order': 1
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        self.assertTrue(HomepageBlock.objects.filter(title='New Block').exists())


@override_settings(SECURE_SSL_REDIRECT=False)
class SiteConfigurationViewTest(TestCase):
    """Test site configuration view."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')

    def test_site_configuration_view_get(self):
        """Test site configuration view GET request."""
        response = self.client.get(reverse('admin_app:site_configuration'))
        self.assertEqual(response.status_code, 200)

    def test_site_configuration_view_post(self):
        """Test site configuration view POST request."""
        response = self.client.post(reverse('admin_app:site_configuration'), {
            'site_name': 'Updated CozyWish',
            'site_tagline': 'Updated tagline',
            'contact_email': '<EMAIL>',
            'maintenance_mode': False,
            'allow_user_registration': True,
            'require_email_verification': True
        })
        self.assertEqual(response.status_code, 302)  # Redirect after update
        
        config = SiteConfiguration.get_instance()
        self.assertEqual(config.site_name, 'Updated CozyWish')


@override_settings(SECURE_SSL_REDIRECT=False)
class AnnouncementViewsTest(TestCase):
    """Test announcement management views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')
        
        self.announcement = Announcement.objects.create(
            title='Test Announcement',
            content='Test content',
            created_by=self.admin_user
        )

    def test_announcement_list_view(self):
        """Test announcement list view."""
        response = self.client.get(reverse('admin_app:announcement_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Announcement')

    def test_announcement_create_view(self):
        """Test announcement create view."""
        start_date = timezone.now()
        response = self.client.post(reverse('admin_app:announcement_create'), {
            'title': 'New Announcement',
            'content': 'New announcement content',
            'announcement_type': 'info',
            'display_location': 'homepage',
            'is_active': True,
            'is_dismissible': True,
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'priority': 0
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        self.assertTrue(Announcement.objects.filter(title='New Announcement').exists())


@override_settings(SECURE_SSL_REDIRECT=False)
class AnalyticsExportViewTest(TestCase):
    """Test CSV analytics export functionality."""

    def setUp(self):
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>', password='testpass123', is_staff=True, is_superuser=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')

    def test_export_csv_response(self):
        response = self.client.get(reverse('admin_app:export_analytics'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment; filename="analytics_', response['Content-Disposition'])
        self.assertIn('Total Users', response.content.decode())


@override_settings(SECURE_SSL_REDIRECT=False)
class GlobalSearchViewTest(TestCase):
    """Test global admin search functionality."""

    def setUp(self):
        self.client = Client()
        self.admin_user = User.objects.create_user(
            email='<EMAIL>', password='testpass123', is_staff=True, is_superuser=True
        )
        self.client.login(email='<EMAIL>', password='testpass123')

        StaticPage.objects.create(title='Search Page', content='Test', created_by=self.admin_user)
        Announcement.objects.create(title='Search Announcement', content='x', announcement_type='info', created_by=self.admin_user)

    def test_search_view_get(self):
        response = self.client.get(reverse('admin_app:admin_search'), {'q': 'Search'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Search Results')
