"""
Integration tests for admin_app.

This module contains comprehensive integration tests that test complex workflows
and interactions between different components of the admin_app, focusing on
admin user management, provider approval workflows, content management,
system monitoring, and cross-app integrations.
"""

# Standard library imports
from unittest.mock import patch, Mock
from datetime import timed<PERSON>ta
from decimal import Decimal

# Django imports
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth.models import Group

# Local imports
from admin_app.models import (
    StaticPage, BlogCategory, BlogPost, HomepageBlock, MediaFile,
    BulkActionLog, SystemHealthLog, SiteConfiguration, Announcement
)
from accounts_app.models import CustomerProfile, ServiceProviderProfile, TeamMember
from venues_app.models import Venue, Service, Category
from booking_cart_app.models import Booking, BookingItem, Cart, CartItem
from review_app.models import Review

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminUserManagementIntegrationTest(TestCase):
    """Test complete admin user management workflows."""

    def setUp(self):
        """Set up test data for admin user management tests."""
        self.client = Client()

        # Create admin users
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='SuperPass123!',
            first_name='Super',
            last_name='Admin',
            is_staff=True,
            is_superuser=True
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='StaffPass123!',
            first_name='Staff',
            last_name='Member',
            is_staff=True,
            is_superuser=False
        )

        # Create test customers
        self.customer1 = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            first_name='John',
            last_name='Customer',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(
            user=self.customer1,
            phone_number='+**********',
            birth_month=1,
            birth_year=1990
        )

        self.customer2 = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            first_name='Jane',
            last_name='Customer',
            role=User.CUSTOMER,
            is_active=False  # Inactive customer for testing
        )
        CustomerProfile.objects.create(
            user=self.customer2,
            phone_number='+**********',
            birth_month=5,
            birth_year=1985
        )

        # Create test service providers
        self.provider1 = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            first_name='Alice',
            last_name='Provider',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile1 = ServiceProviderProfile.objects.create(
            user=self.provider1,
            legal_name='Alice Spa Services',
            phone='+**********',
            contact_name='Alice Provider',
            address='123 Spa Street',
            city='City',
            state='CA',
            zip_code='12345'
        )

        self.provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            first_name='Bob',
            last_name='Provider',
            role=User.SERVICE_PROVIDER,
            is_active=False  # Inactive provider for testing
        )
        self.provider_profile2 = ServiceProviderProfile.objects.create(
            user=self.provider2,
            legal_name='Bob Wellness Center',
            phone='+**********',
            contact_name='Bob Provider',
            address='456 Wellness Ave',
            city='City',
            state='CA',
            zip_code='12345'
        )

        # Create venue category for testing
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Professional spa and wellness services'
        )

        # Create test venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile1,
            venue_name='Alice Premium Spa',
            short_description='Luxury spa services',
            street_number='123',
            street_name='Spa Street',
            city='City',
            county='County',
            state='State',
            approval_status=Venue.PENDING
        )
        self.venue.categories.add(self.category)

    def test_admin_login_and_dashboard_access_workflow(self):
        """Test complete admin login and dashboard access workflow."""
        # Step 1: Test admin login
        login_url = reverse('admin_app:admin_login')
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'SuperPass123!'
        })
        self.assertEqual(response.status_code, 302)  # Should redirect to dashboard

        # Step 2: Access admin dashboard
        dashboard_url = reverse('admin_app:admin_dashboard')
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin Dashboard')

        # Verify dashboard statistics are displayed
        self.assertContains(response, 'Total Users')
        self.assertContains(response, 'Total Providers')
        self.assertContains(response, 'Total Bookings')

        # Step 3: Test staff user login (limited access)
        self.client.logout()
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'StaffPass123!'
        })
        self.assertEqual(response.status_code, 302)

        # Staff should also have dashboard access
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)

        # Step 4: Test regular user cannot access admin
        self.client.logout()
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'CustomerPass123!'
        })
        # Should not redirect (login failed)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invalid credentials')

    def test_complete_user_management_workflow(self):
        """Test complete user management workflow including search, edit, and bulk actions."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Access user list
        user_list_url = reverse('admin_app:user_list')
        response = self.client.get(user_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'User Management')
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, '<EMAIL>')

        # Step 2: Search for specific user
        response = self.client.get(user_list_url, {
            'search_query': 'Alice',
            'role': 'service_provider'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')
        self.assertNotContains(response, '<EMAIL>')

        # Step 3: View user detail
        user_detail_url = reverse('admin_app:user_detail', args=[self.customer1.id])
        response = self.client.get(user_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Customer')
        self.assertContains(response, '<EMAIL>')

        # Step 4: Edit user (toggle active status)
        user_edit_url = reverse('admin_app:user_edit', args=[self.customer2.id])
        response = self.client.post(user_edit_url, {
            'action': 'toggle_active'
        })
        self.assertEqual(response.status_code, 302)

        # Verify user was activated
        self.customer2.refresh_from_db()
        self.assertTrue(self.customer2.is_active)

        # Step 5: Test bulk user actions
        bulk_action_url = reverse('admin_app:bulk_user_actions')
        response = self.client.post(bulk_action_url, {
            'action': 'deactivate',
            'user_ids': f'{self.customer1.id},{self.customer2.id}',
            'reason': 'Testing bulk deactivation'
        })
        self.assertEqual(response.status_code, 302)

        # Verify users were deactivated
        self.customer1.refresh_from_db()
        self.customer2.refresh_from_db()
        self.assertFalse(self.customer1.is_active)
        self.assertFalse(self.customer2.is_active)

        # Verify bulk action was logged
        bulk_log = BulkActionLog.objects.filter(action_type='deactivate').first()
        self.assertIsNotNone(bulk_log)
        self.assertEqual(bulk_log.affected_count, 2)
        self.assertEqual(bulk_log.executed_by, self.superuser)

    def test_provider_approval_workflow(self):
        """Test complete service provider approval workflow."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: View pending providers
        pending_providers_url = reverse('admin_app:pending_providers')
        response = self.client.get(pending_providers_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Pending Provider Approvals')

        # Step 2: View provider approval detail
        provider_approval_url = reverse('admin_app:provider_approval', args=[self.provider1.id])
        response = self.client.get(provider_approval_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice Spa Services')
        self.assertContains(response, '<EMAIL>')

        # Step 3: Approve provider
        response = self.client.post(provider_approval_url, {
            'action': 'approve',
            'approval_notes': 'Business documentation verified. Approved for platform.'
        })
        self.assertEqual(response.status_code, 302)

        # Verify provider profile was updated (if approval fields exist)
        self.provider_profile1.refresh_from_db()
        self.assertTrue(self.provider_profile1.is_public)

        # Step 4: Test provider rejection workflow
        provider2_approval_url = reverse('admin_app:provider_approval', args=[self.provider2.id])
        response = self.client.post(provider2_approval_url, {
            'action': 'reject',
            'rejection_reason': 'Incomplete business documentation. Please resubmit with required documents.'
        })
        self.assertEqual(response.status_code, 302)

        # Verify rejection was processed
        self.provider_profile2.refresh_from_db()
        self.assertFalse(self.provider_profile2.is_public)

    def test_venue_approval_integration_workflow(self):
        """Test venue approval workflow integration with venues_app."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Verify venue is pending approval
        self.assertEqual(self.venue.approval_status, Venue.PENDING)
        self.assertFalse(self.venue.is_visible)

        # Step 2: Access venue through admin (integration with venues_app)
        # This would typically be done through venues_app admin views
        # but we test the integration here

        # Step 3: Approve venue (simulated admin action)
        self.venue.approval_status = Venue.APPROVED
        self.venue.visibility = Venue.ACTIVE
        self.venue.save()

        # Step 4: Verify venue is now visible
        self.venue.refresh_from_db()
        self.assertEqual(self.venue.approval_status, Venue.APPROVED)
        self.assertTrue(self.venue.is_visible)

        # Step 5: Test venue rejection
        venue2 = Venue.objects.create(
            service_provider=self.provider_profile2,
            venue_name='Bob Rejected Venue',
            short_description='Test venue for rejection',
            street_number='456',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='TS',
            approval_status=Venue.PENDING
        )

        # Reject venue
        venue2.approval_status = Venue.REJECTED
        venue2.visibility = Venue.INACTIVE
        # Set rejected_at to satisfy CHECK constraint
        from django.utils import timezone
        venue2.rejected_at = timezone.now()
        venue2.save()

        # Verify rejection
        venue2.refresh_from_db()
        self.assertEqual(venue2.approval_status, Venue.REJECTED)
        self.assertFalse(venue2.is_visible)

    def test_content_management_workflow(self):
        """Test complete content management workflow including static pages and blog posts."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Create static page
        static_page_create_url = reverse('admin_app:static_page_create')
        response = self.client.post(static_page_create_url, {
            'title': 'About CozyWish Platform',
            'content': 'CozyWish is the premier platform for spa and wellness bookings.',
            'status': 'published',
            'is_featured': True,
            'meta_title': 'About CozyWish',
            'meta_description': 'Learn about CozyWish spa booking platform'
        })
        self.assertEqual(response.status_code, 302)

        # Verify static page was created
        static_page = StaticPage.objects.get(title='About CozyWish Platform')
        self.assertEqual(static_page.status, 'published')
        self.assertTrue(static_page.is_featured)
        self.assertEqual(static_page.created_by, self.superuser)

        # Step 2: Create blog category
        blog_category_create_url = reverse('admin_app:blog_category_create')
        response = self.client.post(blog_category_create_url, {
            'name': 'Wellness Tips',
            'description': 'Expert tips for wellness and spa treatments',
            'is_active': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify blog category was created
        blog_category = BlogCategory.objects.get(name='Wellness Tips')
        self.assertEqual(blog_category.slug, 'wellness-tips')
        self.assertTrue(blog_category.is_active)

        # Step 3: Create blog post
        blog_post_create_url = reverse('admin_app:blog_post_create')
        response = self.client.post(blog_post_create_url, {
            'title': 'Top 5 Spa Treatments for Relaxation',
            'content': 'Discover the most relaxing spa treatments available...',
            'excerpt': 'Learn about the best spa treatments for ultimate relaxation.',
            'category': blog_category.id,
            'status': 'published',
            'is_featured': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify blog post was created
        blog_post = BlogPost.objects.get(title='Top 5 Spa Treatments for Relaxation')
        self.assertEqual(blog_post.status, 'published')
        self.assertEqual(blog_post.category, blog_category)
        self.assertEqual(blog_post.author, self.superuser)
        self.assertIsNotNone(blog_post.published_at)

    def test_system_monitoring_and_health_workflow(self):
        """Test system health monitoring and event resolution workflow."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Create system health events
        critical_event = SystemHealthLog.objects.create(
            event_type='error',
            severity='critical',
            title='Database Connection Failure',
            description='Critical database connection failure during peak hours.',
            affected_user=self.customer1,
            ip_address='*************',
            additional_data={'error_code': 'DB_CONN_FAIL', 'retry_attempts': 5}
        )

        warning_event = SystemHealthLog.objects.create(
            event_type='performance',
            severity='medium',
            title='Slow Query Performance',
            description='Database queries taking longer than expected.',
            ip_address='*************',
            additional_data={'avg_query_time': '2.5s', 'threshold': '1.0s'}
        )

        # Step 2: Access system health logs
        health_logs_url = reverse('admin_app:system_health_logs')
        response = self.client.get(health_logs_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'System Health Logs')
        self.assertContains(response, 'Database Connection Failure')
        self.assertContains(response, 'Slow Query Performance')

        # Step 3: Resolve critical event
        resolve_event_url = reverse('admin_app:resolve_health_event', args=[critical_event.id])
        response = self.client.post(resolve_event_url, {
            'resolution_notes': 'Increased database connection pool size and implemented connection retry logic.'
        })
        self.assertEqual(response.status_code, 302)

        # Verify event was resolved
        critical_event.refresh_from_db()
        self.assertTrue(critical_event.is_resolved)
        self.assertEqual(critical_event.resolved_by, self.superuser)
        self.assertIsNotNone(critical_event.resolved_at)
        self.assertIn('connection pool', critical_event.resolution_notes)

        # Step 4: Verify unresolved events still show up
        response = self.client.get(health_logs_url)
        self.assertEqual(response.status_code, 200)
        # Should still contain unresolved warning event
        self.assertContains(response, 'Slow Query Performance')

    def test_site_configuration_and_announcement_workflow(self):
        """Test site configuration and announcement management workflow."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Update site configuration
        site_config_url = reverse('admin_app:site_configuration')
        response = self.client.post(site_config_url, {
            'site_name': 'CozyWish Spa Platform',
            'site_tagline': 'Your Gateway to Wellness',
            'site_description': 'The premier platform for spa and wellness bookings.',
            'contact_email': '<EMAIL>',
            'contact_phone': '******-COZYWISH',
            'facebook_url': 'https://facebook.com/cozywish',
            'twitter_url': 'https://twitter.com/cozywish',
            'default_meta_title': 'CozyWish - Spa & Wellness Platform',
            'default_meta_description': 'Book spa and wellness services with ease.',
            'maintenance_mode': False,
            'allow_user_registration': True,
            'require_email_verification': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify configuration was updated
        config = SiteConfiguration.get_instance()
        self.assertEqual(config.site_name, 'CozyWish Spa Platform')
        self.assertEqual(config.contact_email, '<EMAIL>')
        self.assertEqual(config.updated_by, self.superuser)

        # Step 2: Create site-wide announcement
        start_date = timezone.now()
        end_date = start_date + timedelta(days=30)

        announcement_create_url = reverse('admin_app:announcement_create')
        response = self.client.post(announcement_create_url, {
            'title': 'Grand Opening Special!',
            'content': 'Get 20% off your first booking with code WELCOME20.',
            'announcement_type': 'promotion',
            'display_location': 'all_pages',
            'is_active': True,
            'is_dismissible': True,
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'end_date': end_date.strftime('%Y-%m-%dT%H:%M'),
            'target_user_roles': '',  # All users
            'priority': 10
        })
        self.assertEqual(response.status_code, 302)

        # Verify announcement was created
        announcement = Announcement.objects.get(title='Grand Opening Special!')
        self.assertEqual(announcement.announcement_type, 'promotion')
        self.assertEqual(announcement.display_location, 'all_pages')
        self.assertTrue(announcement.is_current)
        self.assertEqual(announcement.created_by, self.superuser)

        # Step 3: Test maintenance mode
        response = self.client.post(site_config_url, {
            'site_name': 'CozyWish Spa Platform',
            'site_tagline': 'Your Gateway to Wellness',
            'site_description': 'The premier platform for spa and wellness bookings.',
            'contact_email': '<EMAIL>',
            'contact_phone': '******-COZYWISH',
            'maintenance_mode': True,
            'maintenance_message': 'We are currently performing scheduled maintenance. Please check back soon.',
            'allow_user_registration': False,
            'require_email_verification': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify maintenance mode was enabled
        config.refresh_from_db()
        self.assertTrue(config.maintenance_mode)
        self.assertFalse(config.allow_user_registration)
        self.assertIn('scheduled maintenance', config.maintenance_message)

    def test_media_file_management_workflow(self):
        """Test media file upload and management workflow."""
        # Login as superuser
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Step 1: Upload an image file
        test_image = SimpleUploadedFile(
            "spa_hero.jpg",
            b"fake image content for spa hero",
            content_type="image/jpeg"
        )

        media_upload_url = reverse('admin_app:media_file_upload')
        response = self.client.post(media_upload_url, {
            'title': 'Spa Hero Image',
            'file': test_image,
            'description': 'Hero image for spa homepage',
            'alt_text': 'Relaxing spa environment',
            'tags': 'spa, hero, homepage, relaxation',
            'is_public': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify file was uploaded
        media_file = MediaFile.objects.get(title='Spa Hero Image')
        self.assertEqual(media_file.file_type, 'image')
        self.assertEqual(media_file.uploaded_by, self.superuser)
        self.assertTrue(media_file.is_public)
        self.assertEqual(media_file.alt_text, 'Relaxing spa environment')

        # Step 2: View media file list
        media_list_url = reverse('admin_app:media_file_list')
        response = self.client.get(media_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Media Files')
        self.assertContains(response, 'Spa Hero Image')

        # Step 3: Upload document file
        test_document = SimpleUploadedFile(
            "terms_of_service.pdf",
            b"fake pdf content for terms of service",
            content_type="application/pdf"
        )

        response = self.client.post(media_upload_url, {
            'title': 'Terms of Service Document',
            'file': test_document,
            'description': 'Legal terms of service document',
            'tags': 'legal, terms, document',
            'is_public': True
        })
        self.assertEqual(response.status_code, 302)

        # Verify document was uploaded with correct file type
        document_file = MediaFile.objects.get(title='Terms of Service Document')
        self.assertEqual(document_file.file_type, 'document')
        self.assertEqual(document_file.uploaded_by, self.superuser)


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminCrossAppIntegrationTest(TestCase):
    """Test admin_app integration with other CozyWish apps."""

    def setUp(self):
        """Set up test data for cross-app integration tests."""
        self.client = Client()

        # Create admin user
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='AdminPass123!',
            first_name='Admin',
            last_name='User',
            is_staff=True,
            is_superuser=True
        )

        # Create test customer
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            first_name='Test',
            last_name='Customer',
            role=User.CUSTOMER
        )
        self.customer_profile = CustomerProfile.objects.create(
            user=self.customer,
            phone_number='+**********',
            birth_month=1,
            birth_year=1990
        )

        # Create test provider
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            first_name='Test',
            last_name='Provider',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Test Street',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

        # Create venue and service
        self.category = Category.objects.create(
            category_name='Massage Therapy',
            category_description='Professional massage services'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Wellness Spa',
            short_description='Premium wellness and spa services',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='TS',
            approval_status=Venue.PENDING
        )
        self.venue.categories.add(self.category)

        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Relaxation Massage',
            short_description='60-minute full body relaxation massage',
            duration_minutes=60,
            price_min=Decimal('80.00'),
            is_active=True
        )

    def test_admin_venues_app_integration(self):
        """Test admin integration with venues_app for venue and service management."""
        # Login as admin
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Step 1: View admin dashboard with venue statistics
        dashboard_url = reverse('admin_app:admin_dashboard')
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)

        # Dashboard should show venue statistics
        self.assertContains(response, 'Total Venues')

        # Step 2: Access user detail to see provider's venues
        user_detail_url = reverse('admin_app:user_detail', args=[self.provider.id])
        response = self.client.get(user_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Wellness Spa')
        self.assertContains(response, 'Test Spa Business')

        # Step 3: Test venue approval workflow through admin
        # Verify venue is pending
        self.assertEqual(self.venue.approval_status, Venue.PENDING)

        # Simulate admin approval (would be done through venues_app admin views)
        self.venue.approval_status = Venue.APPROVED
        self.venue.visibility = Venue.ACTIVE
        self.venue.save()

        # Verify approval
        self.venue.refresh_from_db()
        self.assertEqual(self.venue.approval_status, Venue.APPROVED)
        self.assertTrue(self.venue.is_visible)

    def test_admin_booking_cart_app_integration(self):
        """Test admin integration with booking_cart_app for booking management."""
        # Login as admin
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Step 1: Create test booking data
        cart = Cart.objects.create(customer=self.customer)
        cart_item = CartItem.objects.create(
            cart=cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=self.service.price_min
        )

        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('80.00'),
            status=Booking.CONFIRMED
        )

        booking_item = BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )

        # Step 2: View admin dashboard with booking statistics
        dashboard_url = reverse('admin_app:admin_dashboard')
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)

        # Dashboard should show booking statistics
        self.assertContains(response, 'Total Bookings')

        # Step 3: View customer detail to see booking history
        user_detail_url = reverse('admin_app:user_detail', args=[self.customer.id])
        response = self.client.get(user_detail_url)
        self.assertEqual(response.status_code, 200)
        # Should show customer's recent bookings
        self.assertContains(response, 'Recent Bookings')

    def test_admin_accounts_app_integration(self):
        """Test admin integration with accounts_app for user management."""
        # Login as admin
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Step 1: Test user management integration
        user_list_url = reverse('admin_app:user_list')
        response = self.client.get(user_list_url)
        self.assertEqual(response.status_code, 200)

        # Should show users from accounts_app
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, '<EMAIL>')

        # Step 2: Test customer profile integration
        customer_detail_url = reverse('admin_app:user_detail', args=[self.customer.id])
        response = self.client.get(customer_detail_url)
        self.assertEqual(response.status_code, 200)

        # Should show customer profile information
        self.assertContains(response, 'Test Customer')
        self.assertContains(response, '+**********')

        # Step 3: Test provider profile integration
        provider_detail_url = reverse('admin_app:user_detail', args=[self.provider.id])
        response = self.client.get(provider_detail_url)
        self.assertEqual(response.status_code, 200)

        # Should show provider profile information
        self.assertContains(response, 'Test Spa Business')
        self.assertContains(response, '+**********')

        # Step 4: Test user status management
        user_edit_url = reverse('admin_app:user_edit', args=[self.customer.id])
        response = self.client.post(user_edit_url, {
            'action': 'toggle_active'
        })
        self.assertEqual(response.status_code, 302)

        # Verify user status was changed
        self.customer.refresh_from_db()
        self.assertFalse(self.customer.is_active)  # Should be deactivated


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminPermissionIntegrationTest(TestCase):
    """Test admin permission and access control integration."""

    def setUp(self):
        """Set up test data for permission tests."""
        self.client = Client()

        # Create different types of users
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='SuperPass123!',
            first_name='Super',
            last_name='User',
            is_staff=True,
            is_superuser=True
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='StaffPass123!',
            first_name='Staff',
            last_name='User',
            is_staff=True,
            is_superuser=False
        )

        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            first_name='Regular',
            last_name='Customer',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            first_name='Service',
            last_name='Provider',
            role=User.SERVICE_PROVIDER
        )

    def test_superuser_comprehensive_access(self):
        """Test that superuser has comprehensive access to all admin features."""
        self.client.login(email='<EMAIL>', password='SuperPass123!')

        # Test access to core admin views
        admin_urls = [
            'admin_app:admin_dashboard',
            'admin_app:user_list',
            'admin_app:static_page_list',
            'admin_app:blog_category_list',
            'admin_app:blog_post_list',
            'admin_app:media_file_list',
            'admin_app:site_configuration',
            'admin_app:announcement_list',
            'admin_app:system_health_logs',
            'admin_app:pending_providers',
            'admin_app:analytics_dashboard'
        ]

        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertEqual(response.status_code, 200, f"Superuser failed to access {url_name}")

        # Test user management actions
        user_detail_url = reverse('admin_app:user_detail', args=[self.customer.id])
        response = self.client.get(user_detail_url)
        self.assertEqual(response.status_code, 200)

        # Test user editing capabilities
        user_edit_url = reverse('admin_app:user_edit', args=[self.customer.id])
        response = self.client.post(user_edit_url, {
            'action': 'make_staff'
        })
        self.assertEqual(response.status_code, 302)

    def test_staff_user_limited_access(self):
        """Test that staff user has appropriate limited access to admin features."""
        self.client.login(email='<EMAIL>', password='StaffPass123!')

        # Should have access to basic admin features
        basic_access_urls = [
            'admin_app:admin_dashboard',
            'admin_app:user_list',
            'admin_app:static_page_list',
            'admin_app:blog_category_list',
            'admin_app:blog_post_list'
        ]

        for url_name in basic_access_urls:
            response = self.client.get(reverse(url_name))
            self.assertEqual(response.status_code, 200, f"Staff user failed to access {url_name}")

        # Should have limited user management access
        user_detail_url = reverse('admin_app:user_detail', args=[self.customer.id])
        response = self.client.get(user_detail_url)
        self.assertEqual(response.status_code, 200)

    def test_regular_users_no_admin_access(self):
        """Test that regular users (customers/providers) have no admin access."""
        # Test customer access
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        admin_urls = [
            'admin_app:admin_dashboard',
            'admin_app:user_list',
            'admin_app:static_page_list',
            'admin_app:site_configuration'
        ]

        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertIn(response.status_code, [302, 403], f"Customer should not access {url_name} (got {response.status_code})")

        # Test provider access
        self.client.logout()
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertIn(response.status_code, [302, 403], f"Provider should not access {url_name} (got {response.status_code})")

    def test_admin_login_security_workflow(self):
        """Test admin login security and failed login handling."""
        login_url = reverse('admin_app:admin_login')

        # Test successful admin login
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'SuperPass123!'
        })
        self.assertEqual(response.status_code, 302)  # Should redirect to dashboard

        # Test failed login with wrong password
        self.client.logout()
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'WrongPassword'
        })
        self.assertEqual(response.status_code, 200)  # Should stay on login page
        self.assertContains(response, 'Invalid credentials')

        # Test failed login with non-admin user
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'CustomerPass123!'
        })
        self.assertEqual(response.status_code, 200)  # Should stay on login page
        self.assertContains(response, 'Invalid credentials')

        # Test incomplete login data
        response = self.client.post(login_url, {
            'email': '<EMAIL>'
            # Missing password
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please provide both email and password')

    def test_admin_session_and_logout_workflow(self):
        """Test admin session management and logout workflow."""
        # Login as admin
        login_url = reverse('admin_app:admin_login')
        response = self.client.post(login_url, {
            'email': '<EMAIL>',
            'password': 'SuperPass123!'
        })
        self.assertEqual(response.status_code, 302)

        # Verify admin can access dashboard
        dashboard_url = reverse('admin_app:admin_dashboard')
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)

        # Test logout
        logout_url = reverse('admin_app:admin_logout')
        response = self.client.get(logout_url)
        self.assertEqual(response.status_code, 302)  # Should redirect to login

        # Verify cannot access dashboard after logout
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 302)  # Should redirect to login
