from django.test import TestCase, Client, override_settings
from django.urls import reverse


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminHealthCheckTests(TestCase):
    def setUp(self):
        self.client = Client()

    def test_health_check(self):
        response = self.client.get(reverse('admin_app:health_check'), secure=True)
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content.decode(), {'status': 'ok'})
