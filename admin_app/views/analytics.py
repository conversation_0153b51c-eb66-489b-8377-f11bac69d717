"""Views related to admin analytics and reporting."""

# --- Standard Library Imports ---
import csv
import logging
from datetime import datetime, timedelta

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking
from venues_app.models import Venue
from admin_app.constants import ANALYTICS_ERROR, EXPORT_ERROR
from admin_app.logging_utils import log_admin_error, log_data_export_event
from .common import admin_required

User = get_user_model()
logger = logging.getLogger(__name__)

# Analytics and Reporting Views
@admin_required
def admin_analytics_dashboard_view(request):
    """Analytics dashboard with platform metrics."""
    try:
        # Date range for analytics (default: last 30 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        # Get date range from request if provided
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        if date_from:
            try:
                start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            except ValueError:
                pass

        if date_to:
            try:
                end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            except ValueError:
                pass

        # User analytics
        total_users = User.objects.count()
        new_users = User.objects.filter(
            date_joined__date__range=[start_date, end_date]
        ).count()

        # Booking analytics
        total_bookings = Booking.objects.count()
        new_bookings = Booking.objects.filter(
            booking_date__date__range=[start_date, end_date]
        ).count()

        # Revenue analytics (placeholder - would integrate with payments_app)
        total_revenue = 0  # Would calculate from actual payments
        period_revenue = 0  # Would calculate from period payments

        # Venue analytics
        total_venues = Venue.objects.count()
        active_venues = Venue.objects.filter(visibility=Venue.ACTIVE).count()

        # Provider analytics
        total_providers = ServiceProviderProfile.objects.count()
        approved_providers = ServiceProviderProfile.objects.filter(
            is_visible=True
        ).count()
        pending_providers = ServiceProviderProfile.objects.filter(
            is_visible=False
        ).count()

        context = {
            'start_date': start_date,
            'end_date': end_date,
            'total_users': total_users,
            'new_users': new_users,
            'total_bookings': total_bookings,
            'new_bookings': new_bookings,
            'total_revenue': total_revenue,
            'period_revenue': period_revenue,
            'total_venues': total_venues,
            'active_venues': active_venues,
            'total_providers': total_providers,
            'approved_providers': approved_providers,
            'pending_providers': pending_providers,
        }

        return render(request, 'admin_app/analytics/dashboard.html', context)

    except Exception as e:
        logger.error(f"Error in analytics dashboard: {str(e)}")
        messages.error(request, ANALYTICS_ERROR)
        return render(request, 'admin_app/analytics/dashboard.html', {})


@admin_required
def admin_export_analytics_view(request):
    """Export analytics data as CSV."""
    try:
        # Get date range
        start_date = request.GET.get('start_date', (timezone.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.GET.get('end_date', timezone.now().strftime('%Y-%m-%d'))

        # Count records for logging
        total_users = User.objects.count()
        total_bookings = Booking.objects.count()
        total_venues = Venue.objects.count()
        total_providers = ServiceProviderProfile.objects.count()
        total_records = total_users + total_bookings + total_venues + total_providers

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="analytics_{start_date}_to_{end_date}.csv"'

        writer = csv.writer(response)
        writer.writerow(['Metric', 'Value', 'Period'])

        # Add analytics data
        writer.writerow(['Total Users', total_users, f'{start_date} to {end_date}'])
        writer.writerow(['Total Bookings', total_bookings, f'{start_date} to {end_date}'])
        writer.writerow(['Total Venues', total_venues, f'{start_date} to {end_date}'])
        writer.writerow(['Total Providers', total_providers, f'{start_date} to {end_date}'])

        # Log data export event
        log_data_export_event(
            admin_user=request.user,
            export_type='analytics',
            export_format='CSV',
            record_count=total_records,
            request=request,
            details={
                'date_range': f'{start_date} to {end_date}',
                'export_method': 'admin_panel',
                'exported_metrics': ['users', 'bookings', 'venues', 'providers'],
                'file_name': f'analytics_{start_date}_to_{end_date}.csv'
            }
        )

        return response

    except Exception as e:
        # Log export error
        log_admin_error(
            error_type='data_export_error',
            error_message=f'Error exporting analytics data: {str(e)}',
            admin_user=request.user,
            request=request,
            exception=e,
            details={
                'export_type': 'analytics',
                'export_format': 'CSV',
                'requested_date_range': f'{start_date} to {end_date}' if 'start_date' in locals() else 'unknown'
            }
        )

        logger.error(f"Error exporting analytics: {str(e)}")
        messages.error(request, EXPORT_ERROR)
        return redirect('admin_app:analytics_dashboard')


