"""Authentication views used in the admin panel."""

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import authenticate, get_user_model, login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.utils.http import url_has_allowed_host_and_scheme

# --- Local App Imports ---
from admin_app.constants import (
    ADMIN_LOGIN_SUCCESS,
    ADMIN_LOGOUT_SUCCESS,
    INVALID_CREDENTIALS,
    MISSING_FIELDS_ERROR,
)
from admin_app.logging_utils import log_admin_activity, log_admin_security_event
from .common import is_admin_user

User = get_user_model()


# Authentication Views
def admin_login_view(request):
    """Admin login view with enhanced security."""
    if request.user.is_authenticated and is_admin_user(request.user):
        return redirect('admin_app:admin_dashboard')

    if request.method == 'POST':
        email = request.POST.get('email')
        password = request.POST.get('password')

        if email and password:
            user = authenticate(request, username=email, password=password)

            if user is not None and is_admin_user(user):
                login(request, user)

                # Log successful admin login
                log_admin_activity(
                    admin_user=user,
                    activity_type='admin_login_success',
                    request=request,
                    details={
                        'login_method': 'admin_panel',
                        'user_role': 'superuser' if user.is_superuser else 'staff'
                    }
                )

                messages.success(request, ADMIN_LOGIN_SUCCESS)

                # Redirect to next URL if safe
                next_url = request.GET.get('next')
                if next_url and url_has_allowed_host_and_scheme(next_url, {request.get_host()}):
                    return redirect(next_url)
                return redirect('admin_app:admin_dashboard')
            else:
                # Log failed login attempt
                log_admin_security_event(
                    event_type='admin_login_failed',
                    request=request,
                    severity='WARNING',
                    details={
                        'attempted_email': email,
                        'failure_reason': 'invalid_credentials_or_insufficient_permissions',
                        'user_exists': User.objects.filter(email=email).exists(),
                        'is_admin_attempt': True
                    }
                )
                messages.error(request, INVALID_CREDENTIALS)
        else:
            # Log incomplete login attempt
            log_admin_security_event(
                event_type='admin_login_incomplete',
                request=request,
                severity='INFO',
                details={
                    'missing_fields': [field for field in ['email', 'password'] if not request.POST.get(field)],
                    'is_admin_attempt': True
                }
            )
            messages.error(request, MISSING_FIELDS_ERROR)

    return render(request, 'admin_app/login.html')


@login_required
def admin_logout_view(request):
    """Admin logout view."""
    if is_admin_user(request.user):
        # Log admin logout
        log_admin_activity(
            admin_user=request.user,
            activity_type='admin_logout',
            request=request,
            details={
                'logout_method': 'admin_panel',
                'session_duration': 'calculated_if_needed'  # Could calculate session duration
            }
        )

    logout(request)
    messages.success(request, ADMIN_LOGOUT_SUCCESS)
    return redirect('admin_app:admin_login')


