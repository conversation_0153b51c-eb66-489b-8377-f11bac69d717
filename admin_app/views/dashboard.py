"""Admin dashboard views with analytics and recent activity."""

# --- Standard Library Imports ---
import logging
import time
from datetime import datetime, timedelta

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.shortcuts import render
from django.utils import timezone

# --- Local App Imports ---
from admin_app.constants import DASHBOARD_ERROR
from admin_app.logging_utils import (
    log_admin_dashboard_access,
    log_admin_error,
    log_admin_performance,
    performance_monitor,
)
from admin_app.models import SiteConfiguration, SystemHealthLog
from booking_cart_app.models import Booking
from review_app.models import Review
from venues_app.models import Venue
from .common import admin_required

User = get_user_model()
logger = logging.getLogger(__name__)

# Dashboard Views
@admin_required
@performance_monitor('admin_dashboard_load')
def admin_dashboard_view(request):
    """Main admin dashboard with overview metrics."""
    start_time = time.time()
    cache_key = 'admin_dashboard_metrics'
    context = cache.get(cache_key)
    if context:
        log_admin_performance(
            operation='dashboard_cache_hit',
            duration=time.time() - start_time,
            admin_user=request.user,
            request=request,
            details={'cached': True}
        )
        return render(request, 'admin_app/dashboard/main.html', context)

    try:
        # Log dashboard access
        log_admin_dashboard_access(
            admin_user=request.user,
            dashboard_section='overview',
            request=request,
            details={'access_method': 'direct_url'}
        )

        # Get basic statistics
        total_users = User.objects.count()
        total_customers = User.objects.filter(role=User.CUSTOMER).count()
        total_providers = User.objects.filter(role=User.SERVICE_PROVIDER).count()
        total_venues = Venue.objects.count()
        total_bookings = Booking.objects.count()
        total_reviews = Review.objects.count()

        # Get recent activity (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_users_30d = User.objects.filter(date_joined__gte=thirty_days_ago).count()
        new_bookings_30d = Booking.objects.filter(booking_date__gte=thirty_days_ago).count()

        # Get pending approvals (placeholder - would need approval fields in model)
        pending_providers = 0  # ServiceProviderProfile.objects.filter(approval_status='pending').count()

        # Get recent system health events
        recent_events = SystemHealthLog.objects.filter(
            severity__in=['high', 'critical'],
            is_resolved=False
        )[:5]

        # Get site configuration
        try:
            site_config = SiteConfiguration.get_instance()
        except Exception:
            # Create a default site configuration if none exists
            site_config = SiteConfiguration.objects.create(updated_by=request.user)

        context = {
            'total_users': total_users,
            'total_customers': total_customers,
            'total_providers': total_providers,
            'total_venues': total_venues,
            'total_bookings': total_bookings,
            'total_reviews': total_reviews,
            'new_users_30d': new_users_30d,
            'new_bookings_30d': new_bookings_30d,
            'pending_providers': pending_providers,
            'recent_events': recent_events,
            'site_config': site_config,
        }
        cache.set(cache_key, context, 300)

        # Log dashboard load performance
        duration = time.time() - start_time
        log_admin_performance(
            operation='dashboard_overview_load',
            duration=duration,
            admin_user=request.user,
            request=request,
            details={
                'metrics_loaded': {
                    'total_users': total_users,
                    'total_venues': total_venues,
                    'total_bookings': total_bookings
                },
                'query_count': 'multiple_queries'  # Could implement query counting
            }
        )

        return render(request, 'admin_app/dashboard/main.html', context)

    except Exception as e:
        # Log dashboard error
        log_admin_error(
            error_type='dashboard_load_error',
            error_message=f'Error loading admin dashboard: {str(e)}',
            admin_user=request.user,
            request=request,
            exception=e,
            details={
                'dashboard_section': 'overview',
                'critical_error': True
            }
        )

        logger.error(f"Error in admin dashboard: {str(e)}")
        messages.error(request, DASHBOARD_ERROR)
        return render(request, 'admin_app/dashboard/main.html', {})


