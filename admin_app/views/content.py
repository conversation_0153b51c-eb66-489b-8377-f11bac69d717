"""Views for creating and managing static pages and other content."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Q
from django.shortcuts import render
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, DetailView, ListView, UpdateView

# --- Local App Imports ---
from admin_app.constants import CONTENT_DELETED_SUCCESS, CONTENT_SAVED_SUCCESS
from admin_app.forms import StaticPageForm, HomepageBlockForm, MediaFileForm
from admin_app.logging_utils import log_admin_activity, log_content_management_event
from admin_app.models import StaticPage, HomepageBlock, MediaFile
from admin_app.recent_utils import add_recent_link
from .common import ITEMS_PER_PAGE, admin_required, is_admin_user

logger = logging.getLogger(__name__)

# Content Management Views (CMS Integration)
class AdminStaticPageListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing static pages."""

    model = StaticPage
    template_name = 'admin_app/content/static_pages/list.html'
    context_object_name = 'pages'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        """Filter pages based on search parameters."""
        queryset = StaticPage.objects.order_by('-updated_at')

        search_query = self.request.GET.get('search', '').strip()
        status = self.request.GET.get('status', '').strip()

        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(content__icontains=search_query)
            )

        if status:
            queryset = queryset.filter(status=status)

        return queryset


class AdminStaticPageCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for static pages."""

    model = StaticPage
    form_class = StaticPageForm
    template_name = 'admin_app/content/static_pages/create.html'
    success_url = reverse_lazy('admin_app:static_page_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the created_by and updated_by fields."""
        form.instance.created_by = self.request.user
        form.instance.updated_by = self.request.user

        response = super().form_valid(form)

        # Log content management event
        log_content_management_event(
            action_type='content_created',
            admin_user=self.request.user,
            content_type='static_page',
            content_id=self.object.id,
            content_title=self.object.title,
            request=self.request,
            details={
                'content_status': self.object.status,
                'content_slug': self.object.slug,
                'is_featured': self.object.is_featured,
                'creation_method': 'admin_panel'
            }
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response


class AdminStaticPageUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """Update view for static pages."""

    model = StaticPage
    form_class = StaticPageForm
    template_name = 'admin_app/content/static_pages/edit.html'
    success_url = reverse_lazy('admin_app:static_page_list')
    slug_url_kwarg = 'slug'
    login_url = 'admin_app:admin_login'

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if request.method == 'GET' and getattr(self, 'object', None) and response.status_code == 200:
            add_recent_link(request, f"Static Page: {self.object.title}", request.path)
        return response

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the updated_by field."""
        form.instance.updated_by = self.request.user

        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type='static_page_updated',
            request=self.request,
            details={'page_title': self.object.title, 'page_slug': self.object.slug}
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response


class AdminStaticPageDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """Detail view for static pages."""

    model = StaticPage
    template_name = 'admin_app/content/static_pages/detail.html'
    context_object_name = 'page'
    slug_url_kwarg = 'slug'
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if request.method == 'GET' and getattr(self, 'object', None) and response.status_code == 200:
            add_recent_link(request, f"Static Page: {self.object.title}", request.path)
        return response


class AdminStaticPageDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """Delete view for static pages."""

    model = StaticPage
    template_name = 'admin_app/content/static_pages/delete.html'
    success_url = reverse_lazy('admin_app:static_page_list')
    slug_url_kwarg = 'slug'
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def delete(self, request, *args, **kwargs):
        """Log the deletion."""
        page_title = self.get_object().title
        response = super().delete(request, *args, **kwargs)

        log_admin_activity(
            admin_user=request.user,
            activity_type='static_page_deleted',
            request=request,
            details={'page_title': page_title}
        )
        messages.success(request, CONTENT_DELETED_SUCCESS)

        return response


# Homepage Block Management Views
class AdminHomepageBlockListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing homepage blocks."""

    model = HomepageBlock
    template_name = 'admin_app/content/homepage_blocks/list.html'
    context_object_name = 'blocks'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)


class AdminHomepageBlockCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for homepage blocks."""

    model = HomepageBlock
    form_class = HomepageBlockForm
    template_name = 'admin_app/content/homepage_blocks/create.html'
    success_url = reverse_lazy('admin_app:homepage_block_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)


# Media File Management Views
class AdminMediaFileListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing media files."""

    model = MediaFile
    template_name = 'admin_app/content/media/list.html'
    context_object_name = 'media_files'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)


class AdminMediaFileCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for media files."""

    model = MediaFile
    form_class = MediaFileForm
    template_name = 'admin_app/content/media/upload.html'
    success_url = reverse_lazy('admin_app:media_file_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the uploaded_by field."""
        form.instance.uploaded_by = self.request.user
        return super().form_valid(form)

