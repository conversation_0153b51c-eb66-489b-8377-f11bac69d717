"""Views for uploading and managing media files in the admin."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Q
from django.urls import reverse_lazy
from django.views.generic import CreateView, ListView

# --- Local App Imports ---
from admin_app.constants import MEDIA_UPLOAD_SUCCESS
from admin_app.forms import MediaFileForm
from admin_app.logging_utils import log_admin_activity
from admin_app.models import MediaFile
from .common import ITEMS_PER_PAGE, is_admin_user

logger = logging.getLogger(__name__)

# Media Management Views
class AdminMediaFileListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing media files."""

    model = MediaFile
    template_name = 'admin_app/content/media/list.html'
    context_object_name = 'media_files'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        """Filter media files based on search parameters."""
        queryset = MediaFile.objects.order_by('-created_at')

        search_query = self.request.GET.get('search', '').strip()
        file_type = self.request.GET.get('file_type', '').strip()

        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(tags__icontains=search_query)
            )

        if file_type:
            queryset = queryset.filter(file_type=file_type)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['file_types'] = MediaFile.FILE_TYPE_CHOICES
        return context


class AdminMediaFileCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for uploading media files."""

    model = MediaFile
    form_class = MediaFileForm
    template_name = 'admin_app/content/media/upload.html'
    success_url = reverse_lazy('admin_app:media_file_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the uploaded_by field."""
        form.instance.uploaded_by = self.request.user

        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type='media_file_uploaded',
            request=self.request,
            details={'media_file_title': self.object.title},
            target_object=f'media_file_{self.object.id}'
        )
        messages.success(self.request, MEDIA_UPLOAD_SUCCESS)

        return response
