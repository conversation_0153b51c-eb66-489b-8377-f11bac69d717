"""User management views for the admin interface."""

# --- Standard Library Imports ---
import logging
import time
from datetime import datetime

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_POST
from django.views.generic import DetailView, ListView

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking
from venues_app.models import Venue
from admin_app.forms import BulkUserActionForm, UserSearchForm
from admin_app.logging_utils import (
    log_user_management_event, log_bulk_user_action, log_bulk_operation_error,
    log_bulk_operation_performance, log_provider_approval_event, log_admin_error,
    performance_monitor
)
from admin_app.models import BulkActionLog, SystemHealthLog
from admin_app.recent_utils import add_recent_link
from .common import ITEMS_PER_PAGE, admin_required, is_admin_user
from admin_app.constants import (
    USER_STATUS_UPDATED,
    STAFF_PRIVILEGES_GRANTED,
    STAFF_PRIVILEGES_REMOVED,
    SERVICE_PROVIDER_VISIBLE,
    SERVICE_PROVIDER_INVISIBLE,
    BULK_ACTION_ERROR,
    INVALID_BULK_ACTION,
)

User = get_user_model()
logger = logging.getLogger(__name__)

# User Management Views
class AdminUserListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing users with search and filtering."""

    model = User
    template_name = 'admin_app/users/list.html'
    context_object_name = 'users'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        """Filter users based on search parameters."""
        queryset = User.objects.select_related(
            'customer_profile', 'service_provider_profile'
        ).prefetch_related(
            'bookings', 'service_provider_profile__venue'
        ).order_by('-date_joined')

        # Get search parameters
        search_query = self.request.GET.get('search_query', '').strip()
        role = self.request.GET.get('role', '').strip()
        status = self.request.GET.get('status', '').strip()
        date_from = self.request.GET.get('date_joined_from', '').strip()
        date_to = self.request.GET.get('date_joined_to', '').strip()

        # Apply search filters
        if search_query:
            queryset = queryset.filter(
                Q(email__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(service_provider_profile__legal_name__icontains=search_query) |
                Q(service_provider_profile__display_name__icontains=search_query)
            )

        if role:
            if role == 'customer':
                queryset = queryset.filter(role=User.CUSTOMER)
            elif role == 'service_provider':
                queryset = queryset.filter(role=User.SERVICE_PROVIDER)
            elif role == 'admin':
                queryset = queryset.filter(Q(is_staff=True) | Q(is_superuser=True))

        if status:
            if status == 'active':
                queryset = queryset.filter(is_active=True)
            elif status == 'inactive':
                queryset = queryset.filter(is_active=False)

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(date_joined__date__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(date_joined__date__lte=date_to_obj)
            except ValueError:
                pass

        return queryset

    def get_context_data(self, **kwargs):
        """Add search form and statistics to context."""
        context = super().get_context_data(**kwargs)

        # Add search form
        context['search_form'] = UserSearchForm(self.request.GET)

        # Add statistics
        context['total_users'] = self.get_queryset().count()
        context['total_customers'] = User.objects.filter(role=User.CUSTOMER).count()
        context['total_providers'] = User.objects.filter(role=User.SERVICE_PROVIDER).count()
        context['pending_providers'] = 0  # ServiceProviderProfile.objects.filter(approval_status='pending').count()

        return context


class AdminUserDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """Detail view for individual user management."""

    model = User
    template_name = 'admin_app/users/detail.html'
    context_object_name = 'user_obj'
    pk_url_kwarg = 'user_id'
    login_url = 'admin_app:admin_login'

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if request.method == 'GET' and getattr(self, 'object', None) and response.status_code == 200:
            add_recent_link(request, f"User: {self.object.email}", request.path)
        return response

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_context_data(self, **kwargs):
        """Add related data to context."""
        context = super().get_context_data(**kwargs)
        user_obj = self.get_object()

        # Add user profile data
        if user_obj.is_customer:
            context['customer_profile'] = getattr(user_obj, 'customer_profile', None)

        if user_obj.is_service_provider:
            context['provider_profile'] = getattr(user_obj, 'service_provider_profile', None)
            if context['provider_profile']:
                context['venues'] = Venue.objects.filter(service_provider=context['provider_profile'])
                context['bookings'] = Booking.objects.filter(
                    items__service__venue__service_provider=context['provider_profile']
                ).distinct()
            else:
                context['venues'] = Venue.objects.none()
                context['bookings'] = Booking.objects.none()

        # Add recent activity
        context['recent_bookings'] = Booking.objects.filter(
            customer=user_obj
        ).order_by('-booking_date')[:5] if user_obj.is_customer else []

        # Add system health events related to this user
        context['recent_events'] = SystemHealthLog.objects.filter(
            affected_user=user_obj
        ).order_by('-recorded_at')[:10]

        return context


@admin_required
def admin_user_edit_view(request, user_id):
    """Edit user information and status."""
    user_obj = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        # Handle user status changes
        action = request.POST.get('action')

        if action == 'toggle_active':
            old_status = user_obj.is_active
            user_obj.is_active = not user_obj.is_active
            user_obj.save()

            status = 'activated' if user_obj.is_active else 'deactivated'
            action_type = f'user_{status}'

            # Log user management event
            log_user_management_event(
                action_type=action_type,
                admin_user=request.user,
                target_user=user_obj,
                request=request,
                details={
                    'action_method': 'admin_panel',
                    'user_role': user_obj.role,
                    'previous_status': old_status
                },
                changes={
                    'is_active': {'old': old_status, 'new': user_obj.is_active}
                }
            )
            messages.success(request, USER_STATUS_UPDATED.format(status=status))

        elif action == 'make_staff':
            user_obj.is_staff = True
            user_obj.save()

            # Log privilege escalation
            log_user_management_event(
                action_type='user_staff_granted',
                admin_user=request.user,
                target_user=user_obj,
                request=request,
                details={
                    'action_method': 'admin_panel',
                    'privilege_type': 'staff',
                    'security_sensitive': True
                },
                changes={
                    'is_staff': {'old': False, 'new': True}
                }
            )
            messages.success(request, STAFF_PRIVILEGES_GRANTED)

        elif action == 'remove_staff':
            user_obj.is_staff = False
            user_obj.save()

            # Log privilege removal
            log_user_management_event(
                action_type='user_staff_removed',
                admin_user=request.user,
                target_user=user_obj,
                request=request,
                details={
                    'action_method': 'admin_panel',
                    'privilege_type': 'staff',
                    'security_sensitive': True
                },
                changes={
                    'is_staff': {'old': True, 'new': False}
                }
            )
            messages.success(request, STAFF_PRIVILEGES_REMOVED)

        return redirect('admin_app:user_detail', user_id=user_obj.id)

    context = {
        'user_obj': user_obj,
    }

    return render(request, 'admin_app/users/edit.html', context)


@admin_required
@require_POST
@performance_monitor('bulk_user_action')
def admin_bulk_user_actions_view(request):
    """Handle bulk actions on users."""
    start_time = time.time()
    form = BulkUserActionForm(request.POST)

    if form.is_valid():
        action = form.cleaned_data['action']
        user_ids = form.cleaned_data['user_ids']
        reason = form.cleaned_data.get('reason', '')

        users = User.objects.filter(id__in=user_ids)
        affected_count = 0
        success_count = 0
        error_count = 0

        try:
            if action == 'activate':
                affected_count = users.update(is_active=True)
                success_count = affected_count
                action_description = f'Bulk activated {affected_count} users'
                action_type = 'bulk_user_activate'

            elif action == 'deactivate':
                affected_count = users.update(is_active=False)
                success_count = affected_count
                action_description = f'Bulk deactivated {affected_count} users'
                action_type = 'bulk_user_deactivate'

            elif action == 'approve_providers':
                # Placeholder for provider approval - would need approval fields in model
                provider_profiles = ServiceProviderProfile.objects.filter(user__in=users)
                affected_count = provider_profiles.update(is_public=True)
                success_count = affected_count
                action_description = f'Bulk made {affected_count} service providers visible'
                action_type = 'bulk_provider_approve'

            elif action == 'reject_providers':
                # Placeholder for provider rejection - would need approval fields in model
                provider_profiles = ServiceProviderProfile.objects.filter(user__in=users)
                affected_count = provider_profiles.update(is_public=False)
                success_count = affected_count
                action_description = f'Bulk made {affected_count} service providers invisible'
                action_type = 'bulk_provider_reject'

            # Log bulk action
            if affected_count > 0:
                # Log using new logging utilities
                log_bulk_user_action(
                    action_type=action_type,
                    admin_user=request.user,
                    affected_users_count=affected_count,
                    request=request,
                    details={
                        'reason': reason,
                        'action_description': action_description,
                        'affected_model': 'CustomUser',
                        'bulk_operation': True
                    },
                    user_ids=user_ids
                )

                # Log performance metrics
                duration = time.time() - start_time
                log_bulk_operation_performance(
                    operation_type=action,
                    items_processed=len(user_ids),
                    duration=duration,
                    admin_user=request.user,
                    request=request,
                    success_count=success_count,
                    error_count=error_count
                )

                # Create legacy bulk action log for compatibility
                BulkActionLog.objects.create(
                    action_type=action,
                    description=f'{action_description}. Reason: {reason}' if reason else action_description,
                    affected_count=affected_count,
                    affected_model='CustomUser',
                    affected_ids=','.join(map(str, user_ids)),
                    executed_by=request.user
                )

                messages.success(request, f'{action_description} successfully.')
            else:
                messages.warning(request, 'No users were affected by this action.')

        except Exception as e:
            error_count = len(user_ids)

            # Log bulk operation error
            log_bulk_operation_error(
                operation_type=action,
                error_message=f'Error in bulk user action: {str(e)}',
                admin_user=request.user,
                failed_items=user_ids,
                request=request,
                exception=e
            )

            logger.error(f"Error in bulk user action: {str(e)}")
            messages.error(request, BULK_ACTION_ERROR)

    else:
        # Log form validation error
        log_admin_error(
            error_type='form_validation_error',
            error_message='Invalid bulk action request - form validation failed',
            admin_user=request.user,
            request=request,
            details={
                'form_errors': form.errors,
                'operation_type': 'bulk_user_action'
            }
        )
        messages.error(request, INVALID_BULK_ACTION)

    return redirect('admin_app:user_list')


@admin_required
def admin_provider_approval_view(request, user_id):
    """Handle individual service provider visibility toggle."""
    user_obj = get_object_or_404(User, id=user_id, role=User.SERVICE_PROVIDER)
    provider_profile = get_object_or_404(ServiceProviderProfile, user=user_obj)

    if request.method == 'POST':
        action = request.POST.get('action')
        reason = request.POST.get('reason', '').strip()

        if action == 'approve':
            provider_profile.is_public = True
            provider_profile.save()

            # Log provider approval event
            log_provider_approval_event(
                action_type='provider_approved',
                admin_user=request.user,
                provider_user=user_obj,
                is_approved=True,
                request=request,
                details={
                    'approval_method': 'admin_panel',
                    'business_name': provider_profile.display_name or provider_profile.legal_name,
                    'approval_reason': reason,
                    'previous_visibility': False
                }
            )
            messages.success(request, SERVICE_PROVIDER_VISIBLE)

        elif action == 'reject':
            provider_profile.is_public = False
            provider_profile.save()

            # Log provider rejection event
            log_provider_approval_event(
                action_type='provider_rejected',
                admin_user=request.user,
                provider_user=user_obj,
                is_approved=False,
                request=request,
                rejection_reason=reason,
                details={
                    'approval_method': 'admin_panel',
                    'business_name': provider_profile.display_name or provider_profile.legal_name,
                    'previous_visibility': True
                }
            )
            messages.success(request, SERVICE_PROVIDER_INVISIBLE)

        return redirect('admin_app:user_detail', user_id=user_obj.id)

    context = {
        'user_obj': user_obj,
        'provider_profile': provider_profile,
    }

    return render(request, 'admin_app/users/provider_approval.html', context)


@admin_required
def admin_pending_providers_view(request):
    """List all service providers that need review."""
    # Show invisible providers as "pending" for review
    pending_providers = ServiceProviderProfile.objects.filter(
        is_public=False
    ).select_related('user').order_by('-created')

    # Pagination
    paginator = Paginator(pending_providers, ITEMS_PER_PAGE)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'pending_providers': page_obj,
        'total_pending': pending_providers.count(),
    }

    return render(request, 'admin_app/users/pending_providers.html', context)


