"""Helpers to track recently visited admin links."""

# --- Standard Library Imports ---
from typing import Dict, List

# --- Third-Party Imports ---
from django.http import HttpRequest


MAX_RECENT = 5

def add_recent_link(request: HttpRequest, label: str, url: str) -> None:
    """Store a recently viewed admin link in the session."""
    recent: List[Dict[str, str]] = request.session.get('admin_recent', [])
    recent = [item for item in recent if item.get('url') != url]
    recent.insert(0, {'label': label, 'url': url})
    request.session['admin_recent'] = recent[:MAX_RECENT]
