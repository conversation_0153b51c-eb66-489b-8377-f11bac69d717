{% extends 'admin_app/base.html' %}

{% block title %}System Health Logs{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">System Health Logs</h1>
<p class="mb-3">Total Logs: {{ health_logs|length }}</p>
{% for log in events %}
<div class="mb-4">
    <h2 class="h6">{{ log.title }}</h2>
    <p><strong>Event Type:</strong> {{ log.event_type }}</p>
    <p><strong>Severity:</strong> {{ log.severity }}</p>
    <p><strong>Description:</strong> {{ log.description }}</p>
    <p><strong>Timestamp:</strong> {{ log.timestamp }}</p>
    {% if log.affected_user %}<p><strong>Affected User:</strong> {{ log.affected_user.email }}</p>{% endif %}
    {% if log.ip_address %}<p><strong>IP Address:</strong> {{ log.ip_address }}</p>{% endif %}
    {% if log.additional_data %}<p><strong>Additional Data:</strong> {{ log.additional_data }}</p>{% endif %}
    <p><strong>Status:</strong> {% if log.is_resolved %}Resolved{% else %}Unresolved{% endif %}</p>
</div>
{% empty %}
<p>No health logs found.</p>
{% endfor %}
<div class="mt-4">
    <a href="{% url 'admin_app:admin_dashboard' %}">Back to Dashboard</a>
</div>
{% endblock %}
