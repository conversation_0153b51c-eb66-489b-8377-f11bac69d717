{% extends 'admin_app/base.html' %}

{% block title %}Pending Providers{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">Pending Provider Approvals</h1>
<p class="mb-3">Total Pending: {{ pending_providers|length }}</p>
{% for provider in pending_providers %}
<div class="border-bottom pb-3 mb-3">
    <h2 class="h6">{{ provider.user.first_name }} {{ provider.user.last_name }}</h2>
    <p class="mb-0"><strong>Email:</strong> {{ provider.user.email }}</p>
    <p class="mb-0"><strong>Business Name:</strong> {{ provider.business_name }}</p>
    <p class="mb-0"><strong>Business Phone:</strong> {{ provider.business_phone_number }}</p>
    <p class="mb-0"><strong>Business Address:</strong> {{ provider.business_address }}</p>
    <p class="mb-2"><strong>Submitted:</strong> {{ provider.created_at }}</p>
    <form method="post" class="d-inline">
        {% csrf_token %}
        <input type="hidden" name="provider_id" value="{{ provider.id }}">
        <input type="hidden" name="action" value="approve">
        <button class="btn btn-primary btn-sm" type="submit">Approve</button>
    </form>
    <form method="post" class="d-inline">
        {% csrf_token %}
        <input type="hidden" name="provider_id" value="{{ provider.id }}">
        <input type="hidden" name="action" value="reject">
        <button class="btn btn-outline-secondary btn-sm" type="submit">Reject</button>
    </form>
</div>
{% empty %}
<p>No pending providers found.</p>
{% endfor %}
<div class="mt-4">
    <a href="{% url 'admin_app:admin_dashboard' %}">Back to Dashboard</a>
</div>
{% endblock %}
