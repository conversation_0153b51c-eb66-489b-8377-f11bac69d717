{% extends 'admin_app/base.html' %}

{% block title %}Provider Approval{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">Provider Approval</h1>
<div class="mb-4">
    <h2 class="h6">Provider Information</h2>
    <p class="mb-0"><strong>Name:</strong> {{ user_obj.first_name }} {{ user_obj.last_name }}</p>
    <p class="mb-0"><strong>Email:</strong> {{ user_obj.email }}</p>
    <p class="mb-0"><strong>Business Name:</strong> {{ provider_profile.business_name }}</p>
    <p class="mb-0"><strong>Business Phone:</strong> {{ provider_profile.business_phone_number }}</p>
    <p class="mb-0"><strong>Contact Person:</strong> {{ provider_profile.contact_person_name }}</p>
    <p class="mb-0"><strong>Business Address:</strong> {{ provider_profile.business_address }}</p>
    <p class="mb-0"><strong>City:</strong> {{ provider_profile.city }}</p>
    <p class="mb-0"><strong>State:</strong> {{ provider_profile.state }}</p>
    <p class="mb-0"><strong>Zip Code:</strong> {{ provider_profile.zip_code }}</p>
    <p class="mb-0"><strong>Current Status:</strong> {% if provider_profile.is_visible %}Approved{% else %}Pending{% endif %}</p>
</div>
<div class="mb-4">
    <h3 class="h6">Approval Actions</h3>
    <form method="post">
        {% csrf_token %}
        <div class="mb-3">
            <label for="reason" class="form-label">Reason/Notes</label>
            <textarea name="reason" id="reason" class="form-control" rows="4" placeholder="Enter approval or rejection reason..."></textarea>
        </div>
        <button type="submit" name="action" value="approve" class="btn btn-primary me-2">Approve Provider</button>
        <button type="submit" name="action" value="reject" class="btn btn-outline-secondary">Reject Provider</button>
    </form>
</div>
<div class="mt-4">
    <a href="{% url 'admin_app:user_detail' user_obj.id %}">Back to User Detail</a> |
    <a href="{% url 'admin_app:pending_providers' %}">Back to Pending Providers</a>
</div>
{% endblock %}
