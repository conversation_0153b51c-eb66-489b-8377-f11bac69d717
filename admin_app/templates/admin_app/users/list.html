{% extends 'admin_app/base.html' %}

{% block title %}User Management{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">User Management</h1>
<p class="mb-3">Total Users: {{ total_users }}</p>
{% for user in users %}
<div class="mb-4 border-bottom pb-2">
    <h2 class="h6 mb-1">{{ user.email }}</h2>
    <p class="mb-0">Name: {{ user.first_name }} {{ user.last_name }}</p>
    <p class="mb-0">Role: {{ user.get_role_display }}</p>
    <p class="mb-0">Status: {% if user.is_active %}Active{% else %}Inactive{% endif %}</p>
    <p class="mb-0">Joined: {{ user.date_joined }}</p>
</div>
{% empty %}
<p>No users found.</p>
{% endfor %}
{% if is_paginated %}
<nav aria-label="Pagination">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Prev</a></li>
        {% endif %}
        <li class="page-item active"><span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span></li>
        {% if page_obj.has_next %}
        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a></li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
