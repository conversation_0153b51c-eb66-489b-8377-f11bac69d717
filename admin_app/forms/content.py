"""Form classes for managing pages, blog posts, and media."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from utils.forms import AriaLabelMixin
from utils.sanitization import sanitize_html
from ..models import (
    BlogCategory,
    BlogPost,
    HomepageBlock,
    MediaFile,
    StaticPage,
)


class StaticPageForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating and editing static pages."""

    class Meta:
        model = StaticPage
        fields = [
            'title', 'slug', 'content', 'meta_title', 'meta_description',
            'meta_keywords', 'status', 'featured_image', 'is_featured'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter page title'
            }),
            'slug': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'URL slug (auto-generated if empty)'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 10,
                'placeholder': 'Enter page content (HTML supported)'
            }),
            'meta_title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SEO title (max 60 characters)',
                'maxlength': 60
            }),
            'meta_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'SEO description (max 160 characters)',
                'maxlength': 160
            }),
            'meta_keywords': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SEO keywords, comma-separated'
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'featured_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/png'
            }),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['slug'].required = False

    def clean_featured_image(self):
        """Validate featured image file."""
        image = self.cleaned_data.get('featured_image')
        if image:
            if image.size > 5 * 1024 * 1024:
                raise ValidationError(_('Image file size must be less than 5MB.'))
            allowed_extensions = ['.jpg', '.jpeg', '.png']
            file_extension = image.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise ValidationError(_('Only JPG and PNG images are allowed.'))
        return image

    def clean_content(self):
        """Sanitize HTML content."""
        content = self.cleaned_data.get('content', '')
        return sanitize_html(content)


class BlogCategoryForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating and editing blog categories."""

    class Meta:
        model = BlogCategory
        fields = ['name', 'slug', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter category name'
            }),
            'slug': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'URL slug (auto-generated if empty)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Optional category description'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['slug'].required = False


class BlogPostForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating and editing blog posts."""

    class Meta:
        model = BlogPost
        fields = [
            'title', 'slug', 'content', 'excerpt', 'category',
            'featured_image', 'meta_title', 'meta_description',
            'meta_keywords', 'status', 'is_featured'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter blog post title'
            }),
            'slug': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'URL slug (auto-generated if empty)'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 12,
                'placeholder': 'Enter blog post content (HTML supported)'
            }),
            'excerpt': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Short excerpt for previews (max 300 characters)',
                'maxlength': 300
            }),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'featured_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/png'
            }),
            'meta_title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SEO title (max 60 characters)',
                'maxlength': 60
            }),
            'meta_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'SEO description (max 160 characters)',
                'maxlength': 160
            }),
            'meta_keywords': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SEO keywords, comma-separated'
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = BlogCategory.objects.filter(is_active=True)
        self.fields['slug'].required = False

    def clean_featured_image(self):
        """Validate featured image file."""
        image = self.cleaned_data.get('featured_image')
        if image:
            if image.size > 5 * 1024 * 1024:
                raise ValidationError(_('Image file size must be less than 5MB.'))
            allowed_extensions = ['.jpg', '.jpeg', '.png']
            file_extension = image.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise ValidationError(_('Only JPG and PNG images are allowed.'))
        return image

    def clean_content(self):
        """Sanitize HTML content."""
        content = self.cleaned_data.get('content', '')
        return sanitize_html(content)


class HomepageBlockForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating and editing homepage blocks."""

    class Meta:
        model = HomepageBlock
        fields = [
            'block_type', 'title', 'subtitle', 'content', 'image',
            'button_text', 'button_url', 'is_active', 'display_order'
        ]
        widgets = {
            'block_type': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter block title'
            }),
            'subtitle': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter subtitle (optional)'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Enter block content (HTML supported)'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/png'
            }),
            'button_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Button text (optional)'
            }),
            'button_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'Button URL (optional)'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'display_order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default values for fields that have model defaults
        if not self.instance.pk:  # Only for new instances
            self.fields['display_order'].initial = 0
            self.fields['is_active'].initial = True

    def clean_image(self):
        """Validate block image file."""
        image = self.cleaned_data.get('image')
        if image:
            if image.size > 5 * 1024 * 1024:
                raise ValidationError(_('Image file size must be less than 5MB.'))
            allowed_extensions = ['.jpg', '.jpeg', '.png']
            file_extension = image.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise ValidationError(_('Only JPG and PNG images are allowed.'))
        return image

    def clean_content(self):
        """Sanitize HTML content."""
        content = self.cleaned_data.get('content', '')
        return sanitize_html(content)

    def clean(self):
        """Validate button text and URL together."""
        cleaned_data = super().clean()
        button_text = cleaned_data.get('button_text')
        button_url = cleaned_data.get('button_url')
        if button_text and not button_url:
            raise ValidationError(_('Button URL is required when button text is provided.'))
        if button_url and not button_text:
            raise ValidationError(_('Button text is required when button URL is provided.'))
        return cleaned_data


class MediaFileForm(AriaLabelMixin, forms.ModelForm):
    """Form for uploading and managing media files."""

    class Meta:
        model = MediaFile
        fields = ['title', 'file', 'description', 'alt_text', 'tags', 'is_public']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter descriptive title for the file'
            }),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Optional description of the file'
            }),
            'alt_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Alternative text for images (accessibility)'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Comma-separated tags for organizing files'
            }),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def clean_file(self):
        """Validate uploaded file."""
        file = self.cleaned_data.get('file')
        if file and file.size > 10 * 1024 * 1024:
            raise ValidationError(_('File size must be less than 10MB.'))
        return file

__all__ = [
    'StaticPageForm',
    'BlogCategoryForm',
    'BlogPostForm',
    'HomepageBlockForm',
    'MediaFileForm',
]
