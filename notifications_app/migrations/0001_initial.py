# Generated by Django 5.2.3 on 2025-06-16 14:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminAnnouncement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(help_text='Announcement title', max_length=255)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug auto-generated from the title', max_length=255, unique=True)),
                ('announcement_text', models.TextField(help_text='Announcement message content')),
                ('target_audience', models.CharField(choices=[('all', 'All Users'), ('customers', 'Customers Only'), ('providers', 'Service Providers Only'), ('admins', 'Admins Only')], default='all', help_text='Who should receive this announcement', max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('cancelled', 'Cancelled')], default='pending', help_text='Current status of the announcement', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, help_text='When the announcement was sent', null=True)),
                ('total_recipients', models.PositiveIntegerField(default=0, help_text='Total number of users who received this announcement')),
                ('created_by', models.ForeignKey(help_text='Admin user who created this announcement', on_delete=django.db.models.deletion.CASCADE, related_name='created_admin_announcements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Admin Announcement',
                'verbose_name_plural': 'Admin Announcements',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['target_audience', 'status'], name='notificatio_target__4fe376_idx'), models.Index(fields=['created_at'], name='notificatio_created_06ebea_idx'), models.Index(fields=['status'], name='notificatio_status_5b5c52_idx')],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('booking', 'Booking'), ('payment', 'Payment'), ('review', 'Review'), ('announcement', 'Announcement'), ('system', 'System')], help_text='Type of notification', max_length=20)),
                ('title', models.CharField(help_text='Notification title', max_length=255)),
                ('message', models.TextField(help_text='Notification message content')),
                ('read_status', models.CharField(choices=[('unread', 'Unread'), ('read', 'Read')], default='unread', help_text='Whether the notification has been read', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, help_text='When the notification was read', null=True)),
                ('related_object_id', models.PositiveIntegerField(blank=True, help_text='ID of related object (booking, payment, etc.)', null=True)),
                ('related_object_type', models.CharField(blank=True, help_text='Type of related object', max_length=50)),
                ('action_url', models.URLField(blank=True, help_text='URL for notification action (optional)')),
                ('user', models.ForeignKey(help_text='User who will receive this notification', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'read_status'], name='notificatio_user_id_40c963_idx'), models.Index(fields=['user', 'created_at'], name='notificatio_user_id_1174e9_idx'), models.Index(fields=['notification_type'], name='notificatio_notific_f7ef6c_idx')],
                'constraints': [models.UniqueConstraint(fields=('user', 'related_object_id', 'related_object_type'), name='unique_notification_object')],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('booking', 'Booking'), ('payment', 'Payment'), ('review', 'Review'), ('announcement', 'Announcement'), ('system', 'System')], max_length=20)),
                ('channel', models.CharField(choices=[('email', 'Email'), ('dashboard', 'Dashboard')], max_length=20)),
                ('is_enabled', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
                'unique_together': {('user', 'notification_type', 'channel')},
            },
        ),
    ]
