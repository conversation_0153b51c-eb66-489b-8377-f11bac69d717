# Generated by Django 5.2.3 on 2025-06-30 16:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailBounceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_address', models.EmailField(help_text='Email address that bounced', max_length=254)),
                ('bounce_type', models.CharField(choices=[('hard', 'Hard Bounce'), ('soft', 'Soft Bounce'), ('complaint', 'Complaint'), ('unsubscribe', 'Unsubscribe')], help_text='Type of bounce', max_length=20)),
                ('bounce_reason', models.TextField(help_text='Reason for the bounce')),
                ('bounce_count', models.PositiveIntegerField(default=1, help_text='Number of times this email has bounced')),
                ('first_bounce_at', models.DateTimeField(auto_now_add=True)),
                ('last_bounce_at', models.DateTimeField(auto_now=True)),
                ('is_suppressed', models.BooleanField(default=False, help_text='Whether this email should be suppressed from future sends')),
                ('suppressed_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, help_text='User associated with the email (if applicable)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='email_bounces', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Email Bounce Record',
                'verbose_name_plural': 'Email Bounce Records',
                'ordering': ['-last_bounce_at'],
                'indexes': [models.Index(fields=['email_address', 'is_suppressed'], name='notificatio_email_a_4e7bc4_idx'), models.Index(fields=['bounce_type', 'last_bounce_at'], name='notificatio_bounce__5ffd22_idx')],
                'unique_together': {('email_address', 'bounce_type')},
            },
        ),
        migrations.CreateModel(
            name='EmailDeliveryStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_id', models.CharField(help_text='Unique identifier for this email', max_length=255, unique=True)),
                ('recipient_email', models.EmailField(help_text='Email address of the recipient', max_length=254)),
                ('email_type', models.CharField(help_text='Type of email (welcome, booking_confirmation, etc.)', max_length=50)),
                ('subject', models.CharField(help_text='Email subject line', max_length=255)),
                ('status', models.CharField(choices=[('sent', 'Sent'), ('delivered', 'Delivered'), ('opened', 'Opened'), ('clicked', 'Clicked'), ('bounced', 'Bounced'), ('failed', 'Failed')], default='sent', help_text='Current delivery status', max_length=20)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('opened_at', models.DateTimeField(blank=True, null=True)),
                ('clicked_at', models.DateTimeField(blank=True, null=True)),
                ('bounced_at', models.DateTimeField(blank=True, null=True)),
                ('failed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, help_text='Error message if delivery failed')),
                ('bounce_type', models.CharField(blank=True, help_text='Type of bounce (hard, soft, etc.)', max_length=50)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional email metadata')),
                ('user', models.ForeignKey(blank=True, help_text='User who received the email (if applicable)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='email_deliveries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Email Delivery Status',
                'verbose_name_plural': 'Email Delivery Statuses',
                'ordering': ['-sent_at'],
                'indexes': [models.Index(fields=['recipient_email', 'status'], name='notificatio_recipie_165c3d_idx'), models.Index(fields=['email_type', 'sent_at'], name='notificatio_email_t_a71820_idx'), models.Index(fields=['user', 'status'], name='notificatio_user_id_a76fd5_idx')],
            },
        ),
    ]
