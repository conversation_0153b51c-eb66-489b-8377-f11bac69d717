"""
Management command to test email configuration.
Sends a test email to verify that email settings are working correctly.
"""

from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth import get_user_model
import smtplib

User = get_user_model()


class Command(BaseCommand):
    """Test email configuration command."""
    
    help = 'Test email configuration by sending a test email'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--recipient',
            type=str,
            help='Email address to send test email to (defaults to first superuser)',
        )
        parser.add_argument(
            '--skip-config-check',
            action='store_true',
            help='Skip email configuration validation',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing email configuration...')
        )
        
        # Check email configuration
        if not options['skip_config_check']:
            if not self.check_email_config():
                return
        
        # Get recipient email
        recipient_email = options.get('recipient')
        if not recipient_email:
            # Use first superuser email
            superuser = User.objects.filter(is_superuser=True).first()
            if superuser:
                recipient_email = superuser.email
            else:
                self.stdout.write(
                    self.style.ERROR('❌ No recipient specified and no superuser found')
                )
                return
        
        # Send test email
        self.send_test_email(recipient_email)

    def check_email_config(self):
        """Check if email configuration is properly set up."""
        self.stdout.write('🔍 Checking email configuration...')
        
        # Check email backend
        backend = getattr(settings, 'EMAIL_BACKEND', '')
        self.stdout.write(f'   Email Backend: {backend}')
        
        if 'console' in backend.lower():
            self.stdout.write(
                self.style.WARNING('   ⚠️ Using console email backend - emails will be printed to console')
            )
            return True
        
        # Check SMTP settings
        email_host = getattr(settings, 'EMAIL_HOST', '')
        email_port = getattr(settings, 'EMAIL_PORT', '')
        email_user = getattr(settings, 'EMAIL_HOST_USER', '')
        email_password = getattr(settings, 'EMAIL_HOST_PASSWORD', '')
        
        self.stdout.write(f'   Email Host: {email_host}')
        self.stdout.write(f'   Email Port: {email_port}')
        self.stdout.write(f'   Email User: {email_user}')
        self.stdout.write(f'   Email Password: {"*" * len(email_password) if email_password else "NOT SET"}')
        
        if not email_host:
            self.stdout.write(
                self.style.ERROR('❌ EMAIL_HOST is not configured')
            )
            return False
        
        if not email_password:
            self.stdout.write(
                self.style.ERROR('❌ EMAIL_HOST_PASSWORD is not configured')
            )
            return False
        
        self.stdout.write('   ✅ Email configuration looks good')
        return True

    def send_test_email(self, recipient_email):
        """Send a test email."""
        self.stdout.write(f'📧 Sending test email to: {recipient_email}')
        
        subject = 'CozyWish Email Configuration Test'
        message = """
Hello!

This is a test email from CozyWish to verify that email configuration is working correctly.

Email Configuration Details:
- Backend: {backend}
- Host: {host}
- Port: {port}
- Use TLS: {use_tls}

If you receive this email, your email configuration is working properly!

Best regards,
CozyWish Development Team
        """.format(
            backend=getattr(settings, 'EMAIL_BACKEND', 'Not set'),
            host=getattr(settings, 'EMAIL_HOST', 'Not set'),
            port=getattr(settings, 'EMAIL_PORT', 'Not set'),
            use_tls=getattr(settings, 'EMAIL_USE_TLS', 'Not set'),
        ).strip()
        
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_email],
                fail_silently=False
            )
            
            self.stdout.write(
                self.style.SUCCESS('✅ Test email sent successfully!')
            )
            
        except smtplib.SMTPException as e:
            self.stdout.write(
                self.style.ERROR(f'❌ SMTP error: {str(e)}')
            )
            if "Connection unexpectedly closed" in str(e):
                self.stdout.write(
                    self.style.WARNING('   This is often a transient error. Try again in a few moments.')
                )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Email sending failed: {str(e)}')
            )
