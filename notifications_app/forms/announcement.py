"""Forms for creating admin announcements."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ..models import AdminAnnouncement



class AdminAnnouncementForm(forms.ModelForm):
    """Form for creating admin announcements."""

    title = forms.CharField(
        label=_('Announcement Title'),
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter announcement title',
            'minlength': '5'
        }),
        help_text=_('Clear and descriptive title for the announcement')
    )

    announcement_text = forms.CharField(
        label=_('Announcement Message'),
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': 'Enter announcement message',
            'minlength': '20'
        }),
        help_text=_('Detailed announcement content that will be sent to users')
    )

    target_audience = forms.ChoiceField(
        choices=AdminAnnouncement.TARGET_AUDIENCE_CHOICES,
        label=_('Target Audience'),
        widget=forms.Select(attrs={'class': 'form-select'}),
        help_text=_('Select who should receive this announcement')
    )

    class Meta:
        model = AdminAnnouncement
        fields = ['title', 'announcement_text', 'target_audience']

    def clean_title(self):
        title = self.cleaned_data.get('title')
        if title and len(title.strip()) < 5:
            raise ValidationError(_('Title must be at least 5 characters long.'))
        return title.strip() if title else title

    def clean_announcement_text(self):
        text = self.cleaned_data.get('announcement_text')
        if text and len(text.strip()) < 20:
            raise ValidationError(_('Announcement message must be at least 20 characters long.'))
        return text.strip() if text else text
