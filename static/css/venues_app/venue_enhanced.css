/**
 * Enhanced Venue Management Styles
 * Styles for FAQ templates, rich text editor, SEO analysis, and improved forms
 */

:root {
    --cw-brand-primary: #2F160F;
    --cw-brand-secondary: #8B4513;
    --cw-neutral-light: #F5F5F5;
    --cw-neutral-medium: #E5E5E5;
    --cw-accent-warm: #D2691E;
    --cw-success: #10B981;
    --cw-warning: #F59E0B;
    --cw-error: #EF4444;
    --cw-info: #3B82F6;
    --cw-shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --cw-shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --cw-shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --cw-font-heading: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --cw-border-radius: 0.5rem;
    --cw-transition: all 0.2s ease;
}

/* =============================================================================
   Rich Text Editor Styles
   ============================================================================= */

.description-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--cw-neutral-light);
    border: 2px solid var(--cw-neutral-medium);
    border-bottom: none;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
    font-family: var(--cw-font-primary);
}

.toolbar-section {
    display: flex;
    gap: 0.25rem;
    padding-right: 0.75rem;
    border-right: 1px solid var(--cw-neutral-medium);
}

.toolbar-section:last-child {
    border-right: none;
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    background: white;
    border: 1px solid var(--cw-neutral-medium);
    border-radius: 0.25rem;
    color: var(--cw-brand-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--cw-transition);
    min-width: 2rem;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--cw-brand-primary);
    color: white;
    border-color: var(--cw-brand-primary);
    transform: translateY(-1px);
}

.toolbar-btn:active {
    transform: translateY(0);
}

.rich-text-wrapper {
    position: relative;
}

.rich-text-editor {
    border-radius: 0 0 var(--cw-border-radius) var(--cw-border-radius) !important;
    border-top: none !important;
    resize: vertical;
    font-family: var(--cw-font-primary);
    line-height: 1.6;
}

.rich-text-editor:focus {
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1) !important;
}

/* =============================================================================
   Character Counter Enhanced
   ============================================================================= */

.character-counter-enhanced {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: var(--cw-neutral-light);
    border: 1px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    font-family: var(--cw-font-primary);
}

.counter-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.char-count {
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.char-limit {
    color: #6b7280;
}

.counter-status {
    color: #6b7280;
    font-style: italic;
}

.counter-bar {
    width: 100%;
    height: 4px;
    background: var(--cw-neutral-medium);
    border-radius: 2px;
    overflow: hidden;
}

.counter-progress {
    height: 100%;
    background: var(--cw-info);
    transition: var(--cw-transition);
    border-radius: 2px;
}

.counter-progress.warning {
    background: var(--cw-warning);
}

.counter-progress.error {
    background: var(--cw-error);
}

.counter-progress.success {
    background: var(--cw-success);
}

/* =============================================================================
   SEO Analysis Panel
   ============================================================================= */

.seo-analysis-panel {
    margin-top: 1rem;
    background: white;
    border: 2px solid var(--cw-info);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-md);
    font-family: var(--cw-font-primary);
    max-width: 500px;
}

.seo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--cw-info);
    color: white;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
}

.seo-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: var(--cw-transition);
}

.close-panel:hover {
    background: rgba(255, 255, 255, 0.2);
}

.seo-content {
    padding: 1rem;
}

.seo-score {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--cw-neutral-medium);
}

.score-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: 3px solid var(--cw-neutral-medium);
    border-radius: 50%;
    background: var(--cw-neutral-light);
}

.score-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
}

.score-number.excellent {
    color: var(--cw-success);
}

.score-number.good {
    color: var(--cw-info);
}

.score-number.fair {
    color: var(--cw-warning);
}

.score-number.needs_improvement {
    color: var(--cw-error);
}

.score-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-status {
    flex: 1;
}

.status-text {
    font-weight: 600;
    font-size: 1.1rem;
}

.status-text.excellent {
    color: var(--cw-success);
}

.status-text.good {
    color: var(--cw-info);
}

.status-text.fair {
    color: var(--cw-warning);
}

.status-text.needs_improvement {
    color: var(--cw-error);
}

.seo-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--cw-neutral-medium);
}

.metric {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.metric-label {
    color: #6b7280;
}

.metric-value {
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.seo-suggestions h5,
.seo-keywords h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.suggestions-list {
    margin: 0;
    padding-left: 1.25rem;
    list-style-type: disc;
}

.suggestions-list li {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
    color: #4b5563;
}

.keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.keyword-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: var(--cw-neutral-light);
    border: 1px solid var(--cw-neutral-medium);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--cw-brand-primary);
}

.keyword-tag.found {
    background: var(--cw-success);
    color: white;
    border-color: var(--cw-success);
}

/* =============================================================================
   Templates Panel
   ============================================================================= */

.templates-panel {
    margin-top: 1rem;
    background: white;
    border: 2px solid var(--cw-accent-warm);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-md);
    font-family: var(--cw-font-primary);
    max-width: 700px;
}

.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--cw-accent-warm);
    color: white;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
}

.templates-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.templates-content {
    padding: 1rem;
}

.templates-help {
    margin: 0 0 1rem 0;
    color: #6b7280;
    font-style: italic;
    font-size: 0.875rem;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.template-card {
    background: var(--cw-neutral-light);
    border: 2px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    padding: 1rem;
    transition: var(--cw-transition);
    cursor: pointer;
}

.template-card:hover {
    border-color: var(--cw-accent-warm);
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-md);
}

.template-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.template-preview {
    margin: 0 0 0.75rem 0;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #4b5563;
}

.template-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.btn-use-template {
    width: 100%;
    padding: 0.5rem;
    background: var(--cw-accent-warm);
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--cw-transition);
}

.btn-use-template:hover {
    background: var(--cw-brand-primary);
    transform: translateY(-1px);
}

/* =============================================================================
   Notifications
   ============================================================================= */

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border: 2px solid var(--cw-info);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-lg);
    font-family: var(--cw-font-primary);
    font-size: 0.875rem;
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-color: var(--cw-success);
    background: #f0f9ff;
}

.notification.warning {
    border-color: var(--cw-warning);
    background: #fffbeb;
}

.notification.error {
    border-color: var(--cw-error);
    background: #fef2f2;
}

.notification-message {
    flex: 1;
    color: var(--cw-brand-primary);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    transition: var(--cw-transition);
}

.notification-close:hover {
    color: var(--cw-brand-primary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* =============================================================================
   FAQ Enhanced Styles
   ============================================================================= */

.faqs-list-section .faq-item[data-faq-id] {
    cursor: grab;
    position: relative;
}

.faqs-list-section .faq-item[data-faq-id]:active {
    cursor: grabbing;
}

.faqs-list-section .faq-item[data-faq-id].dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.faq-drag-handle {
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #d1d5db;
    cursor: grab;
    font-size: 1.25rem;
    opacity: 0;
    transition: var(--cw-transition);
}

.faq-item:hover .faq-drag-handle {
    opacity: 1;
}

.faq-drag-handle:active {
    cursor: grabbing;
}

/* =============================================================================
   Form Enhancements
   ============================================================================= */

.form-group-enhanced {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-label-enhanced {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-family: var(--cw-font-heading);
}

.form-control-enhanced {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    font-size: 1rem;
    font-family: var(--cw-font-primary);
    transition: var(--cw-transition);
    background: white;
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.form-control-enhanced.error {
    border-color: var(--cw-error);
}

.form-control-enhanced.success {
    border-color: var(--cw-success);
}

.form-help-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.form-error-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--cw-error);
    font-weight: 500;
}

/* =============================================================================
   Loading States
   ============================================================================= */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--cw-neutral-medium);
    border-radius: 50%;
    border-top-color: var(--cw-brand-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* =============================================================================
   Responsive Design
   ============================================================================= */

@media (max-width: 768px) {
    .description-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .toolbar-section {
        border-right: none;
        border-bottom: 1px solid var(--cw-neutral-medium);
        padding-bottom: 0.5rem;
        padding-right: 0;
    }

    .toolbar-section:last-child {
        border-bottom: none;
    }

    .seo-analysis-panel,
    .templates-panel {
        max-width: 100%;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .seo-metrics {
        grid-template-columns: 1fr;
    }

    .seo-score {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .notification {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .toolbar-btn {
        min-width: 1.5rem;
        padding: 0.375rem;
        font-size: 0.8rem;
    }

    .toolbar-btn i {
        font-size: 0.875rem;
    }

    .seo-content,
    .templates-content {
        padding: 0.75rem;
    }

    .template-card {
        padding: 0.75rem;
    }
}

/* =============================================================================
   Accessibility Enhancements
   ============================================================================= */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
.toolbar-btn:focus,
.btn-use-template:focus,
.close-panel:focus {
    outline: 2px solid var(--cw-brand-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .toolbar-btn,
    .template-card,
    .seo-analysis-panel,
    .templates-panel {
        border-width: 3px;
    }

    .keyword-tag {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .rich-text-editor,
    .form-control-enhanced {
        background: #1f2937;
        color: white;
        border-color: #374151;
    }

    .seo-analysis-panel,
    .templates-panel {
        background: #1f2937;
        color: white;
    }

    .template-card {
        background: #374151;
        border-color: #4b5563;
    }

    .notification {
        background: #1f2937;
        color: white;
    }
} 