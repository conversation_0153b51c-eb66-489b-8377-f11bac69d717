/**
 * Enhanced Location Autocomplete for Venue Forms
 * Provides improved fuzzy matching, better performance, and enhanced user experience
 */

class EnhancedLocationAutocomplete {
    constructor() {
        this.cache = new Map();
        this.selectedData = {};
        this.debounceTimers = new Map();
        this.currentRequests = new Map();
        this.selectedIndex = -1;
        this.initializeAutocomplete();
    }

    /**
     * Initialize autocomplete functionality
     */
    initializeAutocomplete() {
        // Find all location autocomplete inputs
        const locationInputs = document.querySelectorAll('.location-autocomplete');
        
        locationInputs.forEach(input => {
            this.setupAutocomplete(input);
        });

        // Handle state dropdown changes for hierarchical updates
        const stateSelect = document.querySelector('select[name="state"]');
        if (stateSelect) {
            stateSelect.addEventListener('change', (e) => {
                this.handleStateChange(e.target.value);
            });
        }

        // Handle form submission to clear cache if needed
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', () => {
                this.clearCache();
            });
        });
    }

    /**
     * Setup autocomplete for a specific input
     */
    setupAutocomplete(input) {
        const locationType = input.dataset.locationType || 'general';
        const inputId = input.id || `autocomplete_${Math.random().toString(36).substr(2, 9)}`;

        // Create suggestions container
        const suggestionsContainer = this.createSuggestionsContainer(input, inputId);

        // Handle input events with enhanced debouncing
        input.addEventListener('input', (e) => {
            this.handleInputWithDebounce(input, suggestionsContainer, locationType, inputId);
        });

        // Handle focus events
        input.addEventListener('focus', () => {
            if (input.value.length >= 2) {
                this.handleInput(input, suggestionsContainer, locationType, inputId);
            }
        });

        // Handle blur events with delay to allow for clicking suggestions
        input.addEventListener('blur', () => {
            setTimeout(() => {
                if (!this.isMouseOverSuggestions(suggestionsContainer)) {
                    suggestionsContainer.style.display = 'none';
                    this.selectedIndex = -1;
                }
            }, 150);
        });

        // Enhanced keyboard navigation
        input.addEventListener('keydown', (e) => {
            this.handleKeydown(e, suggestionsContainer, input);
        });

        // Handle click outside to hide suggestions
        document.addEventListener('click', (e) => {
            if (!input.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                suggestionsContainer.style.display = 'none';
                this.selectedIndex = -1;
            }
        });
    }

    /**
     * Create enhanced suggestions dropdown container
     */
    createSuggestionsContainer(input, inputId) {
        const container = document.createElement('div');
        container.className = 'location-suggestions enhanced-suggestions';
        container.id = `suggestions_${inputId}`;
        
        // Enhanced CSS styling
        container.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 250px;
            overflow-y: auto;
            z-index: 1050;
            display: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-size: 14px;
        `;

        // Make input container relative if not already
        const inputContainer = input.parentElement;
        if (window.getComputedStyle(inputContainer).position === 'static') {
            inputContainer.style.position = 'relative';
        }

        inputContainer.appendChild(container);
        return container;
    }

    /**
     * Handle input with enhanced debouncing
     */
    handleInputWithDebounce(input, container, locationType, inputId) {
        // Clear existing timer
        if (this.debounceTimers.has(inputId)) {
            clearTimeout(this.debounceTimers.get(inputId));
        }

        // Cancel existing request
        if (this.currentRequests.has(inputId)) {
            this.currentRequests.get(inputId).abort();
            this.currentRequests.delete(inputId);
        }

        // Set new timer with adaptive delay
        const query = input.value.trim();
        const delay = query.length < 3 ? 500 : 300; // Longer delay for shorter queries

        const timer = setTimeout(() => {
            this.handleInput(input, container, locationType, inputId);
        }, delay);

        this.debounceTimers.set(inputId, timer);
    }

    /**
     * Handle input changes and fetch suggestions with enhanced error handling
     */
    async handleInput(input, container, locationType, inputId) {
        const query = input.value.trim();
        
        if (query.length < 2) {
            container.style.display = 'none';
            this.selectedIndex = -1;
            return;
        }

        // Check cache first with versioned keys
        const cacheKey = `${locationType}_${query}_v2`;
        if (this.cache.has(cacheKey)) {
            const cachedData = this.cache.get(cacheKey);
            // Check if cache is still fresh (30 minutes)
            if (Date.now() - cachedData.timestamp < 1800000) {
                this.displaySuggestions(cachedData.suggestions, container, input);
                return;
            } else {
                this.cache.delete(cacheKey);
            }
        }

        try {
            // Show enhanced loading indicator
            this.showLoadingState(container);

            // Create AbortController for request cancellation
            const abortController = new AbortController();
            this.currentRequests.set(inputId, abortController);

            // Fetch suggestions from enhanced API with timeout
            const timeoutId = setTimeout(() => abortController.abort(), 10000); // 10 second timeout

            const response = await fetch(`/venues/location-autocomplete/?q=${encodeURIComponent(query)}`, {
                signal: abortController.signal,
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            clearTimeout(timeoutId);
            this.currentRequests.delete(inputId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Cache results with timestamp
            this.cache.set(cacheKey, {
                suggestions: data.suggestions,
                timestamp: Date.now()
            });

            // Display suggestions
            this.displaySuggestions(data.suggestions, container, input);

        } catch (error) {
            this.currentRequests.delete(inputId);
            
            if (error.name === 'AbortError') {
                // Request was cancelled, ignore
                return;
            }

            console.error('Error fetching location suggestions:', error);
            this.showErrorState(container, error.message);
        }
    }

    /**
     * Show enhanced loading state
     */
    showLoadingState(container) {
        container.innerHTML = `
            <div class="suggestion-item loading-item">
                <div class="loading-spinner"></div>
                <span>Searching locations...</span>
            </div>
        `;
        container.style.display = 'block';
    }

    /**
     * Show error state with retry option
     */
    showErrorState(container, errorMessage) {
        container.innerHTML = `
            <div class="suggestion-item error-item">
                <span class="error-icon">⚠️</span>
                <div class="error-content">
                    <div>Unable to load suggestions</div>
                    <small>${errorMessage}</small>
                </div>
            </div>
        `;
        container.style.display = 'block';
    }

    /**
     * Display suggestions with enhanced formatting and accessibility
     */
    displaySuggestions(suggestions, container, input) {
        if (!suggestions || suggestions.length === 0) {
            container.innerHTML = `
                <div class="suggestion-item no-results-item">
                    <span class="no-results-icon">🔍</span>
                    <span>No locations found</span>
                </div>
            `;
            container.style.display = 'block';
            return;
        }

        const html = suggestions.map((suggestion, index) => {
            const typeIcon = this.getTypeIcon(suggestion.type);
            const similarity = suggestion.similarity ? Math.round(suggestion.similarity * 100) : 0;
            
            return `
                <div class="suggestion-item" 
                     data-index="${index}" 
                     data-suggestion='${JSON.stringify(suggestion)}'
                     role="option"
                     aria-selected="false">
                    <div class="suggestion-main">
                        <span class="suggestion-icon" aria-hidden="true">${typeIcon}</span>
                        <div class="suggestion-text">
                            <div class="suggestion-label">${this.highlightMatch(suggestion.label, input.value)}</div>
                            ${suggestion.type === 'city' && suggestion.county ? 
                                `<div class="suggestion-details">${suggestion.county}, ${suggestion.state}</div>` : 
                                ''
                            }
                        </div>
                    </div>
                    ${similarity > 0 && similarity < 100 ? 
                        `<div class="suggestion-score" title="Match confidence">${similarity}%</div>` : 
                        ''
                    }
                </div>
            `;
        }).join('');

        container.innerHTML = html;
        container.style.display = 'block';
        container.setAttribute('role', 'listbox');

        // Add enhanced interaction handlers
        this.addSuggestionHandlers(container, input);
        this.selectedIndex = -1;
    }

    /**
     * Add suggestion interaction handlers
     */
    addSuggestionHandlers(container, input) {
        const suggestionItems = container.querySelectorAll('.suggestion-item');
        
        suggestionItems.forEach((item, index) => {
            // Mouse events
            item.addEventListener('mouseenter', () => {
                this.updateSelection(suggestionItems, index);
                this.selectedIndex = index;
            });

            item.addEventListener('mouseleave', () => {
                this.updateSelection(suggestionItems, -1);
            });

            item.addEventListener('click', (e) => {
                e.preventDefault();
                const suggestionData = JSON.parse(item.getAttribute('data-suggestion'));
                this.selectSuggestion(suggestionData, input);
                container.style.display = 'none';
                this.selectedIndex = -1;
            });
        });
    }

    /**
     * Enhanced keyboard navigation
     */
    handleKeydown(event, container, input) {
        const suggestions = container.querySelectorAll('.suggestion-item:not(.loading-item):not(.error-item):not(.no-results-item)');
        
        if (suggestions.length === 0) return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, suggestions.length - 1);
                this.updateSelection(suggestions, this.selectedIndex);
                break;

            case 'ArrowUp':
                event.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection(suggestions, this.selectedIndex);
                break;

            case 'Enter':
                event.preventDefault();
                if (this.selectedIndex >= 0 && suggestions[this.selectedIndex]) {
                    const suggestionData = JSON.parse(suggestions[this.selectedIndex].getAttribute('data-suggestion'));
                    this.selectSuggestion(suggestionData, input);
                    container.style.display = 'none';
                    this.selectedIndex = -1;
                }
                break;

            case 'Escape':
                container.style.display = 'none';
                this.selectedIndex = -1;
                input.blur();
                break;

            case 'Tab':
                // Allow tab to move to next field
                container.style.display = 'none';
                this.selectedIndex = -1;
                break;
        }
    }

    /**
     * Update visual selection state
     */
    updateSelection(suggestions, selectedIndex) {
        suggestions.forEach((item, index) => {
            if (index === selectedIndex) {
                item.classList.add('selected');
                item.setAttribute('aria-selected', 'true');
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('selected');
                item.setAttribute('aria-selected', 'false');
            }
        });
    }

    /**
     * Get enhanced icon for suggestion type
     */
    getTypeIcon(type) {
        const icons = {
            'city': '🏙️',
            'county': '🏞️',
            'state': '🗺️'
        };
        return icons[type] || '📍';
    }

    /**
     * Enhanced text highlighting with better regex handling
     */
    highlightMatch(text, query) {
        if (!query || query.length < 2) return text;
        
        try {
            const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`(${escapedQuery})`, 'gi');
            return text.replace(regex, '<mark class="highlight">$1</mark>');
        } catch (e) {
            // Fallback if regex fails
            return text;
        }
    }

    /**
     * Enhanced suggestion selection with validation
     */
    selectSuggestion(suggestion, input) {
        if (!suggestion || !input) return;

        // Set input value
        input.value = suggestion.value;
        
        // Store suggestion data for form processing
        this.selectedData[input.name] = suggestion;
        
        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);

        // Auto-fill related fields if applicable
        this.autoFillRelatedFields(suggestion, input);

        // Add visual feedback for successful selection
        this.showSelectionFeedback(input);
    }

    /**
     * Auto-fill related location fields based on suggestion
     */
    autoFillRelatedFields(suggestion, currentInput) {
        const form = currentInput.closest('form');
        if (!form) return;

        // Map suggestion data to form fields
        const fieldMappings = {
            'state': suggestion.state_id,
            'county': suggestion.county,
            'city': suggestion.city
        };

        Object.entries(fieldMappings).forEach(([fieldName, value]) => {
            if (!value) return;

            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field && field !== currentInput && !field.value) {
                field.value = value;
                
                // Trigger change event for any dependent functionality
                const changeEvent = new Event('change', { bubbles: true });
                field.dispatchEvent(changeEvent);
            }
        });

        // Handle hidden fields for USCity integration
        const usCityIdField = form.querySelector('[name="us_city_id"]');
        if (usCityIdField && suggestion.city_id) {
            usCityIdField.value = suggestion.city_id;
        }
    }

    /**
     * Show visual feedback for successful selection
     */
    showSelectionFeedback(input) {
        input.classList.add('selection-success');
        setTimeout(() => {
            input.classList.remove('selection-success');
        }, 1000);
    }

    /**
     * Handle state change with enhanced county/city updates
     */
    async handleStateChange(stateCode) {
        if (!stateCode) return;

        const countySelects = document.querySelectorAll('select[name="county"]');
        const citySelects = document.querySelectorAll('select[name="city"]');

        try {
            const response = await fetch(`/venues/get-location-data/?type=counties&state=${encodeURIComponent(stateCode)}`);
            const data = await response.json();

            countySelects.forEach(select => {
                select.innerHTML = '<option value="">Select county</option>';
                data.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    select.appendChild(optionElement);
                });
            });

            // Clear city selects
            citySelects.forEach(select => {
                select.innerHTML = '<option value="">Select city</option>';
            });

        } catch (error) {
            console.error('Error loading counties:', error);
        }
    }

    /**
     * Check if mouse is over suggestions container
     */
    isMouseOverSuggestions(container) {
        return container.matches(':hover');
    }

    /**
     * Clear cache (useful for form resets)
     */
    clearCache() {
        this.cache.clear();
        this.selectedData = {};
    }

    /**
     * Get cached suggestion data for a field
     */
    getSelectedData(fieldName) {
        return this.selectedData[fieldName] || null;
    }
}

// Enhanced CSS styles for better visual feedback
const enhancedStyles = `
<style>
.enhanced-suggestions {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
}

.suggestion-main {
    display: flex;
    align-items: center;
    flex: 1;
}

.suggestion-icon {
    margin-right: 10px;
    font-size: 16px;
}

.suggestion-text {
    flex: 1;
}

.suggestion-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.suggestion-details {
    font-size: 12px;
    color: #666;
}

.suggestion-score {
    font-size: 11px;
    color: #888;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
}

.highlight {
    background-color: #fff3cd;
    font-weight: 600;
    padding: 0 2px;
    border-radius: 2px;
}

.loading-item, .error-item, .no-results-item {
    display: flex;
    align-items: center;
    padding: 16px;
    color: #666;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-item {
    color: #dc3545;
}

.error-icon {
    margin-right: 10px;
}

.error-content small {
    display: block;
    margin-top: 4px;
    opacity: 0.8;
}

.selection-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    transition: all 0.3s ease;
}

.no-results-icon {
    margin-right: 10px;
    opacity: 0.5;
}
</style>
`;

// Inject enhanced styles
document.head.insertAdjacentHTML('beforeend', enhancedStyles);

// Initialize enhanced autocomplete when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new EnhancedLocationAutocomplete();
    });
} else {
    new EnhancedLocationAutocomplete();
}

// Re-initialize on dynamic content changes (for SPA compatibility)
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length > 0) {
                const hasLocationInputs = Array.from(mutation.addedNodes).some(
                    node => node.nodeType === 1 && 
                    (node.querySelector && node.querySelector('.location-autocomplete'))
                );
                if (hasLocationInputs) {
                    new EnhancedLocationAutocomplete();
                }
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
} 