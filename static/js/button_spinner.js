document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.spinner-button').forEach(function(btn) {
        btn.addEventListener('click', function () {
            if (btn.dataset.spinnerActive) return;
            btn.dataset.spinnerActive = 'true';
            var spinner = document.createElement('span');
            spinner.className = 'spinner-border spinner-border-sm ms-2';
            spinner.setAttribute('role', 'status');
            spinner.setAttribute('aria-hidden', 'true');
            btn.appendChild(spinner);
            if (btn.tagName === 'BUTTON') {
                btn.disabled = true;
            }
        });
    });
});
