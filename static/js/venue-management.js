/**
 * Enhanced Venue Management JavaScript
 * Provides improved error handling, status indicators, real-time validation, and guided setup
 */

class VenueManager {
    constructor() {
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        this.statusUpdateInterval = null;
        this.progressAnimationDuration = 800;
        this.guidedSetupSteps = [];
        this.currentStep = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeStatusIndicators();
        this.setupRealTimeValidation();
        this.initializeProgressAnimations();
        this.setupAutoSave();
        this.initializeGuidedSetup();
        this.setupLivePreview();
    }
    
    // === Enhanced Real-Time Validation ===
    
    setupRealTimeValidation() {
        const forms = document.querySelectorAll('form[data-validation="real-time"]');
        forms.forEach(form => {
            this.attachFormValidation(form);
        });
        
        // Setup individual field validation
        const validatedFields = document.querySelectorAll('[data-validation]');
        validatedFields.forEach(field => {
            this.attachFieldValidation(field);
        });
    }
    
    attachFormValidation(form) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        const fields = form.querySelectorAll('input, textarea, select');
        
        // Real-time form validation
        fields.forEach(field => {
            field.addEventListener('input', () => {
                this.validateField(field);
                this.updateFormValidation(form, submitButton);
            });
            
            field.addEventListener('blur', () => {
                this.validateField(field, true);
            });
        });
        
        // Initial validation
        this.updateFormValidation(form, submitButton);
    }
    
    attachFieldValidation(field) {
        const validationType = field.getAttribute('data-validation');
        const fieldContainer = field.closest('.form-group, .mb-3, .form-floating');
        
        // Create validation feedback elements
        if (!fieldContainer.querySelector('.validation-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'validation-feedback';
            fieldContainer.appendChild(feedback);
        }
        
        // Character counter for text fields
        if (field.hasAttribute('maxlength')) {
            this.createCharacterCounter(field, fieldContainer);
        }
        
        // Field-specific validation
        field.addEventListener('input', debounce(() => {
            this.validateField(field);
        }, 300));
        
        field.addEventListener('blur', () => {
            this.validateField(field, true);
        });
    }
    
    async validateField(field, showErrors = false) {
        const validationType = field.getAttribute('data-validation');
        const value = field.value.trim();
        const fieldContainer = field.closest('.form-group, .mb-3, .form-floating');
        const feedback = fieldContainer?.querySelector('.validation-feedback');
        
        let isValid = true;
        let errorMessages = [];
        
        // Basic validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessages.push('This field is required');
        }
        
        // Type-specific validation
        switch (validationType) {
            case 'venue-name':
                if (value.length < 2) {
                    isValid = false;
                    errorMessages.push('Venue name must be at least 2 characters');
                }
                if (value.length > 100) {
                    isValid = false;
                    errorMessages.push('Venue name cannot exceed 100 characters');
                }
                break;
                
            case 'description':
                if (value.length < 10) {
                    isValid = false;
                    errorMessages.push('Description must be at least 10 characters');
                }
                if (value.length > 500) {
                    isValid = false;
                    errorMessages.push('Description cannot exceed 500 characters');
                }
                break;
                
            case 'phone':
                if (value && !this.validatePhoneNumber(value)) {
                    isValid = false;
                    errorMessages.push('Please enter a valid phone number');
                }
                break;
                
            case 'email':
                if (value && !this.validateEmail(value)) {
                    isValid = false;
                    errorMessages.push('Please enter a valid email address');
                }
                break;
                
            case 'url':
                if (value && !this.validateURL(value)) {
                    isValid = false;
                    errorMessages.push('Please enter a valid URL');
                }
                break;
                
            case 'social-media':
                if (value && !this.validateSocialMediaURL(value, field.getAttribute('data-platform'))) {
                    isValid = false;
                    errorMessages.push('Please enter a valid social media URL');
                }
                break;
        }
        
        // Update field appearance
        this.updateFieldValidation(field, {
            isValid,
            errors: showErrors ? errorMessages : [],
            warnings: this.getFieldWarnings(field, value)
        });
        
        // Update character counter
        this.updateCharacterCounter(field);
        
        return { isValid, errors: errorMessages };
    }
    
    validatePhoneNumber(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
        return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
    }
    
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    validateURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    validateSocialMediaURL(url, platform) {
        if (url.startsWith('@')) {
            return url.length > 1; // Handle username format
        }
        
        const platformDomains = {
            'instagram': ['instagram.com', 'instagr.am'],
            'facebook': ['facebook.com', 'fb.com'],
            'twitter': ['twitter.com', 'x.com'],
            'linkedin': ['linkedin.com']
        };
        
        try {
            const urlObj = new URL(url);
            const domains = platformDomains[platform] || [];
            return domains.some(domain => urlObj.hostname.includes(domain));
        } catch {
            return false;
        }
    }
    
    getFieldWarnings(field, value) {
        const warnings = [];
        const maxLength = field.getAttribute('maxlength');
        
        if (maxLength && value.length > maxLength * 0.8) {
            warnings.push(`Approaching character limit (${value.length}/${maxLength})`);
        }
        
        return warnings;
    }
    
    updateFieldValidation(field, result) {
        const fieldContainer = field.closest('.form-group, .mb-3, .form-floating');
        const feedback = fieldContainer?.querySelector('.validation-feedback');
        
        // Remove existing classes
        field.classList.remove('is-valid', 'is-invalid');
        fieldContainer?.classList.remove('has-success', 'has-error', 'has-warning');
        
        if (result.isValid) {
            field.classList.add('is-valid');
            fieldContainer?.classList.add('has-success');
            if (feedback) {
                feedback.innerHTML = '<i class="fas fa-check text-success"></i> <span class="text-success">Looks good!</span>';
            }
        } else if (result.errors.length > 0) {
            field.classList.add('is-invalid');
            fieldContainer?.classList.add('has-error');
            if (feedback) {
                feedback.innerHTML = result.errors.map(error => 
                    `<i class="fas fa-exclamation-circle text-danger"></i> <span class="text-danger">${error}</span>`
                ).join('<br>');
            }
        }
        
        // Show warnings
        if (result.warnings && result.warnings.length > 0 && result.isValid) {
            fieldContainer?.classList.add('has-warning');
            if (feedback) {
                feedback.innerHTML += '<br>' + result.warnings.map(warning => 
                    `<i class="fas fa-exclamation-triangle text-warning"></i> <span class="text-warning">${warning}</span>`
                ).join('<br>');
            }
        }
    }
    
    updateFormValidation(form, submitButton) {
        const requiredFields = form.querySelectorAll('[required]');
        const invalidFields = form.querySelectorAll('.is-invalid');
        
        let formValid = true;
        
        // Check required fields
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                formValid = false;
            }
        });
        
        // Check for invalid fields
        if (invalidFields.length > 0) {
            formValid = false;
        }
        
        // Update submit button
        if (submitButton) {
            submitButton.disabled = !formValid;
            if (formValid) {
                submitButton.classList.remove('btn-disabled');
                submitButton.classList.add('btn-cw-primary');
            } else {
                submitButton.classList.add('btn-disabled');
                submitButton.classList.remove('btn-cw-primary');
            }
        }
        
        return formValid;
    }
    
    // === Character Counter ===
    
    createCharacterCounter(field, container) {
        if (container.querySelector('.character-counter')) return;
        
        const maxLength = field.getAttribute('maxlength');
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        counter.innerHTML = `<span class="current">0</span> / <span class="max">${maxLength}</span> characters`;
        
        container.appendChild(counter);
        this.updateCharacterCounter(field);
    }
    
    updateCharacterCounter(field) {
        const container = field.closest('.form-group, .mb-3, .form-floating');
        const counter = container?.querySelector('.character-counter');
        
        if (!counter) return;
        
        const maxLength = parseInt(field.getAttribute('maxlength'));
        const currentLength = field.value.length;
        const currentSpan = counter.querySelector('.current');
        
        if (currentSpan) {
            currentSpan.textContent = currentLength;
        }
        
        // Update counter styling
        counter.classList.remove('warning', 'danger');
        if (currentLength > maxLength * 0.9) {
            counter.classList.add('danger');
        } else if (currentLength > maxLength * 0.75) {
            counter.classList.add('warning');
        }
    }
    
    // === Guided Setup Wizard ===
    
    initializeGuidedSetup() {
        const setupWizard = document.querySelector('[data-setup-wizard]');
        if (!setupWizard) return;
        
        this.guidedSetupSteps = [
            { id: 'basic', title: 'Basic Information', fields: ['venue_name', 'short_description'] },
            { id: 'location', title: 'Location Details', fields: ['state', 'county', 'city', 'street_number', 'street_name'] },
            { id: 'categories', title: 'Categories', fields: ['categories'] },
            { id: 'contact', title: 'Contact Information', fields: ['phone', 'email', 'website_url'] },
            { id: 'social', title: 'Social Media', fields: ['instagram_url', 'facebook_url', 'twitter_url'] }
        ];
        
        this.createWizardInterface();
        this.updateWizardProgress();
    }
    
    createWizardInterface() {
        const wizard = document.querySelector('[data-setup-wizard]');
        if (!wizard) return;
        
        // Create progress indicator
        const progressContainer = document.createElement('div');
        progressContainer.className = 'wizard-progress';
        progressContainer.innerHTML = `
            <div class="progress-steps">
                ${this.guidedSetupSteps.map((step, index) => `
                    <div class="progress-step" data-step="${index}">
                        <div class="step-number">${index + 1}</div>
                        <div class="step-title">${step.title}</div>
                    </div>
                `).join('')}
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: 0%"></div>
            </div>
        `;
        
        wizard.insertBefore(progressContainer, wizard.firstChild);
        
        // Create navigation buttons
        const navigation = document.createElement('div');
        navigation.className = 'wizard-navigation';
        navigation.innerHTML = `
            <button type="button" class="btn btn-secondary" id="wizard-prev" disabled>
                <i class="fas fa-arrow-left"></i> Previous
            </button>
            <button type="button" class="btn btn-cw-primary" id="wizard-next">
                Next <i class="fas fa-arrow-right"></i>
            </button>
            <button type="submit" class="btn btn-cw-primary" id="wizard-submit" style="display: none;">
                <i class="fas fa-check"></i> Complete Setup
            </button>
        `;
        
        wizard.appendChild(navigation);
        
        this.attachWizardListeners();
    }
    
    attachWizardListeners() {
        const prevBtn = document.getElementById('wizard-prev');
        const nextBtn = document.getElementById('wizard-next');
        
        nextBtn?.addEventListener('click', () => this.nextStep());
        prevBtn?.addEventListener('click', () => this.previousStep());
        
        // Auto-advance on field completion
        this.guidedSetupSteps.forEach((step, index) => {
            step.fields.forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    field.addEventListener('input', debounce(() => {
                        this.checkStepCompletion(index);
                    }, 500));
                }
            });
        });
    }
    
    nextStep() {
        if (this.currentStep < this.guidedSetupSteps.length - 1) {
            this.currentStep++;
            this.updateWizardView();
        }
    }
    
    previousStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.updateWizardView();
        }
    }
    
    updateWizardView() {
        // Show/hide form sections based on current step
        this.guidedSetupSteps.forEach((step, index) => {
            const isCurrentStep = index === this.currentStep;
            
            step.fields.forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                const container = field?.closest('.form-group, .mb-3, .form-floating');
                
                if (container) {
                    container.style.display = isCurrentStep ? 'block' : 'none';
                    if (isCurrentStep && field) {
                        field.focus();
                    }
                }
            });
        });
        
        this.updateWizardProgress();
        this.updateWizardNavigation();
    }
    
    updateWizardProgress() {
        const steps = document.querySelectorAll('.progress-step');
        const progressBar = document.querySelector('.wizard-progress .progress-bar');
        
        steps.forEach((step, index) => {
            step.classList.toggle('active', index === this.currentStep);
            step.classList.toggle('completed', index < this.currentStep);
        });
        
        const progress = ((this.currentStep + 1) / this.guidedSetupSteps.length) * 100;
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }
    
    updateWizardNavigation() {
        const prevBtn = document.getElementById('wizard-prev');
        const nextBtn = document.getElementById('wizard-next');
        const submitBtn = document.getElementById('wizard-submit');
        
        if (prevBtn) prevBtn.disabled = this.currentStep === 0;
        
        const isLastStep = this.currentStep === this.guidedSetupSteps.length - 1;
        if (nextBtn) nextBtn.style.display = isLastStep ? 'none' : 'block';
        if (submitBtn) submitBtn.style.display = isLastStep ? 'block' : 'none';
    }
    
    checkStepCompletion(stepIndex) {
        const step = this.guidedSetupSteps[stepIndex];
        const isComplete = step.fields.every(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            return field && field.value.trim();
        });
        
        // Auto-advance if step is complete and it's the current step
        if (isComplete && stepIndex === this.currentStep && stepIndex < this.guidedSetupSteps.length - 1) {
            setTimeout(() => this.nextStep(), 1000);
        }
        
        return isComplete;
    }
    
    // === Live Preview ===
    
    setupLivePreview() {
        const previewContainer = document.querySelector('[data-live-preview]');
        if (!previewContainer) return;
        
        const previewFields = {
            'venue_name': '.preview-title',
            'short_description': '.preview-description',
            'phone': '.preview-phone',
            'email': '.preview-email',
            'website_url': '.preview-website'
        };
        
        Object.entries(previewFields).forEach(([fieldName, selector]) => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            const previewElement = previewContainer.querySelector(selector);
            
            if (field && previewElement) {
                field.addEventListener('input', debounce(() => {
                    this.updatePreview(field, previewElement);
                }, 300));
                
                // Initial update
                this.updatePreview(field, previewElement);
            }
        });
    }
    
    updatePreview(field, previewElement) {
        const value = field.value.trim();
        const fieldName = field.getAttribute('name');
        
        switch (fieldName) {
            case 'venue_name':
                previewElement.textContent = value || 'Your Venue Name';
                break;
            case 'short_description':
                previewElement.textContent = value || 'Add a description to tell customers about your venue...';
                break;
            case 'phone':
                previewElement.textContent = value || 'Phone not provided';
                previewElement.style.display = value ? 'block' : 'none';
                break;
            case 'email':
                previewElement.textContent = value || 'Email not provided';
                previewElement.style.display = value ? 'block' : 'none';
                break;
            case 'website_url':
                previewElement.textContent = value || 'Website not provided';
                previewElement.href = value || '#';
                previewElement.style.display = value ? 'block' : 'none';
                break;
        }
    }
    
    // === Status Indicators & Visual Feedback ===
    
    initializeStatusIndicators() {
        // Animate progress circles on load
        const progressCircles = document.querySelectorAll('.progress-circle-fill');
        progressCircles.forEach((circle, index) => {
            setTimeout(() => {
                this.animateProgressCircle(circle);
            }, index * 200);
        });
        
        // Initialize status badges with hover effects
        this.setupStatusBadges();
        
        // Setup completion celebration
        this.checkCompletionStatus();
    }
    
    animateProgressCircle(circle) {
        const percentage = parseInt(circle.getAttribute('data-percentage'));
        const circumference = 2 * Math.PI * 90; // radius = 90
        const offset = circumference - (percentage / 100) * circumference;
        
        // Start from 0 and animate to target
        circle.style.strokeDasharray = `0 ${circumference}`;
        
        // Animate to final position
        setTimeout(() => {
            circle.style.strokeDasharray = `${circumference - offset} ${circumference}`;
            circle.style.transition = `stroke-dasharray ${this.progressAnimationDuration}ms ease-out`;
        }, 100);
        
        // Add pulsing effect for high completion
        if (percentage >= 90) {
            setTimeout(() => {
                circle.parentElement.classList.add('pulse-success');
            }, this.progressAnimationDuration);
        }
    }
    
    setupStatusBadges() {
        const statusCards = document.querySelectorAll('.status-card');
        statusCards.forEach(card => {
            // Add completion animations
            if (card.classList.contains('completed')) {
                this.addCompletionAnimation(card);
            }
            
            // Add hover enhancements
            card.addEventListener('mouseenter', () => {
                this.enhanceCardOnHover(card);
            });
            
            card.addEventListener('mouseleave', () => {
                this.resetCardHover(card);
            });
        });
    }
    
    addCompletionAnimation(card) {
        const icon = card.querySelector('.status-icon');
        if (icon && icon.classList.contains('completed')) {
            // Add checkmark bounce animation
            icon.style.animation = 'checkmarkBounce 0.6s ease-in-out';
            
            // Add success particle effect
            this.createSuccessParticles(icon);
        }
    }
    
    createSuccessParticles(element) {
        const particles = 5;
        for (let i = 0; i < particles; i++) {
            const particle = document.createElement('div');
            particle.className = 'success-particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: #10b981;
                border-radius: 50%;
                pointer-events: none;
                animation: particleFloat 1s ease-out forwards;
                animation-delay: ${i * 100}ms;
            `;
            
            element.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1200);
        }
    }
    
    checkCompletionStatus() {
        const progressPercentage = this.getOverallProgress();
        
        if (progressPercentage >= 100) {
            this.triggerCompletionCelebration();
        } else if (progressPercentage >= 80) {
            this.showAlmostCompleteMessage();
        }
    }
    
    triggerCompletionCelebration() {
        // Trigger confetti effect
        this.createConfettiEffect();
        
        // Show completion message
        this.showStatusMessage('Congratulations! Your venue is 100% complete!', 'success', 5000);
        
        // Add celebration class to main container
        const dashboard = document.querySelector('.progress-dashboard');
        if (dashboard) {
            dashboard.classList.add('celebration-mode');
            setTimeout(() => {
                dashboard.classList.remove('celebration-mode');
            }, 3000);
        }
    }
    
    createConfettiEffect() {
        const colors = ['#10b981', '#f59e0b', '#3b82f6', '#ef4444', '#8b5cf6'];
        const confettiCount = 50;
        
        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.cssText = `
                position: fixed;
                width: 8px;
                height: 8px;
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                top: -10px;
                left: ${Math.random() * 100}%;
                z-index: 10000;
                animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
            `;
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                if (confetti.parentNode) {
                    confetti.parentNode.removeChild(confetti);
                }
            }, 5000);
        }
    }
    
    // === Enhanced Error Handling ===
    
    async handleOperation(operation, options = {}) {
        const {
            maxRetries = this.retryAttempts,
            retryDelay = this.retryDelay,
            showLoading = true,
            context = {}
        } = options;
        
        let attempt = 0;
        let lastError = null;
        
        if (showLoading) {
            this.showLoadingState();
        }
        
        while (attempt <= maxRetries) {
            try {
                const result = await operation();
                
                if (showLoading) {
                    this.hideLoadingState();
                }
                
                return result;
            } catch (error) {
                lastError = error;
                attempt++;
                
                if (attempt <= maxRetries) {
                    this.showRetryMessage(attempt, maxRetries);
                    await this.delay(retryDelay);
                    retryDelay *= 1.5; // Exponential backoff
                } else {
                    if (showLoading) {
                        this.hideLoadingState();
                    }
                    this.handleError(lastError, context);
                    throw lastError;
                }
            }
        }
    }
    
    handleError(error, context = {}) {
        console.error('Venue Management Error:', error, context);
        
        const errorInfo = this.parseError(error);
        this.showErrorMessage(errorInfo, context);
        this.trackError(errorInfo, context);
    }
    
    parseError(error) {
        let errorInfo = {
            type: 'unknown',
            title: 'An error occurred',
            message: 'Please try again later.',
            suggestions: [],
            retryable: false
        };
        
        if (error.response) {
            // HTTP error
            const status = error.response.status;
            errorInfo.type = 'http';
            errorInfo.title = this.getHttpErrorMessage(status);
            errorInfo.message = error.response.data?.message || `HTTP ${status} Error`;
            errorInfo.suggestions = this.getHttpErrorSuggestions(status);
            errorInfo.retryable = this.isRetryableHttpError(status);
        } else if (error.name === 'ValidationError') {
            errorInfo.type = 'validation';
            errorInfo.title = 'Validation Error';
            errorInfo.message = error.message;
            errorInfo.suggestions = ['Please check your input and try again'];
            errorInfo.retryable = false;
        } else if (error.name === 'NetworkError' || !navigator.onLine) {
            errorInfo.type = 'network';
            errorInfo.title = 'Connection Problem';
            errorInfo.message = 'Unable to connect to the server';
            errorInfo.suggestions = ['Check your internet connection', 'Try again in a moment'];
            errorInfo.retryable = true;
        }
        
        return errorInfo;
    }
    
    getHttpErrorMessage(status) {
        const messages = {
            400: 'Bad Request',
            401: 'Unauthorized',
            403: 'Access Denied',
            404: 'Not Found',
            429: 'Too Many Requests',
            500: 'Server Error',
            502: 'Service Unavailable',
            503: 'Service Unavailable',
            504: 'Request Timeout'
        };
        return messages[status] || `Error ${status}`;
    }
    
    getHttpErrorSuggestions(status) {
        const suggestions = {
            400: ['Check your input data', 'Ensure all required fields are filled'],
            401: ['Please log in again', 'Check your credentials'],
            403: ['You may not have permission for this action', 'Contact support if needed'],
            404: ['The requested resource was not found', 'Check the URL and try again'],
            429: ['Too many requests', 'Please wait a moment and try again'],
            500: ['Server error occurred', 'Please try again later'],
            502: ['Service temporarily unavailable', 'Please try again in a few minutes'],
            503: ['Service temporarily unavailable', 'Please try again in a few minutes'],
            504: ['Request timed out', 'Please try again']
        };
        return suggestions[status] || ['Please try again later'];
    }
    
    isRetryableHttpError(status) {
        return [408, 429, 500, 502, 503, 504].includes(status);
    }
    
    showErrorMessage(errorInfo, context = {}) {
        const container = this.createErrorContainer(errorInfo, context);
        this.showStatusMessage(container.outerHTML, 'danger', 8000);
    }
    
    createErrorContainer(errorInfo, context) {
        const container = document.createElement('div');
        container.innerHTML = `
            <div class="error-details">
                <h6 class="error-title">${errorInfo.title}</h6>
                <p class="error-message">${errorInfo.message}</p>
                ${errorInfo.suggestions.length > 0 ? `
                    <ul class="error-suggestions">
                        ${errorInfo.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                ` : ''}
                ${errorInfo.retryable ? `
                    <button class="btn btn-sm btn-outline-light retry-btn" onclick="window.location.reload()">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                ` : ''}
            </div>
        `;
        return container;
    }
    
    showStatusMessage(message, type = 'info', duration = 3000) {
        const messageContainer = this.getMessageContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show venue-alert`;
        alert.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas ${this.getStatusIcon(type)} me-2 mt-1"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        messageContainer.appendChild(alert);
        
        // Auto-dismiss
        if (duration > 0) {
            setTimeout(() => {
                this.removeMessage(alert);
            }, duration);
        }
    }
    
    getStatusIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'danger': 'fa-exclamation-triangle',
            'warning': 'fa-exclamation-circle',
            'info': 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    getMessageContainer() {
        let container = document.getElementById('venue-messages');
        if (!container) {
            container = document.createElement('div');
            container.id = 'venue-messages';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }
    
    removeMessage(messageElement) {
        if (messageElement && messageElement.parentNode) {
            messageElement.classList.remove('show');
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 150);
        }
    }
    
    // === Auto-Save & Progress Tracking ===
    
    setupAutoSave() {
        const forms = document.querySelectorAll('form[data-auto-save]');
        forms.forEach(form => {
            const fields = form.querySelectorAll('input, textarea, select');
            fields.forEach(field => {
                field.addEventListener('input', debounce(() => {
                    this.autoSaveProgress(form);
                }, 2000));
            });
        });
    }
    
    async autoSaveProgress(form) {
        try {
            this.showAutoSaveIndicator();
            
            const formData = new FormData(form);
            const response = await fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCsrfToken()
                }
            });
            
            if (response.ok) {
                this.showStatusMessage('Progress saved automatically', 'success', 2000);
            }
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }
    
    showAutoSaveIndicator() {
        const indicator = document.getElementById('auto-save-indicator');
        if (indicator) {
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 1000);
        }
    }
    
    // === Event Listeners & Initialization ===
    
    setupEventListeners() {
        // Form submission enhancements
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-enhanced-submit]')) {
                this.handleEnhancedSubmit(e);
            }
        });
        
        // Image upload enhancements
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="file"][data-image-upload]')) {
                this.handleImageUpload(e);
            }
        });
    }
    
    initializeProgressAnimations() {
        // Initialize any progress animations
        const progressElements = document.querySelectorAll('[data-progress]');
        progressElements.forEach((element, index) => {
            setTimeout(() => {
                this.animateProgressElement(element);
            }, index * 100);
        });
    }
    
    getOverallProgress() {
        const progressElements = document.querySelectorAll('[data-progress]');
        if (progressElements.length === 0) return 0;
        
        const totalProgress = Array.from(progressElements).reduce((sum, element) => {
            return sum + parseInt(element.getAttribute('data-progress') || 0);
        }, 0);
        
        return Math.round(totalProgress / progressElements.length);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    showLoadingState() {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'venue-loading-overlay';
        loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
        loadingOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
        loadingOverlay.style.zIndex = '9998';
        loadingOverlay.innerHTML = `
            <div class="text-center text-white">
                <div class="spinner-border mb-3" role="status"></div>
                <div>Processing...</div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);
    }
    
    hideLoadingState() {
        const overlay = document.getElementById('venue-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    showRetryMessage(attempt, maxAttempts) {
        this.showStatusMessage(
            `Retrying... (${attempt}/${maxAttempts})`,
            'warning',
            2000
        );
    }
    
    trackError(errorInfo, context) {
        // Error tracking implementation
        console.log('Error tracked:', errorInfo, context);
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    window.venueManager = new VenueManager();
});

// Add CSS for validation and wizard features
const validationStyles = `
<style>
/* Validation Styles */
.validation-feedback {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.has-success .form-control {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.has-error .form-control {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.has-warning .form-control {
    border-color: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

/* Character Counter */
.character-counter {
    text-align: right;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.character-counter.warning {
    color: #f59e0b;
}

.character-counter.danger {
    color: #ef4444;
}

/* Wizard Styles */
.wizard-progress {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--cw-gradient-card-subtle);
    border-radius: 0.75rem;
    border: 1px solid var(--cw-brand-accent);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.progress-step {
    text-align: center;
    flex: 1;
    position: relative;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: #e5e7eb;
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.completed::after {
    background: var(--cw-brand-primary);
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.progress-step.active .step-number {
    background: var(--cw-brand-primary);
    color: white;
}

.progress-step.completed .step-number {
    background: #10b981;
    color: white;
}

.step-title {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-step.active .step-title {
    color: var(--cw-brand-primary);
    font-weight: 600;
}

.progress-bar-container {
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--cw-brand-primary);
    transition: width 0.3s ease;
}

.wizard-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Auto-save indicator */
#auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    z-index: 9999;
    display: none;
}

/* Celebration animations */
@keyframes checkmarkBounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -8px, 0); }
    70% { transform: translate3d(0, -4px, 0); }
    90% { transform: translate3d(0, -2px, 0); }
}

@keyframes particleFloat {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

@keyframes confettiFall {
    0% { transform: translateY(-100vh) rotate(0deg); }
    100% { transform: translateY(100vh) rotate(360deg); }
}

.pulse-success {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

.celebration-mode {
    position: relative;
    overflow: hidden;
}

.venue-alert {
    min-width: 300px;
    max-width: 400px;
}

.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .progress-step::after {
        display: none;
    }
    
    .wizard-navigation {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', validationStyles); 