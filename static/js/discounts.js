document.addEventListener('DOMContentLoaded', function() {
    const skeleton = document.getElementById('discount-skeleton');
    const results = document.getElementById('discount-results');
    if (skeleton && results) {
        setTimeout(() => {
            skeleton.style.display = 'none';
            results.style.display = 'flex';
        }, 300);
    }

    const detailSkeleton = document.getElementById('detail-skeleton');
    const detailContent = document.getElementById('detail-content');
    if (detailSkeleton && detailContent) {
        setTimeout(() => {
            detailSkeleton.style.display = 'none';
            detailContent.style.display = 'block';
        }, 300);
    }

    document.querySelectorAll('.badge-filter-link').forEach(function(el) {
        el.addEventListener('click', function(e) {
            e.preventDefault();
            const url = new URL(window.location.href);
            url.searchParams.set('type', this.dataset.type);
            window.location.href = url.toString();
        });
    });
});
