/**
 * Enhanced Venue Creation JavaScript
 * Provides multi-step wizard functionality, real-time validation, progress saving,
 * character counters, field dependencies, and mobile-responsive interactions.
 */

class VenueCreationWizard {
    constructor() {
        this.currentStep = document.querySelector('#wizardForm')?.dataset.step || 'basic';
        this.autoSaveDelay = 2000; // 2 seconds
        this.autoSaveTimeout = null;
        this.validationCache = new Map();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupRealTimeValidation();
        this.setupCharacterCounters();
        this.setupFieldDependencies();
        this.setupMobileEnhancements();
        this.setupAutoSave();
        this.initializeExistingValues();
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('wizardForm');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // Category selection
        this.setupCategorySelection();
        
        // Status selection
        this.setupStatusSelection();

        // Navigation buttons
        this.setupNavigationButtons();

        // Save progress button
        const saveProgressBtn = document.getElementById('saveProgressBtn');
        if (saveProgressBtn) {
            saveProgressBtn.addEventListener('click', this.saveProgress.bind(this));
        }
    }

    setupRealTimeValidation() {
        const validatedFields = [
            'venue_name', 'short_description', 'phone', 'email', 'website_url',
            'state', 'county', 'city', 'street_number', 'street_name'
        ];

        validatedFields.forEach(fieldName => {
            const field = document.getElementById(`id_${fieldName}`);
            if (field) {
                // Input event for real-time validation
                field.addEventListener('input', (e) => this.debounce(() => {
                    this.validateField(fieldName, e.target.value);
                }, 500)());

                // Blur event for final validation
                field.addEventListener('blur', (e) => {
                    this.validateField(fieldName, e.target.value);
                });

                // Focus event to clear previous errors
                field.addEventListener('focus', (e) => {
                    this.clearFieldValidation(fieldName);
                });
            }
        });
    }

    setupCharacterCounters() {
        const fieldsWithCounters = [
            { field: 'short_description', max: 500, min: 10 },
            { field: 'venue_name', max: 255, min: 2 }
        ];

        fieldsWithCounters.forEach(({ field, max, min }) => {
            const input = document.getElementById(`id_${field}`);
            if (input) {
                this.createCharacterCounter(input, max, min);
                
                input.addEventListener('input', (e) => {
                    this.updateCharacterCounter(e.target, max, min);
                });

                // Initialize counter
                this.updateCharacterCounter(input, max, min);
            }
        });
    }

    createCharacterCounter(input, max, min) {
        const counterId = `${input.id}_counter`;
        const existing = document.getElementById(counterId);
        
        if (!existing) {
            const counter = document.createElement('div');
            counter.id = counterId;
            counter.className = 'character-counter d-flex justify-content-between align-items-center mt-2';
            counter.innerHTML = `
                <div class="character-count">
                    <span class="current">0</span> / <span class="max">${max}</span>
                </div>
                <div class="counter-hint text-muted">
                    ${min ? `Minimum ${min} characters` : ''}
                </div>
            `;
            
            input.parentNode.insertBefore(counter, input.nextSibling);
        }
    }

    updateCharacterCounter(input, max, min) {
        const counterId = `${input.id}_counter`;
        const counter = document.getElementById(counterId);
        const currentLength = input.value.length;
        
        if (counter) {
            const currentSpan = counter.querySelector('.current');
            const counterDiv = counter.querySelector('.character-count');
            
            if (currentSpan) {
                currentSpan.textContent = currentLength;
            }
            
            // Apply styling based on length
            counterDiv.className = 'character-count';
            if (currentLength > max) {
                counterDiv.classList.add('text-danger', 'fw-bold');
            } else if (currentLength > max * 0.9) {
                counterDiv.classList.add('text-warning', 'fw-bold');
            } else if (min && currentLength >= min) {
                counterDiv.classList.add('text-success');
            }
        }
    }

    setupFieldDependencies() {
        // State -> County dependency
        const stateField = document.getElementById('id_state');
        const countyField = document.getElementById('id_county');
        const cityField = document.getElementById('id_city');

        if (stateField && countyField) {
            stateField.addEventListener('change', () => {
                this.updateDependentField(stateField, countyField, 'county');
                this.clearField(cityField);
            });
        }

        if (countyField && cityField) {
            countyField.addEventListener('input', this.debounce(() => {
                this.updateDependentField(countyField, cityField, 'city');
            }, 300));
        }
    }

    updateDependentField(sourceField, targetField, type) {
        if (!sourceField.value.trim()) {
            targetField.disabled = true;
            targetField.placeholder = `Select ${sourceField.name} first`;
            return;
        }

        targetField.disabled = false;
        targetField.placeholder = `Enter ${type} name`;
        
        // Add autocomplete suggestions based on state/county
        this.loadLocationSuggestions(sourceField.value, type, targetField);
    }

    async loadLocationSuggestions(parentValue, type, targetField) {
        try {
            const response = await fetch(`/venues/api/location-autocomplete/?type=${type}&parent=${encodeURIComponent(parentValue)}`);
            const data = await response.json();
            
            if (data.suggestions) {
                this.setupAutocomplete(targetField, data.suggestions);
            }
        } catch (error) {
            console.error('Error loading location suggestions:', error);
        }
    }

    setupAutocomplete(field, suggestions) {
        // Remove existing datalist
        const existingDatalist = document.getElementById(`${field.id}_datalist`);
        if (existingDatalist) {
            existingDatalist.remove();
        }

        // Create new datalist
        const datalist = document.createElement('datalist');
        datalist.id = `${field.id}_datalist`;
        
        suggestions.forEach(suggestion => {
            const option = document.createElement('option');
            option.value = suggestion;
            datalist.appendChild(option);
        });

        field.setAttribute('list', datalist.id);
        field.parentNode.appendChild(datalist);
    }

    setupCategorySelection() {
        const categoryCards = document.querySelectorAll('.category-card');
        const maxSelections = 3;

        categoryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                if (!checkbox) return;

                const selectedCount = document.querySelectorAll('.category-card input[type="checkbox"]:checked').length;
                
                if (!checkbox.checked && selectedCount >= maxSelections) {
                    this.showToast('warning', `You can select up to ${maxSelections} categories only.`);
                    return;
                }

                checkbox.checked = !checkbox.checked;
                card.classList.toggle('selected', checkbox.checked);

                // Trigger auto-save
                this.scheduleAutoSave();
            });
        });
    }

    setupStatusSelection() {
        const statusCards = document.querySelectorAll('.status-card');
        
        statusCards.forEach(card => {
            card.addEventListener('click', () => {
                const radio = card.querySelector('input[type="radio"]');
                if (!radio) return;

                // Remove selected class from all cards
                statusCards.forEach(c => c.classList.remove('selected'));
                
                // Select current card
                radio.checked = true;
                card.classList.add('selected');

                // Trigger auto-save
                this.scheduleAutoSave();
            });
        });
    }

    setupNavigationButtons() {
        const nextBtn = document.querySelector('.btn-next');
        const prevBtn = document.querySelector('.btn-prev');

        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                if (!this.validateCurrentStep()) {
                    e.preventDefault();
                    this.showToast('error', 'Please fix the errors before continuing.');
                }
            });
        }
    }

    setupMobileEnhancements() {
        // Improve mobile form experience
        this.setupMobileKeyboard();
        this.setupTouchOptimizations();
        this.setupMobileNavigation();
    }

    setupMobileKeyboard() {
        // Set appropriate input types for mobile keyboards
        const phoneField = document.getElementById('id_phone');
        const emailField = document.getElementById('id_email');
        const websiteField = document.getElementById('id_website_url');

        if (phoneField) {
            phoneField.setAttribute('inputmode', 'tel');
            phoneField.setAttribute('pattern', '[0-9\\-\\(\\)\\+\\s]*');
        }

        if (emailField) {
            emailField.setAttribute('inputmode', 'email');
        }

        if (websiteField) {
            websiteField.setAttribute('inputmode', 'url');
        }
    }

    setupTouchOptimizations() {
        // Increase touch targets on mobile
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            const touchElements = document.querySelectorAll('.category-card, .status-card, .btn-wizard');
            touchElements.forEach(element => {
                element.style.minHeight = '48px';
                element.style.padding = Math.max(12, parseInt(getComputedStyle(element).padding) || 12) + 'px';
            });
        }
    }

    setupMobileNavigation() {
        // Add swipe navigation for mobile
        if ('ontouchstart' in window) {
            let startX = 0;
            let startY = 0;

            document.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            document.addEventListener('touchend', (e) => {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const diffX = startX - endX;
                const diffY = startY - endY;

                // Detect horizontal swipe (and not vertical scroll)
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        // Swipe left - go to next step
                        const nextBtn = document.querySelector('.btn-next');
                        if (nextBtn && !nextBtn.disabled) {
                            nextBtn.click();
                        }
                    } else {
                        // Swipe right - go to previous step
                        const prevBtn = document.querySelector('.btn-prev');
                        if (prevBtn) {
                            prevBtn.click();
                        }
                    }
                }
            });
        }
    }

    setupAutoSave() {
        const form = document.getElementById('wizardForm');
        if (!form) return;

        // Save on form changes
        form.addEventListener('input', () => {
            this.scheduleAutoSave();
        });

        form.addEventListener('change', () => {
            this.scheduleAutoSave();
        });

        // Save before page unload
        window.addEventListener('beforeunload', () => {
            this.saveProgress(false); // Don't show UI feedback
        });
    }

    scheduleAutoSave() {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = setTimeout(() => {
            this.saveProgress();
        }, this.autoSaveDelay);
    }

    async saveProgress(showFeedback = true) {
        const form = document.getElementById('wizardForm');
        if (!form) return;

        const formData = new FormData(form);
        formData.append('action', 'save_progress');

        try {
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });

            const data = await response.json();
            
            if (data.success && showFeedback) {
                this.showAutoSaveIndicator();
                this.updateProgressBar(data.progress_percentage);
            }
        } catch (error) {
            console.error('Auto-save error:', error);
        }
    }

    async validateField(fieldName, value) {
        if (!value.trim()) {
            this.clearFieldValidation(fieldName);
            return;
        }

        // Check cache first
        const cacheKey = `${fieldName}:${value}`;
        if (this.validationCache.has(cacheKey)) {
            const cachedResult = this.validationCache.get(cacheKey);
            this.showFieldValidation(fieldName, cachedResult);
            return;
        }

        const formData = new FormData();
        formData.append('action', 'validate_field');
        formData.append('field_name', fieldName);
        formData.append('field_value', value);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        try {
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();
            
            // Cache result
            this.validationCache.set(cacheKey, data);
            
            this.showFieldValidation(fieldName, data);
        } catch (error) {
            console.error('Validation error:', error);
        }
    }

    showFieldValidation(fieldName, validationResult) {
        const feedbackId = fieldName.replace('_', '-') + '-feedback';
        const feedback = document.getElementById(feedbackId);
        const field = document.getElementById(`id_${fieldName}`);

        if (!feedback || !field) return;

        if (validationResult.is_valid) {
            feedback.innerHTML = '<i class="fas fa-check text-success"></i> <span class="text-success">Looks good!</span>';
            feedback.className = 'field-feedback d-flex align-items-center gap-2 mt-1';
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            const errors = Array.isArray(validationResult.errors) ? validationResult.errors : [validationResult.errors];
            feedback.innerHTML = `<i class="fas fa-times text-danger"></i> <span class="text-danger">${errors.join(', ')}</span>`;
            feedback.className = 'field-feedback d-flex align-items-center gap-2 mt-1';
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
    }

    clearFieldValidation(fieldName) {
        const feedbackId = fieldName.replace('_', '-') + '-feedback';
        const feedback = document.getElementById(feedbackId);
        const field = document.getElementById(`id_${fieldName}`);

        if (feedback) {
            feedback.innerHTML = '';
            feedback.className = 'field-feedback';
        }

        if (field) {
            field.classList.remove('is-valid', 'is-invalid');
        }
    }

    validateCurrentStep() {
        const form = document.getElementById('wizardForm');
        if (!form) return true;

        let isValid = true;
        const visibleFields = form.querySelectorAll('input:not([style*="display: none"]), select:not([style*="display: none"]), textarea:not([style*="display: none"])');

        visibleFields.forEach(field => {
            if (field.required && !field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else if (field.classList.contains('is-invalid')) {
                isValid = false;
            }
        });

        return isValid;
    }

    showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
    }

    updateProgressBar(percentage) {
        const progressFill = document.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
    }

    showToast(type, message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'times'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add styles if not exists
        if (!document.getElementById('toast-styles')) {
            const styles = document.createElement('style');
            styles.id = 'toast-styles';
            styles.textContent = `
                .toast-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 1rem 1.5rem;
                    border-radius: 0.5rem;
                    color: white;
                    font-weight: 500;
                    z-index: 9999;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }
                .toast-success { background: #10b981; }
                .toast-warning { background: #f59e0b; }
                .toast-error { background: #ef4444; }
                .toast-notification.show { transform: translateX(0); }
                .toast-content { display: flex; align-items: center; gap: 0.5rem; }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    clearField(field) {
        if (field) {
            field.value = '';
            field.classList.remove('is-valid', 'is-invalid');
        }
    }

    handleFormSubmit(e) {
        if (!this.validateCurrentStep()) {
            e.preventDefault();
            this.showToast('error', 'Please fix all errors before proceeding.');
            return false;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }

        return true;
    }

    initializeExistingValues() {
        // Initialize character counters for existing values
        const descriptionField = document.getElementById('id_short_description');
        if (descriptionField && descriptionField.value) {
            this.updateCharacterCounter(descriptionField, 500, 10);
        }

        // Initialize selected categories
        const selectedCategories = document.querySelectorAll('.category-card input[type="checkbox"]:checked');
        selectedCategories.forEach(checkbox => {
            checkbox.closest('.category-card').classList.add('selected');
        });

        // Initialize selected status
        const selectedStatus = document.querySelector('.status-card input[type="radio"]:checked');
        if (selectedStatus) {
            selectedStatus.closest('.status-card').classList.add('selected');
        }
    }

    // Utility functions
    debounce(func, delay) {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.venueWizard = new VenueCreationWizard();
});

// Additional utility functions for backward compatibility
function selectStatusOption(element, value) {
    if (window.venueWizard) {
        element.click();
    }
}

function toggleCategoryOption(element, value) {
    if (window.venueWizard) {
        element.click();
    }
} 