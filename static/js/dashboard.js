document.addEventListener('DOMContentLoaded', function() {
    const skeleton = document.getElementById('dashboard-skeleton');
    const content = document.getElementById('dashboard-content');
    if (skeleton && content) {
        setTimeout(() => {
            skeleton.style.display = 'none';
            content.style.display = '';
        }, 300);
    }

    const tableSkeleton = document.getElementById('booking-table-skeleton');
    const todaysTable = document.getElementById('todays-bookings-table');
    if (tableSkeleton && todaysTable) {
        setTimeout(() => {
            tableSkeleton.remove();
            todaysTable.style.display = '';
        }, 300);
    }

    const recentSearch = document.getElementById('recent-bookings-search');
    if (recentSearch) {
        recentSearch.addEventListener('input', function() {
            const term = this.value.toLowerCase();
            document.querySelectorAll('#recent-bookings-table tbody tr').forEach(row => {
                row.style.display = row.textContent.toLowerCase().includes(term) ? '' : 'none';
            });
        });
    }

    const statusChart = document.getElementById('statusChart');
    if (statusChart && window.Chart) {
        const data = [
            parseInt(statusChart.dataset.pending, 10),
            parseInt(statusChart.dataset.confirmed, 10),
            parseInt(statusChart.dataset.completed, 10)
        ];
        new Chart(statusChart, {
            type: 'pie',
            data: {
                labels: ['Pending', 'Confirmed', 'Completed'],
                datasets: [{
                    data: data,
                    backgroundColor: ['#ffc107', '#198754', '#0dcaf0']
                }]
            },
            options: {plugins:{legend:{position:'bottom'}}}
        });
    }
});
