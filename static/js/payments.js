document.addEventListener('DOMContentLoaded', function() {
    const paymentSkeleton = document.getElementById('payment-skeleton');
    const paymentResults = document.getElementById('payment-results');
    if (paymentSkeleton && paymentResults) {
        setTimeout(() => {
            paymentSkeleton.style.display = 'none';
            paymentResults.style.display = '';
        }, 300);
    }

    const refundSkeleton = document.getElementById('refund-skeleton');
    const refundResults = document.getElementById('refund-results');
    if (refundSkeleton && refundResults) {
        setTimeout(() => {
            refundSkeleton.style.display = 'none';
            refundResults.style.display = '';
        }, 300);
    }

    const dashboardSkeleton = document.getElementById('dashboard-skeleton');
    const dashboardContent = document.getElementById('dashboard-content');
    if (dashboardSkeleton && dashboardContent) {
        setTimeout(() => {
            dashboardSkeleton.style.display = 'none';
            dashboardContent.style.display = '';
        }, 300);
    }

    document.querySelectorAll('.btn-copy').forEach(function(btn){
        btn.addEventListener('click', function(){
            const targetId = this.getAttribute('data-copy-target');
            const el = document.getElementById(targetId);
            if (el) {
                navigator.clipboard.writeText(el.textContent.trim());
            }
        });
    });
});
