/**
 * Professional Notifications JavaScript Module
 * Handles real-time notification updates and interactions with enhanced UX
 */

class ProfessionalNotificationManager {
    constructor() {
        this.updateInterval = 15000; // 15 seconds
        this.maxRetries = 3;
        this.retryCount = 0;
        this.lastUpdateTime = Date.now();
        this.isVisible = true;
        this.soundEnabled = this.getSoundPreference();
        this.reviewNotificationSound = null;
        
        this.init();
    }

    init() {
        // Initialize notification system
        this.setupVisibilityTracking();
        this.setupSoundSystem();
        this.startPeriodicUpdates();
        this.setupEventListeners();
        this.updateNotificationCount();
        
        console.log('Professional Notification Manager initialized');
    }

    setupSoundSystem() {
        // Create audio elements for different notification types
        try {
            this.reviewNotificationSound = new Audio('/static/sounds/review-notification.mp3');
            this.reviewNotificationSound.volume = 0.3;
            
            // Fallback to system beep if custom sound fails
            this.reviewNotificationSound.onerror = () => {
                this.reviewNotificationSound = null;
                console.log('Custom notification sound failed, using system beep');
            };
        } catch (error) {
            console.log('Audio initialization failed:', error);
        }
    }

    getSoundPreference() {
        const saved = localStorage.getItem('cozywish_notification_sound');
        return saved !== null ? JSON.parse(saved) : true;
    }

    setSoundPreference(enabled) {
        this.soundEnabled = enabled;
        localStorage.setItem('cozywish_notification_sound', JSON.stringify(enabled));
    }

    playNotificationSound(notificationType = 'general') {
        if (!this.soundEnabled) return;
        
        try {
            if (notificationType === 'review' && this.reviewNotificationSound) {
                this.reviewNotificationSound.currentTime = 0;
                this.reviewNotificationSound.play().catch(e => {
                    console.log('Sound play failed:', e);
                    this.fallbackBeep();
                });
            } else {
                this.fallbackBeep();
            }
        } catch (error) {
            console.log('Sound playback error:', error);
        }
    }

    fallbackBeep() {
        // Use Web Audio API for a simple beep if available
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
            console.log('Fallback beep failed:', error);
        }
    }

    showDesktopNotification(title, message, type = 'general') {
        if (!('Notification' in window)) return;
        
        if (Notification.permission === 'granted') {
            const options = {
                body: message,
                icon: '/static/images/cozywish-icon.png',
                badge: '/static/images/cozywish-badge.png',
                tag: `cozywish-${type}`,
                renotify: true,
                requireInteraction: type === 'review' // Keep review notifications visible longer
            };
            
            const notification = new Notification(title, options);
            
            // Auto-close after 5 seconds (except review notifications)
            if (type !== 'review') {
                setTimeout(() => notification.close(), 5000);
            }
            
            notification.onclick = () => {
                window.focus();
                notification.close();
                
                // Navigate to notifications page
                if (type === 'review') {
                    window.location.href = '/notifications/?notification_type=review';
                } else {
                    window.location.href = '/notifications/';
                }
            };
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    this.showDesktopNotification(title, message, type);
                }
            });
        }
    }

    async updateNotificationCount() {
        try {
            const response = await fetch('/notifications/unread/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                const previousCount = this.getCurrentUnreadCount();
                
                this.updateBadges(data.unread_count);
                this.updateDropdown(data.recent_notifications);
                
                // Check for new review notifications
                this.checkForNewReviewNotifications(data.recent_notifications, data.unread_count, previousCount);
                
                this.retryCount = 0; // Reset retry count on success
                this.lastUpdateTime = Date.now();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error updating notification count:', error);
            this.handleUpdateError();
        }
    }

    checkForNewReviewNotifications(notifications, currentCount, previousCount) {
        // If count increased, check if it's a review notification
        if (currentCount > previousCount) {
            const recentReviewNotifications = notifications.filter(n => 
                n.notification_type === 'review' && 
                n.read_status === 'unread' &&
                this.isRecentNotification(n.created_at)
            );
            
            if (recentReviewNotifications.length > 0) {
                const reviewNotification = recentReviewNotifications[0];
                
                // Play sound for review notifications
                this.playNotificationSound('review');
                
                // Show visual indicator
                this.showReviewNotificationIndicator(reviewNotification);
                
                // Show desktop notification
                const isResponse = reviewNotification.related_object_type === 'ReviewResponse';
                const title = isResponse ? '💬 Provider Response Received!' : '⭐ New Review Received!';
                
                let message = reviewNotification.message;
                if (reviewNotification.metadata && reviewNotification.metadata.venue_name) {
                    message = `${reviewNotification.metadata.venue_name}: ${message}`;
                }
                
                this.showDesktopNotification(title, message, 'review');
            }
        }
    }

    isRecentNotification(createdAt) {
        const notificationTime = new Date(createdAt).getTime();
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
        return notificationTime > fiveMinutesAgo;
    }

    showReviewNotificationIndicator(notification) {
        // Show a temporary visual indicator for review notifications
        const indicator = document.createElement('div');
        indicator.className = 'review-notification-indicator';
        indicator.innerHTML = `
            <div class="indicator-content">
                <div class="indicator-icon">
                    ${notification.related_object_type === 'ReviewResponse' ? '💬' : '⭐'}
                </div>
                <div class="indicator-text">
                    <div class="indicator-title">${notification.title}</div>
                    <div class="indicator-message">${notification.message.substring(0, 60)}...</div>
                </div>
                <button class="indicator-close">&times;</button>
            </div>
        `;
        
        // Add styles
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border-radius: 12px;
            padding: 16px;
            max-width: 350px;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            animation: slideInRight 0.5s ease-out;
            cursor: pointer;
        `;
        
        // Add animation styles
        if (!document.getElementById('review-notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'review-notification-styles';
            styles.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                .indicator-content {
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                }
                .indicator-icon {
                    font-size: 24px;
                    flex-shrink: 0;
                }
                .indicator-text {
                    flex: 1;
                    min-width: 0;
                }
                .indicator-title {
                    font-weight: 600;
                    margin-bottom: 4px;
                    font-size: 14px;
                }
                .indicator-message {
                    font-size: 12px;
                    opacity: 0.9;
                    line-height: 1.4;
                }
                .indicator-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: background-color 0.2s ease;
                }
                .indicator-close:hover {
                    background: rgba(255, 255, 255, 0.2);
                }
            `;
            document.head.appendChild(styles);
        }
        
        document.body.appendChild(indicator);
        
        // Click to view notification
        indicator.addEventListener('click', (e) => {
            if (!e.target.classList.contains('indicator-close')) {
                if (notification.action_url) {
                    window.location.href = notification.action_url;
                } else {
                    window.location.href = '/notifications/?notification_type=review';
                }
            }
        });
        
        // Close button
        indicator.querySelector('.indicator-close').addEventListener('click', (e) => {
            e.stopPropagation();
            indicator.remove();
        });
        
        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.style.animation = 'slideInRight 0.3s ease-in reverse';
                setTimeout(() => indicator.remove(), 300);
            }
        }, 8000);
    }

    getCurrentUnreadCount() {
        const badge = document.getElementById('navNotificationBadge');
        return badge ? parseInt(badge.textContent) || 0 : 0;
    }

    updateBadges(count) {
        // Update navigation badge with animation
        const navBadge = document.getElementById('navNotificationBadge');
        if (navBadge) {
            const currentCount = parseInt(navBadge.textContent) || 0;
            if (currentCount !== count) {
                this.animateBadgeUpdate(navBadge, count);
            }
        }

        // Update dropdown header badge
        const dropdownBadge = document.getElementById('dropdownUnreadBadge');
        if (dropdownBadge) {
            dropdownBadge.textContent = count;
            dropdownBadge.style.display = count > 0 ? 'inline-block' : 'none';
        }

        // Update stats in notification list page
        const unreadStat = document.getElementById('unreadCountStat');
        if (unreadStat) {
            const currentStat = parseInt(unreadStat.textContent) || 0;
            if (currentStat !== count) {
                this.animateCounterUpdate(unreadStat, currentStat, count);
            }
        }

        // Update menu item badges in navigation
        const menuBadges = document.querySelectorAll('.dropdown-menu .badge');
        menuBadges.forEach(badge => {
            if (badge.closest('a[href*="notifications"]')) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline-block' : 'none';
            }
        });

        // Update mark all read button state
        const markAllBtn = document.querySelector('.action-btn[data-bs-target="#markReadConfirmModal"]');
        if (markAllBtn) {
            markAllBtn.disabled = count === 0;
            markAllBtn.innerHTML = `<i class="fas fa-check-double"></i> Mark All Read (${count})`;
        }

        // Update review notification widget badge
        const reviewWidgetBadge = document.querySelector('.review-notification-widget .notification-count-badge');
        if (reviewWidgetBadge) {
            // This would need to be the count of review notifications specifically
            // You might want to make a separate API call for this
        }
    }

    animateBadgeUpdate(badge, newCount) {
        if (newCount > 0) {
            badge.textContent = newCount;
            badge.style.display = 'inline-block';
            
            // Add pulse animation for new notifications
            badge.style.animation = 'badgePulse 0.6s ease-in-out';
            setTimeout(() => {
                badge.style.animation = '';
            }, 600);
        } else {
            badge.style.display = 'none';
        }
    }

    animateCounterUpdate(element, currentCount, newCount) {
        // Animate the counter change
        const difference = newCount - currentCount;
        const duration = 500;
        const steps = 20;
        const stepValue = difference / steps;
        const stepDuration = duration / steps;
        
        let step = 0;
        const timer = setInterval(() => {
            step++;
            const value = Math.round(currentCount + (stepValue * step));
            element.textContent = value;
            
            if (step >= steps) {
                clearInterval(timer);
                element.textContent = newCount;
            }
        }, stepDuration);
    }

    setupVisibilityTracking() {
        // Track page visibility to pause updates when not visible
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                // Resume updates when page becomes visible
                this.updateNotificationCount();
            }
        });
    }

    setupEventListeners() {
        // Handle notification sound toggle
        const soundToggle = document.getElementById('notificationSoundToggle');
        if (soundToggle) {
            soundToggle.checked = this.soundEnabled;
            soundToggle.addEventListener('change', (e) => {
                this.setSoundPreference(e.target.checked);
            });
        }

        // Handle mark all as read
        const markAllForms = document.querySelectorAll('.mark-all-read-form');
        markAllForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleMarkAllRead(form);
            });
        });
    }

    async handleMarkAllRead(form) {
        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: new FormData(form),
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateBadges(0);
                this.showSuccessMessage(data.message || 'All notifications marked as read');
            }
        } catch (error) {
            console.error('Error marking all as read:', error);
            this.showErrorMessage('Failed to mark notifications as read');
        }
    }

    showSuccessMessage(message) {
        // Show a temporary success message
        this.showTemporaryMessage(message, 'success');
    }

    showErrorMessage(message) {
        // Show a temporary error message
        this.showTemporaryMessage(message, 'error');
    }

    showTemporaryMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `temp-message temp-message-${type}`;
        messageElement.textContent = message;
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            animation: slideInDown 0.3s ease-out;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        `;
        
        document.body.appendChild(messageElement);
        
        setTimeout(() => {
            messageElement.style.animation = 'slideInDown 0.3s ease-in reverse';
            setTimeout(() => messageElement.remove(), 300);
        }, 3000);
    }

    startPeriodicUpdates() {
        // Update immediately
        this.updateNotificationCount();
        
        // Set up periodic updates
        setInterval(() => {
            if (this.isVisible) {
                this.updateNotificationCount();
            }
        }, this.updateInterval);
    }

    handleUpdateError() {
        this.retryCount++;
        
        if (this.retryCount <= this.maxRetries) {
            // Exponential backoff
            const delay = Math.pow(2, this.retryCount) * 1000;
            setTimeout(() => {
                this.updateNotificationCount();
            }, delay);
        } else {
            console.error('Max retries reached for notification updates');
            // Reset retry count after 5 minutes
            setTimeout(() => {
                this.retryCount = 0;
            }, 300000);
        }
    }

    updateDropdown(notifications) {
        const dropdownContainer = document.getElementById('dropdownNotificationList');
        if (!dropdownContainer) return;

        // Update dropdown content with new notifications
        // This would be implemented based on your dropdown structure
    }

    // Public method to get current unread count
    getUnreadCount() {
        const badge = document.getElementById('navNotificationBadge');
        return badge ? parseInt(badge.textContent) || 0 : 0;
    }

    // Public method to manually refresh notifications
    refreshNotifications() {
        this.updateNotificationCount();
    }

    // Public method to test notification sound
    testNotificationSound() {
        this.playNotificationSound('review');
    }
}

// Initialize professional notification manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new ProfessionalNotificationManager();

    // Expose refresh method globally for manual updates
    window.refreshNotifications = () => {
        if (window.notificationManager) {
            window.notificationManager.refreshNotifications();
        }
    };
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.notificationManager) {
        window.notificationManager.stopPeriodicUpdates();
    }
});

// Handle browser back/forward navigation
window.addEventListener('pageshow', (event) => {
    if (event.persisted && window.notificationManager) {
        // Page was loaded from cache, refresh notifications
        window.notificationManager.refreshNotifications();
    }
});
