# --- Third-Party Imports ---
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import LoginHistory
from ..logging_utils import (
    log_authentication_event, 
    log_error, 
    performance_monitor
)


# --- Logging Configuration ---
import logging
logger = logging.getLogger(__name__)



# --- User-facing messages (translatable) ---
MESSAGES = {
    'logout_success': _("You have been logged out successfully."),
    'login_success': _("Successfully logged in!"),
    'login_error': _("Invalid email or password. Please try again."),
    'signup_success': _("Welcome to CozyWish! Your account has been created successfully."),
    'signup_success_console': _("Welcome to CozyWish! Your account has been created successfully."),
    'signup_success_email': _("Welcome to CozyWish! Your account has been created successfully. Please check your email at %(email)s for verification instructions."),
    'signup_error': _("There was an error creating your account. Please try again."),
    'profile_update': _("Your profile has been updated successfully."),
    'password_change': _("Your password has been changed successfully. Please log in again."),
    'password_change_error': _("There was an error changing your password. Please try again."),
    'account_deactivated': _("Your account has been deactivated. Your data will remain stored securely."),
    'deactivation_error': _("There was an error deactivating your account. Please try again."),
    'business_profile_update': _("Your business profile has been updated successfully."),
    'provider_signup': _("Your account has been created! Please check your email to verify your account."),
    'email_verified': _("Your email has been verified successfully! You can now log in."),
    'verification_link_invalid': _("The verification link is invalid or has expired. Please request a new one."),
}



# --- Utility Functions ---

def get_client_ip(request) -> str:
    """Retrieve the client's IP address from request metadata"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ips = [ip.strip() for ip in x_forwarded_for.split(',')]
        return ips[0] if ips else ''
    return request.META.get('REMOTE_ADDR', '127.0.0.1')



@performance_monitor("login_attempt_recording")
def record_login_attempt(user, request, success: bool = True) -> None:
    """Record authentication attempts and detect suspicious activity
    
    Args:
        user: Authenticating user object
        request: Current HTTP request
        success: Whether authentication succeeded
    """
    try:
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create login history record
        LoginHistory.objects.create(
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            is_successful=success
        )
        
        # Log authentication event
        log_authentication_event(
            event_type='login_attempt',
            user_email=user.email,
            success=success,
            request=request,
            failure_reason=None if success else 'authentication_failed'
        )
        
        # Check for suspicious activity after failed attempts
        if not success:
            if LoginHistory.detect_suspicious_activity(ip_address, user):
                log_authentication_event(
                    event_type='suspicious_activity_detected',
                    user_email=user.email,
                    success=False,
                    request=request,
                    failure_reason='multiple_failed_attempts',
                    additional_data={'ip_address': ip_address}
                )
                
    except Exception as error:
        log_error(
            error_type='login_tracking',
            error_message="Failed to record login attempt",
            user=user,
            request=request,
            exception=error
        )




# --- Business Landing Page ---

def business_landing_view(request):
    """Display the service provider onboarding and benefits page"""
    return render(request, 'accounts_app/business_landing.html')
