import os
import sys
import django


# Setup Django environment
if __name__ == '__main__':
    # Add the project root to the Python path
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, project_root)
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()

from django.contrib.auth import get_user_model
from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile, TeamMember
from django.db import transaction

User = get_user_model()

def create_test_data():
    """Create test accounts and data for manual testing."""
    
    print("🚀 Creating CozyWish Test Data...")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Create test customer
            print("👤 Creating test customer...")
            customer_email = "<EMAIL>"
            
            # Delete existing customer if exists
            if User.objects.filter(email=customer_email).exists():
                User.objects.filter(email=customer_email).delete()
                print(f"   ♻️  Deleted existing customer: {customer_email}")
            
            customer = User.objects.create_user(
                email=customer_email,
                password="SecurePass123!",
                role=CustomUser.CUSTOMER,
                first_name="Test",
                last_name="Customer"
            )
            
            # Create customer profile
            customer_profile = CustomerProfile.objects.create(
                user=customer,
                first_name="Test",
                last_name="Customer",
                gender="F",
                birth_month=5,
                birth_year=1990,
                phone_number="************",
                address="123 Test Street",
                city="Test City"
            )
            
            print(f"   ✅ Created customer: {customer_email}")
            print(f"      Password: SecurePass123!")
            
            # Create test provider
            print("🏢 Creating test provider...")
            provider_email = "<EMAIL>"
            
            # Delete existing provider if exists
            if User.objects.filter(email=provider_email).exists():
                User.objects.filter(email=provider_email).delete()
                print(f"   ♻️  Deleted existing provider: {provider_email}")
            
            provider = User.objects.create_user(
                email=provider_email,
                password="SecurePass123!",
                role=CustomUser.SERVICE_PROVIDER,
                first_name="Test",
                last_name="Provider",
                is_active=True  # Set as active for testing (skip email verification)
            )
            
            # Create provider profile
            provider_profile = ServiceProviderProfile.objects.create(
                user=provider,
                business_name="Test Spa & Wellness",
                business_description="A test spa for manual testing purposes",
                business_phone_number="+**********",
                website_url="https://testspa.example.com",
                business_address="456 Business Avenue",
                city="Test City",
                state="CA",
                zip_code="12345",
                contact_person_name="Jane Smith"
            )
            
            print(f"   ✅ Created provider: {provider_email}")
            print(f"      Password: SecurePass123!")
            print(f"      Business: {provider_profile.business_name}")
            
            # Create test team member
            print("👥 Creating test team member...")
            team_member = TeamMember.objects.create(
                service_provider=provider_profile,
                name="Sarah Johnson",
                email="<EMAIL>",
                phone_number="************",
                position="Massage Therapist",
                bio="Experienced massage therapist with 5 years of practice",
                is_active=True
            )
            
            print(f"   ✅ Created team member: {team_member.name}")
            
            # Create additional test accounts for deactivation testing
            print("🔄 Creating additional test accounts...")
            
            # Customer for deactivation testing
            deactivate_customer = User.objects.create_user(
                email="<EMAIL>",
                password="DeactivateMe123!",
                role=CustomUser.CUSTOMER,
                first_name="Deactivate",
                last_name="Customer"
            )
            CustomerProfile.objects.create(user=deactivate_customer)
            
            # Provider for deactivation testing
            deactivate_provider = User.objects.create_user(
                email="<EMAIL>",
                password="DeactivateMe123!",
                role=CustomUser.SERVICE_PROVIDER,
                first_name="Deactivate",
                last_name="Provider",
                is_active=True
            )
            ServiceProviderProfile.objects.create(
                user=deactivate_provider,
                business_name="Deactivate Test Spa",
                business_phone_number="+**********",
                business_address="789 Deactivate Street",
                city="Test City",
                state="CA",
                zip_code="54321",
                contact_person_name="John Doe"
            )
            
            print(f"   ✅ Created deactivation test accounts")
            
            print("\n" + "=" * 50)
            print("✅ TEST DATA CREATION COMPLETE!")
            print("=" * 50)
            
            print("\n📋 TEST ACCOUNTS CREATED:")
            print("-" * 30)
            print("👤 CUSTOMER ACCOUNTS:")
            print(f"   Email: <EMAIL>")
            print(f"   Password: SecurePass123!")
            print(f"   Login URL: http://127.0.0.1:8000/accounts/customer/login/")
            print()
            print(f"   Email: <EMAIL>")
            print(f"   Password: DeactivateMe123!")
            print(f"   Purpose: Account deactivation testing")
            print()
            
            print("🏢 PROVIDER ACCOUNTS:")
            print(f"   Email: <EMAIL>")
            print(f"   Password: SecurePass123!")
            print(f"   Business: Test Spa & Wellness")
            print(f"   Login URL: http://127.0.0.1:8000/accounts/provider/login/")
            print()
            print(f"   Email: <EMAIL>")
            print(f"   Password: DeactivateMe123!")
            print(f"   Business: Deactivate Test Spa")
            print(f"   Purpose: Account deactivation testing")
            print()
            
            print("👥 TEAM MEMBERS:")
            print(f"   Name: Sarah Johnson")
            print(f"   Email: <EMAIL>")
            print(f"   Position: Massage Therapist")
            print(f"   Provider: Test Spa & Wellness")
            print()
            
            print("🔗 USEFUL URLS:")
            print(f"   Admin Panel: http://127.0.0.1:8000/admin/")
            print(f"   Customer Signup: http://127.0.0.1:8000/accounts/customer/signup/")
            print(f"   Provider Signup: http://127.0.0.1:8000/accounts/provider/signup/")
            print(f"   Home Page: http://127.0.0.1:8000/")
            print()
            
            print("📝 NEXT STEPS:")
            print("1. Start your development server: python manage.py runserver")
            print("2. Open the MANUAL_TESTING_GUIDE.md file")
            print("3. Follow the test cases using the accounts created above")
            print("4. Use QUICK_TEST_CHECKLIST.md for a quick reference")
            print()
            
            return True
            
    except Exception as e:
        print(f"❌ Error creating test data: {str(e)}")
        return False

def cleanup_test_data():
    """Remove all test data created by this script."""
    
    print("🧹 Cleaning up test data...")
    
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    deleted_count = 0
    for email in test_emails:
        if User.objects.filter(email=email).exists():
            User.objects.filter(email=email).delete()
            deleted_count += 1
            print(f"   ♻️  Deleted: {email}")
    
    print(f"✅ Cleanup complete! Deleted {deleted_count} test accounts.")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'cleanup':
        cleanup_test_data()
    else:
        create_test_data()
        print("\n💡 TIP: Run 'python accounts_app/create_test_data.py cleanup' to remove test data")
