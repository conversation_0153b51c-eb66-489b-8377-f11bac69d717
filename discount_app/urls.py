"""URL configuration for the discount application."""

# --- Third-Party Imports ---
from django.urls import path

# --- Local App Imports ---
from . import views


app_name = 'discount_app'

urlpatterns = [
    # Test URL to verify the app is working
    path('test/', views.test_view, name='test_view'),

    # Provider URLs - Discount Management
    path('provider/', views.provider_discount_dashboard, name='provider_discount_list'),
    path('provider/list/', views.provider_discount_list, name='provider_discount_list_detailed'),
    path('provider/analytics/', views.provider_discount_analytics, name='provider_discount_analytics'),

    # Service Discount URLs
    path('provider/service/create/', views.create_service_discount, name='create_service_discount'),
    path('provider/service/<slug:discount_slug>/edit/', views.edit_service_discount, name='edit_service_discount'),
    path('provider/service/<slug:discount_slug>/quick-edit/', views.quick_edit_discount, {'discount_type':'service'}, name='quick_edit_service_discount'),
    path('provider/service/<slug:discount_slug>/detail/', views.service_discount_detail, name='service_discount_detail'),
    path('provider/service/<slug:discount_slug>/delete/', views.delete_service_discount, name='delete_service_discount'),

    # Venue Discount URLs
    path('provider/venue/create/', views.create_venue_discount, name='create_venue_discount'),
    path('provider/venue/<slug:discount_slug>/edit/', views.edit_venue_discount, name='edit_venue_discount'),
    path('provider/venue/<slug:discount_slug>/quick-edit/', views.quick_edit_discount, {'discount_type':'venue'}, name='quick_edit_venue_discount'),
    path('provider/venue/<slug:discount_slug>/detail/', views.venue_discount_detail, name='venue_discount_detail'),
    path('provider/venue/<slug:discount_slug>/delete/', views.delete_venue_discount, name='delete_venue_discount'),

    # Customer URLs
    path('featured/', views.featured_discounts_view, name='featured_discounts'),
    path('venue/<int:venue_id>/', views.venue_discounts_view, name='venue_discounts'),
    path('search/', views.search_discounts_view, name='search_discounts'),
    path('detail/<slug:discount_slug>/', views.discount_detail_view, name='discount_detail'),

    # Admin URLs
    path('admin/dashboard/', views.admin_discount_dashboard, name='admin_discount_dashboard'),
    path('admin/list/<str:discount_type>/', views.admin_discount_list, name='admin_discount_list'),
    path('admin/detail/<str:discount_type>/<int:discount_id>/', views.admin_discount_detail, name='admin_discount_detail'),
    path('admin/approve/<str:discount_type>/<int:discount_id>/', views.admin_approve_discount, name='admin_approve_discount'),
    path('admin/analytics/', views.admin_usage_analytics, name='admin_usage_analytics'),

    # Platform Discount Management
    path('admin/platform/create/', views.admin_create_platform_discount, name='admin_create_platform_discount'),
    path('admin/platform/<int:discount_id>/edit/', views.admin_edit_platform_discount, name='admin_edit_platform_discount'),
    path('admin/platform/<int:discount_id>/delete/', views.admin_delete_platform_discount, name='admin_delete_platform_discount'),
]
