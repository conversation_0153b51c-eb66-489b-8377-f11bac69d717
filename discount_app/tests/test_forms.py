"""
Unit tests for discount_app forms.

This module contains comprehensive unit tests for all form classes in the discount_app,
including ServiceDiscountForm, VenueDiscountForm, PlatformDiscountForm, and other forms.
"""

# Standard library imports
from datetime import timedel<PERSON>
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.forms import ValidationError as FormValidationError

# Local imports
from discount_app.forms import (
    ServiceDiscountForm, VenueDiscountForm, PlatformDiscountForm,
    DiscountFilterForm, DiscountApprovalForm
)
from discount_app.models import DiscountType, ServiceDiscount, VenueDiscount, PlatformDiscount
from venues_app.models import Category, Venue, Service
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class DiscountFormBaseTest(TestCase):
    """Base test class for discount forms."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider_user.role = User.SERVICE_PROVIDER
        self.provider_user.save()

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue for wellness treatments',
            street_number='123',
            street_name='Spa St',
            city='New York',
            state='NY',
            county='New York County',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        
        # Add category to venue (many-to-many relationship)
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            short_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )

        # Base form data
        self.base_form_data = {
            'name': 'Test Discount',
            'description': 'Test discount description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '20.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
        }


class ServiceDiscountFormTest(DiscountFormBaseTest):
    """Test the ServiceDiscountForm functionality."""

    def test_form_valid_data(self):
        """Test form with valid data."""
        form_data = {
            'service': self.service.id,
            **self.base_form_data
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_invalid_without_service(self):
        """Test form is invalid without service."""
        form_data = {k: v for k, v in self.base_form_data.items()}
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('service', form.errors)

    def test_form_invalid_without_user(self):
        """Test form behavior without user."""
        form_data = {
            'service': self.service.id,
            **self.base_form_data
        }
        form = ServiceDiscountForm(data=form_data)
        # Form should still validate basic fields but service queryset will be empty
        self.assertFalse(form.is_valid())

    def test_form_service_queryset_filtered_by_user_venue(self):
        """Test that service queryset is filtered by user's venue."""
        form = ServiceDiscountForm(user=self.provider_user)
        service_queryset = form.fields['service'].queryset
        self.assertIn(self.service, service_queryset)
        
        # Create another venue and service
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_user.role = User.SERVICE_PROVIDER
        other_user.save()

        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_user,
            legal_name='Other Spa',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Other St',
            city='Boston',
            state='MA',
            zip_code='02101'
        )

        other_venue = Venue.objects.create(
            service_provider=other_provider_profile,
            venue_name='Other Spa Venue',
            short_description='Another great spa venue for wellness services',
            street_number='456',
            street_name='Other St',
            city='Boston',
            state='MA',
            county='Suffolk County',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        
        # Add category to venue (many-to-many relationship)
        other_venue.categories.add(self.category)

        other_service = Service.objects.create(
            venue=other_venue,
            service_title='Other Service',
            short_description='Other service',
            price_min=Decimal('50.00'),
            price_max=Decimal('100.00'),
            duration_minutes=30,
            is_active=True
        )

        # Other service should not be in the queryset
        self.assertNotIn(other_service, service_queryset)

    def test_form_clean_discount_value_percentage_valid(self):
        """Test clean_discount_value with valid percentage."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '50.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_percentage_over_80(self):
        """Test clean_discount_value with percentage over 80%."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '85.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Percentage discount cannot exceed 80%', str(form.errors['discount_value']))

    def test_form_clean_discount_value_percentage_over_100(self):
        """Test clean_discount_value with percentage over 100%."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '150.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Percentage cannot exceed 100%', str(form.errors['discount_value']))

    def test_form_clean_discount_value_zero(self):
        """Test clean_discount_value with zero value."""
        form_data = {
            'service': self.service.id,
            'discount_value': '0.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Discount value must be greater than 0', str(form.errors['discount_value']))

    def test_form_clean_discount_value_fixed_amount_valid(self):
        """Test clean_discount_value with valid fixed amount."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.FIXED_AMOUNT,
            'discount_value': '25.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_fixed_amount_too_high(self):
        """Test clean_discount_value with fixed amount too high."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.FIXED_AMOUNT,
            'discount_value': '15000.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Fixed amount discount seems too high', str(form.errors['discount_value']))

    def test_form_clean_invalid_date_range(self):
        """Test clean method with invalid date range."""
        form_data = {
            'service': self.service.id,
            'start_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            **{k: v for k, v in self.base_form_data.items() if k != 'start_date' and k != 'end_date'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Start date must be before end date', str(form.non_field_errors()))

    def test_form_clean_start_date_too_far_in_past(self):
        """Test clean method with start date too far in the past."""
        form_data = {
            'service': self.service.id,
            'start_date': (timezone.now() - timedelta(hours=2)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M'),
            **{k: v for k, v in self.base_form_data.items() if k != 'start_date' and k != 'end_date'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Start date cannot be more than 1 hour in the past', str(form.non_field_errors()))

    def test_form_clean_fixed_amount_exceeds_service_price(self):
        """Test clean method when fixed amount exceeds service price."""
        form_data = {
            'service': self.service.id,
            'discount_type': DiscountType.FIXED_AMOUNT,
            'discount_value': '150.00',  # Service price_min is 100.00
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Fixed amount discount ($150.00) cannot be equal to or greater than the service price ($100.00)', str(form.non_field_errors()))

    def test_form_save_sets_created_by(self):
        """Test that save method sets created_by field."""
        form_data = {
            'service': self.service.id,
            **self.base_form_data
        }
        form = ServiceDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())
        
        service_discount = form.save()
        self.assertEqual(service_discount.created_by, self.provider_user)
        self.assertEqual(service_discount.service, self.service)

    def test_form_widget_attributes(self):
        """Test that form widgets have correct attributes."""
        form = ServiceDiscountForm(user=self.provider_user)
        
        # Check that widgets have Bootstrap classes
        self.assertIn('form-select', form.fields['service'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['name'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['description'].widget.attrs['class'])
        self.assertIn('form-select', form.fields['discount_type'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['discount_value'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['start_date'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['end_date'].widget.attrs['class'])

    def test_form_required_fields(self):
        """Test that required fields are properly set."""
        form = ServiceDiscountForm(user=self.provider_user)
        
        required_fields = ['service', 'name', 'discount_type', 'discount_value', 'start_date', 'end_date']
        for field_name in required_fields:
            self.assertTrue(form.fields[field_name].required, f'{field_name} should be required')

    def test_form_help_texts(self):
        """Test that form fields have appropriate help texts."""
        form = ServiceDiscountForm(user=self.provider_user)
        
        self.assertIn('Select the service this discount applies to', form.fields['service'].help_text)
        self.assertIn('Give your discount a memorable name', form.fields['name'].help_text)
        self.assertIn('max 80%', form.fields['discount_value'].help_text)

    def test_form_labels(self):
        """Test that form fields have appropriate labels."""
        form = ServiceDiscountForm(user=self.provider_user)
        
        self.assertEqual(form.fields['service'].label, 'Service')
        self.assertEqual(form.fields['name'].label, 'Discount Name')
        self.assertEqual(form.fields['discount_type'].label, 'Discount Type')
        self.assertEqual(form.fields['discount_value'].label, 'Discount Value')
        self.assertEqual(form.fields['start_date'].label, 'Start Date & Time')
        self.assertEqual(form.fields['end_date'].label, 'End Date & Time')


class VenueDiscountFormTest(DiscountFormBaseTest):
    """Test the VenueDiscountForm functionality."""

    def test_form_valid_data(self):
        """Test form with valid data."""
        form_data = {
            'min_booking_value': '50.00',
            'max_discount_amount': '100.00',
            **self.base_form_data
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_valid_data_without_optional_fields(self):
        """Test form with valid data without optional fields."""
        form_data = {**self.base_form_data}
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_percentage_valid(self):
        """Test clean_discount_value with valid percentage."""
        form_data = {
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '50.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_percentage_over_80(self):
        """Test clean_discount_value with percentage over 80%."""
        form_data = {
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '85.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Percentage discount cannot exceed 80%', str(form.errors['discount_value']))

    def test_form_clean_negative_min_booking_value(self):
        """Test clean method with negative min_booking_value."""
        form_data = {
            'min_booking_value': '-10.00',
            **self.base_form_data
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Minimum booking value cannot be negative', str(form.non_field_errors()))

    def test_form_clean_zero_max_discount_amount(self):
        """Test clean method with zero max_discount_amount."""
        form_data = {
            'max_discount_amount': '0.00',
            **self.base_form_data
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Maximum discount amount must be greater than 0', str(form.non_field_errors()))

    def test_form_clean_max_discount_amount_validation(self):
        """Test clean method validates max discount amount against min booking value."""
        form_data = {
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '10.00',
            'min_booking_value': '100.00',
            'max_discount_amount': '1000.00',  # Way too high
            **{k: v for k, v in self.base_form_data.items() if k not in ['discount_type', 'discount_value']}
        }
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertFalse(form.is_valid())
        self.assertIn('Maximum discount amount seems too high', str(form.non_field_errors()))

    def test_form_save_sets_created_by_and_venue(self):
        """Test that save method sets created_by and venue fields."""
        form_data = {**self.base_form_data}
        form = VenueDiscountForm(data=form_data, user=self.provider_user)
        self.assertTrue(form.is_valid())

        venue_discount = form.save()
        self.assertEqual(venue_discount.created_by, self.provider_user)
        self.assertEqual(venue_discount.venue, self.venue)

    def test_form_save_without_venue_raises_error(self):
        """Test that save method raises error when user has no venue."""
        user_without_venue = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        user_without_venue.role = User.SERVICE_PROVIDER
        user_without_venue.save()

        form_data = {**self.base_form_data}
        form = VenueDiscountForm(data=form_data, user=user_without_venue)

        # Form validation should pass but save should fail
        if form.is_valid():
            with self.assertRaises(ValidationError):
                form.save()

    def test_form_widget_attributes(self):
        """Test that form widgets have correct attributes."""
        form = VenueDiscountForm(user=self.provider_user)

        # Check that widgets have Bootstrap classes
        self.assertIn('form-control', form.fields['name'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['description'].widget.attrs['class'])
        self.assertIn('form-select', form.fields['discount_type'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['discount_value'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['min_booking_value'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['max_discount_amount'].widget.attrs['class'])

    def test_form_required_fields(self):
        """Test that required fields are properly set."""
        form = VenueDiscountForm(user=self.provider_user)

        required_fields = ['name', 'discount_type', 'discount_value', 'start_date', 'end_date']
        for field_name in required_fields:
            self.assertTrue(form.fields[field_name].required, f'{field_name} should be required')

        # Optional fields
        optional_fields = ['description', 'min_booking_value', 'max_discount_amount']
        for field_name in optional_fields:
            self.assertFalse(form.fields[field_name].required, f'{field_name} should be optional')

    def test_form_help_texts(self):
        """Test that form fields have appropriate help texts."""
        form = VenueDiscountForm(user=self.provider_user)

        self.assertIn('Give your venue discount a memorable name', form.fields['name'].help_text)
        self.assertIn('max 80%', form.fields['discount_value'].help_text)
        self.assertIn('Minimum total booking value to qualify', form.fields['min_booking_value'].help_text)
        self.assertIn('Cap the maximum discount amount', form.fields['max_discount_amount'].help_text)


class PlatformDiscountFormTest(DiscountFormBaseTest):
    """Test the PlatformDiscountForm functionality."""

    def test_form_valid_data(self):
        """Test form with valid data."""
        form_data = {
            'category': self.category.id,
            'min_booking_value': '75.00',
            'max_discount_amount': '50.00',
            'is_featured': True,
            **self.base_form_data
        }
        form = PlatformDiscountForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_valid_data_without_category(self):
        """Test form with valid data without category (all categories)."""
        form_data = {
            'min_booking_value': '75.00',
            'max_discount_amount': '50.00',
            'is_featured': False,
            **self.base_form_data
        }
        form = PlatformDiscountForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_percentage_valid_100(self):
        """Test clean_discount_value with valid 100% percentage (allowed for platform)."""
        form_data = {
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '100.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = PlatformDiscountForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_clean_discount_value_percentage_over_100(self):
        """Test clean_discount_value with percentage over 100%."""
        form_data = {
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '150.00',
            **{k: v for k, v in self.base_form_data.items() if k != 'discount_type' and k != 'discount_value'}
        }
        form = PlatformDiscountForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('discount_value', form.errors)
        self.assertIn('Percentage discount cannot exceed 100%', str(form.errors['discount_value']))

    def test_form_category_queryset_active_only(self):
        """Test that category queryset includes only active categories."""
        # Create inactive category
        inactive_category = Category.objects.create(
            category_name='Inactive Category',
            category_description='This category is inactive',
            is_active=False
        )

        form = PlatformDiscountForm()
        category_queryset = form.fields['category'].queryset

        self.assertIn(self.category, category_queryset)
        self.assertNotIn(inactive_category, category_queryset)

    def test_form_category_empty_label(self):
        """Test that category field has correct empty label."""
        form = PlatformDiscountForm()
        self.assertEqual(form.fields['category'].empty_label, "All Categories")

    def test_form_required_fields(self):
        """Test that required fields are properly set."""
        form = PlatformDiscountForm()

        required_fields = ['name', 'description', 'discount_type', 'discount_value', 'start_date', 'end_date']
        for field_name in required_fields:
            self.assertTrue(form.fields[field_name].required, f'{field_name} should be required')

        # Optional fields
        optional_fields = ['category', 'min_booking_value', 'max_discount_amount', 'is_featured']
        for field_name in optional_fields:
            self.assertFalse(form.fields[field_name].required, f'{field_name} should be optional')

    def test_form_widget_attributes(self):
        """Test that form widgets have correct attributes."""
        form = PlatformDiscountForm()

        # Check that widgets have Bootstrap classes
        self.assertIn('form-control', form.fields['name'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['description'].widget.attrs['class'])
        self.assertIn('form-select', form.fields['discount_type'].widget.attrs['class'])
        self.assertIn('form-control', form.fields['discount_value'].widget.attrs['class'])
        self.assertIn('form-select', form.fields['category'].widget.attrs['class'])
        self.assertIn('form-check-input', form.fields['is_featured'].widget.attrs['class'])

    def test_form_help_texts(self):
        """Test that form fields have appropriate help texts."""
        form = PlatformDiscountForm()

        self.assertIn('Give this platform discount a memorable name', form.fields['name'].help_text)
        self.assertIn('max 100%', form.fields['discount_value'].help_text)
        self.assertIn('Limit this discount to a specific category', form.fields['category'].help_text)
        self.assertIn('Display this discount prominently on the homepage', form.fields['is_featured'].help_text)
