"""
Management command to seed discount_app with realistic test data.
Creates venue discounts, service discounts, and platform discounts.
"""

import random
from decimal import Decimal
from datetime import timedelta

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model

from discount_app.models import VenueDiscount, ServiceDiscount, PlatformDiscount, DiscountType
from venues_app.models import Venue, Service, Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class Command(BaseCommand):
    """Seed discount_app with realistic test data."""
    
    help = 'Seed discount_app with venue, service, and platform discounts'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing discount data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting discount_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_platform_discounts()
            self.create_venue_discounts()
            self.create_service_discounts()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Discount app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing discount data."""
        self.stdout.write('🧹 Clearing existing discount data...')

        try:
            VenueDiscount.objects.all().delete()
            ServiceDiscount.objects.all().delete()
            PlatformDiscount.objects.all().delete()
            self.stdout.write('   ✅ Existing discount data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )

    def create_platform_discounts(self):
        """Create platform-wide discounts."""
        self.stdout.write('💰 Creating platform discounts...')
        
        # Get admin user for creating platform discounts
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(role=User.ADMIN).first()
        
        categories = list(Category.objects.all())
        
        platform_discounts = [
            {
                'name': 'New Customer Welcome',
                'description': 'Special discount for first-time customers',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('15.00'),
                'start_date': timezone.now() - timedelta(days=30),
                'end_date': timezone.now() + timedelta(days=60),
                'min_booking_value': Decimal('50.00'),
                'max_discount_amount': Decimal('25.00'),
                'is_featured': True,
                'max_uses': 1000,
                'category': None,  # All categories
            },
            {
                'name': 'Weekend Wellness',
                'description': 'Weekend special for all wellness services',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('20.00'),
                'start_date': timezone.now() - timedelta(days=7),
                'end_date': timezone.now() + timedelta(days=30),
                'min_booking_value': Decimal('75.00'),
                'max_discount_amount': Decimal('40.00'),
                'is_featured': True,
                'max_uses': 500,
                'category': categories[0] if categories else None,
            },
            {
                'name': 'Holiday Special',
                'description': 'Holiday season discount for spa services',
                'discount_type': DiscountType.FIXED_AMOUNT,
                'discount_value': Decimal('30.00'),
                'start_date': timezone.now() - timedelta(days=14),
                'end_date': timezone.now() + timedelta(days=45),
                'min_booking_value': Decimal('100.00'),
                'is_featured': False,
                'max_uses': 200,
                'category': categories[1] if len(categories) > 1 else None,
            },
            {
                'name': 'Spring Refresh',
                'description': 'Spring season beauty and wellness promotion',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('25.00'),
                'start_date': timezone.now() + timedelta(days=7),
                'end_date': timezone.now() + timedelta(days=90),
                'min_booking_value': Decimal('80.00'),
                'max_discount_amount': Decimal('50.00'),
                'is_featured': True,
                'max_uses': 300,
                'category': categories[2] if len(categories) > 2 else None,
            },
        ]
        
        for discount_data in platform_discounts:
            discount = PlatformDiscount.objects.create(
                created_by=admin_user,
                **discount_data
            )
            self.stdout.write(f'   ✅ Created platform discount: {discount.name}')

    def create_venue_discounts(self):
        """Create venue-specific discounts."""
        self.stdout.write('🏢 Creating venue discounts...')
        
        venues = list(Venue.objects.filter(approval_status='approved'))
        
        if not venues:
            self.stdout.write('   ⚠️ No approved venues found, skipping venue discounts')
            return
        
        # Create discounts for random venues
        selected_venues = random.sample(venues, min(len(venues), 8))
        
        discount_templates = [
            {
                'name': 'First Visit Special',
                'description': 'Special discount for first-time visitors to our venue',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('20.00'),
                'min_booking_value': Decimal('60.00'),
                'max_discount_amount': Decimal('30.00'),
                'max_uses': 100,
            },
            {
                'name': 'Loyalty Reward',
                'description': 'Thank you discount for our loyal customers',
                'discount_type': DiscountType.FIXED_AMOUNT,
                'discount_value': Decimal('25.00'),
                'min_booking_value': Decimal('80.00'),
                'max_uses': 50,
            },
            {
                'name': 'Group Booking Discount',
                'description': 'Special rate for group bookings',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('15.00'),
                'min_booking_value': Decimal('150.00'),
                'max_discount_amount': Decimal('45.00'),
                'max_uses': 25,
            },
        ]
        
        for venue in selected_venues:
            # Create 1-2 discounts per venue
            num_discounts = random.randint(1, 2)
            selected_templates = random.sample(discount_templates, num_discounts)
            
            for template in selected_templates:
                discount_data = template.copy()
                discount_data.update({
                    'venue': venue,
                    'start_date': timezone.now() - timedelta(days=random.randint(1, 14)),
                    'end_date': timezone.now() + timedelta(days=random.randint(30, 90)),
                    'created_by': venue.service_provider.user,
                    'is_approved': random.choice([True, True, False]),  # 2/3 approved
                })
                
                if discount_data['is_approved']:
                    admin_user = User.objects.filter(is_superuser=True).first()
                    discount_data['approved_by'] = admin_user
                    discount_data['approved_at'] = timezone.now() - timedelta(days=random.randint(1, 7))
                
                discount = VenueDiscount.objects.create(**discount_data)
                status = "✅ approved" if discount.is_approved else "⏳ pending"
                self.stdout.write(f'   {status} venue discount: {discount.name} for {venue.venue_name}')

    def create_service_discounts(self):
        """Create service-specific discounts."""
        self.stdout.write('🛍️ Creating service discounts...')
        
        services = list(Service.objects.filter(is_active=True, venue__approval_status='approved'))
        
        if not services:
            self.stdout.write('   ⚠️ No active services found, skipping service discounts')
            return
        
        # Create discounts for random services
        selected_services = random.sample(services, min(len(services), 12))
        
        discount_names = [
            'Early Bird Special', 'Happy Hour Discount', 'Midweek Deal',
            'Student Discount', 'Senior Discount', 'Birthday Special',
            'Seasonal Promotion', 'Limited Time Offer', 'Flash Sale',
            'Introductory Price', 'Bundle Deal', 'Referral Reward'
        ]
        
        for service in selected_services:
            discount_name = random.choice(discount_names)
            discount_type = random.choice([DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT])
            
            if discount_type == DiscountType.PERCENTAGE:
                discount_value = Decimal(str(random.randint(10, 30)))
            else:
                discount_value = Decimal(str(random.randint(10, 25)))
            
            discount_data = {
                'service': service,
                'name': f'{discount_name} - {service.service_title}',
                'description': f'Special discount for {service.service_title}',
                'discount_type': discount_type,
                'discount_value': discount_value,
                'start_date': timezone.now() - timedelta(days=random.randint(1, 21)),
                'end_date': timezone.now() + timedelta(days=random.randint(14, 60)),
                'created_by': service.venue.service_provider.user,
                'is_approved': random.choice([True, True, False]),  # 2/3 approved
                'max_uses': random.randint(20, 100),
            }
            
            if discount_data['is_approved']:
                admin_user = User.objects.filter(is_superuser=True).first()
                discount_data['approved_by'] = admin_user
                discount_data['approved_at'] = timezone.now() - timedelta(days=random.randint(1, 7))
            
            discount = ServiceDiscount.objects.create(**discount_data)
            status = "✅ approved" if discount.is_approved else "⏳ pending"
            self.stdout.write(f'   {status} service discount: {discount.name}')
