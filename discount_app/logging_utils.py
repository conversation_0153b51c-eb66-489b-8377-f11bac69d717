"""
Logging utilities specifically for the discount_app.

This module provides specialized logging functions for discount-related operations,
building on top of the centralized logging utilities in utils/logging_utils.py.

Usage:
    from discount_app.logging_utils import log_discount_creation, log_discount_usage
    
    log_discount_creation(user, discount, request)
    log_discount_usage(user, discount, booking, savings)
"""

from typing import Optional, Dict, Any
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

from utils.logging_utils import (
    get_app_logger, log_user_activity, log_error, log_security_event,
    log_audit_event, log_performance, get_client_info
)

User = get_user_model()


def log_discount_creation(
    user: User,
    discount: Any,
    request: Optional[HttpRequest] = None,
    discount_type: str = 'unknown'
) -> None:
    """
    Log the creation of a new discount by a provider.
    
    Args:
        user: User who created the discount
        discount: The discount object that was created
        request: Django HttpRequest object
        discount_type: Type of discount ('service', 'venue', 'platform')
    """
    log_user_activity(
        app_name='discount_app',
        activity_type=f'{discount_type}_discount_created',
        user=user,
        request=request,
        details={
            **get_client_info(request),
            'discount_id': discount.id,
            'discount_name': discount.name,
            'discount_type': discount.discount_type,
            'discount_value': float(discount.discount_value),
            'start_date': discount.start_date.isoformat(),
            'end_date': discount.end_date.isoformat(),
            'created_at': timezone.now().isoformat()
        }
    )


def log_discount_update(
    user: User,
    discount: Any,
    original_values: Dict[str, Any],
    request: Optional[HttpRequest] = None,
    discount_type: str = 'unknown'
) -> None:
    """
    Log the update of an existing discount.
    
    Args:
        user: User who updated the discount
        discount: The updated discount object
        original_values: Dictionary of original values before update
        request: Django HttpRequest object
        discount_type: Type of discount ('service', 'venue', 'platform')
    """
    log_user_activity(
        app_name='discount_app',
        activity_type=f'{discount_type}_discount_updated',
        user=user,
        request=request,
        details={
            'discount_id': discount.id,
            'discount_name': discount.name,
            'original_values': original_values,
            'updated_values': {
                'name': discount.name,
                'discount_type': discount.discount_type,
                'discount_value': float(discount.discount_value),
                'start_date': discount.start_date.isoformat(),
                'end_date': discount.end_date.isoformat()
            },
            'updated_at': timezone.now().isoformat()
        }
    )


def log_discount_deletion(
    user: User,
    discount_details: Dict[str, Any],
    request: Optional[HttpRequest] = None,
    discount_type: str = 'unknown'
) -> None:
    """
    Log the deletion of a discount.
    
    Args:
        user: User who deleted the discount
        discount_details: Dictionary containing discount details before deletion
        request: Django HttpRequest object
        discount_type: Type of discount ('service', 'venue', 'platform')
    """
    log_user_activity(
        app_name='discount_app',
        activity_type=f'{discount_type}_discount_deleted',
        user=user,
        request=request,
        details={
            **discount_details,
            'deleted_at': timezone.now().isoformat(),
            'deleted_by': user.email
        }
    )


def log_discount_approval(
    admin_user: User,
    discount: Any,
    is_approved: bool,
    target_user: Optional[User] = None,
    rejection_reason: Optional[str] = None,
    request: Optional[HttpRequest] = None,
    discount_type: str = 'unknown'
) -> None:
    """
    Log discount approval or rejection by admin.
    
    Args:
        admin_user: Admin user who performed the approval/rejection
        discount: The discount object
        is_approved: Whether the discount was approved or rejected
        target_user: The provider who created the discount
        rejection_reason: Reason for rejection (if applicable)
        request: Django HttpRequest object
        discount_type: Type of discount ('service', 'venue', 'platform')
    """
    action = 'discount_approved' if is_approved else 'discount_rejected'
    
    changes = {
        'discount_type': discount_type,
        'discount_id': discount.id,
        'discount_name': discount.name,
        'is_approved': is_approved,
        'action_timestamp': timezone.now().isoformat()
    }
    
    if is_approved:
        changes.update({
            'approved_by': admin_user.email,
            'approved_at': timezone.now().isoformat()
        })
    else:
        changes.update({
            'rejected_by': admin_user.email,
            'rejection_reason': rejection_reason,
            'rejected_at': timezone.now().isoformat()
        })
    
    log_audit_event(
        app_name='discount_app',
        action=action,
        admin_user=admin_user,
        target_user=target_user,
        request=request,
        changes=changes
    )


def log_discount_usage_event(
    user: User,
    discount: Any,
    booking: Any,
    original_price: float,
    discount_amount: float,
    final_price: float,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log when a customer uses a discount during booking.
    
    Args:
        user: Customer who used the discount
        discount: The discount object that was used
        booking: The booking object
        original_price: Original price before discount
        discount_amount: Amount of discount applied
        final_price: Final price after discount
        request: Django HttpRequest object
    """
    savings_percentage = round((discount_amount / original_price) * 100, 2) if original_price > 0 else 0
    
    log_user_activity(
        app_name='discount_app',
        activity_type='discount_applied_to_booking',
        user=user,
        request=request,
        details={
            'discount_id': discount.id,
            'discount_name': discount.name,
            'discount_type': type(discount).__name__,
            'booking_id': booking.id if booking else None,
            'original_price': original_price,
            'discount_amount': discount_amount,
            'final_price': final_price,
            'savings_percentage': savings_percentage,
            'used_at': timezone.now().isoformat()
        }
    )


def log_discount_search(
    user: Optional[User],
    search_params: Dict[str, Any],
    results_count: int,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log customer discount search activity.
    
    Args:
        user: User performing the search (can be None for anonymous users)
        search_params: Dictionary of search parameters
        results_count: Number of results returned
        request: Django HttpRequest object
    """
    log_user_activity(
        app_name='discount_app',
        activity_type='customer_discount_search',
        user=user,
        request=request,
        details={
            'search_params': search_params,
            'results_count': results_count,
            'is_authenticated': user is not None,
            'search_timestamp': timezone.now().isoformat()
        }
    )


def log_discount_calculation_performance(
    operation: str,
    duration: float,
    service_id: int,
    discounts_found: int,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log performance metrics for discount calculations.
    
    Args:
        operation: Name of the operation (e.g., 'get_applicable_discounts')
        duration: Duration of the operation in seconds
        service_id: ID of the service being processed
        discounts_found: Number of applicable discounts found
        user: User object (if applicable)
        request: Django HttpRequest object
    """
    log_performance(
        app_name='discount_app',
        operation=operation,
        duration=duration,
        user=user,
        request=request,
        details={
            'service_id': service_id,
            'discounts_found': discounts_found,
            'performance_category': 'discount_calculation'
        }
    )


def log_unauthorized_discount_access(
    user_email: str,
    attempted_action: str,
    discount_id: int,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log unauthorized attempts to access or modify discounts.
    
    Args:
        user_email: Email of the user attempting unauthorized access
        attempted_action: The action that was attempted
        discount_id: ID of the discount being accessed
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'discount_id': discount_id,
        'attempted_action': attempted_action,
        'timestamp': timezone.now().isoformat()
    }
    
    if additional_details:
        details.update(additional_details)
    
    log_security_event(
        app_name='discount_app',
        event_type='unauthorized_discount_access',
        user_email=user_email,
        request=request,
        details=details
    )


def log_discount_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    discount_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log discount-related errors with context.
    
    Args:
        error_type: Type of error
        error_message: Error message
        user: User object (if applicable)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        discount_context: Additional context about the discount operation
    """
    details = {'error_category': 'discount_operation'}
    
    if discount_context:
        details.update(discount_context)
    
    log_error(
        app_name='discount_app',
        error_type=error_type,
        error_message=error_message,
        user=user,
        request=request,
        exception=exception,
        details=details
    )
