"""Customer-facing views for exploring and using discounts."""

# --- Standard Library Imports ---
from datetime import datetime, timedelta
import json

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q, Sum
from django.http import Http404, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods

# --- Local App Imports ---
from ..forms import (
    ServiceDiscountForm,
    VenueDiscountForm,
    PlatformDiscountForm,
    DiscountFilterForm,
    DiscountApprovalForm,
    QuickServiceDiscountForm,
    QuickVenueDiscountForm,
)
from ..models import PlatformDiscount, DiscountUsage, ServiceDiscount, VenueDiscount
from ..utils import get_applicable_discounts, get_best_discount
from venues_app.models import Service, Venue, Category
from notifications_app.utils import create_notification, send_notification_email
from notifications_app.models import Notification

# --- Logging Imports ---
from utils.logging_utils import (
    get_app_logger, log_user_activity, log_error, log_security_event,
    log_audit_event, log_performance, get_client_info
)
from ..logging_utils import (
    log_discount_creation, log_discount_update, log_discount_deletion,
    log_discount_approval, log_discount_search, log_unauthorized_discount_access,
    log_discount_error
)


def featured_discounts_view(request):
    """
    Display featured discounts for customers.
    Shows active discounts from all venues with highest savings first.
    """
    # Log customer accessing featured discounts
    log_user_activity(
        app_name='discount_app',
        activity_type='customer_featured_discounts_access',
        user=request.user if request.user.is_authenticated else None,
        request=request,
        details={
            'page': request.GET.get('page', '1'),
            'is_authenticated': request.user.is_authenticated
        }
    )

    page_number = request.GET.get('page', '1')
    cache_key = f"featured_discounts_{page_number}"
    cached_context = cache.get(cache_key)
    if cached_context:
        return render(request, 'discount_app/customer/featured_discounts.html', cached_context)

    now = timezone.now()

    # Get active service discounts
    service_discounts = ServiceDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__venue__approval_status='approved',
        service__venue__visibility='active',
        service__is_active=True
    ).select_related('service', 'service__venue').annotate(
        avg_rating=Avg('service__venue__reviews__rating')
    ).order_by('-discount_value')

    # Get active venue discounts
    venue_discounts = VenueDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        venue__approval_status='approved',
        venue__visibility='active'
    ).select_related('venue').annotate(
        avg_rating=Avg('venue__reviews__rating')
    ).order_by('-discount_value')

    # Get active platform discounts
    platform_discounts = PlatformDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now
    ).order_by('-discount_value')

    # Combine all discounts with additional info
    featured_discounts = []

    # Add service discounts
    for discount in service_discounts[:10]:  # Limit to top 10
        badge = None
        if discount.avg_rating and discount.avg_rating >= 4.5:
            badge = 'Top Rated'
        elif discount.get_usage_count() >= 20:
            badge = 'Popular'
        featured_discounts.append({
            'type': 'service',
            'discount': discount,
            'venue': discount.service.venue,
            'service': discount.service,
            'savings_text': f"{discount.discount_value}% off" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
            'original_price': discount.service.price,
            'discounted_price': discount.calculate_discounted_price(discount.service.price),
            'rating': discount.avg_rating,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    # Add venue discounts
    for discount in venue_discounts[:10]:  # Limit to top 10
        badge = None
        if discount.avg_rating and discount.avg_rating >= 4.5:
            badge = 'Top Rated'
        elif discount.get_usage_count() >= 20:
            badge = 'Popular'
        featured_discounts.append({
            'type': 'venue',
            'discount': discount,
            'venue': discount.venue,
            'service': None,
            'savings_text': f"{discount.discount_value}% off" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
            'original_price': None,
            'discounted_price': None,
            'rating': discount.avg_rating,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    # Add platform discounts
    for discount in platform_discounts[:5]:  # Limit to top 5
        badge = None
        if discount.get_usage_count() >= 20:
            badge = 'Popular'
        featured_discounts.append({
            'type': 'platform',
            'discount': discount,
            'venue': None,
            'service': None,
            'savings_text': f"{discount.discount_value}% off" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
            'original_price': None,
            'discounted_price': None,
            'rating': None,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    # Sort by discount value (highest first)
    featured_discounts.sort(key=lambda x: x['discount'].discount_value, reverse=True)

    # Pagination
    paginator = Paginator(featured_discounts, 12)  # 12 discounts per page
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_discounts': len(featured_discounts),
    }

    cache.set(cache_key, context, 300)

    return render(request, 'discount_app/customer/featured_discounts.html', context)


def venue_discounts_view(request, venue_id):
    """
    Display all discounts available for a specific venue.
    """
    venue = get_object_or_404(
        Venue,
        id=venue_id,
        approval_status='approved',
        visibility='active'
    )

    # Get date filter parameters
    start_date_str = request.GET.get('start_date', '')
    end_date_str = request.GET.get('end_date', '')

    # Parse date range
    start_date = None
    end_date = None
    if start_date_str:
        try:
            start_date = timezone.make_aware(datetime.strptime(start_date_str, '%Y-%m-%d'))
        except ValueError:
            start_date = None
    if end_date_str:
        try:
            end_date = timezone.make_aware(datetime.strptime(end_date_str, '%Y-%m-%d')) + timedelta(days=1)
        except ValueError:
            end_date = None

    now = timezone.now()

    # Get active service discounts for this venue
    service_discounts = ServiceDiscount.objects.filter(
        service__venue=venue,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__is_active=True
    ).select_related('service').annotate(
        avg_rating=Avg('service__venue__reviews__rating')
    )

    # Get active venue discounts
    venue_discounts = VenueDiscount.objects.filter(
        venue=venue,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True
    ).annotate(
        avg_rating=Avg('venue__reviews__rating')
    )

    # Get active platform discounts
    platform_discounts = PlatformDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now
    )

    # Apply date range filters
    if start_date:
        service_discounts = service_discounts.filter(start_date__gte=start_date)
        venue_discounts = venue_discounts.filter(start_date__gte=start_date)
        platform_discounts = platform_discounts.filter(start_date__gte=start_date)
    if end_date:
        service_discounts = service_discounts.filter(end_date__lte=end_date)
        venue_discounts = venue_discounts.filter(end_date__lte=end_date)
        platform_discounts = platform_discounts.filter(end_date__lte=end_date)

    # Prepare discount data with calculated prices
    discount_data = []

    # Add service discounts
    for discount in service_discounts:
        original_price = discount.service.price
        discounted_price = discount.calculate_discounted_price(original_price)
        savings = original_price - discounted_price
        badge = None
        if discount.avg_rating and discount.avg_rating >= 4.5:
            badge = 'Top Rated'
        elif discount.get_usage_count() >= 20:
            badge = 'Popular'

        discount_data.append({
            'type': 'service',
            'discount': discount,
            'service': discount.service,
            'original_price': original_price,
            'discounted_price': discounted_price,
            'savings': savings,
            'savings_text': f"Save ${savings:.2f}",
            'rating': discount.avg_rating,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    # Add venue discounts
    for discount in venue_discounts:
        badge = None
        if discount.avg_rating and discount.avg_rating >= 4.5:
            badge = 'Top Rated'
        elif discount.get_usage_count() >= 20:
            badge = 'Popular'
        discount_data.append({
            'type': 'venue',
            'discount': discount,
            'service': None,
            'original_price': None,
            'discounted_price': None,
            'savings': None,
            'savings_text': f"{discount.discount_value}% off all services" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
            'rating': discount.avg_rating,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    # Add platform discounts
    for discount in platform_discounts:
        badge = None
        if discount.get_usage_count() >= 20:
            badge = 'Popular'
        discount_data.append({
            'type': 'platform',
            'discount': discount,
            'service': None,
            'original_price': None,
            'discounted_price': None,
            'savings': None,
            'savings_text': f"{discount.discount_value}% off" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
            'rating': None,
            'usage_count': discount.get_usage_count(),
            'badge': badge,
        })

    context = {
        'venue': venue,
        'discount_data': discount_data,
        'service_discounts_count': len(service_discounts),
        'venue_discounts_count': len(venue_discounts),
        'platform_discounts_count': len(platform_discounts),
    }

    return render(request, 'discount_app/customer/venue_discounts.html', context)


def search_discounts_view(request):
    """
    Search and filter discounts by various criteria.
    Handles both GET and POST requests for search functionality.
    """
    # Determine request data source (GET for normal requests, POST for search forms)
    request_data = request.POST if request.method == 'POST' else request.GET

    # Build cache key based on query params (only for GET requests to avoid caching POST)
    if request.method == 'GET':
        import hashlib, json
        cache_key = 'search_discounts_' + hashlib.md5(json.dumps(request.GET, sort_keys=True).encode()).hexdigest()
        cached = cache.get(cache_key)
        if cached:
            return render(request, 'discount_app/customer/search_discounts.html', cached)

    # Get search parameters from either GET or POST data
    search_query = request_data.get('search', request_data.get('query', '')).strip()
    discount_type_filter = request_data.get('type', 'all')  # all, service, venue, platform
    category_filter = request_data.get('category', '')
    location_filter = request_data.get('location', '')
    min_discount = request_data.get('min_discount', '')
    start_date_str = request_data.get('start_date', '')
    end_date_str = request_data.get('end_date', '')
    sort_option = request_data.get('sort', 'savings')

    # Log customer search activity
    search_params = {
        'search_query': search_query,
        'discount_type_filter': discount_type_filter,
        'category_filter': category_filter,
        'location_filter': location_filter,
        'min_discount': min_discount,
        'start_date': start_date_str,
        'end_date': end_date_str,
        'page': request_data.get('page', '1'),
        'sort': sort_option,
    }

    now = timezone.now()

    # Base querysets for active discounts
    service_discounts = ServiceDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__venue__approval_status='approved',
        service__venue__visibility='active',
        service__is_active=True
    ).select_related('service', 'service__venue').annotate(
        avg_rating=Avg('service__venue__reviews__rating')
    )

    venue_discounts = VenueDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        venue__approval_status='approved',
        venue__visibility='active'
    ).select_related('venue').annotate(
        avg_rating=Avg('venue__reviews__rating')
    )

    platform_discounts = PlatformDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now
    )

    # Apply search query filter
    if search_query:
        service_discounts = service_discounts.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(service__service_title__icontains=search_query) |
            Q(service__venue__venue_name__icontains=search_query)
        )
        venue_discounts = venue_discounts.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(venue__venue_name__icontains=search_query)
        )
        platform_discounts = platform_discounts.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Apply category filter
    if category_filter:
        service_discounts = service_discounts.filter(service__venue__category__id=category_filter)
        venue_discounts = venue_discounts.filter(venue__category__id=category_filter)

    # Apply location filter
    if location_filter:
        service_discounts = service_discounts.filter(
            Q(service__venue__city__icontains=location_filter) |
            Q(service__venue__state__icontains=location_filter)
        )
        venue_discounts = venue_discounts.filter(
            Q(venue__city__icontains=location_filter) |
            Q(venue__state__icontains=location_filter)
        )

    # Apply minimum discount filter
    if min_discount:
        try:
            min_discount_value = float(min_discount)
            service_discounts = service_discounts.filter(discount_value__gte=min_discount_value)
            venue_discounts = venue_discounts.filter(discount_value__gte=min_discount_value)
            platform_discounts = platform_discounts.filter(discount_value__gte=min_discount_value)
        except ValueError:
            pass

    # Combine results based on type filter
    search_results = []

    if discount_type_filter in ['all', 'service']:
        for discount in service_discounts:
            original_price = discount.service.price_min
            discounted_price = discount.calculate_discounted_price(original_price)
            savings = original_price - discounted_price

            badge = None
            if discount.avg_rating and discount.avg_rating >= 4.5:
                badge = 'Top Rated'
            elif discount.get_usage_count() >= 20:
                badge = 'Popular'
            search_results.append({
                'type': 'service',
                'discount': discount,
                'venue': discount.service.venue,
                'service': discount.service,
                'original_price': original_price,
                'discounted_price': discounted_price,
                'savings': savings,
                'savings_text': f"Save ${savings:.2f}",
                'rating': discount.avg_rating,
                'usage_count': discount.get_usage_count(),
                'badge': badge,
            })

    if discount_type_filter in ['all', 'venue']:
        for discount in venue_discounts:
            badge = None
            if discount.avg_rating and discount.avg_rating >= 4.5:
                badge = 'Top Rated'
            elif discount.get_usage_count() >= 20:
                badge = 'Popular'
            search_results.append({
                'type': 'venue',
                'discount': discount,
                'venue': discount.venue,
                'service': None,
                'original_price': None,
                'discounted_price': None,
                'savings': None,
                'savings_text': f"{discount.discount_value}% off all services" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
                'rating': discount.avg_rating,
                'usage_count': discount.get_usage_count(),
                'badge': badge,
            })

    if discount_type_filter in ['all', 'platform']:
        for discount in platform_discounts:
            badge = None
            if discount.get_usage_count() >= 20:
                badge = 'Popular'
            search_results.append({
                'type': 'platform',
                'discount': discount,
                'venue': None,
                'service': None,
                'original_price': None,
                'discounted_price': None,
                'savings': None,
                'savings_text': f"{discount.discount_value}% off" if discount.discount_type == 'percentage' else f"${discount.discount_value} off",
                'rating': None,
                'usage_count': discount.get_usage_count(),
                'badge': badge,
            })

    if sort_option == 'savings':
        search_results.sort(key=lambda x: x.get('savings') or 0, reverse=True)
    elif sort_option == 'popular':
        search_results.sort(key=lambda x: x.get('usage_count') or 0, reverse=True)
    elif sort_option == 'expiry':
        search_results.sort(key=lambda x: x['discount'].end_date)

    # Log search results
    log_discount_search(
        user=request.user if request.user.is_authenticated else None,
        search_params=search_params,
        results_count=len(search_results),
        request=request
    )

    # Pagination
    paginator = Paginator(search_results, 12)  # 12 results per page
    page_number = request_data.get('page')
    page_obj = paginator.get_page(page_number)

    # Get categories for filter dropdown
    categories = Category.objects.all().order_by('category_name')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'discount_type_filter': discount_type_filter,
        'category_filter': category_filter,
        'location_filter': location_filter,
        'min_discount': min_discount,
        'start_date': start_date_str,
        'end_date': end_date_str,
        'categories': categories,
        'total_results': len(search_results),
        'sort': sort_option,
    }

    # Only cache GET requests
    if request.method == 'GET':
        cache.set(cache_key, context, 300)
    return render(request, 'discount_app/customer/search_discounts.html', context)


def discount_detail_view(request, discount_slug):
    """Enhanced public discount detail page with comprehensive information."""
    now = timezone.now()
    discount = ServiceDiscount.objects.filter(
        slug=discount_slug,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__venue__approval_status='approved',
        service__venue__visibility='active',
        service__is_active=True,
    ).select_related('service', 'service__venue').first()
    discount_type = 'service'
    related_entity = None

    if discount:
        related_entity = discount.service
    else:
        discount = VenueDiscount.objects.filter(
            slug=discount_slug,
            start_date__lte=now,
            end_date__gte=now,
            is_approved=True,
            venue__approval_status='approved',
            venue__visibility='active',
        ).select_related('venue').first()
        discount_type = 'venue'
        if discount:
            related_entity = discount.venue

    if not discount:
        discount = PlatformDiscount.objects.filter(
            slug=discount_slug,
            start_date__lte=now,
            end_date__gte=now,
        ).select_related('category').first()
        discount_type = 'platform'
        if discount:
            related_entity = discount.category

    if not discount:
        raise Http404("Discount not found or no longer available")

    # Calculate days remaining
    days_remaining = (discount.end_date.date() - now.date()).days

    # Calculate usage statistics
    usage_count = discount.get_usage_count()
    remaining_uses = discount.remaining_uses()

    # Calculate potential savings for service discounts
    potential_savings = None
    if discount_type == 'service' and related_entity:
        original_price = related_entity.price_min
        discounted_price = discount.calculate_discounted_price(original_price)
        potential_savings = original_price - discounted_price

    # Log discount detail view
    log_user_activity(
        app_name='discount_app',
        activity_type='customer_discount_detail_view',
        user=request.user if request.user.is_authenticated else None,
        request=request,
        details={
            'discount_id': discount.id,
            'discount_name': discount.name,
            'discount_type': discount_type,
            'days_remaining': days_remaining,
            'usage_count': usage_count,
            'is_authenticated': request.user.is_authenticated
        }
    )

    share_url = request.build_absolute_uri(
        reverse('discount_app:discount_detail', args=[discount_slug])
    )

    context = {
        'discount': discount,
        'discount_type': discount_type,
        'related_entity': related_entity,
        'days_remaining': days_remaining,
        'usage_count': usage_count,
        'remaining_uses': remaining_uses,
        'potential_savings': potential_savings,
        'share_url': share_url,
    }
    return render(request, 'discount_app/customer/discount_detail.html', context)


