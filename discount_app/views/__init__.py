"""Convenience imports exposing all public view functions."""

# --- Local App Imports ---
from ..logging_utils import log_unauthorized_discount_access
from .common import service_provider_required, test_view
from .provider import (
    provider_discount_list, create_service_discount, edit_service_discount,
    create_venue_discount, edit_venue_discount, service_discount_detail,
    venue_discount_detail, delete_service_discount, delete_venue_discount,
    quick_edit_discount, provider_discount_analytics, provider_discount_dashboard,
)

from .customer import (
    featured_discounts_view, venue_discounts_view, search_discounts_view,
    discount_detail_view,
)

from .admin import (
    is_admin, admin_discount_dashboard, admin_discount_list, admin_discount_detail,
    admin_create_platform_discount, admin_edit_platform_discount,
    admin_delete_platform_discount, admin_approve_discount,
    admin_usage_analytics,
)


__all__ = [
    'log_unauthorized_discount_access',
    'test_view', 'service_provider_required',
    'provider_discount_list', 'create_service_discount', 'edit_service_discount',
    'create_venue_discount', 'edit_venue_discount', 'service_discount_detail',
    'venue_discount_detail', 'delete_service_discount', 'delete_venue_discount',
    'quick_edit_discount', 'provider_discount_analytics', 'provider_discount_dashboard',
    'featured_discounts_view', 'venue_discounts_view', 'search_discounts_view',
    'discount_detail_view',
    'is_admin', 'admin_discount_dashboard', 'admin_discount_list',
    'admin_discount_detail', 'admin_create_platform_discount',
    'admin_edit_platform_discount', 'admin_delete_platform_discount',
    'admin_approve_discount', 'admin_usage_analytics',
]
