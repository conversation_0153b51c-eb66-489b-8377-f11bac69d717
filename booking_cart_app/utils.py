# Django imports
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.core.cache import cache
from datetime import timedelta, time, datetime

# Local imports
from .models import Cart, CartItem, Booking, BookingItem, ServiceAvailability

# Import logging utilities
try:
    from utils.logging_utils import get_app_logger, log_user_activity, log_error
    LOGGING_ENABLED = True
except ImportError:
    LOGGING_ENABLED = False

    def get_app_logger(app_name, logger_type=''):
        import logging
        return logging.getLogger(app_name)

    def log_user_activity(app_name, activity_type, user=None, request=None, details=None):
        pass

    def log_error(app_name, error_type, error_message, user=None, request=None, exception=None, details=None):
        pass

# Import notification tasks
try:
    from notifications_app.tasks import send_booking_reminder_task
    NOTIFICATIONS_ENABLED = True
except ImportError:
    NOTIFICATIONS_ENABLED = False


def get_or_create_cart(user):
    """
    Get or create a cart for the user.
    Returns the user's active cart, creating one if it doesn't exist.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        raise ValidationError("Only customers can have carts")
    
    cart, created = Cart.objects.get_or_create(customer=user)
    
    # If cart has expired, extend it
    if cart.is_expired:
        cart.extend_expiration()
    
    return cart


def clean_expired_cart_items():
    """
    Remove expired cart items and their empty carts.
    Returns the number of items removed.
    """
    try:
        # Find expired carts
        expired_carts = Cart.objects.filter(expires_at__lt=timezone.now())

        # Count items in expired carts
        expired_items_count = CartItem.objects.filter(cart__in=expired_carts).count()

        if expired_items_count > 0 and LOGGING_ENABLED:
            # Log expired cart cleanup
            log_user_activity(
                'booking_cart_app',
                'expired_carts_cleaned',
                user=None,
                request=None,
                details={
                    'expired_carts_count': expired_carts.count(),
                    'expired_items_count': expired_items_count,
                    'cleanup_timestamp': str(timezone.now())
                }
            )

        # Delete expired carts (this will cascade delete cart items)
        expired_carts.delete()

        return expired_items_count

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cart_cleanup_error',
                f"Error cleaning expired cart items: {str(e)}",
                user=None,
                request=None,
                exception=e
            )
        return 0


def get_cart_total(user):
    """
    Calculate the total price of items in a user's cart.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return 0
    
    try:
        cart = Cart.objects.get(customer=user)
        if not cart.is_expired:
            return cart.total_price
    except Cart.DoesNotExist:
        pass
    
    return 0


def check_service_availability(service, date, time_slot, quantity=1):
    """
    Check if a service is available for booking on a specific date and time.
    Returns (is_available, message) tuple.
    """
    try:
        availability = ServiceAvailability.objects.get(
            service=service,
            available_date=date,
            start_time=time_slot
        )
        
        if not availability.is_available:
            return False, "This time slot is not available for booking."
        
        if availability.is_fully_booked:
            return False, "This time slot is fully booked."
        
        if availability.current_bookings + quantity > availability.max_bookings:
            available_spots = availability.available_spots
            return False, f"Only {available_spots} spot(s) available for this time slot."
        
        return True, "Available"
        
    except ServiceAvailability.DoesNotExist:
        # If no availability record exists, assume it's available
        # This will be created when the booking is made
        return True, "Available"


def get_available_time_slots(service, date):
    """
    Get available time slots for a service on a specific date.
    Returns a list of (time, available_spots) tuples.
    """
    cache_key = f"avail-slots:{service.id}:{date.isoformat()}"
    cached = cache.get(cache_key)
    if cached is not None:
        return cached

    # Get all availability slots for the service on the given date
    availability_slots = ServiceAvailability.objects.filter(
        service=service,
        available_date=date,
        is_available=True
    ).order_by('start_time')
    
    available_slots = []
    for slot in availability_slots:
        if not slot.is_fully_booked:
            available_slots.append((slot.start_time, slot.available_spots))
    
    # If no availability slots exist, generate default time slots
    if not available_slots:
        # Generate default time slots from 9 AM to 5 PM with 1-hour intervals
        start_time = time(9, 0)  # 9:00 AM
        end_time = time(17, 0)   # 5:00 PM
        current_time = datetime.combine(date, start_time)
        end_datetime = datetime.combine(date, end_time)
        
        while current_time < end_datetime:
            available_slots.append((current_time.time(), 1))  # Default 1 spot available
            current_time += timedelta(hours=1)
    
    cache.set(cache_key, available_slots, 300)
    return available_slots


def create_booking_from_cart(cart, notes=''):
    """
    Create bookings from cart items.
    Groups items by venue and creates separate bookings for each venue.
    Returns a list of created booking objects.
    """
    if cart.is_expired:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_expired_cart',
                "Attempted to create booking from expired cart",
                user=cart.customer,
                request=None,
                details={'cart_id': cart.id, 'expires_at': str(cart.expires_at)}
            )
        raise ValidationError("Cart has expired")

    if not cart.items.exists():
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_empty_cart',
                "Attempted to create booking from empty cart",
                user=cart.customer,
                request=None,
                details={'cart_id': cart.id}
            )
        raise ValidationError("Cart is empty")

    try:
        # Group cart items by venue first for validation
        venues_items = {}
        for item in cart.items.all():
            venue = item.service.venue
            if venue not in venues_items:
                venues_items[venue] = []
            venues_items[venue].append(item)

        # Validate concurrent booking limits for all items before creating any bookings
        validation_errors = []
        for venue, items in venues_items.items():
            provider = venue.service_provider
            
            for cart_item in items:
                is_valid, error_message, current_count = validate_concurrent_booking_limit(
                    provider,
                    cart_item.selected_date,
                    cart_item.selected_time_slot
                )
                
                if not is_valid:
                    validation_errors.append({
                        'service': cart_item.service.service_title,
                        'date': cart_item.selected_date,
                        'time': cart_item.selected_time_slot,
                        'venue': venue.venue_name,
                        'error': error_message
                    })

        # If there are validation errors, raise exception with details
        if validation_errors:
            error_details = "; ".join([
                f"{error['service']} at {error['venue']} on {error['date']} {error['time']}: {error['error']}"
                for error in validation_errors
            ])
            
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'booking_creation_concurrent_limit_exceeded',
                    f"Concurrent booking limit validation failed: {error_details}",
                    user=cart.customer,
                    request=None,
                    details={
                        'cart_id': cart.id,
                        'validation_errors': validation_errors
                    }
                )
            
            raise ValidationError(f"Booking failed due to concurrent limit violations: {error_details}")

        bookings_created = []

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_creation_started',
                user=cart.customer,
                request=None,
                details={
                    'cart_id': cart.id,
                    'venues_count': len(venues_items),
                    'total_items': cart.total_items,
                    'total_price': str(cart.total_price),
                    'notes_provided': bool(notes)
                }
            )

        # Create a booking for each venue
        for venue, items in venues_items.items():
            # Calculate total price for this venue's items
            total_price = sum(item.total_price for item in items)

            # Create the booking
            booking = Booking.objects.create(
                customer=cart.customer,
                venue=venue,
                total_price=total_price,
                notes=notes,
                status=Booking.PENDING
            )

            # Create booking items
            booking_items_details = []
            earliest_appointment = None
            
            for cart_item in items:
                booking_item = BookingItem.objects.create(
                    booking=booking,
                    service=cart_item.service,
                    service_title=cart_item.service.service_title,
                    service_price=cart_item.price_per_item,
                    quantity=cart_item.quantity,
                    scheduled_date=cart_item.selected_date,
                    scheduled_time=cart_item.selected_time_slot,
                    duration_minutes=cart_item.service.duration_minutes
                )

                booking_items_details.append({
                    'service_title': cart_item.service.service_title,
                    'scheduled_date': str(cart_item.selected_date),
                    'scheduled_time': str(cart_item.selected_time_slot),
                    'quantity': cart_item.quantity,
                    'price': str(cart_item.price_per_item)
                })

                # Track earliest appointment for reminder scheduling
                appointment_datetime = timezone.make_aware(
                    datetime.combine(cart_item.selected_date, cart_item.selected_time_slot)
                )
                if earliest_appointment is None or appointment_datetime < earliest_appointment:
                    earliest_appointment = appointment_datetime

                # Update service availability
                try:
                    availability = ServiceAvailability.objects.get(
                        service=cart_item.service,
                        available_date=cart_item.selected_date,
                        start_time=cart_item.selected_time_slot
                    )
                    availability.book_slot()
                except ServiceAvailability.DoesNotExist:
                    # Create availability record if it doesn't exist
                    ServiceAvailability.objects.create(
                        service=cart_item.service,
                        available_date=cart_item.selected_date,
                        start_time=cart_item.selected_time_slot,
                        end_time=cart_item.selected_time_slot,  # Same as start time for now
                        max_bookings=10,  # Default max bookings
                        current_bookings=cart_item.quantity,
                        is_available=True
                    )

            # Schedule 2-hour reminder if reminders are enabled and appointment is in future
            if NOTIFICATIONS_ENABLED and earliest_appointment:
                reminder_time = earliest_appointment - timedelta(hours=2)
                now = timezone.now()
                
                # Only schedule if reminder time is in the future
                if reminder_time > now:
                    try:
                        send_booking_reminder_task.apply_async(
                            args=[booking.id],
                            eta=reminder_time,
                            task_id=f"booking_reminder_{booking.id}"
                        )
                        
                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'reminder_scheduled',
                                user=cart.customer,
                                request=None,
                                details={
                                    'booking_id': str(booking.booking_id),
                                    'friendly_id': booking.friendly_id,
                                    'reminder_time': str(reminder_time),
                                    'appointment_time': str(earliest_appointment)
                                }
                            )
                    except Exception as reminder_error:
                        # Log but don't fail booking creation
                        if LOGGING_ENABLED:
                            log_error(
                                'booking_cart_app',
                                'reminder_scheduling_error',
                                f"Failed to schedule reminder for booking {booking.id}: {str(reminder_error)}",
                                user=cart.customer,
                                request=None,
                                exception=reminder_error,
                                details={'booking_id': str(booking.booking_id)}
                            )

            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'booking_created',
                    user=cart.customer,
                    request=None,
                    details={
                        'booking_id': str(booking.booking_id),
                        'friendly_id': booking.friendly_id,
                        'venue_name': venue.venue_name,
                        'venue_id': venue.id,
                        'total_price': str(total_price),
                        'items_count': len(items),
                        'booking_items': booking_items_details,
                        'status': booking.status,
                        'earliest_appointment': str(earliest_appointment) if earliest_appointment else None
                    }
                )

            bookings_created.append(booking)

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_creation_completed',
                user=cart.customer,
                request=None,
                details={
                    'cart_id': cart.id,
                    'bookings_created_count': len(bookings_created),
                    'total_amount': str(sum(b.total_price for b in bookings_created)),
                    'booking_ids': [str(b.booking_id) for b in bookings_created],
                    'friendly_ids': [b.friendly_id for b in bookings_created]
                }
            )

        return bookings_created

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_error',
                f"Error creating bookings from cart: {str(e)}",
                user=cart.customer,
                request=None,
                exception=e,
                details={'cart_id': cart.id}
            )
        raise


def get_upcoming_bookings(user, limit=None):
    """
    Get upcoming bookings for a customer.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return Booking.objects.none()
    
    today = timezone.now().date()
    bookings = Booking.objects.filter(
        customer=user,
        items__scheduled_date__gte=today,
        status__in=[Booking.PENDING, Booking.CONFIRMED]
    ).distinct().order_by('items__scheduled_date', 'items__scheduled_time')
    
    if limit:
        bookings = bookings[:limit]
    
    return bookings


def get_past_bookings(user, limit=None):
    """
    Get past bookings for a customer.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return Booking.objects.none()
    
    today = timezone.now().date()
    bookings = Booking.objects.filter(
        customer=user
    ).filter(
        Q(items__scheduled_date__lt=today) | 
        Q(status__in=[Booking.CANCELLED, Booking.COMPLETED])
    ).distinct().order_by('-items__scheduled_date', '-items__scheduled_time')
    
    if limit:
        bookings = bookings[:limit]
    
    return bookings


def can_cancel_booking(booking):
    """
    Check if a booking can be cancelled (within 6 hours of creation).
    """
    if booking.status in [Booking.CANCELLED, Booking.COMPLETED]:
        return False
    
    # Check if booking was made within last 6 hours
    time_since_booking = timezone.now() - booking.booking_date
    return time_since_booking <= timedelta(hours=6)


def get_booking_analytics(user=None, venue=None, start_date=None, end_date=None):
    """
    Get comprehensive booking analytics data.
    Can be filtered by user, venue, or date range.
    """
    from django.db.models import Sum, Count, Avg
    from decimal import Decimal

    bookings = Booking.objects.all()

    if user:
        bookings = bookings.filter(customer=user)

    if venue:
        bookings = bookings.filter(venue=venue)

    if start_date:
        bookings = bookings.filter(booking_date__gte=start_date)

    if end_date:
        bookings = bookings.filter(booking_date__lte=end_date)

    # Calculate basic analytics
    total_bookings = bookings.count()
    pending_bookings = bookings.filter(status=Booking.PENDING).count()
    confirmed_bookings = bookings.filter(status=Booking.CONFIRMED).count()
    cancelled_bookings = bookings.filter(status=Booking.CANCELLED).count()
    declined_bookings = bookings.filter(status=Booking.DECLINED).count()
    completed_bookings = bookings.filter(status=Booking.COMPLETED).count()
    disputed_bookings = bookings.filter(status=Booking.DISPUTED).count()
    no_show_bookings = bookings.filter(status=Booking.NO_SHOW).count()

    # Calculate revenue
    revenue_bookings = bookings.filter(status__in=[Booking.CONFIRMED, Booking.COMPLETED])
    total_revenue = revenue_bookings.aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')
    avg_booking_value = revenue_bookings.aggregate(avg=Avg('total_price'))['avg'] or Decimal('0.00')

    # Calculate rates
    cancellation_rate = (cancelled_bookings / total_bookings * 100) if total_bookings > 0 else 0
    dispute_rate = (disputed_bookings / total_bookings * 100) if total_bookings > 0 else 0
    no_show_rate = (no_show_bookings / total_bookings * 100) if total_bookings > 0 else 0
    confirmation_rate = (confirmed_bookings / total_bookings * 100) if total_bookings > 0 else 0

    return {
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'confirmed_bookings': confirmed_bookings,
        'cancelled_bookings': cancelled_bookings,
        'declined_bookings': declined_bookings,
        'completed_bookings': completed_bookings,
        'disputed_bookings': disputed_bookings,
        'no_show_bookings': no_show_bookings,
        'total_revenue': total_revenue,
        'avg_booking_value': avg_booking_value,
        'cancellation_rate': round(cancellation_rate, 2),
        'dispute_rate': round(dispute_rate, 2),
        'no_show_rate': round(no_show_rate, 2),
        'confirmation_rate': round(confirmation_rate, 2),
    }


def get_dispute_analytics(start_date=None, end_date=None):
    """
    Get analytics specifically for disputes.
    """
    from django.db.models import Count

    disputes = Booking.objects.filter(status=Booking.DISPUTED)

    if start_date:
        disputes = disputes.filter(dispute_filed_at__gte=start_date)

    if end_date:
        disputes = disputes.filter(dispute_filed_at__lte=end_date)

    total_disputes = disputes.count()
    resolved_disputes = disputes.filter(dispute_resolved_at__isnull=False).count()
    unresolved_disputes = disputes.filter(dispute_resolved_at__isnull=True).count()

    # Disputes by who filed them
    customer_disputes = disputes.filter(dispute_filed_by='customer').count()
    provider_disputes = disputes.filter(dispute_filed_by='provider').count()
    admin_disputes = disputes.filter(dispute_filed_by='admin').count()

    # Average resolution time for resolved disputes
    resolved_dispute_times = []
    for dispute in disputes.filter(dispute_resolved_at__isnull=False):
        if dispute.dispute_filed_at and dispute.dispute_resolved_at:
            resolution_time = dispute.dispute_resolved_at - dispute.dispute_filed_at
            resolved_dispute_times.append(resolution_time.total_seconds() / 3600)  # Convert to hours

    avg_resolution_time = sum(resolved_dispute_times) / len(resolved_dispute_times) if resolved_dispute_times else 0

    return {
        'total_disputes': total_disputes,
        'resolved_disputes': resolved_disputes,
        'unresolved_disputes': unresolved_disputes,
        'customer_disputes': customer_disputes,
        'provider_disputes': provider_disputes,
        'admin_disputes': admin_disputes,
        'avg_resolution_time_hours': round(avg_resolution_time, 2),
        'resolution_rate': round((resolved_disputes / total_disputes * 100) if total_disputes > 0 else 0, 2),
    }


def get_venue_booking_stats(venue_id=None):
    """
    Get booking statistics for venues.
    """
    from django.db.models import Count, Sum
    from venues_app.models import Venue

    if venue_id:
        venues = Venue.objects.filter(id=venue_id)
    else:
        venues = Venue.objects.all()

    venue_stats = []
    for venue in venues:
        bookings = Booking.objects.filter(venue=venue)
        total_bookings = bookings.count()
        total_revenue = bookings.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED]
        ).aggregate(total=Sum('total_price'))['total'] or 0

        venue_stats.append({
            'venue': venue,
            'total_bookings': total_bookings,
            'total_revenue': total_revenue,
            'pending_bookings': bookings.filter(status=Booking.PENDING).count(),
            'confirmed_bookings': bookings.filter(status=Booking.CONFIRMED).count(),
            'disputed_bookings': bookings.filter(status=Booking.DISPUTED).count(),
        })

    return venue_stats


def validate_concurrent_booking_limit(provider, scheduled_date, scheduled_time, exclude_booking=None):
    """
    Validate that a provider doesn't exceed the maximum of 10 concurrent bookings
    for any given time slot.
    
    Args:
        provider: ServiceProvider instance
        scheduled_date: Date of the booking
        scheduled_time: Time of the booking
        exclude_booking: Booking to exclude from count (for updates)
    
    Returns:
        tuple: (is_valid, error_message, current_count)
    """
    try:
        # Import here to avoid circular imports
        from .models import Booking, BookingItem
        
        # Count current bookings for this provider at this time slot
        booking_query = Booking.objects.filter(
            venue__service_provider=provider,
            items__scheduled_date=scheduled_date,
            items__scheduled_time=scheduled_time,
            status__in=[Booking.PENDING, Booking.CONFIRMED]
        )
        
        # Exclude the current booking if updating
        if exclude_booking:
            booking_query = booking_query.exclude(id=exclude_booking.id)
        
        current_count = booking_query.distinct().count()
        
        # Check if adding one more would exceed the limit
        if current_count >= 10:
            return False, f"Maximum of 10 concurrent bookings allowed per time slot. Current count: {current_count}", current_count
        
        return True, "", current_count
        
    except Exception as e:
        return False, f"Error validating concurrent bookings: {str(e)}", 0


def get_concurrent_booking_violations(provider, date=None):
    """
    Get all time slots where the provider has exceeded the 10 booking limit.
    
    Args:
        provider: ServiceProvider instance
        date: Optional date to check (defaults to today)
    
    Returns:
        dict: Time slots with violation details
    """
    try:
        from .models import Booking, BookingItem
        from django.utils import timezone
        
        if not date:
            date = timezone.now().date()
        
        # Get all bookings for the provider on the specified date
        bookings = Booking.objects.filter(
            venue__service_provider=provider,
            items__scheduled_date=date,
            status__in=[Booking.PENDING, Booking.CONFIRMED]
        ).prefetch_related('items').distinct()
        
        # Group by time slot and count
        time_slot_counts = {}
        
        for booking in bookings:
            for item in booking.items.filter(scheduled_date=date):
                time_key = item.scheduled_time
                
                if time_key not in time_slot_counts:
                    time_slot_counts[time_key] = {
                        'count': 0,
                        'bookings': []
                    }
                
                time_slot_counts[time_key]['count'] += 1
                time_slot_counts[time_key]['bookings'].append(booking)
        
        # Filter violations (more than 10 bookings)
        violations = {
            time_slot: data for time_slot, data in time_slot_counts.items()
            if data['count'] > 10
        }
        
        return violations
        
    except Exception as e:
        return {}


def get_provider_booking_analytics(provider, start_date=None, end_date=None):
    """
    Get comprehensive booking analytics for a provider.
    
    Args:
        provider: ServiceProvider instance
        start_date: Start date for analytics (defaults to current month start)
        end_date: End date for analytics (defaults to today)
    
    Returns:
        dict: Analytics data
    """
    try:
        from .models import Booking
        from django.utils import timezone
        from django.db.models import Sum, Count, Avg
        from datetime import datetime
        
        if not start_date:
            start_date = timezone.now().replace(day=1).date()
        if not end_date:
            end_date = timezone.now().date()
        
        # Base queryset for the provider's bookings
        bookings = Booking.objects.filter(
            venue__service_provider=provider,
            booking_date__date__range=[start_date, end_date]
        )
        
        # Status breakdown
        status_breakdown = {}
        for status_code, status_name in Booking.STATUS_CHOICES:
            count = bookings.filter(status=status_code).count()
            status_breakdown[status_code] = {
                'name': status_name,
                'count': count
            }
        
        # Revenue analytics
        confirmed_completed_bookings = bookings.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED]
        )
        
        total_revenue = confirmed_completed_bookings.aggregate(
            total=Sum('total_price')
        )['total'] or 0
        
        average_booking_value = confirmed_completed_bookings.aggregate(
            avg=Avg('total_price')
        )['avg'] or 0
        
        # Booking trends
        booking_trends = bookings.extra(
            select={'date': 'DATE(booking_date)'}
        ).values('date').annotate(
            count=Count('id'),
            revenue=Sum('total_price')
        ).order_by('date')
        
        # Top services
        from django.db.models import F
        top_services = bookings.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED]
        ).values(
            service_name=F('items__service__service_title')
        ).annotate(
            booking_count=Count('id'),
            total_revenue=Sum('total_price')
        ).order_by('-booking_count')[:5]
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'summary': {
                'total_bookings': bookings.count(),
                'total_revenue': total_revenue,
                'average_booking_value': average_booking_value,
            },
            'status_breakdown': status_breakdown,
            'booking_trends': list(booking_trends),
            'top_services': list(top_services),
        }
        
    except Exception as e:
        return {
            'error': f"Error generating analytics: {str(e)}"
        }


def process_booking_refund(booking, refund_reason='Booking cancelled'):
    """
    Process refund for a cancelled booking with enhanced error handling.
    Integrates with payment processing system.
    """
    try:
        # Import payment utilities if available
        try:
            from payments_app.utils import process_refund, RefundStatus
            payments_app_available = True
        except ImportError:
            payments_app_available = False
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'payments_app_not_available',
                    'Payments app not available for refund processing',
                    details={'booking_id': str(booking.booking_id)}
                )

        # Check if booking is eligible for refund
        if booking.status != booking.CANCELLED:
            raise ValidationError("Only cancelled bookings can be refunded")

        # Get payment information
        refund_amount = booking.total_price
        
        # Calculate refund amount based on cancellation policy
        from django.utils import timezone
        earliest_service = booking.items.order_by('scheduled_date', 'scheduled_time').first()
        if earliest_service:
            service_datetime = timezone.make_aware(
                datetime.combine(earliest_service.scheduled_date, earliest_service.scheduled_time)
            )
            time_until_service = service_datetime - timezone.now()
            
            # Refund policy: 
            # - More than 24 hours: 100% refund
            # - 6-24 hours: 75% refund  
            # - Less than 6 hours: 50% refund
            hours_until_service = time_until_service.total_seconds() / 3600
            
            if hours_until_service >= 24:
                refund_percentage = 1.0
            elif hours_until_service >= 6:
                refund_percentage = 0.75
            else:
                refund_percentage = 0.5
                
            refund_amount = booking.total_price * Decimal(str(refund_percentage))

        # Create refund record
        refund_data = {
            'booking': booking,
            'amount': refund_amount,
            'original_amount': booking.total_price,
            'reason': refund_reason,
            'status': 'pending',
            'initiated_at': timezone.now(),
        }

        # Process refund through payment system if available
        if payments_app_available:
            try:
                # Find the original payment
                payment = None
                try:
                    from payments_app.models import Payment
                    payment = Payment.objects.get(booking=booking, status='completed')
                except Payment.DoesNotExist:
                    if LOGGING_ENABLED:
                        log_error(
                            'booking_cart_app',
                            'original_payment_not_found',
                            f'Original payment not found for booking {booking.booking_id}',
                            details={'booking_id': str(booking.booking_id)}
                        )

                if payment:
                    # Process the refund
                    refund_result = process_refund(
                        payment_id=payment.stripe_payment_intent_id,
                        amount=int(refund_amount * 100),  # Convert to cents
                        reason=refund_reason
                    )

                    if refund_result.get('status') == RefundStatus.SUCCEEDED:
                        refund_data.update({
                            'status': 'completed',
                            'completed_at': timezone.now(),
                            'transaction_id': refund_result.get('refund_id'),
                            'processor_response': refund_result,
                        })
                        
                        # Send refund confirmation notification
                        if NOTIFICATIONS_ENABLED:
                            try:
                                from notifications_app.utils import notify_refund_processed
                                notify_refund_processed(booking, refund_amount)
                            except ImportError:
                                pass

                    else:
                        refund_data.update({
                            'status': 'failed',
                            'error_message': refund_result.get('error', 'Unknown error'),
                            'processor_response': refund_result,
                        })

            except Exception as e:
                refund_data.update({
                    'status': 'failed',
                    'error_message': str(e),
                })
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'refund_processing_error',
                        f'Error processing refund: {str(e)}',
                        exception=e,
                        details={
                            'booking_id': str(booking.booking_id),
                            'refund_amount': str(refund_amount)
                        }
                    )
        else:
            # Manual refund process - mark as pending for admin review
            refund_data.update({
                'status': 'manual_review',
                'notes': 'Automatic refund processing not available. Requires manual processing.',
            })

        # Create refund record (you might want to create a Refund model)
        try:
            from payments_app.models import Refund
            refund = Refund.objects.create(**refund_data)
        except ImportError:
            # If Refund model doesn't exist, log the refund information
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'refund_initiated',
                    details={
                        'booking_id': str(booking.booking_id),
                        'refund_amount': str(refund_amount),
                        'original_amount': str(booking.total_price),
                        'status': refund_data['status'],
                        'reason': refund_reason,
                    }
                )

        # Update booking with refund information
        booking.refund_amount = refund_amount
        booking.refund_status = refund_data['status']
        booking.save()

        return {
            'success': True,
            'refund_amount': refund_amount,
            'status': refund_data['status'],
            'message': f'Refund of ${refund_amount} initiated successfully'
        }

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'refund_process_error',
                f'Critical error in refund processing: {str(e)}',
                exception=e,
                details={'booking_id': str(booking.booking_id)}
            )
        
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to process refund. Please contact support.'
        }


def send_booking_confirmation_email(booking):
    """
    Send comprehensive booking confirmation email with all details.
    """
    try:
        from django.core.mail import send_mail
        from django.template.loader import render_to_string
        from django.conf import settings

        # Prepare email context
        context = {
            'booking': booking,
            'customer': booking.customer,
            'venue': booking.venue,
            'booking_items': booking.items.all().order_by('scheduled_date', 'scheduled_time'),
            'cancellation_deadline': booking.cancellation_deadline,
            'site_url': getattr(settings, 'SITE_URL', 'https://cozywish.com'),
        }

        # Render email templates
        subject = f'Booking Confirmation - {booking.friendly_id}'
        html_message = render_to_string('booking_cart_app/emails/booking_confirmation.html', context)
        plain_message = render_to_string('booking_cart_app/emails/booking_confirmation.txt', context)

        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[booking.customer.email],
            html_message=html_message,
            fail_silently=False,
        )

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'confirmation_email_sent',
                details={
                    'booking_id': str(booking.booking_id),
                    'customer_email': booking.customer.email,
                    'venue_name': booking.venue.venue_name,
                }
            )

        return True

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'confirmation_email_error',
                f'Error sending confirmation email: {str(e)}',
                exception=e,
                details={'booking_id': str(booking.booking_id)}
            )
        return False


def enforce_concurrent_booking_limits(service_provider, date, time_slot):
    """
    Enforce the maximum 10 concurrent bookings limit for service providers.
    """
    try:
        # Count confirmed bookings for the provider at the specific date/time
        concurrent_bookings = BookingItem.objects.filter(
            booking__venue__service_provider=service_provider,
            scheduled_date=date,
            scheduled_time=time_slot,
            booking__status__in=[Booking.CONFIRMED, Booking.PENDING]
        ).count()

        if concurrent_bookings >= 10:
            return False, f"Maximum concurrent bookings (10) reached for {time_slot.strftime('%I:%M %p')} on {date.strftime('%B %d, %Y')}"

        return True, f"Available spots: {10 - concurrent_bookings}"

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'concurrent_booking_check_error',
                f'Error checking concurrent booking limits: {str(e)}',
                exception=e,
                details={
                    'service_provider_id': service_provider.id,
                    'date': str(date),
                    'time_slot': str(time_slot)
                }
            )
        # Default to allowing booking on error
        return True, "Could not verify booking limits"


def get_booking_analytics_for_provider(service_provider, start_date=None, end_date=None):
    """
    Get comprehensive booking analytics for a service provider.
    """
    try:
        from django.db.models import Count, Sum, Avg
        from django.utils import timezone

        if not end_date:
            end_date = timezone.now().date()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        bookings = Booking.objects.filter(
            venue__service_provider=service_provider,
            booking_date__date__range=[start_date, end_date]
        )

        analytics = {
            'total_bookings': bookings.count(),
            'total_revenue': bookings.filter(
                status__in=[Booking.CONFIRMED, Booking.COMPLETED]
            ).aggregate(total=Sum('total_price'))['total'] or 0,
            'average_booking_value': bookings.filter(
                status__in=[Booking.CONFIRMED, Booking.COMPLETED]
            ).aggregate(avg=Avg('total_price'))['avg'] or 0,
            'status_breakdown': {
                'pending': bookings.filter(status=Booking.PENDING).count(),
                'confirmed': bookings.filter(status=Booking.CONFIRMED).count(),
                'completed': bookings.filter(status=Booking.COMPLETED).count(),
                'cancelled': bookings.filter(status=Booking.CANCELLED).count(),
                'declined': bookings.filter(status=Booking.DECLINED).count(),
            },
            'top_services': list(
                BookingItem.objects.filter(
                    booking__venue__service_provider=service_provider,
                    booking__booking_date__date__range=[start_date, end_date]
                ).values('service_title')
                .annotate(booking_count=Count('id'), revenue=Sum('service_price'))
                .order_by('-booking_count')[:5]
            ),
            'date_range': {
                'start': start_date,
                'end': end_date,
                'days': (end_date - start_date).days
            }
        }

        return analytics

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'analytics_error',
                f'Error generating booking analytics: {str(e)}',
                exception=e,
                details={'service_provider_id': service_provider.id}
            )
        return {}
