# Django imports
from django import forms
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import ServiceAvailability, RecurringAvailabilityPattern, PatternDateExclusion, ServiceAvailabilityTemplate
from ..form_mixins import AvailabilityValidationMixin


class ServiceAvailabilityForm(AvailabilityValidationMixin, forms.ModelForm):
    """Form for managing service availability."""

    class Meta:
        model = ServiceAvailability
        fields = [
            "available_date",
            "start_time",
            "end_time",
            "max_bookings",
            "is_available",
            "is_exception",
            "exception_reason",
        ]
        widgets = {
            "available_date": forms.DateInput(
                attrs={"class": "form-control", "type": "date", "min": timezone.now().date().isoformat()}
            ),
            "start_time": forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
            "end_time": forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
            "max_bookings": forms.NumberInput(attrs={"class": "form-control", "min": 1, "max": 20}),
            "is_available": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "is_exception": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "exception_reason": forms.TextInput(attrs={"class": "form-control", "placeholder": "Reason for exception"}),
        }
        labels = {
            "available_date": _("Date"),
            "start_time": _("Start Time"),
            "end_time": _("End Time"),
            "max_bookings": _("Maximum Bookings"),
            "is_available": _("Available"),
            "is_exception": _("Exception"),
            "exception_reason": _("Exception Reason"),
        }
        help_texts = {
            "available_date": _("Date when service is available"),
            "start_time": _("Start time of availability slot"),
            "end_time": _("End time of availability slot"),
            "max_bookings": _("Maximum number of bookings for this slot"),
            "is_available": _("Whether this slot is available for booking"),
            "is_exception": _("Mark as exception to normal schedule"),
            "exception_reason": _("Reason for the schedule exception"),
        }

    def clean(self):
        cleaned_data = super().clean()
        available_date = cleaned_data.get("available_date")
        start_time = cleaned_data.get("start_time")
        end_time = cleaned_data.get("end_time")

        # Validate future date with field-specific error
        if available_date and available_date <= timezone.now().date():
            self.add_error('available_date', _('Date must be in the future.'))

        # Validate start/end time with field-specific error
        if start_time and end_time and end_time <= start_time:
            self.add_error('end_time', _('End time must be after start time.'))

        return cleaned_data

    def save(self, service=None, commit=True):
        """Save the service availability with the provided service."""
        availability = super().save(commit=False)

        if service:
            availability.service = service

        # If is_available was not provided in form data, default to True
        if 'is_available' not in self.data:
            availability.is_available = True

        if commit:
            availability.save()

        return availability


class RecurringAvailabilityPatternForm(forms.ModelForm):
    """Form for creating and managing recurring availability patterns."""

    class Meta:
        model = RecurringAvailabilityPattern
        fields = [
            'name', 'pattern_type', 'start_time', 'end_time',
            'slot_duration_minutes', 'break_between_slots',
            'monday', 'tuesday', 'wednesday', 'thursday',
            'friday', 'saturday', 'sunday',
            'start_date', 'end_date', 'max_bookings_per_slot',
            'is_active', 'generate_advance_days', 'exclude_holidays'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Pattern name (e.g., "Weekly Schedule")'
            }),
            'pattern_type': forms.Select(attrs={'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'slot_duration_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 15,
                'max': 480,
                'placeholder': '60'
            }),
            'break_between_slots': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 120,
                'placeholder': '0'
            }),
            'monday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'tuesday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'wednesday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'thursday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'friday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'saturday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'sunday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'min': timezone.now().date().isoformat()
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
            }),
            'max_bookings_per_slot': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 20,
                'placeholder': '1'
            }),
            'generate_advance_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 365,
                'placeholder': '30'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'exclude_holidays': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': _('Pattern Name'),
            'pattern_type': _('Pattern Type'),
            'start_time': _('Start Time'),
            'end_time': _('End Time'),
            'slot_duration_minutes': _('Slot Duration (minutes)'),
            'break_between_slots': _('Break Between Slots (minutes)'),
            'start_date': _('Start Date'),
            'end_date': _('End Date'),
            'max_bookings_per_slot': _('Max Bookings per Slot'),
            'generate_advance_days': _('Generate Advance Days'),
            'is_active': _('Active'),
            'exclude_holidays': _('Exclude Holidays'),
        }
        help_texts = {
            'name': _('Descriptive name for this recurring pattern'),
            'pattern_type': _('How often the pattern repeats'),
            'start_time': _('When availability starts each day'),
            'end_time': _('When availability ends each day'),
            'slot_duration_minutes': _('Length of each bookable time slot'),
            'break_between_slots': _('Buffer time between consecutive slots'),
            'start_date': _('When this pattern becomes effective'),
            'end_date': _('When this pattern expires (optional)'),
            'max_bookings_per_slot': _('Maximum concurrent bookings per slot'),
            'generate_advance_days': _('How far ahead to create availability slots'),
            'is_active': _('Whether this pattern is actively creating slots'),
            'exclude_holidays': _('Skip generating slots on holidays'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default values for new patterns
        if not self.instance.pk:
            self.fields['slot_duration_minutes'].initial = 60
            self.fields['break_between_slots'].initial = 0
            self.fields['max_bookings_per_slot'].initial = 1
            self.fields['generate_advance_days'].initial = 30
            self.fields['is_active'].initial = True
            
            # Default to weekdays only
            self.fields['monday'].initial = True
            self.fields['tuesday'].initial = True
            self.fields['wednesday'].initial = True
            self.fields['thursday'].initial = True
            self.fields['friday'].initial = True

    def clean(self):
        """Validate the recurring pattern."""
        cleaned_data = super().clean()
        pattern_type = cleaned_data.get('pattern_type')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        # Time validation
        if start_time and end_time and start_time >= end_time:
            raise ValidationError(_('End time must be after start time.'))

        # Date validation
        if start_date and start_date <= timezone.now().date():
            self.add_error('start_date', _('Start date must be in the future.'))

        if end_date and start_date and end_date <= start_date:
            self.add_error('end_date', _('End date must be after start date.'))

        # Weekly pattern validation
        if pattern_type == RecurringAvailabilityPattern.WEEKLY:
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            selected_days = [cleaned_data.get(day, False) for day in weekday_fields]
            if not any(selected_days):
                raise ValidationError(_('At least one day must be selected for weekly patterns.'))

        return cleaned_data

    def save(self, service=None, commit=True):
        """Save the pattern with the provided service."""
        pattern = super().save(commit=False)
        
        if service:
            pattern.service = service
            
        if commit:
            pattern.save()
            
        return pattern


class PatternDateExclusionForm(forms.ModelForm):
    """Form for excluding specific dates from recurring patterns."""

    class Meta:
        model = PatternDateExclusion
        fields = ['excluded_date', 'reason']
        widgets = {
            'excluded_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'min': timezone.now().date().isoformat()
            }),
            'reason': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Holiday, vacation, etc.'
            }),
        }
        labels = {
            'excluded_date': _('Date to Exclude'),
            'reason': _('Reason'),
        }
        help_texts = {
            'excluded_date': _('Date that should be excluded from the pattern'),
            'reason': _('Optional reason for the exclusion'),
        }

    def clean_excluded_date(self):
        """Validate that the excluded date is in the future."""
        excluded_date = self.cleaned_data.get('excluded_date')
        if excluded_date and excluded_date <= timezone.now().date():
            raise ValidationError(_('Excluded date must be in the future.'))
        return excluded_date


class DateRangeAvailabilityForm(forms.Form):
    """Form for bulk creating availability slots over a date range."""
    
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'min': timezone.now().date().isoformat()
        }),
        label=_('Start Date'),
        help_text=_('First date to create availability')
    )
    
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
        }),
        label=_('End Date'),
        help_text=_('Last date to create availability')
    )
    
    start_time = forms.TimeField(
        widget=forms.TimeInput(attrs={
            'class': 'form-control',
            'type': 'time'
        }),
        label=_('Start Time'),
        help_text=_('When availability starts each day')
    )
    
    end_time = forms.TimeField(
        widget=forms.TimeInput(attrs={
            'class': 'form-control',
            'type': 'time'
        }),
        label=_('End Time'),
        help_text=_('When availability ends each day')
    )
    
    interval_minutes = forms.IntegerField(
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': 15,
            'max': 480,
            'placeholder': '60'
        }),
        label=_('Slot Interval (minutes)'),
        help_text=_('Duration of each time slot'),
        initial=60
    )
    
    max_bookings = forms.IntegerField(
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': 1,
            'max': 20,
            'placeholder': '1'
        }),
        label=_('Max Bookings per Slot'),
        help_text=_('Maximum bookings allowed per slot'),
        initial=1
    )
    
    # Day selection
    monday = forms.BooleanField(required=False, label=_('Monday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    tuesday = forms.BooleanField(required=False, label=_('Tuesday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    wednesday = forms.BooleanField(required=False, label=_('Wednesday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    thursday = forms.BooleanField(required=False, label=_('Thursday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    friday = forms.BooleanField(required=False, label=_('Friday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    saturday = forms.BooleanField(required=False, label=_('Saturday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    sunday = forms.BooleanField(required=False, label=_('Sunday'), widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))
    
    is_available = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label=_('Available for Booking'),
        help_text=_('Whether created slots are available for booking')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Default to weekdays
        self.fields['monday'].initial = True
        self.fields['tuesday'].initial = True
        self.fields['wednesday'].initial = True
        self.fields['thursday'].initial = True
        self.fields['friday'].initial = True

    def clean(self):
        """Validate the date range and settings."""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        # Date validation
        if start_date and start_date <= timezone.now().date():
            self.add_error('start_date', _('Start date must be in the future.'))

        if end_date and start_date and end_date <= start_date:
            self.add_error('end_date', _('End date must be after start date.'))

        # Time validation
        if start_time and end_time and start_time >= end_time:
            raise ValidationError(_('End time must be after start time.'))

        # Day selection validation
        weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        selected_days = [cleaned_data.get(day, False) for day in weekday_fields]
        if not any(selected_days):
            raise ValidationError(_('At least one day must be selected.'))

        return cleaned_data

    @property
    def selected_weekdays(self):
        """Get list of selected weekdays (0=Monday, 6=Sunday)."""
        if not hasattr(self, 'cleaned_data'):
            return []
        
        days = []
        weekday_mapping = [
            ('monday', 0), ('tuesday', 1), ('wednesday', 2), ('thursday', 3),
            ('friday', 4), ('saturday', 5), ('sunday', 6)
        ]
        
        for field_name, weekday in weekday_mapping:
            if self.cleaned_data.get(field_name, False):
                days.append(weekday)
        
        return days


class ServiceAvailabilityTemplateForm(forms.ModelForm):
    """Form for creating and managing availability templates."""

    class Meta:
        model = ServiceAvailabilityTemplate
        fields = ['name', 'description', 'is_default']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Template name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Description of this template'
            }),
            'is_default': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': _('Template Name'),
            'description': _('Description'),
            'is_default': _('Default Template'),
        }
        help_texts = {
            'name': _('Name for this availability template'),
            'description': _('Optional description of what this template includes'),
            'is_default': _('Make this the default template for the service'),
        }


class BookingActionForm(forms.Form):
    """Form for provider booking actions."""

    action_reason = forms.CharField(
        label=_("Reason (Optional)"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Provide a reason for this action (optional)",
                "maxlength": 500,
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    def clean_action_reason(self):
        reason = self.cleaned_data.get("action_reason", "")
        return reason.strip()


class BookingCompletionForm(forms.Form):
    """Form for marking bookings as completed."""

    completion_notes = forms.CharField(
        label=_("Completion Notes (Optional)"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Any notes about the completed service (optional)",
                "maxlength": 500,
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    def clean_completion_notes(self):
        notes = self.cleaned_data.get("completion_notes", "")
        return notes.strip()
