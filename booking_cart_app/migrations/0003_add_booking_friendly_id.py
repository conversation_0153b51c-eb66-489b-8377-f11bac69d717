# Generated by Django 5.2.3 on 2025-06-30 06:49

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('booking_cart_app', '0002_add_recurring_availability_patterns'),
        ('venues_app', '0017_add_service_tags_system'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='booking',
            name='friendly_id',
            field=models.CharField(blank=True, help_text='Customer-friendly booking reference (e.g., CW-2024-001234)', max_length=20, unique=True),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['friendly_id'], name='booking_car_friendl_687387_idx'),
        ),
    ]
