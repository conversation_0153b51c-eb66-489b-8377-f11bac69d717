# Generated by Django 5.2.3 on 2025-06-29 20:14

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('booking_cart_app', '0001_initial'),
        ('venues_app', '0014_add_service_categorization_and_enhancements'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceAvailabilityTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this availability template', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of this template')),
                ('configuration', models.JSONField(help_text='JSON configuration for availability settings')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default template for the service')),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Service Availability Template',
                'verbose_name_plural': 'Service Availability Templates',
                'ordering': ['service', 'name'],
            },
        ),
        migrations.AddField(
            model_name='serviceavailability',
            name='exception_reason',
            field=models.CharField(blank=True, help_text='Reason for the schedule exception', max_length=200),
        ),
        migrations.AddField(
            model_name='serviceavailability',
            name='is_exception',
            field=models.BooleanField(default=False, help_text='Whether this slot is an exception to the normal schedule'),
        ),
        migrations.AddField(
            model_name='serviceavailability',
            name='is_recurring',
            field=models.BooleanField(default=False, help_text='Whether this slot was created from a recurring pattern'),
        ),
        migrations.CreateModel(
            name='RecurringAvailabilityPattern',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this recurring pattern', max_length=100)),
                ('pattern_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], help_text='Type of recurring pattern', max_length=20)),
                ('start_time', models.TimeField(help_text='Start time for availability slots')),
                ('end_time', models.TimeField(help_text='End time for availability slots')),
                ('slot_duration_minutes', models.PositiveIntegerField(default=60, help_text='Duration of each slot in minutes', validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(480)])),
                ('break_between_slots', models.PositiveIntegerField(default=0, help_text='Break time between slots in minutes', validators=[django.core.validators.MaxValueValidator(120)])),
                ('monday', models.BooleanField(default=True, verbose_name='Monday')),
                ('tuesday', models.BooleanField(default=True, verbose_name='Tuesday')),
                ('wednesday', models.BooleanField(default=True, verbose_name='Wednesday')),
                ('thursday', models.BooleanField(default=True, verbose_name='Thursday')),
                ('friday', models.BooleanField(default=True, verbose_name='Friday')),
                ('saturday', models.BooleanField(default=False, verbose_name='Saturday')),
                ('sunday', models.BooleanField(default=False, verbose_name='Sunday')),
                ('start_date', models.DateField(help_text='Start date for pattern application')),
                ('end_date', models.DateField(blank=True, help_text='End date for pattern application (leave blank for ongoing)', null=True)),
                ('max_bookings_per_slot', models.PositiveIntegerField(default=1, help_text='Maximum bookings per generated slot', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(20)])),
                ('is_active', models.BooleanField(default=True, help_text='Whether this pattern is actively generating slots')),
                ('generate_advance_days', models.PositiveIntegerField(default=30, help_text='How many days in advance to generate slots', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)])),
                ('exclude_holidays', models.BooleanField(default=False, help_text='Whether to exclude holidays from generation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_generated', models.DateTimeField(blank=True, help_text='When slots were last generated for this pattern', null=True)),
                ('service', models.ForeignKey(help_text='Service this pattern applies to', on_delete=django.db.models.deletion.CASCADE, related_name='recurring_patterns', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Recurring Availability Pattern',
                'verbose_name_plural': 'Recurring Availability Patterns',
                'ordering': ['service', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PatternDateExclusion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excluded_date', models.DateField(help_text='Date to exclude from pattern generation')),
                ('reason', models.CharField(blank=True, help_text='Reason for excluding this date', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pattern', models.ForeignKey(help_text='Pattern to exclude date from', on_delete=django.db.models.deletion.CASCADE, related_name='date_exclusions', to='booking_cart_app.recurringavailabilitypattern')),
            ],
            options={
                'verbose_name': 'Pattern Date Exclusion',
                'verbose_name_plural': 'Pattern Date Exclusions',
                'ordering': ['excluded_date'],
            },
        ),
        migrations.AddField(
            model_name='serviceavailability',
            name='recurring_pattern',
            field=models.ForeignKey(blank=True, help_text='Recurring pattern that generated this slot', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_slots', to='booking_cart_app.recurringavailabilitypattern'),
        ),
        migrations.AddIndex(
            model_name='serviceavailability',
            index=models.Index(fields=['is_recurring'], name='booking_car_is_recu_0625cd_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceavailability',
            index=models.Index(fields=['recurring_pattern'], name='booking_car_recurri_96e514_idx'),
        ),
        migrations.AddField(
            model_name='serviceavailabilitytemplate',
            name='service',
            field=models.ForeignKey(help_text='Service this template applies to', on_delete=django.db.models.deletion.CASCADE, related_name='availability_templates', to='venues_app.service'),
        ),
        migrations.AlterUniqueTogether(
            name='patterndateexclusion',
            unique_together={('pattern', 'excluded_date')},
        ),
        migrations.AlterUniqueTogether(
            name='serviceavailabilitytemplate',
            unique_together={('service', 'name')},
        ),
    ]
