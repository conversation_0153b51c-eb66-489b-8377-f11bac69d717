# Django imports
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.http import JsonResponse
from django.db import transaction, models
from django.db.models import Q, Sum, Count
from django.urls import reverse
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views import View
from django.utils.decorators import method_decorator
from django.conf import settings
from django.core.exceptions import ValidationError

from datetime import timedelta, datetime, time
import json

# Local imports
from venues_app.models import Service, Venue
from ..models import Cart, CartItem, Booking, BookingItem, ServiceAvailability
from ..forms import (
    AddToCartForm,
    UpdateCartItemForm,
    CheckoutForm,
    BookingCancellationForm,
    ServiceAvailabilityForm,
    DateRangeAvailabilityForm,
    BookingActionForm,
    AdminBookingStatusForm,
    DisputeResolutionForm,
    BookingFilterForm,
)
from discount_app.utils import get_best_discount, record_discount_usage
from ..utils import (
    get_or_create_cart,
    get_cart_total,
    check_service_availability,
    get_available_time_slots,
    create_booking_from_cart,
    get_booking_analytics,
    get_dispute_analytics,
    get_venue_booking_stats,
)

try:
    from notifications_app.utils import notify_new_booking, notify_booking_cancelled, run_async
    NOTIFICATIONS_ENABLED = True
except ImportError:  # pragma: no cover - notifications optional
    NOTIFICATIONS_ENABLED = False

    def notify_new_booking(booking):  # pragma: no cover - fallback
        pass

    def notify_booking_cancelled(booking):  # pragma: no cover - fallback
        pass

    def run_async(func, *args, **kwargs):  # pragma: no cover - fallback
        func(*args, **kwargs)

try:
    from utils.logging_utils import get_app_logger, log_user_activity, log_error, log_security_event
    LOGGING_ENABLED = True
except ImportError:  # pragma: no cover - logging optional
    LOGGING_ENABLED = False

    def get_app_logger(app_name, logger_type=''):
        import logging
        return logging.getLogger(app_name)

    def log_user_activity(app_name, activity_type, user=None, request=None, details=None):
        pass

    def log_error(app_name, error_type, error_message, user=None, request=None, exception=None, details=None):
        pass

    def log_security_event(app_name, event_type, user_email=None, request=None, details=None):
        pass
