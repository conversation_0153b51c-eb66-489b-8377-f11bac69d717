from .common import *
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.exceptions import PermissionDenied
from django.db import models
from datetime import timedelta
from ..models import BookingStatusHistory

# ===== CUSTOMER VIEWS =====

@method_decorator(login_required, name='dispatch')
class AddToCartView(View):
    """Add a service to the customer's cart with date and time slot selection."""

    def get(self, request, service_id):
        return add_to_cart_view(request, service_id)

    def post(self, request, service_id):
        return add_to_cart_view(request, service_id)


@login_required
def add_to_cart_view(request, service_id):
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_access',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'add_to_cart', 'service_id': service_id}
            )
        messages.error(request, "Only customers can add services to cart.")
        return redirect('/')

    # Get the service
    service = get_object_or_404(Service, id=service_id, is_active=True)

    # Check if venue is active and approved
    if service.venue.visibility != 'active' or service.venue.approval_status != 'approved':
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'add_to_cart_unavailable_service',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'venue_visibility': service.venue.visibility,
                    'venue_status': service.venue.approval_status
                }
            )
        messages.error(request, "This service is not currently available.")
        return redirect('venues_app:venue_list')


    if request.method == 'POST':
        form = AddToCartForm(service=service, user=request.user, data=request.POST)
        if form.is_valid():
            selected_date = form.cleaned_data['selected_date']
            selected_time_slot = form.cleaned_data['selected_time_slot']
            quantity = form.cleaned_data['quantity']

            # Check service availability
            is_available, message = check_service_availability(
                service, selected_date, selected_time_slot, quantity
            )

            if not is_available:
                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'add_to_cart_unavailable_slot',
                        user=request.user,
                        request=request,
                        details={
                            'service_id': service_id,
                            'selected_date': str(selected_date),
                            'selected_time_slot': str(selected_time_slot),
                            'quantity': quantity,
                            'availability_message': message
                        }
                    )
                messages.error(request, message)
                return render(request, 'booking_cart_app/customer/add_to_cart.html', {
                    'form': form,
                    'service': service,
                })

            try:
                with transaction.atomic():
                    # Get or create cart for user
                    cart = get_or_create_cart(request.user)

                    # Check if the service is already in the cart for the same date and time
                    existing_item = CartItem.objects.filter(
                        cart=cart,
                        service=service,
                        selected_date=selected_date,
                        selected_time_slot=selected_time_slot
                    ).first()

                    if existing_item:
                        # Update the quantity
                        old_quantity = existing_item.quantity
                        existing_item.quantity = quantity
                        existing_item.save()

                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'cart_item_updated',
                                user=request.user,
                                request=request,
                                details={
                                    'service_id': service_id,
                                    'service_title': service.service_title,
                                    'selected_date': str(selected_date),
                                    'selected_time_slot': str(selected_time_slot),
                                    'old_quantity': old_quantity,
                                    'new_quantity': quantity,
                                    'price_per_item': str(existing_item.price_per_item)
                                }
                            )
                        messages.success(request, "Cart updated successfully.")
                    else:
                        # Calculate price with any applicable discounts
                        discount_info = get_best_discount(service, request.user)
                        if discount_info:
                            # discount_info is a tuple: (discount_object, discount_amount, final_price)
                            _, _, final_price = discount_info
                            price_per_item = final_price
                        else:
                            price_per_item = service.price_min

                        # Create a new cart item
                        cart_item = CartItem.objects.create(
                            cart=cart,
                            service=service,
                            selected_date=selected_date,
                            selected_time_slot=selected_time_slot,
                            quantity=quantity,
                            price_per_item=price_per_item
                        )

                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'item_added_to_cart',
                                user=request.user,
                                request=request,
                                details={
                                    'service_id': service_id,
                                    'service_title': service.service_title,
                                    'venue_name': service.venue.venue_name,
                                    'selected_date': str(selected_date),
                                    'selected_time_slot': str(selected_time_slot),
                                    'quantity': quantity,
                                    'price_per_item': str(price_per_item),
                                    'total_price': str(cart_item.total_price),
                                    'discount_applied': discount_info is not None
                                }
                            )
                        messages.success(request, "Service added to cart successfully.")

                    # Extend cart expiration
                    cart.extend_expiration()

                    # Redirect to cart
                    return redirect('booking_cart_app:cart_view')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'add_to_cart_error',
                        f"Error adding service to cart: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={
                            'service_id': service_id,
                            'selected_date': str(selected_date),
                            'selected_time_slot': str(selected_time_slot),
                            'quantity': quantity
                        }
                    )
                messages.error(request, f"Error adding service to cart: {str(e)}")
                return render(request, 'booking_cart_app/customer/add_to_cart.html', {
                    'form': form,
                    'service': service,
                })
    else:
        # Log cart view access
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'add_to_cart_page_viewed',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'service_title': service.service_title,
                    'venue_name': service.venue.venue_name
                }
            )
        form = AddToCartForm(service=service, user=request.user)

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/customer/add_to_cart.html', context)


@login_required
def get_available_slots_ajax(request, service_id):
    """
    AJAX endpoint to get available time slots for a service on a specific date.
    Enhanced to support the new booking widget.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint.'}, status=403)

    # Get the service
    from venues_app.models import Service
    try:
        service = Service.objects.get(id=service_id, is_active=True)
    except Service.DoesNotExist:
        return JsonResponse({'error': 'Service not found'}, status=404)

    # Get and validate the selected date
    date_str = request.GET.get('date')
    if not date_str:
        return JsonResponse({'error': 'Date parameter is required'}, status=400)

    try:
        selected_date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return JsonResponse({'error': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

    # Validate date is not in the past
    today = timezone.now().date()
    if selected_date < today:
        return JsonResponse({'error': 'Cannot book services in the past'}, status=400)

    # Validate date is within booking window
    max_advance_days = getattr(service, 'max_advance_booking_days', 60)
    max_date = today + timedelta(days=max_advance_days)
    if selected_date > max_date:
        return JsonResponse({'error': f'Cannot book more than {max_advance_days} days in advance'}, status=400)

    try:
        # Get available time slots for the service on the selected date
        available_slots = get_available_time_slots(service, selected_date)

        # Format slots for JSON response with enhanced information
        slots_data = []
        for slot_time, available_spots in available_slots:
            # Calculate end time
            from datetime import datetime, timedelta
            start_datetime = datetime.combine(selected_date, slot_time)
            end_datetime = start_datetime + timedelta(minutes=service.duration_minutes)
            
            slots_data.append({
                'time': slot_time.strftime('%H:%M'),
                'display': slot_time.strftime('%I:%M %p'),
                'end_time': end_datetime.strftime('%I:%M %p'),
                'available_spots': available_spots,
                'duration': service.duration_minutes,
                'is_available': available_spots > 0
            })

        # Sort slots by time
        slots_data.sort(key=lambda x: x['time'])

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'ajax_slots_retrieved',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'selected_date': str(selected_date),
                    'slots_count': len(slots_data)
                }
            )

        return JsonResponse({
            'slots': slots_data,
            'service_info': {
                'title': service.service_title,
                'duration': service.duration_minutes,
                'price': str(service.price_min)
            },
            'date_info': {
                'selected': selected_date.strftime('%Y-%m-%d'),
                'display': selected_date.strftime('%B %d, %Y')
            }
        })

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'ajax_slots_error',
                f"Error retrieving available slots: {str(e)}",
                user=request.user,
                request=request,
                exception=e,
                details={'service_id': service_id, 'selected_date': date_str}
            )
        return JsonResponse({'error': 'Error retrieving available slots'}, status=500)


@login_required
def get_monthly_availability_ajax(request, service_id):
    """
    AJAX endpoint to get availability overview for a month.
    Used by the calendar widget to show which dates have availability.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint.'}, status=403)

    # Get the service
    from venues_app.models import Service
    try:
        service = Service.objects.get(id=service_id, is_active=True)
    except Service.DoesNotExist:
        return JsonResponse({'error': 'Service not found'}, status=404)

    # Get year and month parameters
    try:
        year = int(request.GET.get('year', timezone.now().year))
        month = int(request.GET.get('month', timezone.now().month))
    except (ValueError, TypeError):
        return JsonResponse({'error': 'Invalid year or month parameter'}, status=400)

    # Validate month/year
    if month < 1 or month > 12:
        return JsonResponse({'error': 'Month must be between 1 and 12'}, status=400)
    
    current_year = timezone.now().year
    if year < current_year or year > current_year + 2:
        return JsonResponse({'error': 'Year must be within 2 years from now'}, status=400)

    try:
        # Get availability for the entire month
        from calendar import monthrange
        from datetime import date
        
        _, last_day = monthrange(year, month)
        start_date = date(year, month, 1)
        end_date = date(year, month, last_day)
        
        # Get all availability slots for the month
        availability_slots = ServiceAvailability.objects.filter(
            service=service,
            available_date__gte=start_date,
            available_date__lte=end_date,
            is_available=True
        ).exclude(
            current_bookings__gte=models.F('max_bookings')
        )
        
        # Group by date and count available slots
        availability_by_date = {}
        for slot in availability_slots:
            date_str = slot.available_date.strftime('%Y-%m-%d')
            if date_str not in availability_by_date:
                availability_by_date[date_str] = 0
            availability_by_date[date_str] += slot.available_spots

        # Also check for dates without explicit availability (use default scheduling)
        today = timezone.now().date()
        for day in range(1, last_day + 1):
            check_date = date(year, month, day)
            if check_date >= today:  # Only check future dates
                date_str = check_date.strftime('%Y-%m-%d')
                if date_str not in availability_by_date:
                    # Check if this date would have default availability
                    default_slots = get_available_time_slots(service, check_date)
                    if default_slots:
                        availability_by_date[date_str] = len(default_slots)

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'ajax_monthly_availability_retrieved',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'year': year,
                    'month': month,
                    'available_dates': len(availability_by_date)
                }
            )

        return JsonResponse({
            'availability': availability_by_date,
            'month_info': {
                'year': year,
                'month': month,
                'total_days': last_day
            },
            'service_info': {
                'title': service.service_title,
                'duration': service.duration_minutes
            }
        })

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'ajax_monthly_availability_error',
                f"Error retrieving monthly availability: {str(e)}",
                user=request.user,
                request=request,
                exception=e,
                details={'service_id': service_id, 'year': year, 'month': month}
            )
        return JsonResponse({'error': 'Error retrieving availability data'}, status=500)


@login_required  
def enhanced_add_to_cart_view(request, service_id):
    """
    Enhanced add to cart view that supports AJAX requests and provides better error handling.
    This replaces the old add_to_cart view with improved functionality.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'error': 'Only customers can add items to cart.'}, status=403)
        messages.error(request, "Only customers can add items to cart.")
        return redirect('utility_app:home')

    # Get the service
    from venues_app.models import Service
    try:
        service = Service.objects.get(id=service_id, is_active=True)
    except Service.DoesNotExist:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'error': 'Service not found'}, status=404)
        messages.error(request, "Service not found.")
        return redirect('venues_app:home')

    # Get or create cart
    cart, created = Cart.objects.get_or_create(customer=request.user)

    if request.method == 'POST':
        form = AddToCartForm(request.POST, service=service, user=request.user)
        
        if form.is_valid():
            try:
                # Check availability before adding to cart
                selected_date = form.cleaned_data['selected_date']
                selected_time_slot = form.cleaned_data['selected_time_slot']
                quantity = form.cleaned_data['quantity']
                
                # Parse time slot
                from datetime import time
                hour, minute = map(int, selected_time_slot.split(':'))
                time_slot = time(hour, minute)
                
                # Check if slot is still available
                is_available, message = check_service_availability(service, selected_date, time_slot, quantity)
                
                if not is_available:
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'error': message}, status=400)
                    form.add_error(None, message)
                    return render(request, 'booking_cart_app/customer/add_to_cart.html', {
                        'form': form,
                        'service': service,
                        'venue': service.venue
                    })

                # Check if item already exists in cart
                existing_item = cart.items.filter(
                    service=service,
                    selected_date=selected_date,
                    selected_time_slot=selected_time_slot
                ).first()

                if existing_item:
                    # Update quantity
                    existing_item.quantity += quantity
                    existing_item.save()
                    cart_item = existing_item
                else:
                    # Create new cart item
                    cart_item = form.save(cart=cart)

                # Log successful addition
                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'item_added_to_cart',
                        user=request.user,
                        request=request,
                        details={
                            'service_id': service_id,
                            'service_title': service.service_title,
                            'selected_date': str(selected_date),
                            'selected_time': selected_time_slot,
                            'quantity': quantity,
                            'price': str(cart_item.price_per_item)
                        }
                    )

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': True,
                        'message': 'Service added to cart successfully!',
                        'cart_count': cart.items.count(),
                        'cart_total': str(cart.total_price),
                        'redirect_url': reverse('booking_cart_app:cart')
                    })

                messages.success(request, f"{service.service_title} added to your cart!")
                return redirect('booking_cart_app:cart')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'add_to_cart_error',
                        f"Error adding item to cart: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'service_id': service_id}
                    )
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': 'An error occurred while adding to cart.'}, status=500)
                messages.error(request, "An error occurred while adding to cart. Please try again.")

    else:
        form = AddToCartForm(service=service, user=request.user)

    # For GET requests or form errors, render the form
    return render(request, 'booking_cart_app/customer/add_to_cart.html', {
        'form': form,
        'service': service,
        'venue': service.venue
    })


@login_required
def cart_view(request):
    """Display customer's shopping cart with items and pricing."""
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view cart.")
        return redirect('dashboard_app:dashboard')

    try:
        # Get or create cart for user
        cart = get_or_create_cart(request.user)

        # Check if cart has expired
        if cart.is_expired:
            messages.warning(request, "Your cart has expired. Please add items again.")
            cart.items.all().delete()  # Clear expired items
            return redirect('venues_app:venue_list')

        # Clean up expired items
        cart.clear_expired_items()

        # Get cart items with related data
        cart_items = cart.items.select_related(
            'service',
            'service__venue',
            'service__venue__service_provider'
        ).all()

        # Apply stackable discounts to cart
        discount_summary = None
        try:
            from discount_app.utils import apply_discount_to_cart
            discount_summary = apply_discount_to_cart(cart)
        except ImportError:
            pass  # Discount app not available

        # Calculate availability warnings
        unavailable_items = []
        for item in cart_items:
            is_available, message = check_service_availability(
                item.service,
                item.selected_date,
                item.selected_time_slot,
                item.quantity
            )
            if not is_available:
                unavailable_items.append({
                    'item': item,
                    'message': message
                })

        # Get unavailable items from session (from checkout attempts)
        session_unavailable = request.session.pop('unavailable_items', [])

        # Log cart view
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'cart_viewed',
                user=request.user,
                request=request,
                details={
                    'cart_id': cart.id,
                    'items_count': cart.total_items,
                    'total_price': str(cart.total_price),
                    'unavailable_items_count': len(unavailable_items),
                    'cart_expires_at': str(cart.expires_at),
                    'discount_applied': discount_summary is not None and discount_summary['total_discount_amount'] > 0 if discount_summary else False
                }
            )

        context = {
            'cart': cart,
            'cart_items': cart_items,
            'unavailable_items': unavailable_items,
            'session_unavailable': session_unavailable,
            'discount_summary': discount_summary,
        }
        return render(request, 'booking_cart_app/customer/cart.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cart_view_error',
                f"Error displaying cart: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading your cart.")
        return redirect('venues_app:venue_list')


@login_required
def update_cart_item_view(request, item_id):
    """
    Update the quantity of a cart item.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_update',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'update_cart_item', 'item_id': item_id}
            )
        messages.error(request, "Only customers can update cart.")
        return redirect('/')

    # Get the cart item
    cart_item = get_object_or_404(CartItem, id=item_id, cart__customer=request.user)

    if request.method == 'POST':
        form = UpdateCartItemForm(request.POST, instance=cart_item)
        if form.is_valid():
            old_quantity = cart_item.quantity
            quantity = form.cleaned_data['quantity']

            # Check service availability for the new quantity
            is_available, message = check_service_availability(
                cart_item.service,
                cart_item.selected_date,
                cart_item.selected_time_slot,
                quantity
            )

            if not is_available:
                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'cart_update_unavailable',
                        user=request.user,
                        request=request,
                        details={
                            'item_id': item_id,
                            'service_id': cart_item.service.id,
                            'old_quantity': old_quantity,
                            'requested_quantity': quantity,
                            'availability_message': message
                        }
                    )
                messages.error(request, message)
                return redirect('booking_cart_app:cart_view')

            try:
                # Update the cart item
                cart_item.quantity = quantity
                cart_item.save()

                # Extend cart expiration
                cart_item.cart.extend_expiration()

                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'cart_item_quantity_updated',
                        user=request.user,
                        request=request,
                        details={
                            'item_id': item_id,
                            'service_id': cart_item.service.id,
                            'service_title': cart_item.service.service_title,
                            'old_quantity': old_quantity,
                            'new_quantity': quantity,
                            'selected_date': str(cart_item.selected_date),
                            'selected_time_slot': str(cart_item.selected_time_slot)
                        }
                    )

                messages.success(request, "Cart updated successfully.")
                return redirect('booking_cart_app:cart_view')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'cart_update_error',
                        f"Error updating cart item: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'item_id': item_id, 'quantity': quantity}
                    )
                messages.error(request, "An error occurred while updating your cart.")
                return redirect('booking_cart_app:cart_view')
    else:
        form = UpdateCartItemForm(instance=cart_item)

    context = {
        'form': form,
        'cart_item': cart_item,
    }

    return render(request, 'booking_cart_app/customer/update_cart_item.html', context)


@login_required
def remove_from_cart_view(request, item_id):
    """
    Remove an item from the cart.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_removal',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'remove_from_cart', 'item_id': item_id}
            )
        messages.error(request, "Only customers can modify cart.")
        return redirect('/')

    # Get the cart item
    cart_item = get_object_or_404(CartItem, id=item_id, cart__customer=request.user)

    if request.method == 'POST':
        try:
            # Log the removal before deleting
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'cart_item_removed',
                    user=request.user,
                    request=request,
                    details={
                        'item_id': item_id,
                        'service_id': cart_item.service.id,
                        'service_title': cart_item.service.service_title,
                        'quantity': cart_item.quantity,
                        'selected_date': str(cart_item.selected_date),
                        'selected_time_slot': str(cart_item.selected_time_slot),
                        'total_price': str(cart_item.total_price)
                    }
                )

            cart_item.delete()
            messages.success(request, "Item removed from cart successfully.")

        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'cart_removal_error',
                    f"Error removing cart item: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e,
                    details={'item_id': item_id}
                )
            messages.error(request, "An error occurred while removing the item.")

    return redirect('booking_cart_app:cart_view')


@login_required
def checkout_view(request):
    """Handle checkout process for cart items."""
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can checkout.")
        return redirect('dashboard_app:dashboard')

    try:
        # Get cart
        cart = get_or_create_cart(request.user)

        # Validate cart
        if cart.is_expired:
            messages.error(request, "Your cart has expired. Please add items again.")
            return redirect('booking_cart_app:cart')

        if not cart.items.exists():
            messages.error(request, "Your cart is empty.")
            return redirect('venues_app:venue_list')

        # Apply stackable discounts to cart
        discount_summary = None
        try:
            from discount_app.utils import apply_discount_to_cart
            discount_summary = apply_discount_to_cart(cart)
        except ImportError:
            pass  # Discount app not available

        # Re-validate availability for all items
        unavailable_items = []
        for item in cart.items.all():
            is_available, message = check_service_availability(
                item.service,
                item.selected_date,
                item.selected_time_slot,
                item.quantity
            )
            if not is_available:
                unavailable_items.append({
                    'item': item,
                    'message': message
                })

        if unavailable_items:
            # Store unavailable items in session
            request.session['unavailable_items'] = [
                {
                    'service_title': item['item'].service.service_title,
                    'date': str(item['item'].selected_date),
                    'time': str(item['item'].selected_time_slot),
                    'message': item['message']
                }
                for item in unavailable_items
            ]
            messages.error(request, "Some items in your cart are no longer available.")
            return redirect('booking_cart_app:cart')

        if request.method == 'POST':
            form = CheckoutForm(request.POST)
            if form.is_valid():
                notes = form.cleaned_data.get('notes', '')

                try:
                    with transaction.atomic():
                        # Log checkout attempt
                        if LOGGING_ENABLED:
                            checkout_details = {
                                'cart_id': cart.id,
                                'cart_items_count': cart.total_items,
                                'cart_total_price': str(cart.total_price),
                                'notes_provided': bool(notes)
                            }
                            
                            # Add discount information if available
                            if discount_summary:
                                checkout_details.update({
                                    'original_price': str(discount_summary['total_original_price']),
                                    'discount_amount': str(discount_summary['total_discount_amount']),
                                    'final_price': str(discount_summary['total_final_price']),
                                    'savings_percentage': discount_summary['savings_percentage'],
                                    'group_discount_applied': discount_summary['group_discount_applied'],
                                    'discounts_count': len(discount_summary['applied_discounts'])
                                })
                            
                            log_user_activity(
                                'booking_cart_app',
                                'checkout_initiated',
                                user=request.user,
                                request=request,
                                details=checkout_details
                            )

                        # Create booking from cart
                        bookings = create_booking_from_cart(cart, notes)
                        
                        # Clear the cart after successful booking
                        cart.items.all().delete()
                        
                        # If multiple bookings were created, redirect to booking list
                        # If single booking, redirect to confirmation page
                        if len(bookings) == 1:
                            booking = bookings[0]
                            messages.success(request, "Booking created successfully!")
                            return redirect('booking_cart_app:booking_detail', booking_slug=booking.slug)
                        else:
                            messages.success(request, f"Successfully created {len(bookings)} bookings!")
                            return redirect('booking_cart_app:booking_list')

                except ValidationError as e:
                    messages.error(request, str(e))
                    # If booking creation fails, redirect to cart
                    return redirect('booking_cart_app:cart_view')
            
            context = {
                'form': form,
                'cart': cart,
                'total_items': cart.items.count(),
                'total_price': cart.total_price,
                'discount_summary': discount_summary,
                'stripe_public_key': getattr(settings, 'STRIPE_PUBLIC_KEY', ''),
            }
            
            try:
                return render(request, 'booking_cart_app/customer/checkout.html', context)
            except Exception as e:
                # Log the error
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'checkout_view_error',
                        f"Error in checkout view: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e
                    )
                messages.error(request, "An error occurred during checkout.")
                return redirect('booking_cart_app:cart_view')
        else:
            form = CheckoutForm()

        context = {
            'form': form,
            'cart': cart,
            'total_items': cart.items.count(),
            'total_price': cart.total_price,
            'discount_summary': discount_summary,
            'stripe_public_key': getattr(settings, 'STRIPE_PUBLIC_KEY', ''),
        }
        return render(request, 'booking_cart_app/customer/checkout.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'checkout_view_error',
                f"Error in checkout view: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred during checkout.")
        return redirect('booking_cart_app:cart_view')


@login_required
def enhanced_booking_list_view(request):
    """
    Enhanced customer booking dashboard with better error handling and loading states.
    """
    try:
        # Check if user is a customer
        if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
            messages.error(request, "Only customers can view bookings.")
            return redirect('/')

        # Get filter parameters
        status_filter = request.GET.get('status', 'all')
        view_type = request.GET.get('view', 'dashboard')
        page_number = request.GET.get('page', 1)

        # Show loading state for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'loading': True})

        # Get all bookings for the user with optimized queries
        bookings = (
            Booking.objects.filter(customer=request.user)
            .select_related('venue', 'venue__service_provider')
            .prefetch_related('items__service', 'status_history')
            .order_by('-booking_date')
        )

        # Apply status filter
        if status_filter != 'all':
            bookings = bookings.filter(status=status_filter)

        # Separate upcoming and past bookings
        today = timezone.now().date()
        upcoming_bookings = []
        past_bookings = []
        
        # Status analytics with error handling
        try:
            status_counts = {
                'total': bookings.count(),
                'pending': bookings.filter(status=Booking.PENDING).count(),
                'confirmed': bookings.filter(status=Booking.CONFIRMED).count(),
                'completed': bookings.filter(status=Booking.COMPLETED).count(),
                'cancelled': bookings.filter(status=Booking.CANCELLED).count(),
            }
        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'booking_stats_error',
                    f"Error calculating booking stats: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e
                )
            status_counts = {'total': 0, 'pending': 0, 'confirmed': 0, 'completed': 0, 'cancelled': 0}

        # Process bookings with error handling
        for booking in bookings:
            try:
                # Check if any booking item is scheduled for today or future
                has_future_items = booking.items.filter(scheduled_date__gte=today).exists()

                if has_future_items and booking.status not in [Booking.CANCELLED, Booking.COMPLETED]:
                    upcoming_bookings.append(booking)
                else:
                    past_bookings.append(booking)
            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_processing_error',
                        f"Error processing booking {booking.id}: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e
                    )
                # Skip this booking and continue
                continue

        # Get next upcoming booking
        next_booking = upcoming_bookings[0] if upcoming_bookings else None

        # Calculate total spent with error handling
        try:
            total_spent = bookings.filter(status__in=[Booking.CONFIRMED, Booking.COMPLETED]).aggregate(
                total=models.Sum('total_price')
            )['total'] or 0
        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'total_spent_calculation_error',
                    f"Error calculating total spent: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e
                )
            total_spent = 0

        # Recent status changes with error handling
        try:
            recent_status_changes = BookingStatusHistory.objects.filter(
                booking__customer=request.user,
                changed_at__gte=timezone.now() - timedelta(days=7)
            ).select_related('booking').order_by('-changed_at')[:5]
        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'status_changes_error',
                    f"Error fetching status changes: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e
                )
            recent_status_changes = []

        # Pagination with error handling
        try:
            paginator = Paginator(bookings, 10)
            page_obj = paginator.get_page(page_number)
        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'pagination_error',
                    f"Error in pagination: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e
                )
            page_obj = None
            messages.warning(request, "There was an issue loading your bookings. Showing limited results.")

        context = {
            'bookings': page_obj,
            'upcoming_bookings': upcoming_bookings[:5],
            'past_bookings': past_bookings[:5],
            'next_booking': next_booking,
            'status_filter': status_filter,
            'view_type': view_type,
            'status_choices': Booking.STATUS_CHOICES,
            'status_counts': status_counts,
            'total_spent': total_spent,
            'recent_status_changes': recent_status_changes,
            'today': today,
            'has_errors': page_obj is None,  # Flag for template to show error state
        }

        template = 'booking_cart_app/customer/booking_dashboard.html' if view_type == 'dashboard' else 'booking_cart_app/customer/booking_list.html'
        return render(request, template, context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_list_view_error',
                f"Critical error in booking list view: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        
        # Show user-friendly error page
        messages.error(
            request, 
            "We're experiencing technical difficulties loading your bookings. "
            "Please try again in a few minutes or contact support if the problem persists."
        )
        
        # Fallback context
        context = {
            'bookings': None,
            'upcoming_bookings': [],
            'past_bookings': [],
            'status_counts': {'total': 0, 'pending': 0, 'confirmed': 0, 'completed': 0, 'cancelled': 0},
            'total_spent': 0,
            'critical_error': True,
            'error_message': "Unable to load booking data. Please refresh the page or try again later.",
            'today': timezone.now().date(),
        }
        
        return render(request, 'booking_cart_app/customer/booking_dashboard.html', context)


@login_required
def enhanced_cancel_booking_view(request, booking_slug):
    """
    Enhanced booking cancellation with better error handling and user feedback.
    """
    try:
        # Check if user is a customer
        if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
            if LOGGING_ENABLED:
                log_security_event(
                    'booking_cart_app',
                    'unauthorized_booking_cancellation',
                    user_email=request.user.email if request.user.is_authenticated else None,
                    request=request,
                    details={'attempted_action': 'cancel_booking', 'booking_slug': booking_slug}
                )
            messages.error(request, "Only customers can cancel bookings.")
            return redirect('/')

        # Get the booking with error handling
        try:
            booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)
        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'booking_not_found_error',
                    f"Booking not found: {booking_slug}",
                    user=request.user,
                    request=request,
                    exception=e
                )
            messages.error(request, "Booking not found or you don't have permission to access it.")
            return redirect('booking_cart_app:booking_list')

        # Check if booking can be cancelled
        if not booking.can_be_cancelled:
            cancellation_reason = booking.cancellation_reason_display
            
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'booking_cancellation_denied',
                    user=request.user,
                    request=request,
                    details={
                        'booking_id': str(booking.booking_id),
                        'booking_date': str(booking.booking_date),
                        'reason': 'outside_cancellation_window',
                        'cancellation_reason': cancellation_reason
                    }
                )
            
            messages.error(
                request, 
                f"This booking cannot be cancelled. {cancellation_reason}. "
                f"If you need assistance, please contact customer support."
            )
            return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)

        if request.method == 'POST':
            form = BookingCancellationForm(request.POST)
            if form.is_valid():
                cancellation_reason = form.cleaned_data['cancellation_reason']

                try:
                    with transaction.atomic():
                        # Log cancellation attempt
                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'booking_cancellation_initiated',
                                user=request.user,
                                request=request,
                                details={
                                    'booking_id': str(booking.booking_id),
                                    'venue_name': booking.venue.venue_name,
                                    'total_price': str(booking.total_price),
                                    'cancellation_reason': cancellation_reason,
                                    'items_count': booking.items.count()
                                }
                            )

                        # Cancel the booking
                        booking.cancel_booking(cancellation_reason)

                        # Update service availability for all booking items
                        availability_errors = []
                        for item in booking.items.all():
                            try:
                                availability = ServiceAvailability.objects.get(
                                    service=item.service,
                                    available_date=item.scheduled_date,
                                    start_time=item.scheduled_time
                                )
                                availability.cancel_booking()
                            except ServiceAvailability.DoesNotExist:
                                availability_errors.append(f"Availability slot for {item.service_title} not found")
                            except Exception as e:
                                availability_errors.append(f"Error updating availability for {item.service_title}: {str(e)}")

                        # Log availability errors if any
                        if availability_errors and LOGGING_ENABLED:
                            log_error(
                                'booking_cart_app',
                                'availability_update_errors',
                                f"Errors updating availability: {', '.join(availability_errors)}",
                                user=request.user,
                                request=request,
                                details={'booking_id': str(booking.booking_id), 'errors': availability_errors}
                            )

                        # Log successful cancellation
                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'booking_cancelled',
                                user=request.user,
                                request=request,
                                details={
                                    'booking_id': str(booking.booking_id),
                                    'venue_name': booking.venue.venue_name,
                                    'total_price': str(booking.total_price),
                                    'cancellation_reason': cancellation_reason,
                                    'cancelled_at': str(timezone.now()),
                                    'availability_errors': len(availability_errors)
                                }
                            )

                        # Send notification with error handling
                        notification_sent = False
                        if NOTIFICATIONS_ENABLED:
                            try:
                                notify_booking_cancelled(booking)
                                notification_sent = True
                            except Exception as e:
                                if LOGGING_ENABLED:
                                    log_error(
                                        'booking_cart_app',
                                        'cancellation_notification_error',
                                        f"Error sending cancellation notification: {str(e)}",
                                        user=request.user,
                                        request=request,
                                        exception=e,
                                        details={'booking_id': str(booking.booking_id)}
                                    )

                        # Success message with additional info
                        success_message = "Booking cancelled successfully."
                        if availability_errors:
                            success_message += " Note: Some availability slots could not be updated automatically."
                        if not notification_sent:
                            success_message += " A confirmation will be sent to your email shortly."
                        
                        messages.success(request, success_message)
                        
                        # Add refund information if applicable
                        messages.info(
                            request,
                            "Your refund will be processed within 3-5 business days. "
                            "You will receive an email confirmation once the refund is initiated."
                        )
                        
                        return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)

                except ValidationError as e:
                    messages.error(request, f"Cancellation failed: {str(e)}")
                    return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)
                    
                except Exception as e:
                    if LOGGING_ENABLED:
                        log_error(
                            'booking_cart_app',
                            'booking_cancellation_error',
                            f"Error cancelling booking: {str(e)}",
                            user=request.user,
                            request=request,
                            exception=e,
                            details={'booking_id': str(booking.booking_id)}
                        )
                    
                    messages.error(
                        request, 
                        "We encountered an error while cancelling your booking. "
                        "Please try again or contact customer support for assistance."
                    )
                    return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)
        else:
            # Log cancellation page view
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'booking_cancellation_page_viewed',
                    user=request.user,
                    request=request,
                    details={
                        'booking_id': str(booking.booking_id),
                        'venue_name': booking.venue.venue_name,
                        'can_be_cancelled': booking.can_be_cancelled
                    }
                )
            form = BookingCancellationForm()

        # Calculate cancellation deadline for display
        deadline = booking.cancellation_deadline
        time_until_deadline = None
        if deadline:
            time_until_deadline = deadline - timezone.now()

        context = {
            'form': form,
            'booking': booking,
            'cancellation_deadline': deadline,
            'time_until_deadline': time_until_deadline,
            'can_cancel': booking.can_be_cancelled,
            'cancellation_reason': booking.cancellation_reason_display,
        }

        return render(request, 'booking_cart_app/customer/cancel_booking.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cancel_booking_view_critical_error',
                f"Critical error in cancel booking view: {str(e)}",
                user=request.user,
                request=request,
                exception=e,
                details={'booking_slug': booking_slug}
            )
        
        messages.error(
            request,
            "We're experiencing technical difficulties. Please try again later or contact support."
        )
        return redirect('booking_cart_app:booking_list')


@login_required
@require_http_methods(["POST"])
def check_booking_availability_api(request):
    """
    API endpoint to check booking availability with enhanced error handling.
    """
    try:
        data = json.loads(request.body)
        booking_id = data.get('booking_id')
        
        if not booking_id:
            return JsonResponse({'error': 'Missing booking ID'}, status=400)

        try:
            booking = Booking.objects.get(id=booking_id, customer=request.user)
        except Booking.DoesNotExist:
            return JsonResponse({'error': 'Booking not found'}, status=404)

        # Check if booking is still valid and cancellable
        availability_status = {
            'can_cancel': booking.can_be_cancelled,
            'cancellation_deadline': booking.cancellation_deadline.isoformat() if booking.cancellation_deadline else None,
            'status': booking.status,
            'booking_id': str(booking.booking_id),
            'items_status': []
        }

        # Check each booking item
        for item in booking.items.all():
            item_status = {
                'service_title': item.service_title,
                'scheduled_date': item.scheduled_date.isoformat(),
                'scheduled_time': item.scheduled_time.isoformat(),
                'is_past': item.scheduled_date < timezone.now().date(),
                'service_active': item.service.is_active if hasattr(item, 'service') else False,
            }
            availability_status['items_status'].append(item_status)

        return JsonResponse(availability_status)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_availability_api_error',
                f"Error checking booking availability: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required
@require_http_methods(["GET"])
def service_details_api(request, service_id):
    """
    API endpoint to get service details for quick view modal.
    Returns service information in JSON format.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint'}, status=403)

    try:
        service = get_object_or_404(Service, id=service_id, is_active=True)
        
        # Build response data
        data = {
            'id': service.id,
            'title': service.service_title,
            'description': service.service_description,
            'short_description': service.short_description,
            'venue_name': service.venue.venue_name,
            'category': service.service_category.name if service.service_category else 'Uncategorized',
            'duration': service.duration_minutes,
            'price_min': str(service.price_min),
            'price_max': str(service.price_max) if service.price_max else None,
            'image': service.service_image.url if service.service_image else None,
            'amenities': list(service.venue.amenities.values_list('name', flat=True)) if hasattr(service.venue, 'amenities') else [],
            'requires_booking': service.requires_booking,
            'max_advance_booking_days': service.max_advance_booking_days,
            'min_advance_booking_hours': service.min_advance_booking_hours,
        }
        
        # Log API access
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'service_details_api_accessed',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'service_title': service.service_title,
                    'venue_name': service.venue.venue_name
                }
            )
        
        return JsonResponse(data)
        
    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'service_details_api_error',
                f"Error fetching service details: {str(e)}",
                user=request.user,
                request=request,
                exception=e,
                details={'service_id': service_id}
            )
        return JsonResponse({'error': 'Failed to load service details'}, status=500)


@login_required
@require_http_methods(["POST"])
def validate_cart_availability_api(request):
    """
    API endpoint to validate cart availability in real-time.
    Used during checkout process to ensure all items are still available.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint'}, status=403)

    try:
        # Get user's cart
        cart = get_or_create_cart(request.user)
        
        if not cart.items.exists():
            return JsonResponse({
                'available': False,
                'error': 'Cart is empty'
            }, status=400)

        # Check each cart item for availability
        unavailable_items = []
        available_items = []
        
        for item in cart.items.all():
            try:
                # Parse time slot
                from datetime import time
                if isinstance(item.selected_time_slot, str):
                    hour, minute = map(int, item.selected_time_slot.split(':'))
                    time_slot = time(hour, minute)
                else:
                    time_slot = item.selected_time_slot
                
                # Check real-time availability
                is_available, message = check_service_availability(
                    item.service, 
                    item.selected_date, 
                    time_slot, 
                    item.quantity
                )
                
                if not is_available:
                    unavailable_items.append({
                        'id': item.id,
                        'service_title': item.service.service_title,
                        'venue_name': item.service.venue.venue_name,
                        'date': item.selected_date.strftime('%Y-%m-%d'),
                        'time': str(time_slot),
                        'quantity': item.quantity,
                        'message': message
                    })
                else:
                    available_items.append({
                        'id': item.id,
                        'service_title': item.service.service_title
                    })
                    
            except Exception as e:
                unavailable_items.append({
                    'id': item.id,
                    'service_title': item.service.service_title,
                    'venue_name': item.service.venue.venue_name,
                    'date': str(item.selected_date),
                    'time': str(item.selected_time_slot),
                    'quantity': item.quantity,
                    'message': f'Error checking availability: {str(e)}'
                })

        # Log validation results
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'cart_availability_validated',
                user=request.user,
                request=request,
                details={
                    'total_items': cart.items.count(),
                    'available_items': len(available_items),
                    'unavailable_items': len(unavailable_items),
                    'validation_successful': len(unavailable_items) == 0
                }
            )

        return JsonResponse({
            'available': len(unavailable_items) == 0,
            'total_items': cart.items.count(),
            'available_items': available_items,
            'unavailable_items': unavailable_items,
            'message': 'All items are available' if len(unavailable_items) == 0 else f'{len(unavailable_items)} items are no longer available'
        })

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cart_validation_api_error',
                f"Error validating cart availability: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        return JsonResponse({
            'available': False,
            'error': 'Unable to validate availability at this time'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def lock_cart_slots_api(request):
    """
    API endpoint to temporarily lock cart slots during checkout process.
    Prevents double-booking while user is completing checkout.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint'}, status=403)

    try:
        import json
        data = json.loads(request.body)
        action = data.get('action', 'lock')
        
        # Get user's cart
        cart = get_or_create_cart(request.user)
        
        if not cart.items.exists():
            return JsonResponse({
                'success': False,
                'error': 'Cart is empty'
            }, status=400)

        if action == 'lock':
            # Create temporary locks for cart items
            from django.core.cache import cache
            lock_duration = 600  # 10 minutes
            locks_created = []
            
            for item in cart.items.all():
                # Create unique lock key
                lock_key = f"slot_lock_{item.service.id}_{item.selected_date}_{item.selected_time_slot}_{request.user.id}"
                
                # Check if slot is already locked by another user
                existing_lock = cache.get(lock_key.replace(f'_{request.user.id}', '_*'))
                if existing_lock and existing_lock != request.user.id:
                    return JsonResponse({
                        'success': False,
                        'error': f'Slot for {item.service.service_title} is currently being booked by another customer',
                        'conflict_item': {
                            'service_title': item.service.service_title,
                            'date': str(item.selected_date),
                            'time': str(item.selected_time_slot)
                        }
                    }, status=409)
                
                # Create lock
                lock_data = {
                    'user_id': request.user.id,
                    'locked_at': timezone.now().isoformat(),
                    'service_id': item.service.id,
                    'quantity': item.quantity
                }
                
                cache.set(lock_key, lock_data, lock_duration)
                locks_created.append(lock_key)
            
            # Log lock creation
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'cart_slots_locked',
                    user=request.user,
                    request=request,
                    details={
                        'locks_created': len(locks_created),
                        'lock_duration': lock_duration,
                        'cart_items': cart.items.count()
                    }
                )
            
            return JsonResponse({
                'success': True,
                'message': 'Slots locked successfully',
                'locks_created': len(locks_created),
                'lock_expires_in': lock_duration
            })
            
        elif action == 'release':
            # Release locks for cart items
            from django.core.cache import cache
            locks_released = 0
            
            for item in cart.items.all():
                lock_key = f"slot_lock_{item.service.id}_{item.selected_date}_{item.selected_time_slot}_{request.user.id}"
                if cache.get(lock_key):
                    cache.delete(lock_key)
                    locks_released += 1
            
            # Log lock release
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'cart_slots_unlocked',
                    user=request.user,
                    request=request,
                    details={
                        'locks_released': locks_released,
                        'cart_items': cart.items.count()
                    }
                )
            
            return JsonResponse({
                'success': True,
                'message': 'Slots unlocked successfully',
                'locks_released': locks_released
            })
        
        else:
            return JsonResponse({
                'success': False,
                'error': 'Invalid action'
            }, status=400)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'slot_locking_api_error',
                f"Error in slot locking API: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        return JsonResponse({
            'success': False,
            'error': 'Unable to process slot locking request'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def check_provider_conflicts_api(request):
    """
    API endpoint to check for conflicts with provider's existing bookings.
    Ensures no double-booking from provider's perspective.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        return JsonResponse({'error': 'Only customers can access this endpoint'}, status=403)

    try:
        import json
        data = json.loads(request.body)
        
        # Get cart items or specific service details
        cart = get_or_create_cart(request.user)
        conflicts = []
        
        for item in cart.items.all():
            # Check for existing bookings that might conflict
            from datetime import time, datetime, timedelta
            
            if isinstance(item.selected_time_slot, str):
                hour, minute = map(int, item.selected_time_slot.split(':'))
                time_slot = time(hour, minute)
            else:
                time_slot = item.selected_time_slot
            
            # Create datetime for comparison
            item_datetime = datetime.combine(item.selected_date, time_slot)
            service_duration = timedelta(minutes=item.service.duration_minutes)
            item_end_datetime = item_datetime + service_duration
            
            # Check for overlapping bookings for this service
            existing_bookings = BookingItem.objects.filter(
                service=item.service,
                scheduled_date=item.selected_date,
                booking__status__in=[Booking.PENDING, Booking.CONFIRMED, Booking.IN_PROGRESS]
            ).exclude(
                booking__customer=request.user  # Exclude user's own bookings
            )
            
            for booking_item in existing_bookings:
                # Parse existing booking time
                if isinstance(booking_item.scheduled_time, str):
                    existing_hour, existing_minute = map(int, booking_item.scheduled_time.split(':'))
                    existing_time_slot = time(existing_hour, existing_minute)
                else:
                    existing_time_slot = booking_item.scheduled_time
                
                existing_datetime = datetime.combine(booking_item.scheduled_date, existing_time_slot)
                existing_end_datetime = existing_datetime + timedelta(minutes=booking_item.duration_minutes)
                
                # Check for overlap
                if (item_datetime < existing_end_datetime and item_end_datetime > existing_datetime):
                    conflicts.append({
                        'service_title': item.service.service_title,
                        'venue_name': item.service.venue.venue_name,
                        'requested_date': str(item.selected_date),
                        'requested_time': str(time_slot),
                        'conflicting_booking_id': str(booking_item.booking.booking_id),
                        'conflicting_time': str(existing_time_slot),
                        'conflict_type': 'time_overlap',
                        'message': f'Conflicts with existing booking from {existing_datetime.strftime("%I:%M %p")} to {existing_end_datetime.strftime("%I:%M %p")}'
                    })
            
            # Check provider's manual availability/blackout periods
            # This would integrate with provider's calendar or availability system
            provider_conflicts = check_provider_availability_conflicts(
                item.service.venue.provider,
                item.selected_date,
                time_slot,
                item.service.duration_minutes
            )
            
            conflicts.extend(provider_conflicts)

        # Log conflict checking
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'provider_conflicts_checked',
                user=request.user,
                request=request,
                details={
                    'cart_items_checked': cart.items.count(),
                    'conflicts_found': len(conflicts),
                    'has_conflicts': len(conflicts) > 0
                }
            )

        return JsonResponse({
            'has_conflicts': len(conflicts) > 0,
            'conflicts': conflicts,
            'total_items_checked': cart.items.count(),
            'message': 'No conflicts found' if len(conflicts) == 0 else f'{len(conflicts)} conflicts detected'
        })

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'provider_conflicts_api_error',
                f"Error checking provider conflicts: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        return JsonResponse({
            'has_conflicts': True,
            'error': 'Unable to check for conflicts at this time'
        }, status=500)


def check_provider_availability_conflicts(provider, date, time_slot, duration_minutes):
    """
    Helper function to check provider's specific availability conflicts.
    This can be extended to integrate with external calendar systems.
    """
    conflicts = []
    
    try:
        # Check if provider has marked this time as unavailable
        # This would integrate with provider's calendar or manual availability settings
        
        # Example: Check for provider's blocked times or special events
        from venues_app.models import ServiceAvailability
        
        # Look for any provider-level availability restrictions
        provider_availabilities = ServiceAvailability.objects.filter(
            service__venue__provider=provider,
            available_date=date,
            start_time__lte=time_slot,
            end_time__gte=time_slot,
            is_available=False
        )
        
        for unavailable_slot in provider_availabilities:
            conflicts.append({
                'conflict_type': 'provider_unavailable',
                'message': f'Provider has marked this time as unavailable',
                'unavailable_reason': getattr(unavailable_slot, 'unavailable_reason', 'Provider unavailable')
            })
    
    except Exception as e:
        # Log error but don't fail the entire check
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'provider_availability_check_error',
                f"Error checking provider availability: {str(e)}",
                details={
                    'provider_id': provider.id if provider else None,
                    'date': str(date),
                    'time_slot': str(time_slot)
                }
            )
    
    return conflicts


@login_required
def booking_list_view(request):
    """
    Basic booking list view for customers.
    Redirects to the enhanced booking list view.
    """
    return enhanced_booking_list_view(request)


@login_required
def booking_detail_view(request, booking_slug):
    """
    Basic booking detail view for customers.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view bookings.")
        return redirect('dashboard_app:dashboard')

    try:
        # Get the booking
        booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)

        # Get all booking items
        booking_items = booking.items.all().order_by('scheduled_date', 'scheduled_time')

        # Log booking detail view
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_detail_viewed',
                user=request.user,
                request=request,
                details={
                    'booking_id': str(booking.booking_id),
                    'venue_name': booking.venue.venue_name,
                    'status': booking.status
                }
            )

        context = {
            'booking': booking,
            'booking_items': booking_items,
        }
        return render(request, 'booking_cart_app/customer/booking_detail.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_detail_error',
                f"Error displaying booking detail: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading the booking details.")
        return redirect('booking_cart_app:booking_list')


@login_required
def cancel_booking_view(request, booking_slug):
    """
    Basic booking cancellation view for customers.
    Redirects to the enhanced cancellation view.
    """
    return enhanced_cancel_booking_view(request, booking_slug)


@login_required
def booking_confirmation_view(request, booking_slug):
    """
    Booking confirmation view for customers.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view booking confirmations.")
        return redirect('dashboard_app:dashboard')

    try:
        # Get the booking
        booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)

        context = {
            'booking': booking,
        }
        return render(request, 'booking_cart_app/customer/booking_confirmation.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_confirmation_error',
                f"Error displaying booking confirmation: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading the booking confirmation.")
        return redirect('booking_cart_app:booking_list')


