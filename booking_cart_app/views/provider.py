from .common import *
from django.db import models
from datetime import timed<PERSON><PERSON>
from ..models import BookingStatusHistory
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.db.models import Q
from datetime import datetime
# ===== PROVIDER VIEWS =====


@login_required
def provider_availability_list_view(request):
    """
    Display all services and their availability for the service provider.
    Shows overview of availability management for all provider's services.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_provider_access',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_view': 'provider_availability_list'}
            )
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    try:
        # Log user activity
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'provider_availability_list_viewed',
                user=request.user,
                request=request,
                details={'action': 'view_availability_list'}
            )

        # Get all services for the provider's venues
        from venues_app.models import Service, Venue
        provider_profile = request.user.service_provider_profile
        services = Service.objects.filter(
            venue__service_provider=provider_profile,
            venue__visibility=Venue.ACTIVE,
            is_active=True
        ).select_related('venue').prefetch_related('availability_slots')

        # Add availability stats for each service
        services_with_stats = []
        for service in services:
            total_slots = service.availability_slots.count()
            available_slots = service.availability_slots.filter(is_available=True).count()
            future_slots = service.availability_slots.filter(
                available_date__gte=timezone.now().date()
            ).count()

            services_with_stats.append({
                'service': service,
                'total_slots': total_slots,
                'available_slots': available_slots,
                'future_slots': future_slots,
            })

        context = {
            'services_with_stats': services_with_stats,
        }

        return render(request, 'booking_cart_app/provider/availability_list.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'provider_availability_list_error',
                f"Error loading availability list: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading your availability. Please try again.")
        return redirect('home')


@login_required
def provider_service_availability_view(request, service_id):
    """
    Display availability slots for a specific service.
    Shows all time slots with booking status and management options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    # Get availability slots for the service
    availability_slots = ServiceAvailability.objects.filter(
        service=service
    ).order_by('available_date', 'start_time')

    # Filter by date range if provided
    date_filter = request.GET.get('date_filter', 'upcoming')
    today = timezone.now().date()

    if date_filter == 'upcoming':
        availability_slots = availability_slots.filter(available_date__gte=today)
    elif date_filter == 'past':
        availability_slots = availability_slots.filter(available_date__lt=today)
    elif date_filter == 'today':
        availability_slots = availability_slots.filter(available_date=today)

    # Pagination
    paginator = Paginator(availability_slots, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'service': service,
        'availability_slots': page_obj,
        'date_filter': date_filter,
    }

    return render(request, 'booking_cart_app/provider/service_availability.html', context)


@login_required
def provider_availability_calendar_view(request, service_id):
    """Calendar view of availability for easier overview."""
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    slots = ServiceAvailability.objects.filter(service=service).order_by('available_date', 'start_time')

    calendar = {}
    for slot in slots:
        calendar.setdefault(slot.available_date, []).append(slot)

    context = {
        'service': service,
        'calendar': calendar,
    }

    return render(request, 'booking_cart_app/provider/availability_calendar.html', context)


@login_required
def provider_add_availability_view(request, service_id):
    """
    Add a new availability slot for a service.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    if request.method == 'POST':
        form = ServiceAvailabilityForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    availability = form.save(commit=False)
                    availability.service = service
                    availability.save()

                    messages.success(request, "Availability slot added successfully.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=service.id)

            except Exception as e:
                messages.error(request, f"Error adding availability slot: {str(e)}")
    else:
        form = ServiceAvailabilityForm()

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/provider/add_availability.html', context)


@login_required
def provider_bulk_availability_view(request, service_id):
    """
    Bulk add availability slots for a service over a date range.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    if request.method == 'POST':
        form = DateRangeAvailabilityForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    start_date = form.cleaned_data['start_date']
                    end_date = form.cleaned_data['end_date']
                    start_time = form.cleaned_data['start_time']
                    end_time = form.cleaned_data['end_time']
                    interval = form.cleaned_data['interval']
                    max_bookings = form.cleaned_data['max_bookings']
                    is_available = form.cleaned_data['is_available']

                    # Generate time slots
                    current_date = start_date
                    slots_created = 0

                    while current_date <= end_date:
                        current_time = datetime.combine(current_date, start_time)
                        end_datetime = datetime.combine(current_date, end_time)

                        while current_time < end_datetime:
                            slot_end_time = current_time + timedelta(minutes=interval)
                            if slot_end_time > end_datetime:
                                break

                            # Check if slot already exists
                            existing_slot = ServiceAvailability.objects.filter(
                                service=service,
                                available_date=current_date,
                                start_time=current_time.time()
                            ).first()

                            if not existing_slot:
                                ServiceAvailability.objects.create(
                                    service=service,
                                    available_date=current_date,
                                    start_time=current_time.time(),
                                    end_time=slot_end_time.time(),
                                    max_bookings=max_bookings,
                                    is_available=is_available
                                )
                                slots_created += 1

                            current_time = slot_end_time

                        current_date += timedelta(days=1)

                    messages.success(request, f"Successfully created {slots_created} availability slots.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=service.id)

            except Exception as e:
                messages.error(request, f"Error creating availability slots: {str(e)}")
    else:
        form = DateRangeAvailabilityForm()

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/provider/bulk_availability.html', context)


@login_required
def provider_edit_availability_view(request, availability_id):
    """
    Edit an existing availability slot.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the availability slot
    provider_profile = request.user.service_provider_profile
    availability = get_object_or_404(
        ServiceAvailability,
        id=availability_id,
        service__venue__service_provider=provider_profile
    )

    if request.method == 'POST':
        form = ServiceAvailabilityForm(request.POST, instance=availability)
        if form.is_valid():
            try:
                with transaction.atomic():
                    form.save()
                    messages.success(request, "Availability slot updated successfully.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=availability.service.id)

            except Exception as e:
                messages.error(request, f"Error updating availability slot: {str(e)}")
    else:
        form = ServiceAvailabilityForm(instance=availability)

    context = {
        'form': form,
        'availability': availability,
        'service': availability.service,
    }

    return render(request, 'booking_cart_app/provider/edit_availability.html', context)


@login_required
@require_http_methods(["POST"])
def provider_delete_availability_view(request, availability_id):
    """
    Delete an availability slot.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the availability slot
    provider_profile = request.user.service_provider_profile
    availability = get_object_or_404(
        ServiceAvailability,
        id=availability_id,
        service__venue__service_provider=provider_profile
    )

    service_id = availability.service.id

    try:
        with transaction.atomic():
            # Check if there are any bookings for this slot
            if availability.current_bookings > 0:
                messages.error(request, "Cannot delete availability slot with existing bookings.")
                return redirect('booking_cart_app:provider_service_availability', service_id=service_id)

            availability.delete()
            messages.success(request, "Availability slot deleted successfully.")

    except Exception as e:
        messages.error(request, f"Error deleting availability slot: {str(e)}")

    return redirect('booking_cart_app:provider_service_availability', service_id=service_id)


@login_required
def provider_booking_list_view(request):
    """
    Display all bookings for the service provider's venues.
    Shows pending, confirmed, and recent bookings with filtering options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get filter parameters
    status_filter = request.GET.get('status', 'all')
    date_filter = request.GET.get('date_filter', 'upcoming')

    # Get all bookings for the provider's venues
    provider_profile = request.user.service_provider_profile
    bookings = (
        Booking.objects.filter(venue__service_provider=provider_profile)
        .prefetch_related('items__service', 'customer', 'venue')
    )

    # Apply status filter
    if status_filter != 'all':
        bookings = bookings.filter(status=status_filter)

    # Apply date filter
    today = timezone.now().date()
    if date_filter == 'upcoming':
        bookings = bookings.filter(
            items__scheduled_date__gte=today
        ).distinct()
    elif date_filter == 'past':
        bookings = bookings.filter(
            items__scheduled_date__lt=today
        ).distinct()
    elif date_filter == 'today':
        bookings = bookings.filter(
            items__scheduled_date=today
        ).distinct()

    # Order by booking date (newest first)
    bookings = bookings.order_by('-booking_date')

    # Separate bookings by status for quick stats
    pending_count = bookings.filter(status=Booking.PENDING).count()
    confirmed_count = bookings.filter(status=Booking.CONFIRMED).count()
    total_count = bookings.count()

    # Pagination
    paginator = Paginator(bookings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'bookings': page_obj,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'status_choices': Booking.STATUS_CHOICES,
        'pending_count': pending_count,
        'confirmed_count': confirmed_count,
        'total_count': total_count,
    }

    return render(request, 'booking_cart_app/provider/booking_list.html', context)


@login_required
def provider_booking_detail_view(request, booking_slug):
    """
    Display detailed information about a specific booking for the provider.
    Shows customer information, booking items, and action options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Get all booking items
    booking_items = booking.items.all().order_by('scheduled_date', 'scheduled_time')

    # Check if booking can be accepted or declined
    can_accept = booking.status == Booking.PENDING
    can_decline = booking.status == Booking.PENDING

    context = {
        'booking': booking,
        'booking_items': booking_items,
        'can_accept': can_accept,
        'can_decline': can_decline,
    }

    return render(request, 'booking_cart_app/provider/booking_detail.html', context)


@login_required
def provider_accept_booking_view(request, booking_slug):
    """
    Accept a pending booking.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Check if booking can be accepted
    if booking.status != Booking.PENDING:
        messages.error(request, "Only pending bookings can be accepted.")
        return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingActionForm(request.POST)
        if form.is_valid():
            action_reason = form.cleaned_data.get('action_reason', '')

            try:
                with transaction.atomic():
                    # Accept the booking
                    booking.confirm_booking()

                    # Update availability slots
                    for item in booking.items.all():
                        try:
                            availability = ServiceAvailability.objects.get(
                                service=item.service,
                                available_date=item.scheduled_date,
                                start_time=item.scheduled_time
                            )
                            availability.book_slot()
                        except ServiceAvailability.DoesNotExist:
                            pass  # Availability record doesn't exist, skip

                    # Log booking acceptance
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_accepted',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'customer_email': booking.customer.email,
                                'total_price': str(booking.total_price),
                                'action_reason': action_reason
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        from notifications_app.utils import notify_booking_status_changed
                        try:
                            notify_booking_status_changed(booking, 'pending')
                        except ImportError:
                            pass  # notifications_app not available

                    messages.success(request, "Booking accepted successfully.")
                    return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_accept_error',
                        f"Error accepting booking {booking.booking_id}: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error accepting booking: {str(e)}")
                return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)
    else:
        form = BookingActionForm()

    context = {
        'form': form,
        'booking': booking,
        'action': 'accept',
    }

    return render(request, 'booking_cart_app/provider/booking_action.html', context)


@login_required
def provider_decline_booking_view(request, booking_slug):
    """
    Decline a pending booking.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Check if booking can be declined
    if booking.status != Booking.PENDING:
        messages.error(request, "Only pending bookings can be declined.")
        return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingActionForm(request.POST)
        if form.is_valid():
            action_reason = form.cleaned_data.get('action_reason', '')

            try:
                with transaction.atomic():
                    # Decline the booking
                    booking.decline_booking(action_reason)

                    # Restore service availability for declined booking
                    for item in booking.items.all():
                        try:
                            availability = ServiceAvailability.objects.get(
                                service=item.service,
                                available_date=item.scheduled_date,
                                start_time=item.scheduled_time
                            )
                            # Restore availability for the quantity that was booked
                            for _ in range(item.quantity):
                                availability.cancel_booking()
                        except ServiceAvailability.DoesNotExist:
                            # If availability doesn't exist, skip
                            pass
                        except Exception as e:
                            # Log error but don't fail the decline
                            if LOGGING_ENABLED:
                                log_error(
                                    'booking_cart_app',
                                    'availability_restore_error',
                                    f"Error restoring availability after decline: {str(e)}",
                                    user=request.user,
                                    request=request,
                                    exception=e,
                                    details={
                                        'booking_id': str(booking.booking_id),
                                        'service_id': item.service.id,
                                        'scheduled_date': str(item.scheduled_date),
                                        'scheduled_time': str(item.scheduled_time)
                                    }
                                )

                    # Log booking decline
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_declined',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'customer_email': booking.customer.email,
                                'total_price': str(booking.total_price),
                                'action_reason': action_reason
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        from notifications_app.utils import notify_booking_status_changed
                        try:
                            notify_booking_status_changed(booking, 'pending')
                        except ImportError:
                            pass  # notifications_app not available

                    messages.success(request, "Booking declined successfully.")
                    return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_decline_error',
                        f"Error declining booking {booking.booking_id}: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error declining booking: {str(e)}")
                return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)
    else:
        form = BookingActionForm()

    context = {
        'form': form,
        'booking': booking,
        'action': 'decline',
    }

    return render(request, 'booking_cart_app/provider/booking_action.html', context)


@login_required
def provider_booking_dashboard_view(request):
    """
    Enhanced provider booking dashboard with analytics and today's bookings.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    provider_profile = request.user.service_provider_profile
    today = timezone.now().date()
    
    # Get all bookings for the provider's venues
    all_bookings = (
        Booking.objects.filter(venue__service_provider=provider_profile)
        .prefetch_related('items__service', 'customer', 'venue')
    )

    # Today's bookings
    todays_bookings = all_bookings.filter(
        items__scheduled_date=today
    ).distinct().order_by('items__scheduled_time')

    # Upcoming bookings (next 7 days)
    upcoming_bookings = all_bookings.filter(
        items__scheduled_date__gt=today,
        items__scheduled_date__lte=today + timedelta(days=7),
        status__in=[Booking.CONFIRMED, Booking.PENDING]
    ).distinct().order_by('items__scheduled_date', 'items__scheduled_time')

    # Status analytics
    status_counts = {
        'total': all_bookings.count(),
        'pending': all_bookings.filter(status=Booking.PENDING).count(),
        'confirmed': all_bookings.filter(status=Booking.CONFIRMED).count(),
        'completed': all_bookings.filter(status=Booking.COMPLETED).count(),
        'todays_total': todays_bookings.count(),
    }

    # Revenue analytics (this month)
    current_month = timezone.now().replace(day=1).date()
    monthly_revenue = all_bookings.filter(
        booking_date__gte=current_month,
        status__in=[Booking.CONFIRMED, Booking.COMPLETED]
    ).aggregate(total=models.Sum('total_price'))['total'] or 0

    # Recent status changes
    recent_status_changes = BookingStatusHistory.objects.filter(
        booking__venue__service_provider=provider_profile,
        changed_at__gte=timezone.now() - timedelta(days=7)
    ).select_related('booking').order_by('-changed_at')[:5]

    # Check for concurrent booking violations
    concurrent_bookings_by_time = {}
    for booking in todays_bookings:
        for item in booking.items.filter(scheduled_date=today):
            time_key = f"{item.scheduled_date}_{item.scheduled_time}"
            if time_key not in concurrent_bookings_by_time:
                concurrent_bookings_by_time[time_key] = []
            concurrent_bookings_by_time[time_key].append(booking)
    
    # Find time slots with more than 10 bookings (violation)
    booking_violations = {
        time_key: bookings for time_key, bookings in concurrent_bookings_by_time.items()
        if len(bookings) > 10
    }

    context = {
        'todays_bookings': todays_bookings[:10],  # Limit for dashboard view
        'upcoming_bookings': upcoming_bookings[:5],
        'status_counts': status_counts,
        'monthly_revenue': monthly_revenue,
        'recent_status_changes': recent_status_changes,
        'booking_violations': booking_violations,
        'today': today,
    }

    return render(request, 'booking_cart_app/provider/booking_dashboard.html', context)


@login_required
def provider_todays_bookings_view(request):
    """
    Detailed view of today's confirmed bookings for the service provider.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    provider_profile = request.user.service_provider_profile
    today = timezone.now().date()
    
    # Get today's bookings
    todays_bookings = (
        Booking.objects.filter(
            venue__service_provider=provider_profile,
            items__scheduled_date=today
        )
        .prefetch_related('items__service', 'customer', 'venue')
        .distinct()
        .order_by('items__scheduled_time')
    )

    # Separate by status
    confirmed_bookings = todays_bookings.filter(status=Booking.CONFIRMED)
    pending_bookings = todays_bookings.filter(status=Booking.PENDING)
    completed_bookings = todays_bookings.filter(status=Booking.COMPLETED)

    context = {
        'todays_bookings': todays_bookings,
        'confirmed_bookings': confirmed_bookings,
        'pending_bookings': pending_bookings,
        'completed_bookings': completed_bookings,
        'today': today,
    }

    return render(request, 'booking_cart_app/provider/todays_bookings.html', context)


@login_required
def provider_mark_completed_view(request, booking_slug):
    """
    Mark a booking as completed.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Check if booking can be marked as completed
    if booking.status != Booking.CONFIRMED:
        messages.error(request, "Only confirmed bookings can be marked as completed.")
        return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingCompletionForm(request.POST)
        if form.is_valid():
            completion_notes = form.cleaned_data.get('completion_notes', '')

            try:
                with transaction.atomic():
                    # Mark booking as completed
                    old_status = booking.status
                    booking.status = Booking.COMPLETED
                    booking.save()

                    # Create status history
                    BookingStatusHistory.objects.create(
                        booking=booking,
                        old_status=old_status,
                        new_status=Booking.COMPLETED,
                    )

                    # Log completion
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_completed',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'customer_email': booking.customer.email,
                                'total_price': str(booking.total_price),
                                'completion_notes': completion_notes
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        from notifications_app.utils import notify_booking_status_changed
                        try:
                            notify_booking_status_changed(booking, old_status)
                        except ImportError:
                            pass  # notifications_app not available

                    messages.success(request, "Booking marked as completed successfully.")
                    return redirect('booking_cart_app:provider_todays_bookings')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_completion_error',
                        f"Error marking booking as completed: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error marking booking as completed: {str(e)}")
    else:
        form = BookingCompletionForm()

    context = {
        'form': form,
        'booking': booking,
    }

    return render(request, 'booking_cart_app/provider/mark_completed.html', context)


@login_required
def provider_daily_schedule_view(request):
    """
    Enhanced daily schedule view with drag-and-drop booking management.
    Calendar-style interface for better visualization and management.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    provider_profile = request.user.service_provider_profile
    
    # Get target date (default to today)
    date_str = request.GET.get('date')
    if date_str:
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            target_date = timezone.now().date()
    else:
        target_date = timezone.now().date()

    # Get all bookings for the target date
    daily_bookings = (
        Booking.objects.filter(
            venue__service_provider=provider_profile,
            items__scheduled_date=target_date
        )
        .prefetch_related('items__service', 'customer', 'venue')
        .distinct()
        .order_by('items__scheduled_time')
    )

    # Get all availability slots for the date
    availability_slots = ServiceAvailability.objects.filter(
        service__venue__service_provider=provider_profile,
        available_date=target_date
    ).select_related('service', 'service__venue').order_by('start_time')

    # Create hourly schedule grid (6 AM to 10 PM)
    schedule_hours = []
    for hour in range(6, 23):  # 6 AM to 10 PM
        hour_data = {
            'hour': hour,
            'hour_display': f"{hour:02d}:00",
            'bookings': [],
            'availability': [],
            'conflicts': []
        }
        
        # Add bookings for this hour
        for booking in daily_bookings:
            for item in booking.items.filter(scheduled_date=target_date):
                if item.scheduled_time.hour == hour:
                    hour_data['bookings'].append({
                        'booking': booking,
                        'item': item,
                        'duration_minutes': item.duration_minutes,
                    })
        
        # Add availability slots for this hour
        for slot in availability_slots:
            if slot.start_time.hour <= hour < slot.end_time.hour:
                hour_data['availability'].append(slot)
        
        # Check for conflicts (more than 10 bookings at same time)
        time_conflicts = {}
        for booking_data in hour_data['bookings']:
            time_key = booking_data['item'].scheduled_time
            if time_key not in time_conflicts:
                time_conflicts[time_key] = []
            time_conflicts[time_key].append(booking_data)
        
        for time_key, bookings_at_time in time_conflicts.items():
            if len(bookings_at_time) > 10:
                hour_data['conflicts'].append({
                    'time': time_key,
                    'count': len(bookings_at_time),
                    'bookings': bookings_at_time
                })
        
        schedule_hours.append(hour_data)

    # Statistics for the day
    day_stats = {
        'total_bookings': daily_bookings.count(),
        'confirmed_bookings': daily_bookings.filter(status=Booking.CONFIRMED).count(),
        'pending_bookings': daily_bookings.filter(status=Booking.PENDING).count(),
        'completed_bookings': daily_bookings.filter(status=Booking.COMPLETED).count(),
        'total_revenue': daily_bookings.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED]
        ).aggregate(total=models.Sum('total_price'))['total'] or 0,
        'available_slots': availability_slots.filter(is_available=True).count(),
        'booked_slots': availability_slots.filter(current_bookings__gt=0).count(),
    }

    # Get previous and next dates for navigation
    prev_date = target_date - timedelta(days=1)
    next_date = target_date + timedelta(days=1)

    context = {
        'target_date': target_date,
        'prev_date': prev_date,
        'next_date': next_date,
        'schedule_hours': schedule_hours,
        'daily_bookings': daily_bookings,
        'availability_slots': availability_slots,
        'day_stats': day_stats,
        'today': timezone.now().date(),
    }

    return render(request, 'booking_cart_app/provider/daily_schedule.html', context)


@login_required 
def provider_notification_center_view(request):
    """
    Centralized notification management with priority levels and filtering.
    """
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    try:
        from notifications_app.models import Notification
        
        # Get filter parameters
        priority_filter = request.GET.get('priority', 'all')
        status_filter = request.GET.get('status', 'unread')
        category_filter = request.GET.get('category', 'all')

        # Get notifications for the provider
        notifications = Notification.objects.filter(
            recipient=request.user
        ).order_by('-created_at')

        # Apply filters
        if priority_filter != 'all':
            notifications = notifications.filter(priority=priority_filter)
        
        if status_filter == 'unread':
            notifications = notifications.filter(is_read=False)
        elif status_filter == 'read':
            notifications = notifications.filter(is_read=True)
        
        if category_filter != 'all':
            notifications = notifications.filter(category=category_filter)

        # Get notification statistics
        notification_stats = {
            'total': Notification.objects.filter(recipient=request.user).count(),
            'unread': Notification.objects.filter(recipient=request.user, is_read=False).count(),
            'high_priority': Notification.objects.filter(
                recipient=request.user, priority='high', is_read=False
            ).count(),
            'booking_related': Notification.objects.filter(
                recipient=request.user, category='booking'
            ).count(),
        }

        # Group notifications by date
        notifications_by_date = {}
        for notification in notifications[:50]:  # Limit for performance
            date_key = notification.created_at.date()
            if date_key not in notifications_by_date:
                notifications_by_date[date_key] = []
            notifications_by_date[date_key].append(notification)

        context = {
            'notifications': notifications[:20],  # Paginate later
            'notifications_by_date': notifications_by_date,
            'notification_stats': notification_stats,
            'priority_filter': priority_filter,
            'status_filter': status_filter,
            'category_filter': category_filter,
        }

    except ImportError:
        # If notifications app is not available, show a placeholder
        context = {
            'notifications': [],
            'notifications_by_date': {},
            'notification_stats': {'total': 0, 'unread': 0, 'high_priority': 0, 'booking_related': 0},
            'notifications_disabled': True,
        }

    return render(request, 'booking_cart_app/provider/notification_center.html', context)


@login_required
def provider_customer_profiles_view(request):
    """
    Quick access to customer history and preferences for better service.
    """
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    provider_profile = request.user.service_provider_profile
    
    # Get search parameter
    search = request.GET.get('search', '')
    
    # Get all customers who have bookings with this provider
    customer_bookings = (
        Booking.objects.filter(venue__service_provider=provider_profile)
        .select_related('customer')
        .prefetch_related('items__service')
        .order_by('customer', '-booking_date')
    )

    if search:
        customer_bookings = customer_bookings.filter(
            Q(customer__email__icontains=search) |
            Q(customer__first_name__icontains=search) |
            Q(customer__last_name__icontains=search)
        )

    # Group by customer and get their stats
    customer_profiles = {}
    for booking in customer_bookings:
        customer = booking.customer
        if customer.id not in customer_profiles:
            customer_profiles[customer.id] = {
                'customer': customer,
                'total_bookings': 0,
                'total_spent': 0,
                'last_booking_date': None,
                'favorite_services': {},
                'booking_history': [],
                'avg_booking_value': 0,
                'loyalty_score': 0,
            }
        
        profile = customer_profiles[customer.id]
        profile['total_bookings'] += 1
        profile['total_spent'] += booking.total_price
        
        if not profile['last_booking_date'] or booking.booking_date.date() > profile['last_booking_date']:
            profile['last_booking_date'] = booking.booking_date.date()
        
        profile['booking_history'].append(booking)
        
        # Track favorite services
        for item in booking.items.all():
            service_name = item.service_title
            if service_name not in profile['favorite_services']:
                profile['favorite_services'][service_name] = 0
            profile['favorite_services'][service_name] += 1

    # Calculate additional metrics
    for customer_id, profile in customer_profiles.items():
        if profile['total_bookings'] > 0:
            profile['avg_booking_value'] = profile['total_spent'] / profile['total_bookings']
            
            # Simple loyalty score based on bookings and recency
            recency_bonus = 0
            if profile['last_booking_date']:
                days_since_last = (timezone.now().date() - profile['last_booking_date']).days
                if days_since_last < 30:
                    recency_bonus = 2
                elif days_since_last < 90:
                    recency_bonus = 1
            
            profile['loyalty_score'] = min(10, profile['total_bookings'] + recency_bonus)
            
            # Get top favorite service
            if profile['favorite_services']:
                profile['top_service'] = max(
                    profile['favorite_services'].items(), 
                    key=lambda x: x[1]
                )[0]
            else:
                profile['top_service'] = 'None'

    # Sort by loyalty score and recent activity
    sorted_profiles = sorted(
        customer_profiles.values(),
        key=lambda x: (-x['loyalty_score'], x['last_booking_date'] or timezone.now().date()),
        reverse=False
    )

    # Pagination
    paginator = Paginator(sorted_profiles, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'customer_profiles': page_obj,
        'search': search,
        'total_customers': len(customer_profiles),
    }

    return render(request, 'booking_cart_app/provider/customer_profiles.html', context)


@login_required
@require_http_methods(["POST"])
def provider_bulk_booking_action_view(request):
    """
    Bulk actions for managing multiple bookings at once.
    """
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    action = request.POST.get('action')
    booking_ids = request.POST.getlist('booking_ids')
    
    if not action or not booking_ids:
        return JsonResponse({'error': 'Missing required parameters'}, status=400)

    provider_profile = request.user.service_provider_profile
    
    # Get bookings that belong to this provider
    bookings = Booking.objects.filter(
        id__in=booking_ids,
        venue__service_provider=provider_profile
    )

    results = {'success': [], 'errors': []}
    
    try:
        with transaction.atomic():
            for booking in bookings:
                try:
                    if action == 'accept' and booking.status == Booking.PENDING:
                        booking.confirm_booking()
                        results['success'].append(f"Accepted booking {booking.friendly_id}")
                        
                        # Send notification
                        if NOTIFICATIONS_ENABLED:
                            try:
                                from notifications_app.utils import notify_booking_status_changed
                                notify_booking_status_changed(booking, Booking.PENDING)
                            except ImportError:
                                pass
                                
                    elif action == 'decline' and booking.status == Booking.PENDING:
                        decline_reason = request.POST.get('decline_reason', 'Bulk decline action')
                        booking.decline_booking(decline_reason)
                        results['success'].append(f"Declined booking {booking.friendly_id}")
                        
                        # Send notification
                        if NOTIFICATIONS_ENABLED:
                            try:
                                from notifications_app.utils import notify_booking_status_changed
                                notify_booking_status_changed(booking, Booking.PENDING)
                            except ImportError:
                                pass
                                
                    elif action == 'complete' and booking.status == Booking.CONFIRMED:
                        old_status = booking.status
                        booking.status = Booking.COMPLETED
                        booking.save()
                        
                        # Create status history
                        BookingStatusHistory.objects.create(
                            booking=booking,
                            old_status=old_status,
                            new_status=Booking.COMPLETED,
                        )
                        
                        results['success'].append(f"Completed booking {booking.friendly_id}")
                        
                        # Send notification
                        if NOTIFICATIONS_ENABLED:
                            try:
                                from notifications_app.utils import notify_booking_status_changed
                                notify_booking_status_changed(booking, old_status)
                            except ImportError:
                                pass
                    else:
                        results['errors'].append(f"Cannot {action} booking {booking.friendly_id} (current status: {booking.get_status_display()})")
                        
                except Exception as e:
                    results['errors'].append(f"Error processing booking {booking.friendly_id}: {str(e)}")

            # Log bulk action
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'provider_bulk_booking_action',
                    user=request.user,
                    request=request,
                    details={
                        'action': action,
                        'booking_count': len(booking_ids),
                        'success_count': len(results['success']),
                        'error_count': len(results['errors'])
                    }
                )

    except Exception as e:
        return JsonResponse({'error': f'Bulk action failed: {str(e)}'}, status=500)

    return JsonResponse(results)


