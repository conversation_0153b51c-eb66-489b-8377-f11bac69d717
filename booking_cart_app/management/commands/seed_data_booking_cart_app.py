"""
Management command to seed booking_cart_app with realistic test data.
Creates bookings, booking items, carts, cart items, and service availability.
"""

import random
from decimal import Decimal
from datetime import timedelta, time, date

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model

from booking_cart_app.models import (
    Booking, BookingItem, Cart, CartItem, ServiceAvailability
)
from venues_app.models import Venue, Service
from accounts_app.models import CustomerProfile

User = get_user_model()


class Command(BaseCommand):
    """Seed booking_cart_app with realistic test data."""
    
    help = 'Seed booking_cart_app with bookings, carts, and availability'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing booking and cart data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting booking_cart_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_service_availability()
            self.create_bookings()
            self.create_carts()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Booking cart app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing booking and cart data."""
        self.stdout.write('🧹 Clearing existing booking and cart data...')

        try:
            BookingItem.objects.all().delete()
            Booking.objects.all().delete()
            CartItem.objects.all().delete()
            Cart.objects.all().delete()
            ServiceAvailability.objects.all().delete()
            self.stdout.write('   ✅ Existing booking and cart data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )

    def create_service_availability(self):
        """Create service availability slots."""
        self.stdout.write('📅 Creating service availability slots...')
        
        services = list(Service.objects.filter(is_active=True, venue__approval_status='approved'))
        
        if not services:
            self.stdout.write('   ⚠️ No active services found, skipping availability creation')
            return
        
        # Create availability for next 30 days
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=30)
        
        time_slots = [
            (time(9, 0), time(10, 30)),
            (time(10, 30), time(12, 0)),
            (time(13, 0), time(14, 30)),
            (time(14, 30), time(16, 0)),
            (time(16, 0), time(17, 30)),
            (time(17, 30), time(19, 0)),
        ]
        
        created_count = 0
        for service in services:
            # Create availability for random days
            current_date = start_date
            while current_date <= end_date:
                # Skip some days randomly (not all services available every day)
                if random.random() < 0.7:  # 70% chance of availability
                    # Create 2-4 time slots per day
                    num_slots = random.randint(2, 4)
                    selected_slots = random.sample(time_slots, num_slots)
                    
                    for start_time, end_time in selected_slots:
                        availability, created = ServiceAvailability.objects.get_or_create(
                            service=service,
                            available_date=current_date,
                            start_time=start_time,
                            defaults={
                                'end_time': end_time,
                                'max_bookings': random.randint(1, 3),
                                'current_bookings': 0,
                                'is_available': True,
                            }
                        )
                        if created:
                            created_count += 1
                
                current_date += timedelta(days=1)
        
        self.stdout.write(f'   ✅ Created {created_count} availability slots')

    def create_bookings(self):
        """Create realistic bookings with different statuses."""
        self.stdout.write('📋 Creating bookings...')
        
        customers = list(User.objects.filter(role=User.CUSTOMER))
        venues = list(Venue.objects.filter(approval_status='approved'))
        services = list(Service.objects.filter(is_active=True, venue__approval_status='approved'))
        
        if not customers or not venues or not services:
            self.stdout.write('   ⚠️ Missing required data, skipping booking creation')
            return
        
        booking_statuses = [
            (Booking.PENDING, 0.15),      # 15% pending
            (Booking.CONFIRMED, 0.50),    # 50% confirmed
            (Booking.COMPLETED, 0.25),    # 25% completed
            (Booking.CANCELLED, 0.07),    # 7% cancelled
            (Booking.DECLINED, 0.03),     # 3% declined
        ]
        
        # Create 30-50 bookings
        num_bookings = random.randint(30, 50)
        
        for i in range(num_bookings):
            customer = random.choice(customers)
            venue = random.choice(venues)
            venue_services = [s for s in services if s.venue == venue]
            
            if not venue_services:
                continue
            
            # Determine booking status
            status_rand = random.random()
            cumulative_prob = 0
            booking_status = Booking.PENDING
            
            for status, prob in booking_statuses:
                cumulative_prob += prob
                if status_rand <= cumulative_prob:
                    booking_status = status
                    break
            
            # Calculate booking date based on status
            if booking_status == Booking.COMPLETED:
                booking_date = timezone.now() - timedelta(days=random.randint(7, 60))
            elif booking_status in [Booking.CANCELLED, Booking.DECLINED]:
                booking_date = timezone.now() - timedelta(days=random.randint(1, 30))
            else:
                booking_date = timezone.now() - timedelta(days=random.randint(0, 14))
            
            # Create booking
            booking = Booking.objects.create(
                customer=customer,
                venue=venue,
                status=booking_status,
                total_price=Decimal('0.00'),  # Will be updated after adding items
                booking_date=booking_date,
                notes=random.choice([
                    '', 'First time visit', 'Special occasion', 'Gift for someone',
                    'Relaxation needed', 'Regular appointment', 'Recommended by friend'
                ]),
            )
            
            # Add cancellation/dispute reasons if applicable
            if booking_status == Booking.CANCELLED:
                booking.cancellation_reason = random.choice([
                    'Schedule conflict', 'Emergency came up', 'Changed mind',
                    'Found better deal elsewhere', 'Personal reasons'
                ])
                booking.save()
            elif booking_status == Booking.DECLINED:
                booking.cancellation_reason = random.choice([
                    'Fully booked', 'Service not available', 'Staff unavailable',
                    'Venue closed for maintenance'
                ])
                booking.save()
            
            # Create 1-3 booking items
            num_items = random.randint(1, 3)
            selected_services = random.sample(venue_services, min(num_items, len(venue_services)))
            
            total_price = Decimal('0.00')
            
            for service in selected_services:
                # Calculate scheduled date based on booking status
                if booking_status == Booking.COMPLETED:
                    scheduled_date = booking_date.date() + timedelta(days=random.randint(1, 7))
                else:
                    scheduled_date = timezone.now().date() + timedelta(days=random.randint(1, 30))
                
                scheduled_time = random.choice([
                    time(9, 0), time(10, 30), time(13, 0), 
                    time(14, 30), time(16, 0), time(17, 30)
                ])
                
                quantity = random.randint(1, 2)
                service_price = service.price_min
                
                BookingItem.objects.create(
                    booking=booking,
                    service=service,
                    service_title=service.service_title,
                    service_price=service_price,
                    quantity=quantity,
                    scheduled_date=scheduled_date,
                    scheduled_time=scheduled_time,
                    duration_minutes=service.duration_minutes,
                )
                
                total_price += service_price * quantity
            
            # Update booking total price
            booking.total_price = total_price
            booking.save()
            
            status_emoji = {
                Booking.PENDING: '⏳',
                Booking.CONFIRMED: '✅',
                Booking.COMPLETED: '🎉',
                Booking.CANCELLED: '❌',
                Booking.DECLINED: '🚫',
            }
            
            self.stdout.write(
                f'   {status_emoji.get(booking_status, "📋")} Created {booking_status} booking: '
                f'{booking.slug} for {customer.email} at {venue.venue_name}'
            )

    def create_carts(self):
        """Create active shopping carts for some customers."""
        self.stdout.write('🛒 Creating shopping carts...')
        
        customers = list(User.objects.filter(role=User.CUSTOMER))
        services = list(Service.objects.filter(is_active=True, venue__approval_status='approved'))
        
        if not customers or not services:
            self.stdout.write('   ⚠️ Missing required data, skipping cart creation')
            return
        
        # Create carts for 20-30% of customers
        num_carts = max(1, len(customers) // 4)
        selected_customers = random.sample(customers, min(num_carts, len(customers)))
        
        for customer in selected_customers:
            # Create cart
            cart = Cart.objects.create(
                customer=customer,
                expires_at=timezone.now() + timedelta(hours=24),
            )
            
            # Add 1-3 items to cart
            num_items = random.randint(1, 3)
            selected_services = random.sample(services, min(num_items, len(services)))
            
            for service in selected_services:
                selected_date = timezone.now().date() + timedelta(days=random.randint(1, 14))
                selected_time = random.choice([
                    time(9, 0), time(10, 30), time(13, 0), 
                    time(14, 30), time(16, 0), time(17, 30)
                ])
                
                CartItem.objects.create(
                    cart=cart,
                    service=service,
                    selected_date=selected_date,
                    selected_time_slot=selected_time,
                    quantity=random.randint(1, 2),
                    price_per_item=service.price_min,
                )
            
            self.stdout.write(f'   🛒 Created cart for {customer.email} with {cart.items.count()} items')
