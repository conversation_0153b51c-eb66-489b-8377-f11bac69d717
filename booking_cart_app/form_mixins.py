from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class AvailabilityValidationMixin:
    """Mixin providing shared date/time validation for availability forms."""
    def validate_future_date(self, date, field_name='available_date'):
        """Validate that date is in the future and add field-specific error."""
        if date and date <= timezone.now().date():
            if hasattr(self, 'add_error'):
                self.add_error(field_name, _('Date must be in the future.'))
            else:
                raise ValidationError(_('Date must be in the future.'))

    def validate_start_end(self, start, end, field_name='end_time'):
        """Validate that end time is after start time and add field-specific error."""
        if start and end and end <= start:
            if hasattr(self, 'add_error'):
                self.add_error(field_name, _('End time must be after start time.'))
            else:
                raise ValidationError(_('End time must be after start time.'))
