{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block title %}Notification Center{% endblock %}

{% block extra_css %}
<style>
    .notification-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .notification-header {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
    }
    
    .stat-card.unread {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .stat-card.high-priority {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .stat-card.booking {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 4px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .filter-tabs {
        display: flex;
        gap: 8px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .filter-tab {
        background: white;
        border: 2px solid #e5e7eb;
        color: #6b7280;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        font-size: 0.9rem;
    }
    
    .filter-tab:hover {
        border-color: #4f46e5;
        color: #4f46e5;
        text-decoration: none;
    }
    
    .filter-tab.active {
        background: #4f46e5;
        border-color: #4f46e5;
        color: white;
    }
    
    .notifications-list {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }
    
    .notification-group {
        border-bottom: 1px solid #e5e7eb;
    }
    
    .notification-group:last-child {
        border-bottom: none;
    }
    
    .date-header {
        background: #f9fafb;
        padding: 12px 20px;
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .notification-item {
        padding: 16px 20px;
        border-bottom: 1px solid #f3f4f6;
        transition: all 0.2s;
        cursor: pointer;
        position: relative;
    }
    
    .notification-item:last-child {
        border-bottom: none;
    }
    
    .notification-item:hover {
        background: #f9fafb;
    }
    
    .notification-item.unread {
        background: #fef7ff;
        border-left: 4px solid #8b5cf6;
    }
    
    .notification-item.high-priority {
        border-left: 4px solid #ef4444;
    }
    
    .notification-item.high-priority .priority-badge {
        background: #fee2e2;
        color: #b91c1c;
    }
    
    .notification-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }
    
    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        flex-shrink: 0;
    }
    
    .notification-icon.booking {
        background: #dbeafe;
        color: #3b82f6;
    }
    
    .notification-icon.system {
        background: #f3e8ff;
        color: #8b5cf6;
    }
    
    .notification-icon.payment {
        background: #d1fae5;
        color: #10b981;
    }
    
    .notification-icon.alert {
        background: #fee2e2;
        color: #ef4444;
    }
    
    .notification-body {
        flex: 1;
        min-width: 0;
    }
    
    .notification-title {
        font-weight: 600;
        color: #111827;
        margin-bottom: 4px;
        line-height: 1.4;
    }
    
    .notification-message {
        color: #6b7280;
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 8px;
    }
    
    .notification-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 0.8rem;
        color: #9ca3af;
    }
    
    .priority-badge {
        background: #fef3c7;
        color: #d97706;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .notification-actions {
        position: absolute;
        top: 16px;
        right: 20px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.2s;
    }
    
    .notification-item:hover .notification-actions {
        opacity: 1;
    }
    
    .action-btn {
        background: none;
        border: 1px solid #d1d5db;
        color: #6b7280;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.75rem;
        transition: all 0.2s;
    }
    
    .action-btn:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }
    
    .action-btn.mark-read {
        color: #059669;
        border-color: #059669;
    }
    
    .action-btn.mark-read:hover {
        background: #ecfdf5;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6b7280;
    }
    
    .empty-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    .bulk-actions-bar {
        background: white;
        border-radius: 8px;
        padding: 12px 20px;
        margin-bottom: 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        display: none;
        align-items: center;
        justify-content: space-between;
    }
    
    .bulk-actions-bar.show {
        display: flex;
    }
    
    .bulk-action-btn {
        background: #4f46e5;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        margin: 0 4px;
        font-size: 0.85rem;
        transition: all 0.2s;
    }
    
    .bulk-action-btn:hover {
        background: #4338ca;
    }
    
    .bulk-action-btn.delete {
        background: #ef4444;
    }
    
    .bulk-action-btn.delete:hover {
        background: #dc2626;
    }
    
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .filter-tabs {
            justify-content: center;
        }
        
        .notification-content {
            flex-direction: column;
            align-items: stretch;
        }
        
        .notification-actions {
            position: static;
            opacity: 1;
            margin-top: 8px;
            justify-content: flex-end;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="notification-container">
    <div class="container-fluid">
        <!-- Notification Header -->
        <div class="notification-header">
            <h2 class="mb-3">
                <i class="fas fa-bell"></i> Notification Center
            </h2>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ notification_stats.total }}</div>
                    <div class="stat-label">Total Notifications</div>
                </div>
                <div class="stat-card unread">
                    <div class="stat-value">{{ notification_stats.unread }}</div>
                    <div class="stat-label">Unread</div>
                </div>
                <div class="stat-card high-priority">
                    <div class="stat-value">{{ notification_stats.high_priority }}</div>
                    <div class="stat-label">High Priority</div>
                </div>
                <div class="stat-card booking">
                    <div class="stat-value">{{ notification_stats.booking_related }}</div>
                    <div class="stat-label">Booking Related</div>
                </div>
            </div>
            
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <a href="?status=unread&priority={{ priority_filter }}&category={{ category_filter }}" 
                   class="filter-tab {% if status_filter == 'unread' %}active{% endif %}">
                    Unread ({{ notification_stats.unread }})
                </a>
                <a href="?status=read&priority={{ priority_filter }}&category={{ category_filter }}" 
                   class="filter-tab {% if status_filter == 'read' %}active{% endif %}">
                    Read
                </a>
                <a href="?status=all&priority={{ priority_filter }}&category={{ category_filter }}" 
                   class="filter-tab {% if status_filter == 'all' %}active{% endif %}">
                    All
                </a>
                <a href="?status={{ status_filter }}&priority=high&category={{ category_filter }}" 
                   class="filter-tab {% if priority_filter == 'high' %}active{% endif %}">
                    High Priority
                </a>
                <a href="?status={{ status_filter }}&priority={{ priority_filter }}&category=booking" 
                   class="filter-tab {% if category_filter == 'booking' %}active{% endif %}">
                    Bookings
                </a>
                <a href="?status={{ status_filter }}&priority={{ priority_filter }}&category=system" 
                   class="filter-tab {% if category_filter == 'system' %}active{% endif %}">
                    System
                </a>
            </div>
            
            <!-- Bulk Actions -->
            <div class="d-flex gap-2 align-items-center">
                <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i> Mark All Read
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="clearOldNotifications()">
                    <i class="fas fa-trash"></i> Clear Old
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="refreshNotifications()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
        
        <!-- Bulk Actions Bar -->
        <div class="bulk-actions-bar" id="bulkActionsBar">
            <div>
                <span id="selectedCount">0</span> notifications selected
            </div>
            <div>
                <button class="bulk-action-btn" onclick="bulkMarkAsRead()">
                    <i class="fas fa-check"></i> Mark Read
                </button>
                <button class="bulk-action-btn delete" onclick="bulkDelete()">
                    <i class="fas fa-trash"></i> Delete
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                    Clear
                </button>
            </div>
        </div>
        
        <!-- Notifications List -->
        <div class="notifications-list">
            {% if notifications_disabled %}
            <div class="empty-state">
                <i class="fas fa-bell-slash empty-icon"></i>
                <h5>Notifications Disabled</h5>
                <p>The notification system is not available at the moment.</p>
            </div>
            {% elif not notifications %}
            <div class="empty-state">
                <i class="fas fa-inbox empty-icon"></i>
                <h5>No Notifications</h5>
                <p>You're all caught up! No new notifications to show.</p>
            </div>
            {% else %}
                {% for date, date_notifications in notifications_by_date.items %}
                <div class="notification-group">
                    <div class="date-header">
                        {% if date == today %}
                            Today - {{ date|date:"F j, Y" }}
                        {% else %}
                            {{ date|naturalday|title }} - {{ date|date:"F j, Y" }}
                        {% endif %}
                    </div>
                    
                    {% for notification in date_notifications %}
                    <div class="notification-item {% if not notification.is_read %}unread{% endif %} {% if notification.priority == 'high' %}high-priority{% endif %}"
                         data-notification-id="{{ notification.id }}"
                         onclick="toggleNotificationSelection(this, {{ notification.id }})">
                        
                        <div class="notification-content">
                            <div class="notification-icon {{ notification.category|default:'system' }}">
                                {% if notification.category == 'booking' %}
                                    <i class="fas fa-calendar-check"></i>
                                {% elif notification.category == 'payment' %}
                                    <i class="fas fa-credit-card"></i>
                                {% elif notification.category == 'alert' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% else %}
                                    <i class="fas fa-info-circle"></i>
                                {% endif %}
                            </div>
                            
                            <div class="notification-body">
                                <div class="notification-title">
                                    {{ notification.title }}
                                </div>
                                <div class="notification-message">
                                    {{ notification.message|truncatewords:20 }}
                                </div>
                                <div class="notification-meta">
                                    <span>{{ notification.created_at|naturaltime }}</span>
                                    {% if notification.priority == 'high' %}
                                    <span class="priority-badge">High Priority</span>
                                    {% endif %}
                                    {% if notification.category %}
                                    <span class="badge bg-light text-dark">{{ notification.get_category_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification-actions">
                            {% if not notification.is_read %}
                            <button class="action-btn mark-read" onclick="markAsRead({{ notification.id }}); event.stopPropagation();">
                                Mark Read
                            </button>
                            {% endif %}
                            <button class="action-btn" onclick="deleteNotification({{ notification.id }}); event.stopPropagation();">
                                Delete
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedNotifications = new Set();

function toggleNotificationSelection(element, notificationId) {
    if (event.ctrlKey || event.metaKey) {
        // Multi-select mode
        element.classList.toggle('selected');
        
        if (selectedNotifications.has(notificationId)) {
            selectedNotifications.delete(notificationId);
        } else {
            selectedNotifications.add(notificationId);
        }
        
        updateBulkActions();
        event.preventDefault();
    } else {
        // Single click - mark as read if unread
        if (element.classList.contains('unread')) {
            markAsRead(notificationId);
        }
    }
}

function updateBulkActions() {
    const bar = document.getElementById('bulkActionsBar');
    const count = document.getElementById('selectedCount');
    
    count.textContent = selectedNotifications.size;
    
    if (selectedNotifications.size > 0) {
        bar.classList.add('show');
    } else {
        bar.classList.remove('show');
    }
}

function clearSelection() {
    selectedNotifications.clear();
    document.querySelectorAll('.notification-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    updateBulkActions();
}

function markAsRead(notificationId) {
    fetch(`/notifications/mark-read/${notificationId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (element) {
                element.classList.remove('unread');
                element.querySelector('.action-btn.mark-read')?.remove();
            }
            updateNotificationStats();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function deleteNotification(notificationId) {
    if (!confirm('Are you sure you want to delete this notification?')) {
        return;
    }
    
    fetch(`/notifications/delete/${notificationId}/`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (element) {
                element.remove();
            }
            updateNotificationStats();
        }
    })
    .catch(error => {
        console.error('Error deleting notification:', error);
    });
}

function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) {
        return;
    }
    
    fetch('/notifications/mark-all-read/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking all as read:', error);
    });
}

function clearOldNotifications() {
    if (!confirm('Delete notifications older than 30 days?')) {
        return;
    }
    
    fetch('/notifications/clear-old/', {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error clearing old notifications:', error);
    });
}

function refreshNotifications() {
    location.reload();
}

function bulkMarkAsRead() {
    if (selectedNotifications.size === 0) return;
    
    Promise.all([...selectedNotifications].map(id => 
        fetch(`/notifications/mark-read/${id}/`, {
            method: 'POST',
            headers: { 'X-CSRFToken': getCsrfToken() }
        })
    ))
    .then(() => {
        location.reload();
    })
    .catch(error => {
        console.error('Error in bulk mark as read:', error);
    });
}

function bulkDelete() {
    if (selectedNotifications.size === 0) return;
    
    if (!confirm(`Delete ${selectedNotifications.size} selected notifications?`)) {
        return;
    }
    
    Promise.all([...selectedNotifications].map(id => 
        fetch(`/notifications/delete/${id}/`, {
            method: 'DELETE',
            headers: { 'X-CSRFToken': getCsrfToken() }
        })
    ))
    .then(() => {
        location.reload();
    })
    .catch(error => {
        console.error('Error in bulk delete:', error);
    });
}

function updateNotificationStats() {
    // Update the stats counters
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    document.querySelector('.stat-card.unread .stat-value').textContent = unreadCount;
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '{{ csrf_token }}';
}

// Add CSRF token to page if not present
if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = '{{ csrf_token }}';
    document.body.appendChild(csrfInput);
}

// Auto-refresh every 2 minutes
setInterval(refreshNotifications, 120000);
</script>
{% endblock %} 