{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Mark Booking Completed - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .completion-card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    .booking-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 1.5rem;
    }
    .service-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #007bff;
    }
    .completion-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    .completion-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }
    .customer-info {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: white;
        border-radius: 12px;
        margin-bottom: 1rem;
        border: 2px solid #e9ecef;
    }
    .customer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.5rem;
        margin-right: 1rem;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex align-items-center mb-4">
                <a href="{% url 'booking_cart_app:provider_todays_bookings' %}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h2 class="mb-0">Mark Booking Completed</h2>
                    <p class="text-muted mb-0">Confirm that the service has been successfully provided</p>
                </div>
            </div>

            <div class="completion-card card">
                <div class="card-body p-4">
                    <!-- Customer Information -->
                    <div class="customer-info">
                        <div class="customer-avatar">
                            {{ booking.customer.first_name|first|default:booking.customer.email|first|upper }}
                        </div>
                        <div>
                            <h5 class="mb-1">{{ booking.customer.get_full_name|default:booking.customer.email }}</h5>
                            <div class="text-muted">{{ booking.customer.email }}</div>
                            {% if booking.customer.phone %}
                                <div class="text-muted small">
                                    <i class="fas fa-phone me-1"></i>{{ booking.customer.phone }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="booking-summary mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3">
                                    <i class="fas fa-calendar-alt me-2"></i>Appointment Details
                                </h6>
                                <div class="mb-2">
                                    <strong>Booking ID:</strong> {{ booking.friendly_id }}
                                </div>
                                <div class="mb-2">
                                    <strong>Date:</strong> 
                                    {% for item in booking.items.all|slice:":1" %}
                                        {{ item.scheduled_date|date:"F d, Y" }}
                                    {% endfor %}
                                </div>
                                <div class="mb-2">
                                    <strong>Time:</strong> 
                                    {% for item in booking.items.all|slice:":1" %}
                                        {{ item.scheduled_time }}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-3">
                                    <i class="fas fa-dollar-sign me-2"></i>Booking Value
                                </h6>
                                <div class="mb-2">
                                    <strong>Total Amount:</strong> ${{ booking.total_price }}
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong> 
                                    <span class="badge bg-success">{{ booking.get_status_display }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Services Provided -->
                    <h6 class="mb-3">
                        <i class="fas fa-spa me-2"></i>Services Provided
                    </h6>
                    {% for item in booking.items.all %}
                    <div class="service-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ item.service_title }}</h6>
                                <div class="text-muted small">
                                    Duration: {{ item.duration_minutes }} minutes
                                    {% if item.quantity > 1 %}
                                        • Quantity: {{ item.quantity }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${{ item.service_price }}</div>
                                {% if item.quantity > 1 %}
                                    <div class="text-muted small">x{{ item.quantity }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Customer Notes -->
                    {% if booking.notes %}
                    <div class="mt-4">
                        <h6 class="mb-2">
                            <i class="fas fa-comment me-2"></i>Customer Notes
                        </h6>
                        <div class="p-3 bg-light rounded">
                            {{ booking.notes }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Completion Form -->
                    <div class="mt-4 pt-4 border-top">
                        <h6 class="mb-3">
                            <i class="fas fa-check-circle me-2"></i>Service Completion
                        </h6>
                        
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label for="{{ form.completion_notes.id_for_label }}" class="form-label">
                                    {{ form.completion_notes.label }}
                                </label>
                                {{ form.completion_notes }}
                                {% if form.completion_notes.help_text %}
                                    <div class="form-text">{{ form.completion_notes.help_text }}</div>
                                {% endif %}
                                {% if form.completion_notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.completion_notes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>Completion Confirmation
                                </h6>
                                <p class="mb-0">
                                    By marking this booking as completed, you confirm that:
                                </p>
                                <ul class="mb-0 mt-2">
                                    <li>All scheduled services have been successfully provided</li>
                                    <li>The customer was satisfied with the service quality</li>
                                    <li>The appointment ended as scheduled</li>
                                    <li>Payment has been processed (if applicable)</li>
                                </ul>
                            </div>

                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="confirmCompletion" required>
                                <label class="form-check-label" for="confirmCompletion">
                                    I confirm that the service has been completed successfully and the customer is satisfied
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}" 
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="completion-btn">
                                    <i class="fas fa-check me-2"></i>Mark as Completed
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        const confirmed = confirm('Are you sure you want to mark this booking as completed? This action cannot be undone.');
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
        
        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });
});
</script>
{% endblock %} 