{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Today's Schedule - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .schedule-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        transition: all 0.2s ease;
        margin-bottom: 1rem;
    }
    .schedule-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }
    .time-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.875rem;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
    .status-confirmed { background-color: #28a745; }
    .status-pending { background-color: #ffc107; }
    .status-completed { background-color: #17a2b8; }
    .status-cancelled { background-color: #dc3545; }
    .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
    }
    .section-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .action-btn {
        border-radius: 20px;
        padding: 0.375rem 1rem;
        font-size: 0.875rem;
        border: none;
        transition: all 0.2s ease;
    }
    .action-btn:hover {
        transform: translateY(-1px);
    }
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">Today's Schedule</h2>
                    <p class="text-muted mb-0">{{ today|date:"F d, Y (l)" }}</p>
                </div>
                <div>
                    <a href="{% url 'booking_cart_app:provider_booking_dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i>All Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ todays_bookings.count }}</h4>
                    <small class="text-muted">Total Appointments</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">{{ confirmed_bookings.count }}</h4>
                    <small class="text-muted">Confirmed</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ pending_bookings.count }}</h4>
                    <small class="text-muted">Pending</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ completed_bookings.count }}</h4>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Bookings -->
    {% if pending_bookings %}
    <div class="section-header">
        <h5 class="mb-0">
            <i class="fas fa-clock me-2"></i>Pending Approval ({{ pending_bookings.count }})
        </h5>
    </div>
    {% for booking in pending_bookings %}
    <div class="schedule-card card">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <div class="customer-avatar">
                        {{ booking.customer.first_name|first|default:booking.customer.email|first|upper }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="fw-bold">{{ booking.customer.get_full_name|default:booking.customer.email }}</div>
                    <div class="text-muted small">{{ booking.customer.email }}</div>
                </div>
                <div class="col-md-2">
                    {% for item in booking.items.all|slice:":1" %}
                    <span class="time-badge">{{ item.scheduled_time }}</span>
                    {% endfor %}
                </div>
                <div class="col-md-3">
                    <div class="small">
                        {% for item in booking.items.all|slice:":2" %}
                            <div>{{ item.service_title }}</div>
                        {% endfor %}
                        {% if booking.items.count > 2 %}
                            <div class="text-muted">+{{ booking.items.count|add:"-2" }} more services</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="fw-bold mb-1">${{ booking.total_price }}</div>
                    <span class="status-indicator status-pending"></span>
                    <small class="text-muted">Pending</small>
                </div>
                <div class="col-md-1 text-end">
                    <div class="btn-group-vertical btn-group-sm">
                        <a href="{% url 'booking_cart_app:provider_accept_booking' booking.slug %}" 
                           class="action-btn btn btn-success btn-sm mb-1">
                            <i class="fas fa-check"></i>
                        </a>
                        <a href="{% url 'booking_cart_app:provider_decline_booking' booking.slug %}" 
                           class="action-btn btn btn-danger btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}

    <!-- Confirmed Bookings -->
    {% if confirmed_bookings %}
    <div class="section-header">
        <h5 class="mb-0">
            <i class="fas fa-check-circle me-2"></i>Confirmed Appointments ({{ confirmed_bookings.count }})
        </h5>
    </div>
    {% for booking in confirmed_bookings %}
    <div class="schedule-card card">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <div class="customer-avatar">
                        {{ booking.customer.first_name|first|default:booking.customer.email|first|upper }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="fw-bold">{{ booking.customer.get_full_name|default:booking.customer.email }}</div>
                    <div class="text-muted small">{{ booking.customer.email }}</div>
                </div>
                <div class="col-md-2">
                    {% for item in booking.items.all|slice:":1" %}
                    <span class="time-badge">{{ item.scheduled_time }}</span>
                    {% endfor %}
                </div>
                <div class="col-md-3">
                    <div class="small">
                        {% for item in booking.items.all|slice:":2" %}
                            <div>{{ item.service_title }}</div>
                        {% endfor %}
                        {% if booking.items.count > 2 %}
                            <div class="text-muted">+{{ booking.items.count|add:"-2" }} more services</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="fw-bold mb-1">${{ booking.total_price }}</div>
                    <span class="status-indicator status-confirmed"></span>
                    <small class="text-muted">Confirmed</small>
                </div>
                <div class="col-md-1 text-end">
                    <div class="btn-group-vertical btn-group-sm">
                        <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}" 
                           class="action-btn btn btn-outline-primary btn-sm mb-1">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'booking_cart_app:provider_mark_completed' booking.slug %}" 
                           class="action-btn btn btn-success btn-sm">
                            <i class="fas fa-check"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}

    <!-- Completed Bookings -->
    {% if completed_bookings %}
    <div class="section-header">
        <h5 class="mb-0">
            <i class="fas fa-check-double me-2"></i>Completed Today ({{ completed_bookings.count }})
        </h5>
    </div>
    {% for booking in completed_bookings %}
    <div class="schedule-card card">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <div class="customer-avatar">
                        {{ booking.customer.first_name|first|default:booking.customer.email|first|upper }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="fw-bold">{{ booking.customer.get_full_name|default:booking.customer.email }}</div>
                    <div class="text-muted small">{{ booking.customer.email }}</div>
                </div>
                <div class="col-md-2">
                    {% for item in booking.items.all|slice:":1" %}
                    <span class="time-badge">{{ item.scheduled_time }}</span>
                    {% endfor %}
                </div>
                <div class="col-md-3">
                    <div class="small">
                        {% for item in booking.items.all|slice:":2" %}
                            <div>{{ item.service_title }}</div>
                        {% endfor %}
                        {% if booking.items.count > 2 %}
                            <div class="text-muted">+{{ booking.items.count|add:"-2" }} more services</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="fw-bold mb-1">${{ booking.total_price }}</div>
                    <span class="status-indicator status-completed"></span>
                    <small class="text-muted">Completed</small>
                </div>
                <div class="col-md-1 text-end">
                    <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}" 
                       class="action-btn btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}

    <!-- Empty State -->
    {% if not todays_bookings %}
    <div class="empty-state">
        <i class="fas fa-calendar-day fa-4x mb-3"></i>
        <h4>No appointments today</h4>
        <p class="mb-4">You have a free day! This is a great time to:</p>
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    <a href="{% url 'booking_cart_app:provider_availability_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-plus me-2"></i>Set Availability for Tomorrow
                    </a>
                    <a href="{% url 'venues_app:service_list' %}" class="btn btn-outline-success">
                        <i class="fas fa-spa me-2"></i>Manage Your Services
                    </a>
                    <a href="{% url 'venues_app:venue_detail' %}" class="btn btn-outline-info">
                        <i class="fas fa-store me-2"></i>Update Venue Information
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Quick Complete Modal -->
<div class="modal fade" id="quickCompleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mark as Completed</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to mark this appointment as completed?</p>
                <div class="form-group">
                    <label>Optional Notes:</label>
                    <textarea class="form-control" rows="3" placeholder="Any notes about the completed service..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success">Mark Completed</button>
            </div>
        </div>
    </div>
</div>
{% endblock %} 