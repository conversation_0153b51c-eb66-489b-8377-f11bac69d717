{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Provider Booking Detail - {{ booking.booking_id }}{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>Booking Details</h4>
                        <div>
                            <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to My Bookings
                            </a>
                        </div>
                    </div>
                    <small class="text-muted">Booking ID: {{ booking.booking_id }}</small>
                </div>
                <div class="card-body">
                    <!-- Customer Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Customer Information</h6>
                            <p><strong>Email:</strong> {{ booking.customer.email }}</p>
                            {% if booking.customer.customerprofile %}
                            <p><strong>Name:</strong> {{ booking.customer.customerprofile.first_name }} {{ booking.customer.customerprofile.last_name }}</p>
                            <p><strong>Phone:</strong> {{ booking.customer.customerprofile.phone_number|default:"Not provided" }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>Booking Status</h6>
                            <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %} p-2">
                                {{ booking.get_status_display }}
                            </span>
                            
                            <h6 class="mt-3">Booking Date</h6>
                            <p>{{ booking.created_at|date:"F d, Y g:i A" }}</p>
                            
                            <h6>Total Amount</h6>
                            <p class="h5 text-success">${{ booking.total_amount }}</p>
                        </div>
                    </div>

                    <!-- Venue Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>Venue</h6>
                            <p><strong>{{ booking.venue.venue_name }}</strong></p>
                            <p>{{ booking.venue.short_description }}</p>
                            <p>
                                {{ booking.venue.street_number }} {{ booking.venue.street_name }}<br>
                                {{ booking.venue.city }}, {{ booking.venue.state }}
                            </p>
                        </div>
                    </div>

                    {% if booking.notes %}
                    <div class="mb-4">
                        <h6>Customer Notes</h6>
                        <div class="alert alert-info">
                            {{ booking.notes }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Services Booked -->
                    <div class="mb-4">
                        <h6>Services Booked</h6>
                        {% for item in booking.items.all %}
                        <div class="border p-3 mb-2">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service_title }}</h6>
                                    <p class="text-muted">{{ item.service.short_description }}</p>
                                    <p><strong>Date:</strong> {{ item.scheduled_date|date:"F d, Y" }}</p>
                                    <p><strong>Time:</strong> {{ item.scheduled_time|time:"g:i A" }}</p>
                                    <p><strong>Duration:</strong> {{ item.duration_minutes }} minutes</p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.service_price }} each</p>
                                    <p class="h6"><strong>Total:</strong> ${{ item.service_price|floatformat:2 }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Provider Actions -->
                    {% if booking.status == 'pending' %}
                    <div class="mt-4">
                        <h6>Actions</h6>
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i> This booking is pending your response.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="{% url 'booking_cart_app:provider_accept_booking' booking.slug %}" 
                               class="btn btn-success"
                               onclick="return confirm('Are you sure you want to accept this booking?')">
                                <i class="fas fa-check"></i> Accept Booking
                            </a>
                            
                            <button class="btn btn-danger" onclick="showDeclineModal()">
                                <i class="fas fa-times"></i> Decline Booking
                            </button>
                        </div>
                    </div>
                    {% elif booking.status == 'confirmed' %}
                    <div class="mt-4">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> You have accepted this booking.
                        </div>
                    </div>
                    {% elif booking.status == 'declined' %}
                    <div class="mt-4">
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i> You have declined this booking.
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Booking Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6>Booking Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Booking Created</h6>
                                <p class="text-muted">{{ booking.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        
                        {% if booking.confirmed_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>Booking Confirmed</h6>
                                <p class="text-muted">{{ booking.confirmed_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if booking.cancelled_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6>Booking Cancelled</h6>
                                <p class="text-muted">{{ booking.cancelled_at|date:"M d, Y H:i" }}</p>
                                {% if booking.cancellation_reason %}
                                <p><strong>Reason:</strong> {{ booking.cancellation_reason }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>Customer Summary</h6>
                </div>
                <div class="card-body">
                    <p><strong>Total Bookings:</strong> {{ booking.customer.booking_set.count }}</p>
                    <p><strong>Member Since:</strong> {{ booking.customer.date_joined|date:"M Y" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Decline Modal -->
<div class="modal fade" id="declineModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Decline Booking</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="post" action="{% url 'booking_cart_app:provider_decline_booking' booking.slug %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="decline_reason">Reason for declining:</label>
                        <textarea class="form-control" id="decline_reason" name="decline_reason" rows="3" required
                                  placeholder="Please provide a reason for declining this booking..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Decline Booking</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>

<script>
function showDeclineModal() {
    $('#declineModal').modal('show');
}
</script>
{% endblock %}
