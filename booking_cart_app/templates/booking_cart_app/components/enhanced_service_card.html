{% load static %}

<!-- Enhanced Service Card Component -->
<div class="enhanced-service-card" data-service-id="{{ service.id }}">
    <!-- Service Image with Overlay -->
    <div class="service-image-wrapper">
        {% if service.image %}
        <img src="{{ service.image.url }}" alt="{{ service.service_title }}" class="service-card-image">
        {% else %}
        <div class="service-card-image service-placeholder">
            <i class="fas fa-concierge-bell"></i>
        </div>
        {% endif %}
        
        <!-- Service Badges -->
        <div class="service-badges">
            {% if service.is_featured %}
            <span class="service-badge featured">
                <i class="fas fa-star me-1"></i>Featured
            </span>
            {% endif %}
            
            {% if service.is_new %}
            <span class="service-badge new">
                <i class="fas fa-sparkles me-1"></i>New
            </span>
            {% endif %}
            
            {% if service.has_discount %}
            <span class="service-badge discount">
                <i class="fas fa-percent me-1"></i>{{ service.discount_percentage }}% Off
            </span>
            {% endif %}
        </div>
        
        <!-- Quick Actions -->
        <div class="service-quick-actions">
            <button class="quick-action-btn favorite-btn" data-service-id="{{ service.id }}" title="Add to Favorites">
                <i class="far fa-heart"></i>
            </button>
            <button class="quick-action-btn share-btn" data-service-id="{{ service.id }}" title="Share Service">
                <i class="fas fa-share-alt"></i>
            </button>
        </div>
        
        <!-- Rating Overlay -->
        {% if service.average_rating %}
        <div class="rating-overlay">
            <div class="rating-stars">
                {% for i in "12345" %}
                    {% if forloop.counter <= service.average_rating %}
                        <i class="fas fa-star"></i>
                    {% elif forloop.counter <= service.average_rating|add:0.5 %}
                        <i class="fas fa-star-half-alt"></i>
                    {% else %}
                        <i class="far fa-star"></i>
                    {% endif %}
                {% endfor %}
            </div>
            <span class="rating-score">{{ service.average_rating|floatformat:1 }}</span>
            <span class="rating-count">({{ service.review_count }})</span>
        </div>
        {% endif %}
    </div>
    
    <!-- Service Content -->
    <div class="service-card-content">
        <!-- Header Section -->
        <div class="service-header">
            <h3 class="service-title">
                <a href="{% url 'venues_app:service_detail' service.id %}">{{ service.service_title }}</a>
            </h3>
            <div class="service-category">
                <i class="fas fa-tag me-1"></i>{{ service.category|default:"Service" }}
            </div>
        </div>
        
        <!-- Description -->
        <p class="service-description">{{ service.short_description|truncatechars:120 }}</p>
        
        <!-- Service Details Grid -->
        <div class="service-details-grid">
            <div class="detail-item">
                <i class="fas fa-map-marker-alt text-primary"></i>
                <span class="detail-label">Location</span>
                <span class="detail-value">{{ service.venue.venue_name|truncatechars:20 }}</span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-clock text-info"></i>
                <span class="detail-label">Duration</span>
                <span class="detail-value">{{ service.duration_minutes }}min</span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-users text-success"></i>
                <span class="detail-label">Max Group</span>
                <span class="detail-value">{{ service.max_capacity|default:"10" }}</span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-calendar text-warning"></i>
                <span class="detail-label">Available</span>
                <span class="detail-value availability-indicator" data-service-id="{{ service.id }}">
                    <span class="loading-dot"></span>Checking...
                </span>
            </div>
        </div>
        
        <!-- Provider Information -->
        <div class="provider-section">
            <div class="provider-avatar">
                {% if service.venue.service_provider.profile_picture %}
                <img src="{{ service.venue.service_provider.profile_picture.url }}" alt="{{ service.venue.service_provider.get_full_name }}">
                {% else %}
                <div class="avatar-placeholder">
                    {{ service.venue.service_provider.first_name.0|default:"P" }}{{ service.venue.service_provider.last_name.0|default:"" }}
                </div>
                {% endif %}
            </div>
            <div class="provider-info">
                <h6 class="provider-name">{{ service.venue.service_provider.get_full_name|default:"Professional Provider" }}</h6>
                <div class="provider-meta">
                    <span class="experience">{{ service.venue.service_provider.years_experience|default:"5" }}+ years</span>
                    <span class="verification-badge">
                        <i class="fas fa-shield-alt text-success"></i>Verified
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Pricing and Booking Section -->
        <div class="booking-section">
            <div class="pricing-info">
                <div class="price-main">
                    <span class="currency">$</span>
                    <span class="price-amount">{{ service.price_min }}</span>
                    {% if service.price_max and service.price_max != service.price_min %}
                    <span class="price-range"> - ${{ service.price_max }}</span>
                    {% endif %}
                </div>
                <div class="price-period">per appointment</div>
                
                {% if service.original_price and service.price_min < service.original_price %}
                <div class="price-original">
                    <span class="original-price">${{ service.original_price }}</span>
                    <span class="discount-badge">
                        Save ${{ service.original_price|sub:service.price_min }}
                    </span>
                </div>
                {% endif %}
            </div>
            
            <div class="booking-actions">
                <a href="{% url 'booking_cart_app:enhanced_add_to_cart' service.id %}" 
                   class="btn btn-book-now">
                    <i class="fas fa-calendar-plus me-2"></i>Book Now
                </a>
                
                <button class="btn btn-view-details" 
                        onclick="location.href='{% url 'venues_app:service_detail' service.id %}'">
                    <i class="fas fa-eye me-1"></i>Details
                </button>
            </div>
        </div>
        
        <!-- Tags and Features -->
        {% if service.tags.all or service.features.all %}
        <div class="service-tags">
            {% for tag in service.tags.all|slice:":3" %}
            <span class="service-tag">{{ tag.name }}</span>
            {% endfor %}
            {% if service.tags.count > 3 %}
            <span class="service-tag more">+{{ service.tags.count|add:"-3" }} more</span>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <!-- Loading State Overlay -->
    <div class="card-loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <span>Loading availability...</span>
    </div>
</div>

<style>
.enhanced-service-card {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.enhanced-service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

/* Service Image */
.service-image-wrapper {
    position: relative;
    height: 240px;
    overflow: hidden;
}

.service-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-placeholder {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.enhanced-service-card:hover .service-card-image {
    transform: scale(1.1);
}

/* Service Badges */
.service-badges {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.service-badge.featured {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.service-badge.new {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.service-badge.discount {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

/* Quick Actions */
.service-quick-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.enhanced-service-card:hover .service-quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.9);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

/* Rating Overlay */
.rating-overlay {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
}

.rating-stars {
    color: #ffc107;
    font-size: 0.875rem;
}

.rating-score {
    font-weight: 600;
}

.rating-count {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Service Content */
.service-card-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Service Header */
.service-header {
    margin-bottom: 1rem;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.service-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.service-title a:hover {
    color: #667eea;
}

.service-category {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Service Description */
.service-description {
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 1.5rem;
    flex: 1;
}

/* Service Details Grid */
.service-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.detail-label {
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #333;
    font-weight: 600;
}

.availability-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.loading-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6c757d;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Provider Section */
.provider-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.provider-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.provider-name {
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
}

.provider-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.75rem;
    color: #6c757d;
}

.verification-badge {
    color: #28a745;
    font-weight: 500;
}

/* Booking Section */
.booking-section {
    margin-top: auto;
}

.pricing-info {
    margin-bottom: 1rem;
}

.price-main {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
    margin-bottom: 0.25rem;
}

.currency {
    font-size: 1rem;
    font-weight: 600;
    color: #28a745;
}

.price-amount {
    font-size: 1.75rem;
    font-weight: 700;
    color: #28a745;
}

.price-range {
    font-size: 1rem;
    color: #6c757d;
}

.price-period {
    font-size: 0.875rem;
    color: #6c757d;
}

.price-original {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.original-price {
    font-size: 0.875rem;
    color: #6c757d;
    text-decoration: line-through;
}

.discount-badge {
    font-size: 0.75rem;
    background: #e8f5e8;
    color: #28a745;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
}

/* Booking Actions */
.booking-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-book-now {
    flex: 1;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 0.875rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-book-now:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    color: white;
    text-decoration: none;
}

.btn-view-details {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
    padding: 0.875rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-view-details:hover {
    border-color: #667eea;
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* Service Tags */
.service-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.service-tag {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.service-tag.more {
    background: #667eea;
    color: white;
}

/* Loading Overlay */
.card-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-weight: 500;
    color: #6c757d;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .service-image-wrapper {
        height: 200px;
    }
    
    .service-card-content {
        padding: 1rem;
    }
    
    .service-details-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .booking-actions {
        flex-direction: column;
    }
    
    .provider-section {
        padding: 0.75rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced service cards
    initServiceCards();
});

function initServiceCards() {
    const serviceCards = document.querySelectorAll('.enhanced-service-card');
    
    serviceCards.forEach(card => {
        const serviceId = card.dataset.serviceId;
        
        // Load availability status
        loadAvailabilityStatus(serviceId, card);
        
        // Initialize favorite functionality
        initFavoriteToggle(card);
        
        // Initialize share functionality
        initShareFeature(card);
    });
}

function loadAvailabilityStatus(serviceId, card) {
    const indicator = card.querySelector('.availability-indicator');
    if (!indicator) return;
    
    // Get next 7 days availability
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    fetch(`/bookings/ajax/month-availability/${serviceId}/?start=${today.toISOString().split('T')[0]}&end=${nextWeek.toISOString().split('T')[0]}`)
        .then(response => response.json())
        .then(data => {
            updateAvailabilityIndicator(indicator, data.availability);
        })
        .catch(error => {
            indicator.innerHTML = '<span style="color: #dc3545;">Unavailable</span>';
        });
}

function updateAvailabilityIndicator(indicator, availability) {
    const availableDays = Object.values(availability).filter(day => day.available_slots > 0).length;
    const totalDays = Object.keys(availability).length;
    
    let status, color;
    if (availableDays === 0) {
        status = 'Fully booked';
        color = '#dc3545';
    } else if (availableDays <= totalDays * 0.3) {
        status = 'Limited availability';
        color = '#ffc107';
    } else {
        status = 'Available';
        color = '#28a745';
    }
    
    indicator.innerHTML = `<span style="color: ${color};">${status}</span>`;
}

function initFavoriteToggle(card) {
    const favoriteBtn = card.querySelector('.favorite-btn');
    if (!favoriteBtn) return;
    
    favoriteBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const icon = this.querySelector('i');
        const isFavorited = icon.classList.contains('fas');
        
        if (isFavorited) {
            icon.classList.remove('fas');
            icon.classList.add('far');
            this.title = 'Add to Favorites';
        } else {
            icon.classList.remove('far');
            icon.classList.add('fas');
            this.title = 'Remove from Favorites';
        }
        
        // Here you would make an AJAX call to update favorites
        // updateFavoriteStatus(serviceId, !isFavorited);
    });
}

function initShareFeature(card) {
    const shareBtn = card.querySelector('.share-btn');
    if (!shareBtn) return;
    
    shareBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const serviceId = card.dataset.serviceId;
        const serviceTitle = card.querySelector('.service-title a').textContent;
        const serviceUrl = card.querySelector('.service-title a').href;
        
        if (navigator.share) {
            navigator.share({
                title: serviceTitle,
                text: `Check out this service: ${serviceTitle}`,
                url: serviceUrl
            });
        } else {
            // Fallback: Copy to clipboard
            navigator.clipboard.writeText(serviceUrl).then(() => {
                // Show temporary feedback
                const originalIcon = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.innerHTML = originalIcon;
                }, 2000);
            });
        }
    });
}
</script> 