{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Checkout - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
/* Progress Indicator Styles */
.checkout-progress {
    background: #fff;
    border-radius: 12px;
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.progress-step {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    font-weight: bold;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.progress-step.active {
    background: #667eea;
    color: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.3);
}

.progress-step.completed {
    background: #28a745;
    color: white;
}

.progress-line {
    height: 2px;
    background: #e9ecef;
    flex: 1;
    margin: 0 1rem;
    position: relative;
}

.progress-line.completed {
    background: #28a745;
}

.checkout-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 2rem;
}

.service-review-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.service-review-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.form-section {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.order-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 2rem;
    position: sticky;
    top: 100px;
}

.summary-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.summary-item:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1rem;
    color: #28a745;
}

.btn-complete-booking {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 10px;
    padding: 1.25rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-complete-booking:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-complete-booking:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-complete-booking .spinner-border {
    width: 1.2rem;
    height: 1.2rem;
}

.security-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #6610f2 100%);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    margin-top: 1rem;
}

.policy-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.terms-checkbox {
    transform: scale(1.2);
    margin-right: 0.75rem;
}

@media (max-width: 768px) {
    .checkout-progress {
        padding: 1rem;
    }
    
    .progress-step {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }
    
    .progress-line {
        margin: 0 0.5rem;
    }
    
    .order-summary {
        position: static;
        margin-top: 2rem;
    }
    
    .btn-complete-booking {
        width: 100%;
    }
}

.validation-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <!-- Progress Indicator -->
    <div class="checkout-progress fade-in">
        <div class="d-flex align-items-center justify-content-center">
            <div class="progress-step completed">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="progress-line completed"></div>
            <div class="progress-step active pulse">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <i class="fas fa-check"></i>
            </div>
        </div>
        <div class="d-flex justify-content-between mt-3">
            <small class="text-success fw-bold">Cart Review</small>
            <small class="text-primary fw-bold">Checkout</small>
            <small class="text-muted">Confirmation</small>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Checkout Form -->
            <div class="form-section fade-in">
                <div class="checkout-header">
                    <h3 class="mb-1">
                        <i class="fas fa-credit-card me-2"></i>Complete Your Booking
                    </h3>
                    <p class="mb-0 opacity-75">Review your services and provide booking details</p>
                </div>
                
                <div class="p-0">
                    <form method="post" id="checkout-form" novalidate>
                        {% csrf_token %}
                        
                        <!-- Services Review Section -->
                        <div class="p-4">
                            <h4 class="mb-4">
                                <i class="fas fa-list-check me-2"></i>Services to Book
                            </h4>
                            {% for item in cart.items.all %}
                            <div class="service-review-card">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-2 text-dark">{{ item.service.service_title }}</h5>
                                        <p class="text-muted mb-2">{{ item.service.short_description }}</p>
                                        
                                        <div class="row g-2">
                                            <div class="col-sm-6">
                                                <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                                <strong>Venue:</strong> {{ item.service.venue.venue_name }}
                                            </div>
                                            <div class="col-sm-6">
                                                <i class="fas fa-clock text-info me-1"></i>
                                                <strong>Duration:</strong> {{ item.service.duration_minutes }} minutes
                                            </div>
                                            <div class="col-sm-6">
                                                <i class="fas fa-calendar text-success me-1"></i>
                                                <strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}
                                            </div>
                                            <div class="col-sm-6">
                                                <i class="fas fa-clock text-warning me-1"></i>
                                                <strong>Time:</strong> {{ item.selected_time_slot|time:"g:i A" }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <div class="mb-2">
                                            <span class="badge bg-primary">
                                                {{ item.quantity }} appointment{{ item.quantity|pluralize }}
                                            </span>
                                        </div>
                                        <div class="text-muted mb-1">
                                            ${{ item.price_per_item }} each
                                        </div>
                                        <div class="h5 text-success mb-0">
                                            Total: ${{ item.total_price }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Customer Notes Section -->
                        <div class="border-top p-4">
                            <h5 class="mb-3">
                                <i class="fas fa-comment-dots me-2"></i>Special Requests
                            </h5>
                            <div class="form-floating">
                                {{ form.notes }}
                                <label for="{{ form.notes.id_for_label }}">Any special requests or notes for the service provider?</label>
                                <div class="form-text">Maximum 500 characters. This information will be shared with your service provider.</div>
                            </div>
                            {% if form.notes.errors %}
                            <div class="error-message mt-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div id="notes-counter" class="text-muted small mt-2">0 / 500 characters</div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="border-top p-4">
                            <h5 class="mb-3">
                                <i class="fas fa-file-contract me-2"></i>Terms & Conditions
                            </h5>
                            <div class="form-check mb-3">
                                <input class="form-check-input terms-checkbox" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" target="_blank" class="text-decoration-none">Terms and Conditions</a> and 
                                    <a href="#" target="_blank" class="text-decoration-none">Cancellation Policy</a>
                                </label>
                                <div class="invalid-feedback">
                                    Please accept the terms and conditions to continue.
                                </div>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="marketing" value="yes">
                                <label class="form-check-label" for="marketing">
                                    I would like to receive email updates about new services and special offers
                                </label>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="border-top p-4 d-flex gap-3 justify-content-between flex-wrap">
                            <a href="{% url 'booking_cart_app:cart_view' %}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Back to Cart
                            </a>
                            <button type="submit" class="btn btn-complete-booking btn-lg" id="submit-btn">
                                <span class="btn-text">
                                    <i class="fas fa-check me-2"></i>Complete Booking
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="order-summary fade-in">
                <h4 class="mb-4">
                    <i class="fas fa-receipt me-2"></i>Order Summary
                </h4>
                
                <div class="summary-item d-flex justify-content-between">
                    <span>Subtotal ({{ cart.items.count }} item{{ cart.items.count|pluralize }}):</span>
                    <span>${{ cart.total_price }}</span>
                </div>
                
                {% if discount %}
                <div class="summary-item d-flex justify-content-between text-success">
                    <span>Discount ({{ discount.code }}):</span>
                    <span>-${{ discount_amount }}</span>
                </div>
                {% endif %}
                
                <div class="summary-item d-flex justify-content-between">
                    <span>Service Fee:</span>
                    <span class="text-success">FREE</span>
                </div>
                
                <div class="summary-item d-flex justify-content-between">
                    <span>Taxes & Fees:</span>
                    <span>${{ tax_amount|default:"0.00" }}</span>
                </div>
                
                <div class="summary-item d-flex justify-content-between">
                    <span><strong>Total Amount:</strong></span>
                    <span><strong>${{ final_total|default:cart.total_price }}</strong></span>
                </div>
                
                <div class="security-badge">
                    <i class="fas fa-shield-alt me-2"></i>
                    <strong>Secure Checkout</strong>
                    <br>
                    <small>Your information is protected with 256-bit SSL encryption</small>
                </div>
            </div>
            
            <!-- Cancellation Policy -->
            <div class="policy-card">
                <h6 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>Cancellation Policy
                </h6>
                <ul class="mb-0 small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        <strong>Free cancellation</strong> up to 24 hours before your appointment
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-percentage text-warning me-1"></i>
                        <strong>50% refund</strong> for cancellations within 24 hours
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-times text-danger me-1"></i>
                        <strong>No refund</strong> for no-shows or same-day cancellations
                    </li>
                </ul>
            </div>
            
            <!-- Support Information -->
            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="mb-2">
                    <i class="fas fa-headset text-primary me-1"></i>Need Help?
                </h6>
                <p class="mb-2 small">Our customer support team is here to assist you.</p>
                <div class="d-flex gap-2">
                    <a href="tel:+1234567890" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-phone me-1"></i>Call Us
                    </a>
                    <a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checkout-form');
    const submitBtn = document.getElementById('submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const notesField = document.getElementById('{{ form.notes.id_for_label }}');
    const notesCounter = document.getElementById('notes-counter');
    const termsCheckbox = document.getElementById('terms');
    
    // Character counter for notes field
    if (notesField && notesCounter) {
        function updateCounter() {
            const count = notesField.value.length;
            notesCounter.textContent = `${count} / 500 characters`;
            notesCounter.className = count > 450 ? 'text-warning small mt-2' : 'text-muted small mt-2';
        }
        
        notesField.addEventListener('input', updateCounter);
        updateCounter(); // Initialize counter
    }
    
    // Real-time validation for terms checkbox
    if (termsCheckbox) {
        termsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    }
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate terms checkbox
        if (!termsCheckbox.checked) {
            termsCheckbox.classList.add('is-invalid');
            termsCheckbox.focus();
            return false;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        
        // Show progress in steps
        setTimeout(() => {
            // Simulate processing steps
            const steps = document.querySelectorAll('.progress-step');
            const lines = document.querySelectorAll('.progress-line');
            
            // Activate final step
            steps[2].classList.add('active');
            steps[2].classList.remove('pulse');
            lines[1].classList.add('completed');
            
            // Submit the form after animation
            setTimeout(() => {
                form.submit();
            }, 500);
        }, 1000);
    });
    
    // Prevent multiple submissions
    let submitted = false;
    form.addEventListener('submit', function(e) {
        if (submitted) {
            e.preventDefault();
            return false;
        }
        submitted = true;
    });
    
    // Auto-save form data to localStorage
    const formElements = form.querySelectorAll('input, textarea, select');
    formElements.forEach(element => {
        // Load saved data
        const savedValue = localStorage.getItem(`checkout_${element.name}`);
        if (savedValue && element.type !== 'checkbox') {
            element.value = savedValue;
        } else if (savedValue && element.type === 'checkbox') {
            element.checked = savedValue === 'true';
        }
        
        // Save data on change
        element.addEventListener('change', function() {
            if (this.type === 'checkbox') {
                localStorage.setItem(`checkout_${this.name}`, this.checked);
            } else {
                localStorage.setItem(`checkout_${this.name}`, this.value);
            }
        });
    });
    
    // Clear saved data on successful submission
    window.addEventListener('beforeunload', function() {
        if (submitted) {
            formElements.forEach(element => {
                localStorage.removeItem(`checkout_${element.name}`);
            });
        }
    });
});
</script>
{% endblock %}
