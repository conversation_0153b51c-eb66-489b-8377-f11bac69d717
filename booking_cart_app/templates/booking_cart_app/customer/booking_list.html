{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}My Bookings - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .booking-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        transition: all 0.2s ease;
        margin-bottom: 1rem;
    }
    .booking-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }
    .status-badge {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
    }
    .view-toggle {
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        border: 2px solid #e9ecef;
        background: white;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    .view-toggle.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    .view-toggle:hover {
        text-decoration: none;
        color: #007bff;
        border-color: #007bff;
    }
    .view-toggle.active:hover {
        color: white;
    }
    .booking-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <!-- Header with View Toggle -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center booking-header">
                <div>
                    <h2 class="mb-0">My Bookings</h2>
                    <p class="text-muted mb-0">View and manage your service bookings</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="?view=dashboard" class="view-toggle {% if view_type == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-th-large me-1"></i>Dashboard
                    </a>
                    <a href="?view=list" class="view-toggle {% if view_type == 'list' or not view_type %}active{% endif %}">
                        <i class="fas fa-list me-1"></i>List View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Options -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex flex-wrap gap-2">
                <a href="?view=list" class="btn btn-sm {% if not status_filter or status_filter == 'all' %}btn-primary{% else %}btn-outline-secondary{% endif %}">
                    All Bookings
                </a>
                <a href="?view=list&status=pending" class="btn btn-sm {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-secondary{% endif %}">
                    Pending
                </a>
                <a href="?view=list&status=confirmed" class="btn btn-sm {% if status_filter == 'confirmed' %}btn-success{% else %}btn-outline-secondary{% endif %}">
                    Confirmed
                </a>
                <a href="?view=list&status=completed" class="btn btn-sm {% if status_filter == 'completed' %}btn-info{% else %}btn-outline-secondary{% endif %}">
                    Completed
                </a>
                <a href="?view=list&status=cancelled" class="btn btn-sm {% if status_filter == 'cancelled' %}btn-secondary{% else %}btn-outline-secondary{% endif %}">
                    Cancelled
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {% if bookings %}
                {% for booking in bookings %}
                <div class="booking-card card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-lg-3 col-md-4 mb-2 mb-md-0">
                                <h6 class="mb-1">{{ booking.venue.venue_name }}</h6>
                                <div class="text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ booking.venue.city }}, {{ booking.venue.state }}
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ booking.booking_date|date:"M d, Y" }}
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 mb-2 mb-md-0">
                                <div class="small text-muted mb-1">Services:</div>
                                {% for item in booking.items.all|slice:":2" %}
                                    <div class="small">{{ item.service_title }}</div>
                                {% endfor %}
                                {% if booking.items.count > 2 %}
                                    <div class="small text-muted">+{{ booking.items.count|add:"-2" }} more</div>
                                {% endif %}
                            </div>
                            <div class="col-lg-2 col-md-4 mb-2 mb-md-0">
                                <div class="small text-muted mb-1">Appointment:</div>
                                {% for item in booking.items.all|slice:":1" %}
                                    <div class="fw-bold">{{ item.scheduled_date|date:"M d" }}</div>
                                    <div class="small">{{ item.scheduled_time }}</div>
                                {% endfor %}
                                {% if booking.items.count > 1 %}
                                    <div class="small text-muted">+{{ booking.items.count|add:"-1" }} more</div>
                                {% endif %}
                            </div>
                            <div class="col-lg-2 col-6 mb-2 mb-lg-0">
                                <div class="text-center">
                                    {% if booking.status == 'confirmed' %}
                                        <span class="status-badge bg-success text-white">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'pending' %}
                                        <span class="status-badge bg-warning text-dark">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'cancelled' %}
                                        <span class="status-badge bg-danger text-white">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'completed' %}
                                        <span class="status-badge bg-info text-white">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'declined' %}
                                        <span class="status-badge bg-secondary text-white">{{ booking.get_status_display }}</span>
                                    {% else %}
                                        <span class="status-badge bg-secondary text-white">{{ booking.get_status_display }}</span>
                                    {% endif %}
                                </div>
                                <div class="text-center mt-2">
                                    <div class="fw-bold">${{ booking.total_price }}</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-6">
                                <div class="d-flex flex-column gap-1">
                                    <a href="{% url 'booking_cart_app:booking_detail' booking.slug %}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    {% if booking.status == 'pending' or booking.status == 'confirmed' %}
                                        {% if booking.can_be_cancelled %}
                                        <a href="{% url 'booking_cart_app:cancel_booking' booking.slug %}" 
                                           class="btn btn-outline-danger btn-sm"
                                           onclick="return confirm('Are you sure you want to cancel this booking?')">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </a>
                                        {% else %}
                                        <button class="btn btn-outline-secondary btn-sm" disabled title="Cannot cancel within 6 hours of appointment">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </button>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Bookings pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?view=list&page=1{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?view=list&page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?view=list&page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?view=list&page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5>No bookings found</h5>
                    {% if status_filter and status_filter != 'all' %}
                        <p class="text-muted">No {{ status_filter }} bookings found. <a href="?view=list">View all bookings</a></p>
                    {% else %}
                        <p class="text-muted">You haven't made any bookings yet. Browse our services to get started.</p>
                    {% endif %}
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Browse Services
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
