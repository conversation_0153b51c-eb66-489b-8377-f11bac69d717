{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Booking Confirmed - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
.confirmation-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.confirmation-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.confirmation-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

.booking-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.booking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.booking-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
}

.booking-details {
    padding: 2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-value {
    font-weight: 500;
    color: #212529;
}

.status-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.service-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.service-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.service-info {
    flex: 1;
}

.service-pricing {
    text-align: right;
    min-width: 120px;
}

.next-steps-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.next-steps-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.next-steps-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.3);
}

.next-steps-item:last-child {
    border-bottom: none;
}

.next-steps-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-primary-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-primary-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-outline-gradient {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-gradient:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
}

.contact-support {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.support-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .confirmation-header {
        padding: 2rem 1rem;
    }
    
    .confirmation-icon {
        font-size: 3rem;
    }
    
    .booking-details {
        padding: 1rem;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .service-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .service-pricing {
        text-align: left;
        min-width: auto;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn-primary-gradient,
    .btn-outline-gradient {
        width: 100%;
    }
}

.fade-in {
    animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Success Header -->
            <div class="confirmation-header fade-in">
                <div class="confirmation-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="mb-3">Booking Confirmed!</h1>
                <p class="lead mb-4">Your reservation has been successfully confirmed. We've sent a confirmation email to your inbox.</p>
                <div class="d-flex justify-content-center align-items-center gap-3 flex-wrap">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-check me-2"></i>
                        <span>Booking ID: <strong>{{ booking.friendly_id }}</strong></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <span>Booked: {{ booking.booking_date|date:"M d, Y g:i A" }}</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Booking Details Card -->
                    <div class="booking-card slide-in-left">
                        <div class="booking-card-header">
                            <h3 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Booking Details
                            </h3>
                        </div>
                        <div class="booking-details">
                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-hashtag text-primary"></i>
                                    Booking Reference
                                </div>
                                <div class="detail-value">{{ booking.booking_id }}</div>
                            </div>
                            
                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-flag text-info"></i>
                                    Status
                                </div>
                                <div class="detail-value">
                                    <span class="status-badge">{{ booking.get_status_display }}</span>
                                </div>
                            </div>
                            
                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-dollar-sign text-success"></i>
                                    Total Amount
                                </div>
                                <div class="detail-value">
                                    <span class="h5 text-success mb-0">${{ booking.total_price }}</span>
                                </div>
                            </div>
                            
                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-calendar text-warning"></i>
                                    Booking Date
                                </div>
                                <div class="detail-value">{{ booking.booking_date|date:"F d, Y g:i A" }}</div>
                            </div>
                            
                            {% if booking.notes %}
                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-comment-dots text-secondary"></i>
                                    Special Notes
                                </div>
                                <div class="detail-value">{{ booking.notes }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Booked Services -->
                    <div class="booking-card slide-in-left" style="animation-delay: 0.2s;">
                        <div class="booking-card-header">
                            <h3 class="mb-0">
                                <i class="fas fa-concierge-bell me-2"></i>Booked Services
                            </h3>
                        </div>
                        <div class="booking-details">
                            {% for item in booking.items.all %}
                            <div class="service-item">
                                <div class="service-header">
                                    <div class="service-info">
                                        <h5 class="mb-2 text-dark">{{ item.service_title }}</h5>
                                        <div class="row g-2 small text-muted">
                                            <div class="col-md-6">
                                                <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                                {{ item.service.venue.venue_name }}
                                            </div>
                                            <div class="col-md-6">
                                                <i class="fas fa-clock text-info me-1"></i>
                                                {{ item.duration_minutes }} minutes
                                            </div>
                                            <div class="col-md-6">
                                                <i class="fas fa-calendar text-success me-1"></i>
                                                {{ item.scheduled_date|date:"M d, Y" }}
                                            </div>
                                            <div class="col-md-6">
                                                <i class="fas fa-clock text-warning me-1"></i>
                                                {{ item.scheduled_time|time:"g:i A" }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-pricing">
                                        <div class="text-muted small">Price: ${{ item.service_price }}</div>
                                        <div class="text-muted small">Quantity: {{ item.quantity }}</div>
                                        <div class="h6 text-success mb-0">Total: ${{ item.total_price }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Next Steps -->
                    <div class="next-steps-card slide-in-right">
                        <h4 class="mb-4">
                            <i class="fas fa-route me-2"></i>What's Next?
                        </h4>
                        <ul class="next-steps-list">
                            <li class="next-steps-item">
                                <div class="next-steps-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <strong>Confirmation Email</strong>
                                    <div class="small text-muted">Check your inbox for detailed booking information</div>
                                </div>
                            </li>
                            <li class="next-steps-item">
                                <div class="next-steps-icon" style="background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); color: white;">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div>
                                    <strong>Reminder Notification</strong>
                                    <div class="small text-muted">We'll remind you 24 hours before your appointment</div>
                                </div>
                            </li>
                            <li class="next-steps-item">
                                <div class="next-steps-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #0056b3 100%); color: white;">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <strong>Provider Contact</strong>
                                    <div class="small text-muted">The service provider may contact you to confirm details</div>
                                </div>
                            </li>
                            <li class="next-steps-item">
                                <div class="next-steps-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div>
                                    <strong>Manage Booking</strong>
                                    <div class="small text-muted">View and manage your bookings in your account</div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- Contact Support -->
                    <div class="contact-support slide-in-right" style="animation-delay: 0.2s;">
                        <div class="support-icon pulse-animation">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="mb-3">Need Assistance?</h5>
                        <p class="small text-muted mb-3">Our support team is available 24/7 to help with any questions or concerns.</p>
                        <div class="d-flex gap-2 justify-content-center">
                            <a href="tel:+**********" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-phone me-1"></i>Call
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-envelope me-1"></i>Email
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-comments me-1"></i>Chat
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons fade-in" style="animation-delay: 0.4s;">
                <a href="{% url 'booking_cart_app:booking_list' %}" class="btn btn-primary-gradient">
                    <i class="fas fa-list me-2"></i>View All Bookings
                </a>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-outline-gradient">
                    <i class="fas fa-search me-2"></i>Book More Services
                </a>
                <a href="/" class="btn btn-outline-gradient">
                    <i class="fas fa-home me-2"></i>Back to Home
                </a>
            </div>

            <!-- Social Sharing -->
            <div class="text-center mt-4 fade-in" style="animation-delay: 0.6s;">
                <p class="text-muted mb-3">Share your experience with friends!</p>
                <div class="d-flex gap-2 justify-content-center">
                    <a href="#" class="btn btn-sm btn-outline-secondary" target="_blank">
                        <i class="fab fa-facebook-f me-1"></i>Facebook
                    </a>
                    <a href="#" class="btn btn-sm btn-outline-secondary" target="_blank">
                        <i class="fab fa-twitter me-1"></i>Twitter
                    </a>
                    <a href="#" class="btn btn-sm btn-outline-secondary" target="_blank">
                        <i class="fab fa-instagram me-1"></i>Instagram
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh booking status every 30 seconds
    const statusElement = document.querySelector('.status-badge');
    if (statusElement) {
        setInterval(function() {
            fetch(window.location.href, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newStatus = doc.querySelector('.status-badge');
                if (newStatus && newStatus.textContent !== statusElement.textContent) {
                    statusElement.textContent = newStatus.textContent;
                    statusElement.classList.add('pulse-animation');
                    setTimeout(() => statusElement.classList.remove('pulse-animation'), 2000);
                }
            })
            .catch(err => console.log('Status update failed:', err));
        }, 30000);
    }
    
    // Add to calendar functionality
    const addToCalendarBtn = document.createElement('button');
    addToCalendarBtn.className = 'btn btn-outline-gradient mt-3';
    addToCalendarBtn.innerHTML = '<i class="fas fa-calendar-plus me-2"></i>Add to Calendar';
    addToCalendarBtn.onclick = function() {
        // Generate calendar event data
        const title = 'CozyWish Booking - {{ booking.friendly_id }}';
        const startDate = new Date('{{ booking.items.first.scheduled_date }} {{ booking.items.first.scheduled_time }}');
        const endDate = new Date(startDate.getTime() + ({{ booking.items.first.duration_minutes }} * 60000));
        
        const eventData = {
            title: title,
            start: startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
            end: endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
            description: 'Booking confirmed via CozyWish',
            location: '{{ booking.venue.venue_name }}'
        };
        
        const url = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventData.title)}&dates=${eventData.start}/${eventData.end}&details=${encodeURIComponent(eventData.description)}&location=${encodeURIComponent(eventData.location)}`;
        window.open(url, '_blank');
    };
    
    const actionButtons = document.querySelector('.action-buttons');
    if (actionButtons) {
        actionButtons.appendChild(addToCalendarBtn);
    }
});
</script>
{% endblock %}
