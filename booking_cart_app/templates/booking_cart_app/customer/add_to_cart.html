{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Add to Cart - {{ service.service_title }}{% endblock %}

{% block booking_extra_css %}
<style>
.service-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.service-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: rgba(255,255,255,0.1);
    transform: rotate(15deg);
}

.service-form-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.form-section {
    padding: 2rem;
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    padding: 1rem 0.75rem;
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-floating .form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-floating .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.validation-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.availability-indicator {
    padding: 0.5rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    display: none;
}

.availability-loading {
    background: #e2e3e5;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.availability-available {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.availability-unavailable {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.service-details-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    position: sticky;
    top: 100px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.service-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.service-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.service-detail-item:last-child {
    border-bottom: none;
}

.detail-icon {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

.btn-add-to-cart {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 10px;
    padding: 1.25rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-add-to-cart:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.quantity-controls {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.quantity-controls:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.quantity-btn {
    background: #667eea;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s ease;
}

.quantity-btn:hover {
    background: #5a6fd8;
}

.quantity-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.quantity-input {
    border: none;
    background: transparent;
    text-align: center;
    width: 60px;
    padding: 0.5rem;
    font-weight: 600;
}

.quantity-input:focus {
    outline: none;
}

.calendar-widget {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.calendar-nav {
    background: #667eea;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-nav:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.calendar-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
}

.calendar-day-header {
    text-align: center;
    font-weight: 600;
    color: #6c757d;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.calendar-day.other-month {
    color: #ced4da;
    cursor: not-allowed;
}

.calendar-day.past {
    color: #ced4da;
    cursor: not-allowed;
}

.calendar-day.available {
    background: #e8f5e8;
    color: #28a745;
    border: 2px solid #c3e6cb;
}

.calendar-day.available:hover {
    background: #28a745;
    color: white;
    transform: scale(1.1);
}

.calendar-day.limited {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffeaa7;
}

.calendar-day.limited:hover {
    background: #ffc107;
    color: white;
    transform: scale(1.1);
}

.calendar-day.unavailable {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #f5c6cb;
    cursor: not-allowed;
}

.calendar-day.selected {
    background: #667eea;
    color: white;
    border: 2px solid #5a6fd8;
    transform: scale(1.1);
}

.calendar-day .availability-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.availability-indicator.high {
    background: #28a745;
}

.availability-indicator.medium {
    background: #ffc107;
}

.availability-indicator.low {
    background: #dc3545;
}

.time-slots-section {
    margin-top: 2rem;
}

.time-slots-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.time-slot-filters {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.time-slot-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.time-slot-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #28a745;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.time-slot-card:hover::before,
.time-slot-card.selected::before {
    transform: scaleY(1);
}

.time-slot-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.time-slot-card.selected {
    background: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.time-slot-card.unavailable {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    cursor: not-allowed;
    opacity: 0.6;
}

.time-slot-time {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.time-slot-duration {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.time-slot-price {
    font-size: 1rem;
    font-weight: 600;
    color: #28a745;
}

.time-slot-card.selected .time-slot-price {
    color: #b8f5c9;
}

.time-slot-availability {
    font-size: 0.75rem;
    margin-top: 0.5rem;
    opacity: 0.9;
}

.service-details-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    overflow: hidden;
    position: sticky;
    top: 100px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.service-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.service-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-image:hover {
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.service-rating-overlay {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    color: #ffc107;
    font-size: 0.875rem;
}

.service-content {
    padding: 2rem;
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.service-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.service-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-icon {
    width: 20px;
    text-align: center;
    font-size: 1rem;
}

.feature-label {
    font-weight: 500;
    color: #495057;
    flex: 1;
}

.feature-value {
    font-weight: 600;
    color: #333;
}

.price-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.price-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.price-amount {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.price-period {
    font-size: 0.875rem;
    opacity: 0.8;
}

.provider-info {
    background: #e8f4fd;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.provider-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.provider-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.provider-details h6 {
    margin: 0;
    color: #333;
}

.provider-details small {
    color: #6c757d;
}

.trust-badges {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #28a745;
}

@media (max-width: 768px) {
    .service-header {
        padding: 1.5rem 1rem;
    }
    
    .form-section {
        padding: 1rem;
    }
    
    .service-details-card {
        position: static;
        margin-top: 2rem;
    }
    
    .btn-add-to-cart {
        width: 100%;
    }
    
    .calendar-grid {
        gap: 4px;
    }
    
    .calendar-day {
        font-size: 0.875rem;
    }
    
    .time-slots-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    .time-slot-card {
        padding: 0.75rem;
    }
    
    .service-image-container {
        height: 200px;
    }
    
    .service-content {
        padding: 1.5rem;
    }
    
    .time-slot-filters {
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="service-form-card fade-in">
                <div class="service-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h2 class="mb-2">Add to Cart</h2>
                            <p class="mb-0 opacity-75">Select your preferred date, time, and quantity</p>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <form method="post" id="add-to-cart-form" novalidate>
                        {% csrf_token %}
                        
                        <!-- Date Selection with Interactive Calendar -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar me-2"></i>Select Date
                            </h5>
                            <div class="form-floating">
                                {{ form.selected_date }}
                                <label for="{{ form.selected_date.id_for_label }}">Selected Date</label>
                            </div>
                            
                            <!-- Interactive Calendar Widget -->
                            <div class="calendar-widget" id="calendar-widget">
                                <div class="calendar-header">
                                    <button type="button" class="calendar-nav" id="prev-month">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <h6 class="calendar-title" id="calendar-title">Loading...</h6>
                                    <button type="button" class="calendar-nav" id="next-month">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                                
                                <div class="calendar-grid" id="calendar-grid">
                                    <!-- Day headers -->
                                    <div class="calendar-day-header">Sun</div>
                                    <div class="calendar-day-header">Mon</div>
                                    <div class="calendar-day-header">Tue</div>
                                    <div class="calendar-day-header">Wed</div>
                                    <div class="calendar-day-header">Thu</div>
                                    <div class="calendar-day-header">Fri</div>
                                    <div class="calendar-day-header">Sat</div>
                                    <!-- Calendar days will be populated by JavaScript -->
                                </div>
                            </div>
                            
                            {% if form.selected_date.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                {% for error in form.selected_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Enhanced Time Selection -->
                        <div class="time-slots-section" id="time-slots-section" style="display: none;">
                            <div class="time-slots-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>Select Time Slot
                                </h5>
                                <div class="time-slot-filters">
                                    <button type="button" class="filter-btn active" data-filter="all">All Times</button>
                                    <button type="button" class="filter-btn" data-filter="morning">Morning</button>
                                    <button type="button" class="filter-btn" data-filter="afternoon">Afternoon</button>
                                    <button type="button" class="filter-btn" data-filter="evening">Evening</button>
                                </div>
                            </div>
                            
                            <div class="form-floating" style="display: none;">
                                {{ form.selected_time_slot }}
                                <label for="{{ form.selected_time_slot.id_for_label }}">Selected Time</label>
                            </div>
                            
                            <div class="time-slots-grid" id="time-slots-grid">
                                <!-- Time slots will be populated by JavaScript -->
                            </div>
                            
                            {% if form.selected_time_slot.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                {% for error in form.selected_time_slot.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Quantity Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-users me-2"></i>Number of Appointments
                            </label>
                            <div class="quantity-controls">
                                <button type="button" class="quantity-btn" id="quantity-decrease">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="quantity-input" id="id_quantity" name="quantity" 
                                       value="1" min="1" max="10" readonly>
                                <button type="button" class="quantity-btn" id="quantity-increase">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted mt-2">Maximum 10 appointments per booking</small>
                            {% if form.quantity.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                {% for error in form.quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex gap-3 justify-content-between flex-wrap">
                            <a href="{% url 'venues_app:service_detail' service.id %}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Back to Service
                            </a>
                            <button type="submit" class="btn btn-add-to-cart btn-lg" id="submit-btn" disabled>
                                <span class="btn-text">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="loading-spinner me-2"></span>
                                    Adding...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Enhanced Service Card -->
            <div class="service-details-card fade-in">
                <div class="service-image-container">
                    {% if service.image %}
                    <img src="{{ service.image.url }}" alt="{{ service.service_title }}" class="service-image">
                    {% else %}
                    <div class="service-image" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                        <i class="fas fa-concierge-bell"></i>
                    </div>
                    {% endif %}
                    
                    <div class="service-badge">
                        <i class="fas fa-star me-1"></i>Featured
                    </div>
                    
                    {% if service.average_rating %}
                    <div class="service-rating-overlay">
                        <div class="rating-stars">
                            {% for i in "12345" %}
                                {% if forloop.counter <= service.average_rating %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <span>{{ service.average_rating|floatformat:1 }} ({{ service.review_count }})</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="service-content">
                    <h3 class="service-title">{{ service.service_title }}</h3>
                    <p class="service-description">{{ service.short_description }}</p>
                    
                    <!-- Price Section -->
                    <div class="price-section">
                        <div class="price-label">Starting from</div>
                        <div class="price-amount">
                            ${{ service.price_min }}
                            {% if service.price_max and service.price_max != service.price_min %}
                                <small style="font-size: 1rem; opacity: 0.8;"> - ${{ service.price_max }}</small>
                            {% endif %}
                        </div>
                        <div class="price-period">per appointment</div>
                    </div>
                    
                    <!-- Service Features -->
                    <div class="service-features">
                        <div class="feature-item">
                            <i class="fas fa-map-marker-alt feature-icon text-primary"></i>
                            <span class="feature-label">Venue</span>
                            <span class="feature-value">{{ service.venue.venue_name }}</span>
                        </div>
                        
                        <div class="feature-item">
                            <i class="fas fa-clock feature-icon text-info"></i>
                            <span class="feature-label">Duration</span>
                            <span class="feature-value">{{ service.duration_minutes }} minutes</span>
                        </div>
                        
                        <div class="feature-item">
                            <i class="fas fa-users feature-icon text-success"></i>
                            <span class="feature-label">Max Group Size</span>
                            <span class="feature-value">{{ service.max_capacity|default:"10" }} people</span>
                        </div>
                        
                        <div class="feature-item">
                            <i class="fas fa-tag feature-icon text-warning"></i>
                            <span class="feature-label">Category</span>
                            <span class="feature-value">{{ service.category|default:"Service" }}</span>
                        </div>
                    </div>
                    
                    <!-- Provider Information -->
                    <div class="provider-info">
                        <div class="provider-header">
                            <div class="provider-avatar">
                                {{ service.venue.service_provider.first_name.0|default:"P" }}{{ service.venue.service_provider.last_name.0|default:"" }}
                            </div>
                            <div class="provider-details">
                                <h6>{{ service.venue.service_provider.get_full_name|default:"Professional Provider" }}</h6>
                                <small>{{ service.venue.service_provider.years_experience|default:"5" }}+ years experience</small>
                            </div>
                        </div>
                        
                        <div class="trust-badges">
                            <div class="trust-badge">
                                <i class="fas fa-shield-alt"></i>
                                <span>Verified</span>
                            </div>
                            <div class="trust-badge">
                                <i class="fas fa-certificate"></i>
                                <span>Licensed</span>
                            </div>
                            <div class="trust-badge">
                                <i class="fas fa-heart"></i>
                                <span>Trusted</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if service.cancellation_policy %}
                    <div class="mt-3 p-3 bg-light rounded">
                        <h6 class="mb-2">
                            <i class="fas fa-info-circle me-2"></i>Cancellation Policy
                        </h6>
                        <small class="text-muted">{{ service.cancellation_policy }}</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('add-to-cart-form');
    const dateInput = document.getElementById('id_selected_date');
    const timeSlotSelect = document.getElementById('id_selected_time_slot');
    const quantityInput = document.getElementById('id_quantity');
    const submitBtn = document.getElementById('submit-btn');
    const serviceId = {{ service.id }};
    
    let currentDate = new Date();
    let selectedDate = null;
    let selectedTimeSlot = null;
    let availabilityData = {};
    
    // Initialize calendar
    initCalendar();
    initQuantityControls();
    initTimeSlotFilters();
    
    function initCalendar() {
        updateCalendarDisplay();
        
        document.getElementById('prev-month').addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            updateCalendarDisplay();
        });
        
        document.getElementById('next-month').addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            updateCalendarDisplay();
        });
    }
    
    function updateCalendarDisplay() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        
        // Update title
        document.getElementById('calendar-title').textContent = 
            new Date(year, month).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        
        // Generate calendar days
        generateCalendarDays(year, month);
        
        // Load availability for this month
        loadMonthAvailability(year, month);
    }
    
    function generateCalendarDays(year, month) {
        const grid = document.getElementById('calendar-grid');
        const dayHeaders = grid.querySelectorAll('.calendar-day-header');
        
        // Clear existing days
        const existingDays = grid.querySelectorAll('.calendar-day');
        existingDays.forEach(day => day.remove());
        
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        const today = new Date();
        
        // Add empty cells for previous month days
        for (let i = 0; i < firstDay; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day other-month';
            grid.appendChild(emptyDay);
        }
        
        // Add days of current month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            dayElement.dataset.date = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            
            const dayDate = new Date(year, month, day);
            
            // Mark past days
            if (dayDate <= today) {
                dayElement.classList.add('past');
            } else {
                dayElement.addEventListener('click', () => selectDate(dayElement.dataset.date, dayElement));
            }
            
            grid.appendChild(dayElement);
        }
    }
    
    function loadMonthAvailability(year, month) {
        const startDate = `${year}-${String(month + 1).padStart(2, '0')}-01`;
        const endDate = `${year}-${String(month + 2).padStart(2, '0')}-01`;
        
        fetch(`/bookings/ajax/month-availability/${serviceId}/?start=${startDate}&end=${endDate}`)
            .then(response => response.json())
            .then(data => {
                availabilityData = data.availability || {};
                updateCalendarAvailability();
            })
            .catch(error => console.error('Error loading availability:', error));
    }
    
    function updateCalendarAvailability() {
        document.querySelectorAll('.calendar-day[data-date]').forEach(dayElement => {
            const date = dayElement.dataset.date;
            const availability = availabilityData[date];
            
            if (availability) {
                dayElement.classList.remove('unavailable', 'limited', 'available');
                
                if (availability.total_slots === 0) {
                    dayElement.classList.add('unavailable');
                } else if (availability.available_slots <= availability.total_slots * 0.3) {
                    dayElement.classList.add('limited');
                    dayElement.innerHTML = `${dayElement.textContent}<span class="availability-indicator low"></span>`;
                } else if (availability.available_slots <= availability.total_slots * 0.7) {
                    dayElement.classList.add('available');
                    dayElement.innerHTML = `${dayElement.textContent}<span class="availability-indicator medium"></span>`;
                } else {
                    dayElement.classList.add('available');
                    dayElement.innerHTML = `${dayElement.textContent}<span class="availability-indicator high"></span>`;
                }
            }
        });
    }
    
    function selectDate(date, element) {
        // Remove previous selection
        document.querySelectorAll('.calendar-day.selected').forEach(day => {
            day.classList.remove('selected');
        });
        
        // Select new date
        element.classList.add('selected');
        selectedDate = date;
        dateInput.value = date;
        
        // Load time slots for selected date
        loadTimeSlots(date);
        
        // Show time slots section
        document.getElementById('time-slots-section').style.display = 'block';
        
        validateForm();
    }
    
    function loadTimeSlots(date) {
        const grid = document.getElementById('time-slots-grid');
        grid.innerHTML = '<div class="col-span-full text-center"><div class="loading-spinner"></div><span class="ms-2">Loading available times...</span></div>';
        
        fetch(`/bookings/ajax/slots/${serviceId}/?date=${date}`)
            .then(response => response.json())
            .then(data => {
                displayTimeSlots(data.slots || []);
            })
            .catch(error => {
                console.error('Error loading time slots:', error);
                grid.innerHTML = '<div class="col-span-full text-center text-danger">Error loading time slots. Please try again.</div>';
            });
    }
    
    function displayTimeSlots(slots) {
        const grid = document.getElementById('time-slots-grid');
        grid.innerHTML = '';
        
        if (slots.length === 0) {
            grid.innerHTML = '<div class="col-span-full text-center text-muted">No available time slots for this date.</div>';
            return;
        }
        
        slots.forEach(slot => {
            const slotElement = document.createElement('div');
            slotElement.className = 'time-slot-card';
            slotElement.dataset.time = slot.time;
            slotElement.dataset.filter = getTimeFilter(slot.time);
            
            if (slot.available_spots === 0) {
                slotElement.classList.add('unavailable');
            }
            
            slotElement.innerHTML = `
                <div class="time-slot-time">${slot.display}</div>
                <div class="time-slot-duration">
                    <i class="fas fa-clock me-1"></i>${{ service.duration_minutes }} min
                </div>
                <div class="time-slot-price">
                    <i class="fas fa-dollar-sign me-1"></i>${{ service.price_min }}
                </div>
                <div class="time-slot-availability">
                    ${slot.available_spots > 0 ? `${slot.available_spots} spots available` : 'Fully booked'}
                </div>
            `;
            
            if (slot.available_spots > 0) {
                slotElement.addEventListener('click', () => selectTimeSlot(slot.time, slotElement));
            }
            
            grid.appendChild(slotElement);
        });
        
        // Update time slot select options
        updateTimeSlotSelect(slots);
    }
    
    function getTimeFilter(time) {
        const hour = parseInt(time.split(':')[0]);
        if (hour < 12) return 'morning';
        if (hour < 17) return 'afternoon';
        return 'evening';
    }
    
    function selectTimeSlot(time, element) {
        // Remove previous selection
        document.querySelectorAll('.time-slot-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Select new time slot
        element.classList.add('selected');
        selectedTimeSlot = time;
        timeSlotSelect.value = time;
        
        validateForm();
    }
    
    function updateTimeSlotSelect(slots) {
        timeSlotSelect.innerHTML = '<option value="">Choose a time slot...</option>';
        slots.forEach(slot => {
            const option = document.createElement('option');
            option.value = slot.time;
            option.textContent = `${slot.display} (${slot.available_spots} available)`;
            if (slot.available_spots === 0) {
                option.disabled = true;
            }
            timeSlotSelect.appendChild(option);
        });
    }
    
    function initQuantityControls() {
        const decreaseBtn = document.getElementById('quantity-decrease');
        const increaseBtn = document.getElementById('quantity-increase');
        
        decreaseBtn.addEventListener('click', () => {
            const current = parseInt(quantityInput.value);
            if (current > 1) {
                quantityInput.value = current - 1;
                updateQuantityButtons();
                validateForm();
            }
        });
        
        increaseBtn.addEventListener('click', () => {
            const current = parseInt(quantityInput.value);
            if (current < 10) {
                quantityInput.value = current + 1;
                updateQuantityButtons();
                validateForm();
            }
        });
        
        updateQuantityButtons();
    }
    
    function updateQuantityButtons() {
        const current = parseInt(quantityInput.value);
        document.getElementById('quantity-decrease').disabled = current <= 1;
        document.getElementById('quantity-increase').disabled = current >= 10;
    }
    
    function initTimeSlotFilters() {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // Update active filter
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Filter time slots
                const filter = btn.dataset.filter;
                document.querySelectorAll('.time-slot-card').forEach(card => {
                    if (filter === 'all' || card.dataset.filter === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    }
    
    function validateForm() {
        const dateValid = selectedDate && selectedDate !== '';
        const timeValid = selectedTimeSlot && selectedTimeSlot !== '';
        const quantityValid = quantityInput.value >= 1 && quantityInput.value <= 10;
        
        submitBtn.disabled = !(dateValid && timeValid && quantityValid);
    }
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (submitBtn.disabled) return;
        
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');
        
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        
        setTimeout(() => {
            form.submit();
        }, 1000);
    });
    
    // Initialize validation
    validateForm();
});
</script>
{% endblock %}


