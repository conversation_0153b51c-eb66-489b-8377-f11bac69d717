# Standard library imports
import uuid
from datetime import timedelta, datetime
from decimal import Decimal

# Django imports
from django.db import models, transaction
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils.crypto import get_random_string
from django.core.exceptions import ValidationError

# Local imports
from venues_app.models import Service, Venue


class Cart(models.Model):
    """
    Model for managing customer shopping carts with automatic expiration.
    Each customer can have only one active cart at a time.
    """

    customer = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='cart',
        help_text=_('Customer who owns this cart')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the cart was created')
    )
    expires_at = models.DateTimeField(
        help_text=_('When the cart expires (24 hours after creation)')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When the cart was last updated')
    )

    class Meta:
        verbose_name = _('Cart')
        verbose_name_plural = _('Carts')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.email}'s Cart"

    def save(self, *args, **kwargs):
        """Override save to set expiration time if not already set."""
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if the cart has expired."""
        return timezone.now() > self.expires_at

    @property
    def total_items(self):
        """Get the total number of items in the cart."""
        return self.items.count()

    @property
    def total_price(self):
        """Calculate the total price of all items in the cart."""
        return sum(item.total_price for item in self.items.all())

    def extend_expiration(self):
        """Extend cart expiration by 24 hours from now."""
        self.expires_at = timezone.now() + timedelta(hours=24)
        self.save()

    def clear_expired_items(self):
        """Remove items that are no longer available or have past dates."""
        current_date = timezone.now().date()
        self.items.filter(selected_date__lt=current_date).delete()


class CartItem(models.Model):
    """
    Model for individual items in a customer's cart.
    Each item represents a service booking for a specific date and time.
    """

    cart = models.ForeignKey(
        Cart,
        on_delete=models.CASCADE,
        related_name='items',
        help_text=_('Cart this item belongs to')
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='cart_items',
        help_text=_('Service being booked')
    )
    selected_date = models.DateField(
        help_text=_('Date for the service appointment')
    )
    selected_time_slot = models.TimeField(
        help_text=_('Time slot for the service appointment')
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Number of appointments (max 10)')
    )
    price_per_item = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Price per service including any applicable discounts')
    )
    added_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the item was added to cart')
    )

    class Meta:
        verbose_name = _('Cart Item')
        verbose_name_plural = _('Cart Items')
        ordering = ['selected_date', 'selected_time_slot']
        unique_together = ['cart', 'service', 'selected_date', 'selected_time_slot']

    def __str__(self):
        return f"{self.service.service_title} - {self.selected_date} {self.selected_time_slot}"

    def clean(self):
        """Custom validation for cart items."""
        super().clean()

        # Validate that selected date is in the future
        if self.selected_date and self.selected_date <= timezone.now().date():
            raise ValidationError(_('Selected date must be in the future'))

        # Validate that the service is active (only if service exists)
        if self.service_id:
            try:
                if self.service and not self.service.is_active:
                    raise ValidationError(_('Cannot add inactive service to cart'))
            except CartItem.service.RelatedObjectDoesNotExist:
                pass  # Service will be validated elsewhere

        # Validate that the customer is not a service provider (only if cart exists)
        if self.cart_id:
            try:
                if self.cart and self.cart.customer.is_service_provider:
                    raise ValidationError(_('Service providers cannot add items to cart'))
            except (CartItem.cart.RelatedObjectDoesNotExist, Cart.customer.RelatedObjectDoesNotExist):
                pass  # Cart/customer will be validated elsewhere

    @property
    def total_price(self):
        """Calculate total price for this cart item."""
        return self.price_per_item * self.quantity

    @property
    def venue(self):
        """Get the venue for this cart item."""
        return self.service.venue if self.service else None


class BookingManager(models.Manager):
    """Custom manager with common booking filters."""

    def upcoming_for_user(self, user):
        today = timezone.now().date()
        return (
            self.filter(customer=user, items__scheduled_date__gte=today)
            .prefetch_related('items__service', 'venue')
            .distinct()
        )

    def pending_for_provider(self, provider):
        return (
            self.filter(venue__service_provider=provider, status='pending')
            .prefetch_related('items__service', 'customer')
            .distinct()
        )


class Booking(models.Model):
    """
    Model for confirmed bookings created from cart checkout.
    Tracks the overall booking status and customer information.
    """

    # Booking status choices
    PENDING = 'pending'
    CONFIRMED = 'confirmed'
    CANCELLED = 'cancelled'
    DECLINED = 'declined'
    COMPLETED = 'completed'
    DISPUTED = 'disputed'
    NO_SHOW = 'no_show'

    STATUS_CHOICES = [
        (PENDING, _('Pending')),
        (CONFIRMED, _('Confirmed')),
        (CANCELLED, _('Cancelled')),
        (DECLINED, _('Declined')),
        (COMPLETED, _('Completed')),
        (DISPUTED, _('Disputed')),
        (NO_SHOW, _('No Show')),
    ]

    booking_id = models.UUIDField(
        default=uuid.uuid4,
        editable=False,
        unique=True,
        help_text=_('Unique booking identifier')
    )
    # Customer-friendly booking reference number
    friendly_id = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        help_text=_('Customer-friendly booking reference (e.g., CW-2024-001234)')
    )
    slug = models.SlugField(
        max_length=8,
        unique=True,
        blank=True,
        help_text=_('Short reference code for URLs')
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text=_('Customer who made the booking')
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text=_('Venue where services will be provided')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Current status of the booking')
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Total price for all services in this booking')
    )
    booking_date = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the booking was created')
    )
    notes = models.TextField(
        blank=True,
        max_length=500,
        help_text=_('Additional notes from customer (max 500 characters)')
    )
    cancellation_reason = models.TextField(
        blank=True,
        max_length=500,
        help_text=_('Reason for cancellation (if applicable)')
    )
    dispute_reason = models.TextField(
        blank=True,
        max_length=500,
        help_text=_('Reason for dispute (if applicable)')
    )
    dispute_filed_by = models.CharField(
        max_length=20,
        blank=True,
        choices=[
            ('customer', _('Customer')),
            ('provider', _('Provider')),
            ('admin', _('Admin')),
        ],
        help_text=_('Who filed the dispute')
    )
    dispute_filed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the dispute was filed')
    )
    dispute_resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the dispute was resolved')
    )
    dispute_resolution_notes = models.TextField(
        blank=True,
        max_length=1000,
        help_text=_('Admin notes for dispute resolution')
    )
    last_status_change = models.DateTimeField(
        auto_now=True,
        help_text=_('When the status was last changed')
    )

    objects = BookingManager()

    class Meta:
        verbose_name = _('Booking')
        verbose_name_plural = _('Bookings')
        ordering = ['-booking_date']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['booking_date']),
            models.Index(fields=['friendly_id']),
        ]

    def __str__(self):
        return f"Booking {self.friendly_id or self.slug or self.booking_id} - {self.customer.email}"

    def save(self, *args, **kwargs):
        # Generate friendly booking ID if not set
        if not self.friendly_id:
            self.friendly_id = self._generate_friendly_id()
        
        # Generate slug for URLs if not set
        if not self.slug:
            base = get_random_string(8, allowed_chars='abcdefghijklmnopqrstuvwxyz0123456789')
            while Booking.objects.filter(slug=base).exists():
                base = get_random_string(8, allowed_chars='abcdefghijklmnopqrstuvwxyz0123456789')
            self.slug = base
        super().save(*args, **kwargs)

    def _generate_friendly_id(self):
        """Generate a customer-friendly booking reference number."""
        current_year = timezone.now().year
        prefix = f"CW-{current_year}-"
        
        # Get the highest existing number for this year
        with transaction.atomic():
            existing_bookings = Booking.objects.filter(
                friendly_id__startswith=prefix
            ).order_by('friendly_id').last()
            
            if existing_bookings and existing_bookings.friendly_id:
                try:
                    # Extract the number part and increment
                    last_number = int(existing_bookings.friendly_id.split('-')[-1])
                    next_number = last_number + 1
                except (ValueError, IndexError):
                    next_number = 1
            else:
                next_number = 1
            
            # Format with leading zeros (6 digits)
            friendly_id = f"{prefix}{next_number:06d}"
            
            # Ensure uniqueness (double-check)
            while Booking.objects.filter(friendly_id=friendly_id).exists():
                next_number += 1
                friendly_id = f"{prefix}{next_number:06d}"
            
            return friendly_id

    def clean(self):
        """Custom validation for bookings."""
        super().clean()

        # Validate that customer is not a service provider (only if customer exists)
        if self.customer_id:
            try:
                if self.customer and self.customer.is_service_provider:
                    raise ValidationError(_('Service providers cannot create bookings'))
            except Booking.customer.RelatedObjectDoesNotExist:
                pass  # Customer will be validated elsewhere

        # Validate notes field length
        if self.notes and len(self.notes) > 500:
            raise ValidationError(_('Notes cannot exceed 500 characters'))

    @property
    def can_be_cancelled(self):
        """Check if booking can be cancelled with enhanced logic."""
        if self.status in [self.CANCELLED, self.COMPLETED, self.DECLINED]:
            return False
        
        now = timezone.now()
        
        # Find the earliest scheduled service time
        earliest_service = self.items.order_by('scheduled_date', 'scheduled_time').first()
        if not earliest_service:
            return False
        
        # Create datetime object for the earliest service
        service_datetime = timezone.make_aware(
            datetime.combine(earliest_service.scheduled_date, earliest_service.scheduled_time)
        )
        
        # Enhanced cancellation policy:
        # - Can cancel up to 6 hours before service time
        # - OR within 2 hours of booking creation (for immediate booking regret)
        time_until_service = service_datetime - now
        time_since_booking = now - self.booking_date
        
        # Allow cancellation if:
        # 1. More than 6 hours until service
        # 2. OR less than 2 hours since booking was made (grace period)
        can_cancel_by_time = time_until_service.total_seconds() > 6 * 3600  # 6 hours
        can_cancel_grace_period = time_since_booking.total_seconds() <= 2 * 3600  # 2 hours
        
        return can_cancel_by_time or can_cancel_grace_period

    @property
    def cancellation_deadline(self):
        """Get the cancellation deadline for this booking."""
        earliest_service = self.items.order_by('scheduled_date', 'scheduled_time').first()
        if not earliest_service:
            return None
        
        service_datetime = timezone.make_aware(
            datetime.combine(earliest_service.scheduled_date, earliest_service.scheduled_time)
        )
        
        # 6 hours before service time
        deadline = service_datetime - timedelta(hours=6)
        
        # But also check the 2-hour grace period from booking
        grace_deadline = self.booking_date + timedelta(hours=2)
        
        # Return the later of the two deadlines
        return max(deadline, grace_deadline)

    @property
    def cancellation_reason_display(self):
        """Get user-friendly cancellation reason."""
        if not self.can_be_cancelled:
            earliest_service = self.items.order_by('scheduled_date', 'scheduled_time').first()
            if earliest_service:
                service_datetime = timezone.make_aware(
                    datetime.combine(earliest_service.scheduled_date, earliest_service.scheduled_time)
                )
                now = timezone.now()
                time_until_service = service_datetime - now
                
                if time_until_service.total_seconds() <= 6 * 3600:
                    hours_left = max(0, time_until_service.total_seconds() / 3600)
                    if hours_left < 1:
                        return "Cannot cancel - service starts in less than 1 hour"
                    return f"Cannot cancel - only {hours_left:.1f} hours until service (minimum 6 hours required)"
            return "Cannot cancel - booking deadline has passed"
        return "Can be cancelled"

    @property
    def service_provider(self):
        """Get the service provider for this booking."""
        return self.venue.service_provider if self.venue else None

    def cancel_booking(self, reason=''):
        """Cancel the booking with optional reason."""
        if not self.can_be_cancelled:
            raise ValidationError(_('Booking cannot be cancelled'))
        old = self.status
        self.status = self.CANCELLED
        self.cancellation_reason = reason
        self.save()
        BookingStatusHistory.objects.create(
            booking=self,
            old_status=old,
            new_status=self.CANCELLED,
        )

    def confirm_booking(self):
        """Confirm the booking (provider action)."""
        if self.status != self.PENDING:
            raise ValidationError(_('Only pending bookings can be confirmed'))
        old = self.status
        self.status = self.CONFIRMED
        self.save()
        BookingStatusHistory.objects.create(
            booking=self,
            old_status=old,
            new_status=self.CONFIRMED,
        )

    def decline_booking(self, reason=''):
        """Decline the booking (provider action)."""
        if self.status != self.PENDING:
            raise ValidationError(_('Only pending bookings can be declined'))
        old = self.status
        self.status = self.DECLINED
        self.cancellation_reason = reason
        self.save()
        BookingStatusHistory.objects.create(
            booking=self,
            old_status=old,
            new_status=self.DECLINED,
        )

    def file_dispute(self, reason='', filed_by='customer'):
        """File a dispute for this booking."""
        if self.status in [self.CANCELLED, self.DECLINED]:
            raise ValidationError(_('Cannot file dispute for cancelled or declined bookings'))
        old = self.status
        self.status = self.DISPUTED
        self.dispute_reason = reason
        self.dispute_filed_by = filed_by
        self.dispute_filed_at = timezone.now()
        self.save()
        BookingStatusHistory.objects.create(
            booking=self,
            old_status=old,
            new_status=self.DISPUTED,
        )

    def resolve_dispute(self, resolution_notes='', new_status=None):
        """Resolve a dispute (admin action)."""
        if self.status != self.DISPUTED:
            raise ValidationError(_('Only disputed bookings can be resolved'))

        self.dispute_resolved_at = timezone.now()
        self.dispute_resolution_notes = resolution_notes

        if new_status and new_status in [choice[0] for choice in self.STATUS_CHOICES]:
            old = self.status
            self.status = new_status
        else:
            old = self.status
            self.status = self.CONFIRMED  # Default resolution

        self.save()
        BookingStatusHistory.objects.create(
            booking=self,
            old_status=old,
            new_status=self.status,
        )

    @property
    def is_disputed(self):
        """Check if booking is currently disputed."""
        return self.status == self.DISPUTED

    @property
    def has_unresolved_dispute(self):
        """Check if booking has an unresolved dispute."""
        return self.status == self.DISPUTED and not self.dispute_resolved_at


class BookingItem(models.Model):
    """
    Model for individual services within a booking.
    Allows multiple services to be booked together in one booking.
    """

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='items',
        help_text=_('Booking this item belongs to')
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='booking_items',
        help_text=_('Service being booked')
    )
    service_title = models.CharField(
        max_length=255,
        help_text=_('Service title at time of booking (for record keeping)')
    )
    service_price = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Service price at time of booking')
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Number of appointments for this service')
    )
    scheduled_date = models.DateField(
        help_text=_('Date when service is scheduled')
    )
    scheduled_time = models.TimeField(
        help_text=_('Time when service is scheduled')
    )
    duration_minutes = models.PositiveIntegerField(
        help_text=_('Duration of service in minutes (copied from service)')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When this booking item was created')
    )

    class Meta:
        verbose_name = _('Booking Item')
        verbose_name_plural = _('Booking Items')
        ordering = ['scheduled_date', 'scheduled_time']
        unique_together = [
            ('booking', 'service', 'scheduled_date', 'scheduled_time')
        ]

    def __str__(self):
        return f"{self.service_title} - {self.scheduled_date} {self.scheduled_time}"

    def clean(self):
        """Custom validation for booking items."""
        super().clean()

        # Validate that scheduled date is in the future
        if self.scheduled_date and self.scheduled_date <= timezone.now().date():
            raise ValidationError(_('Scheduled date must be in the future'))

        # Validate that service belongs to the same venue as booking
        if self.service and self.booking and self.service.venue != self.booking.venue:
            raise ValidationError(_('Service must belong to the same venue as the booking'))

    @property
    def total_price(self):
        """Calculate total price for this booking item."""
        return self.service_price * self.quantity

    @property
    def end_time(self):
        """Calculate the end time of the service."""
        from datetime import datetime, time
        start_datetime = datetime.combine(self.scheduled_date, self.scheduled_time)
        end_datetime = start_datetime + timedelta(minutes=self.duration_minutes)
        return end_datetime.time()


class ServiceAvailability(models.Model):
    """
    Model for managing service provider availability slots.
    Providers can set available time slots for their services.
    """

    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='availability_slots',
        help_text=_('Service this availability is for')
    )
    available_date = models.DateField(
        help_text=_('Date when service is available'),
        db_index=True
    )
    start_time = models.TimeField(
        help_text=_('Start time of availability slot')
    )
    end_time = models.TimeField(
        help_text=_('End time of availability slot')
    )
    max_bookings = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        help_text=_('Maximum number of bookings for this slot')
    )
    current_bookings = models.PositiveIntegerField(
        default=0,
        help_text=_('Current number of confirmed bookings for this slot')
    )
    is_available = models.BooleanField(
        default=True,
        help_text=_('Whether this slot is available for booking')
    )
    
    # Enhanced fields for automated scheduling
    is_recurring = models.BooleanField(
        default=False,
        help_text=_('Whether this slot was created from a recurring pattern')
    )
    recurring_pattern = models.ForeignKey(
        'RecurringAvailabilityPattern',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_slots',
        help_text=_('Recurring pattern that generated this slot')
    )
    is_exception = models.BooleanField(
        default=False,
        help_text=_('Whether this slot is an exception to the normal schedule')
    )
    exception_reason = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Reason for the schedule exception')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When this availability slot was created')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When this availability slot was last updated')
    )

    class Meta:
        verbose_name = _('Service Availability')
        verbose_name_plural = _('Service Availabilities')
        ordering = ['available_date', 'start_time']
        unique_together = ['service', 'available_date', 'start_time']
        indexes = [
            models.Index(fields=['available_date']),
            models.Index(fields=['is_recurring']),
            models.Index(fields=['recurring_pattern']),
        ]

    def __str__(self):
        return f"{self.service.service_title} - {self.available_date} {self.start_time}-{self.end_time}"

    def clean(self):
        """Custom validation for service availability."""
        super().clean()

        # Validate that available date is in the future
        if self.available_date and self.available_date <= timezone.now().date():
            raise ValidationError(_('Available date must be in the future'))

        # Validate that end time is after start time
        if self.start_time and self.end_time and self.end_time <= self.start_time:
            raise ValidationError(_('End time must be after start time'))

        # Validate no overlapping time ranges for the same service (only if service exists)
        if self.service_id and self.available_date and self.start_time and self.end_time:
            try:
                service = self.service
                overlapping = ServiceAvailability.objects.filter(
                    service=self.service,
                    available_date=self.available_date,
                ).exclude(pk=self.pk)

                for slot in overlapping:
                    if self.start_time < slot.end_time and self.end_time > slot.start_time:
                        raise ValidationError(_('This time range overlaps with an existing availability slot'))
            except ServiceAvailability.service.RelatedObjectDoesNotExist:
                pass  # Service will be validated elsewhere

        # Validate that current bookings don't exceed max bookings
        if self.current_bookings > self.max_bookings:
            raise ValidationError(_('Current bookings cannot exceed maximum bookings'))

    @property
    def is_fully_booked(self):
        """Check if this availability slot is fully booked."""
        return self.current_bookings >= self.max_bookings

    @property
    def available_spots(self):
        """Get number of available spots remaining."""
        return max(0, self.max_bookings - self.current_bookings)

    @property
    def duration_minutes(self):
        """Calculate the duration of this availability slot in minutes."""
        from datetime import datetime, time
        start_datetime = datetime.combine(self.available_date, self.start_time)
        end_datetime = datetime.combine(self.available_date, self.end_time)
        return int((end_datetime - start_datetime).total_seconds() / 60)

    def book_slot(self):
        """Book one spot in this availability slot."""
        with transaction.atomic():
            slot = ServiceAvailability.objects.select_for_update().get(pk=self.pk)
            if slot.current_bookings >= slot.max_bookings:
                raise ValidationError(_('This time slot is fully booked'))
            slot.current_bookings += 1
            slot.save()
            self.current_bookings = slot.current_bookings

    def cancel_booking(self):
        """Cancel one booking from this availability slot."""
        with transaction.atomic():
            slot = ServiceAvailability.objects.select_for_update().get(pk=self.pk)
            if slot.current_bookings <= 0:
                raise ValidationError(_('No bookings to cancel'))
            slot.current_bookings -= 1
            slot.save()
            self.current_bookings = slot.current_bookings


# --- Recurring Availability Pattern Model ---
class RecurringAvailabilityPattern(models.Model):
    """
    Model for defining recurring availability patterns for services.
    Allows automated generation of availability slots.
    """
    
    DAILY = 'daily'
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    
    PATTERN_TYPES = [
        (DAILY, _('Daily')),
        (WEEKLY, _('Weekly')),
        (MONTHLY, _('Monthly')),
    ]

    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='recurring_patterns',
        help_text=_('Service this pattern applies to')
    )
    name = models.CharField(
        max_length=100,
        help_text=_('Name for this recurring pattern')
    )
    pattern_type = models.CharField(
        max_length=20,
        choices=PATTERN_TYPES,
        help_text=_('Type of recurring pattern')
    )
    
    # Time configuration
    start_time = models.TimeField(
        help_text=_('Start time for availability slots')
    )
    end_time = models.TimeField(
        help_text=_('End time for availability slots')
    )
    slot_duration_minutes = models.PositiveIntegerField(
        default=60,
        validators=[MinValueValidator(15), MaxValueValidator(480)],
        help_text=_('Duration of each slot in minutes')
    )
    break_between_slots = models.PositiveIntegerField(
        default=0,
        validators=[MaxValueValidator(120)],
        help_text=_('Break time between slots in minutes')
    )
    
    # Days configuration for weekly patterns
    monday = models.BooleanField(default=True, verbose_name=_('Monday'))
    tuesday = models.BooleanField(default=True, verbose_name=_('Tuesday'))
    wednesday = models.BooleanField(default=True, verbose_name=_('Wednesday'))
    thursday = models.BooleanField(default=True, verbose_name=_('Thursday'))
    friday = models.BooleanField(default=True, verbose_name=_('Friday'))
    saturday = models.BooleanField(default=False, verbose_name=_('Saturday'))
    sunday = models.BooleanField(default=False, verbose_name=_('Sunday'))
    
    # Date range for pattern application
    start_date = models.DateField(
        help_text=_('Start date for pattern application')
    )
    end_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('End date for pattern application (leave blank for ongoing)')
    )
    
    # Booking limits
    max_bookings_per_slot = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        help_text=_('Maximum bookings per generated slot')
    )
    
    # Pattern settings
    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether this pattern is actively generating slots')
    )
    generate_advance_days = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        help_text=_('How many days in advance to generate slots')
    )
    
    # Exclusions
    exclude_holidays = models.BooleanField(
        default=False,
        help_text=_('Whether to exclude holidays from generation')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_generated = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When slots were last generated for this pattern')
    )

    class Meta:
        verbose_name = _('Recurring Availability Pattern')
        verbose_name_plural = _('Recurring Availability Patterns')
        ordering = ['service', 'name']

    def __str__(self):
        return f"{self.service.service_title} - {self.name}"

    def clean(self):
        """Validate the recurring pattern."""
        super().clean()
        
        if self.start_time >= self.end_time:
            raise ValidationError(_('End time must be after start time'))
        
        if self.end_date and self.end_date <= self.start_date:
            raise ValidationError(_('End date must be after start date'))
        
        if self.pattern_type == self.WEEKLY:
            selected_days = [
                self.monday, self.tuesday, self.wednesday, self.thursday,
                self.friday, self.saturday, self.sunday
            ]
            if not any(selected_days):
                raise ValidationError(_('At least one day must be selected for weekly patterns'))

    @property
    def selected_weekdays(self):
        """Get list of selected weekdays (0=Monday, 6=Sunday)."""
        days = []
        if self.monday: days.append(0)
        if self.tuesday: days.append(1)
        if self.wednesday: days.append(2)
        if self.thursday: days.append(3)
        if self.friday: days.append(4)
        if self.saturday: days.append(5)
        if self.sunday: days.append(6)
        return days

    def generate_slots(self, target_date=None):
        """Generate availability slots for this pattern."""
        from datetime import datetime, timedelta
        
        if not self.is_active:
            return 0
        
        if not target_date:
            target_date = timezone.now().date()
        
        # Generate slots up to the advance booking limit
        end_generation_date = target_date + timedelta(days=self.generate_advance_days)
        
        if self.end_date and end_generation_date > self.end_date:
            end_generation_date = self.end_date
        
        slots_created = 0
        current_date = max(target_date, self.start_date)
        
        while current_date <= end_generation_date:
            # Check if this date should have slots based on pattern
            should_generate = False
            
            if self.pattern_type == self.DAILY:
                should_generate = True
            elif self.pattern_type == self.WEEKLY:
                weekday = current_date.weekday()  # 0=Monday, 6=Sunday
                should_generate = weekday in self.selected_weekdays
            elif self.pattern_type == self.MONTHLY:
                # Generate on the same day of month as start_date
                should_generate = current_date.day == self.start_date.day
            
            if should_generate and not self._is_excluded_date(current_date):
                slots_created += self._generate_slots_for_date(current_date)
            
            current_date += timedelta(days=1)
        
        self.last_generated = timezone.now()
        self.save(update_fields=['last_generated'])
        
        return slots_created

    def _is_excluded_date(self, date):
        """Check if a date should be excluded from generation."""
        # Check for existing pattern exclusions
        if PatternDateExclusion.objects.filter(pattern=self, excluded_date=date).exists():
            return True
        
        # Check for holidays if enabled
        if self.exclude_holidays:
            # You can implement holiday checking logic here
            # For now, we'll skip this
            pass
        
        return False

    def _generate_slots_for_date(self, date):
        """Generate individual time slots for a specific date."""
        from datetime import datetime, timedelta
        
        slots_created = 0
        current_time = self.start_time
        
        while current_time < self.end_time:
            # Calculate end time for this slot
            start_datetime = datetime.combine(date, current_time)
            end_datetime = start_datetime + timedelta(minutes=self.slot_duration_minutes)
            slot_end_time = end_datetime.time()
            
            # Don't create slots that extend beyond the pattern end time
            if slot_end_time > self.end_time:
                break
            
            # Check if slot already exists
            existing_slot = ServiceAvailability.objects.filter(
                service=self.service,
                available_date=date,
                start_time=current_time
            ).first()
            
            if not existing_slot:
                ServiceAvailability.objects.create(
                    service=self.service,
                    available_date=date,
                    start_time=current_time,
                    end_time=slot_end_time,
                    max_bookings=self.max_bookings_per_slot,
                    is_recurring=True,
                    recurring_pattern=self
                )
                slots_created += 1
            
            # Move to next slot
            current_time = (end_datetime + timedelta(minutes=self.break_between_slots)).time()
        
        return slots_created


# --- Pattern Date Exclusion Model ---
class PatternDateExclusion(models.Model):
    """
    Model for excluding specific dates from recurring patterns.
    Useful for holidays, vacations, or special events.
    """
    
    pattern = models.ForeignKey(
        RecurringAvailabilityPattern,
        on_delete=models.CASCADE,
        related_name='date_exclusions',
        help_text=_('Pattern to exclude date from')
    )
    excluded_date = models.DateField(
        help_text=_('Date to exclude from pattern generation')
    )
    reason = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Reason for excluding this date')
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Pattern Date Exclusion')
        verbose_name_plural = _('Pattern Date Exclusions')
        unique_together = ['pattern', 'excluded_date']
        ordering = ['excluded_date']

    def __str__(self):
        return f"{self.pattern.name} - Exclude {self.excluded_date}"


# --- Service Availability Template Model ---
class ServiceAvailabilityTemplate(models.Model):
    """
    Model for saving and reusing common availability configurations.
    """
    
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='availability_templates',
        help_text=_('Service this template applies to')
    )
    name = models.CharField(
        max_length=100,
        help_text=_('Name for this availability template')
    )
    description = models.TextField(
        blank=True,
        help_text=_('Description of this template')
    )
    
    # Template configuration (JSON field)
    configuration = models.JSONField(
        help_text=_('JSON configuration for availability settings')
    )
    
    is_default = models.BooleanField(
        default=False,
        help_text=_('Whether this is the default template for the service')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Service Availability Template')
        verbose_name_plural = _('Service Availability Templates')
        unique_together = ['service', 'name']
        ordering = ['service', 'name']

    def __str__(self):
        return f"{self.service.service_title} - {self.name}"

    def clean(self):
        """Ensure only one default template per service."""
        super().clean()
        if self.is_default and self.service_id:
            existing_default = ServiceAvailabilityTemplate.objects.filter(
                service=self.service,
                is_default=True
            ).exclude(pk=self.pk)
            if existing_default.exists():
                raise ValidationError(_('Only one default template allowed per service'))


class BookingStatusHistory(models.Model):
    """Record history of booking status changes."""

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name="status_history",
    )
    old_status = models.CharField(max_length=20)
    new_status = models.CharField(max_length=20)
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-changed_at"]
        verbose_name = _("Booking Status History")
        verbose_name_plural = _("Booking Status Histories")

    def __str__(self):
        return f"{self.booking} {self.old_status}→{self.new_status}"
