"""
Tests package for booking_cart_app.

This package contains all unit and integration tests for the booking_cart_app.
All test modules are imported here to ensure proper test discovery.
"""

# Import all test modules to ensure they are discovered by <PERSON><PERSON><PERSON>'s test runner
from .test_models import (
    CartModelTest, CartItemModelTest, BookingModelTest,
    BookingItemModelTest, ServiceAvailabilityModelTest
)
from .test_forms import (
    AddToCartFormTest, CheckoutFormTest, ServiceAvailabilityFormTest
)
from .test_views import (
    CustomerViewsTest, ProviderViewsTest
)
from .test_utils import (
    CartUtilityTest, BookingUtilityTest, AvailabilityUtilityTest
)
from .test_urls import (
    BookingCartURLsTest, URLAccessTest
)
from .test_integration import (
    CustomerBookingWorkflowIntegrationTest, ProviderBookingManagementIntegrationTest,
    AdminBookingManagementIntegrationTest, CrossAppIntegrationTest,
    SecurityPermissionIntegrationTest, RealTimeAvailabilityIntegrationTest
)
