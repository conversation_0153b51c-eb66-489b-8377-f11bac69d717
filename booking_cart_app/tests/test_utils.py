"""
Unit tests for booking_cart_app utilities.

This module contains comprehensive unit tests for utility functions and helper classes
in the booking_cart_app, including cart management utilities, booking utilities, and
availability management utilities.
"""

# Standard library imports
from datetime import timedelta, time
from decimal import Decimal

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError

# Local imports
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category, Venue, Service, VenueCategory
from booking_cart_app.models import Cart, CartItem, Booking, BookingItem, ServiceAvailability
from booking_cart_app.utils import (
    get_or_create_cart, clean_expired_cart_items, get_cart_total,
    check_service_availability, get_available_time_slots,
    create_booking_from_cart, get_upcoming_bookings, get_past_bookings,
    can_cancel_booking, get_booking_analytics
)

User = get_user_model()


class CartUtilityTest(TestCase):
    """Test cart utility functions."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

    def test_get_or_create_cart(self):
        """Test get_or_create_cart utility function."""
        # Get cart for customer (should create new one)
        cart = get_or_create_cart(self.customer)
        self.assertEqual(cart.customer, self.customer)

        # Get cart again (should return existing one)
        cart2 = get_or_create_cart(self.customer)
        self.assertEqual(cart, cart2)

    def test_get_or_create_cart_invalid_user(self):
        """Test get_or_create_cart with invalid user."""
        # Test with service provider
        with self.assertRaises(ValidationError):
            get_or_create_cart(self.provider)

    def test_get_cart_total(self):
        """Test get_cart_total utility function."""
        # Create cart with items
        cart = Cart.objects.create(customer=self.customer)
        CartItem.objects.create(
            cart=cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=time(10, 0),
            quantity=2,
            price_per_item=Decimal("100.00")
        )
        CartItem.objects.create(
            cart=cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=2),
            selected_time_slot=time(11, 0),
            quantity=1,
            price_per_item=Decimal("75.00")
        )

        total = get_cart_total(self.customer)
        expected_total = Decimal("275.00")  # (2 * 100.00) + (1 * 75.00)
        self.assertEqual(total, expected_total)

    def test_get_cart_total_no_cart(self):
        """Test get_cart_total with no cart."""
        total = get_cart_total(self.customer)
        self.assertEqual(total, 0)

    def test_clean_expired_cart_items(self):
        """Test clean_expired_cart_items utility function."""
        # Create expired cart
        expired_cart = Cart.objects.create(
            customer=self.customer,
            expires_at=timezone.now() - timedelta(hours=1)
        )
        CartItem.objects.create(
            cart=expired_cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=time(10, 0),
            quantity=1,
            price_per_item=Decimal("100.00")
        )

        # Clean expired items
        cleaned_count = clean_expired_cart_items()
        self.assertGreaterEqual(cleaned_count, 0)

        # Check that expired cart was deleted
        self.assertFalse(Cart.objects.filter(id=expired_cart.id).exists())


class BookingUtilityTest(TestCase):
    """Test booking utility functions."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

        # Create cart with items
        self.cart = Cart.objects.create(customer=self.customer)
        self.cart_item = CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=time(10, 0),
            quantity=1,
            price_per_item=Decimal("100.00")
        )

    def test_create_booking_from_cart(self):
        """Test create_booking_from_cart utility function."""
        bookings = create_booking_from_cart(
            cart=self.cart,
            notes="Please use lavender oil"
        )

        self.assertEqual(len(bookings), 1)
        booking = bookings[0]

        self.assertEqual(booking.customer, self.customer)
        self.assertEqual(booking.venue, self.venue)
        self.assertEqual(booking.total_price, Decimal("100.00"))
        self.assertEqual(booking.notes, "Please use lavender oil")
        self.assertEqual(booking.status, Booking.PENDING)

        # Check that booking items were created
        self.assertEqual(booking.items.count(), 1)
        booking_item = booking.items.first()
        self.assertEqual(booking_item.service, self.service)

    def test_create_booking_from_empty_cart(self):
        """Test create_booking_from_cart with empty cart."""
        # Clear cart items
        self.cart.items.all().delete()

        with self.assertRaises(ValidationError):
            create_booking_from_cart(self.cart)

    def test_can_cancel_booking(self):
        """Test can_cancel_booking utility function."""
        # Create recent booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING
        )

        # Should be cancellable within 6 hours
        self.assertTrue(can_cancel_booking(booking))

        # Create old booking and manually set booking_date to 7 hours ago
        # Note: booking_date has auto_now_add=True, so we need to update it after creation
        old_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING
        )

        # Update the booking_date to 7 hours ago (bypassing auto_now_add)
        Booking.objects.filter(id=old_booking.id).update(
            booking_date=timezone.now() - timedelta(hours=7)
        )
        # Refresh the object to get the updated booking_date
        old_booking.refresh_from_db()

        # Should not be cancellable after 6 hours
        self.assertFalse(can_cancel_booking(old_booking))

    def test_get_upcoming_bookings(self):
        """Test get_upcoming_bookings utility function."""
        # Create upcoming booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED
        )
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=time(10, 0),
            duration_minutes=60
        )

        upcoming = get_upcoming_bookings(self.customer)
        self.assertEqual(upcoming.count(), 1)
        self.assertEqual(upcoming.first(), booking)

    def test_get_past_bookings(self):
        """Test get_past_bookings utility function."""
        # Create past booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.COMPLETED
        )
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=timezone.now().date() - timedelta(days=1),
            scheduled_time=time(10, 0),
            duration_minutes=60
        )

        past = get_past_bookings(self.customer)
        self.assertEqual(past.count(), 1)
        self.assertEqual(past.first(), booking)


class AvailabilityUtilityTest(TestCase):
    """Test availability utility functions."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

    def test_check_service_availability(self):
        """Test check_service_availability utility function."""
        # Test with no availability record (should be available)
        availability_date = timezone.now().date() + timedelta(days=1)
        is_available, message = check_service_availability(
            self.service,
            availability_date,
            time(10, 0),
            1
        )
        self.assertTrue(is_available)
        self.assertEqual(message, "Available")

        # Create availability record
        ServiceAvailability.objects.create(
            service=self.service,
            available_date=availability_date,
            start_time=time(10, 0),
            end_time=time(11, 0),
            max_bookings=5,
            current_bookings=2,
            is_available=True
        )

        # Test with available slot
        is_available, message = check_service_availability(
            self.service,
            availability_date,
            time(10, 0),
            1
        )
        self.assertTrue(is_available)
        self.assertEqual(message, "Available")

        # Test with fully booked slot
        availability = ServiceAvailability.objects.get(
            service=self.service,
            available_date=availability_date,
            start_time=time(10, 0)
        )
        availability.current_bookings = 5  # Make it fully booked
        availability.save()

        is_available, message = check_service_availability(
            self.service,
            availability_date,
            time(10, 0),
            1
        )
        self.assertFalse(is_available)
        self.assertIn("fully booked", message.lower())

    def test_get_available_time_slots(self):
        """Test get_available_time_slots utility function."""
        # Test with no availability records (should return default slots)
        availability_date = timezone.now().date() + timedelta(days=1)
        available_slots = get_available_time_slots(self.service, availability_date)
        self.assertIsInstance(available_slots, list)
        self.assertGreater(len(available_slots), 0)

        # Create availability record
        availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=availability_date,
            start_time=time(9, 0),
            end_time=time(10, 0),
            max_bookings=5,
            current_bookings=2,
            is_available=True
        )

        # Clear cache to ensure fresh data
        from django.core.cache import cache
        cache_key = f"avail-slots:{self.service.id}:{availability_date.isoformat()}"
        cache.delete(cache_key)

        available_slots = get_available_time_slots(self.service, availability_date)
        self.assertIsInstance(available_slots, list)
        self.assertGreater(len(available_slots), 0)

        # Check that the slot has correct available spots
        slot_time, available_spots = available_slots[0]
        self.assertEqual(slot_time, time(9, 0))
        self.assertEqual(available_spots, 3)  # 5 - 2

    def test_get_booking_analytics(self):
        """Test get_booking_analytics utility function."""
        # Create some bookings
        booking1 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED
        )
        booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("150.00"),
            status=Booking.CANCELLED
        )

        analytics = get_booking_analytics()

        self.assertIsInstance(analytics, dict)
        self.assertIn('total_bookings', analytics)
        self.assertIn('confirmed_bookings', analytics)
        self.assertIn('cancelled_bookings', analytics)
        self.assertIn('total_revenue', analytics)
        self.assertIn('cancellation_rate', analytics)

        self.assertEqual(analytics['total_bookings'], 2)
        self.assertEqual(analytics['confirmed_bookings'], 1)
        self.assertEqual(analytics['cancelled_bookings'], 1)

    def test_get_booking_analytics_filtered(self):
        """Test get_booking_analytics with filters."""
        # Create booking for specific user
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED
        )

        # Get analytics for specific user
        analytics = get_booking_analytics(user=self.customer)

        self.assertEqual(analytics['total_bookings'], 1)
        self.assertEqual(analytics['confirmed_bookings'], 1)

        # Get analytics for specific venue
        analytics = get_booking_analytics(venue=self.venue)

        self.assertEqual(analytics['total_bookings'], 1)
        self.assertEqual(analytics['confirmed_bookings'], 1)
