"""
Unit tests for booking_cart_app views.

This module contains comprehensive unit tests for all view classes and functions
in the booking_cart_app, including customer views, provider views, and admin views.
"""

# Standard library imports
from datetime import timedelta
from decimal import Decimal

# Django imports
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from django.contrib.messages import get_messages

# Local imports
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category, Venue, Service, VenueCategory
from booking_cart_app.models import Cart, CartItem, Booking, BookingItem, ServiceAvailability

User = get_user_model()


class CustomerViewsTest(TestCase):
    """Test customer-related views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        # Create provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

    def test_cart_view_authenticated_customer(self):
        """Test cart view for authenticated customer."""
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.get(reverse('booking_cart_app:cart_view'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Your Cart')

    def test_cart_view_unauthenticated_user(self):
        """Test cart view for unauthenticated user."""
        response = self.client.get(reverse('booking_cart_app:cart_view'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_add_to_cart_view_post(self):
        """Test add to cart view with POST request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        post_data = {
            'selected_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'selected_time_slot': '10:00',
            'quantity': 1
        }
        
        response = self.client.post(
            reverse('booking_cart_app:add_to_cart', kwargs={'service_id': self.service.id}),
            data=post_data
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful add
        
        # Check that cart item was created
        cart = Cart.objects.get(customer=self.customer)
        self.assertEqual(cart.items.count(), 1)

    def test_remove_from_cart_view(self):
        """Test remove from cart view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create cart and cart item
        cart = Cart.objects.create(customer=self.customer)
        cart_item = CartItem.objects.create(
            cart=cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00")
        )
        
        response = self.client.post(
            reverse('booking_cart_app:remove_from_cart', kwargs={'item_id': cart_item.id})
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after removal
        self.assertEqual(cart.items.count(), 0)  # Item should be removed

    def test_checkout_view_get(self):
        """Test checkout view GET request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create cart with items
        cart = Cart.objects.create(customer=self.customer)
        CartItem.objects.create(
            cart=cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00")
        )
        
        response = self.client.get(reverse('booking_cart_app:checkout'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Checkout')


    def test_booking_detail_view(self):
        """Test booking detail view for customer."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00")
        )

        # Create booking item
        from booking_cart_app.models import BookingItem
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        response = self.client.get(
            reverse('booking_cart_app:booking_detail', kwargs={'booking_slug': booking.slug})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(booking.booking_id))


class ProviderViewsTest(TestCase):
    """Test provider-related views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

    def test_provider_bookings_view(self):
        """Test provider bookings view."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00")
        )

        # Create booking item
        from booking_cart_app.models import BookingItem
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )

        response = self.client.get(reverse('booking_cart_app:provider_booking_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Bookings Management')
        # Template uses truncatechars:8 which shows first 5 chars + "..." for long strings
        self.assertContains(response, str(booking.booking_id)[:5])

    def test_confirm_booking_view(self):
        """Test confirm booking view for provider."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING
        )

        # Create booking item
        from booking_cart_app.models import BookingItem
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        response = self.client.post(
            reverse('booking_cart_app:provider_accept_booking', kwargs={'booking_slug': booking.slug})
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after confirmation
        
        # Check that booking was confirmed
        booking.refresh_from_db()
        self.assertEqual(booking.status, Booking.CONFIRMED)

    def test_decline_booking_view(self):
        """Test decline booking view for provider."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING
        )

        # Create booking item
        from booking_cart_app.models import BookingItem
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        response = self.client.post(
            reverse('booking_cart_app:provider_decline_booking', kwargs={'booking_slug': booking.slug}),
            data={'action_reason': 'Fully booked'}
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after decline
        
        # Check that booking was declined
        booking.refresh_from_db()
        self.assertEqual(booking.status, Booking.DECLINED)
        self.assertEqual(booking.cancellation_reason, 'Fully booked')

    def test_service_availability_create_view(self):
        """Test service availability create view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        post_data = {
            'available_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'start_time': '09:00',
            'end_time': '10:00',
            'max_bookings': 5
        }
        
        response = self.client.post(
            reverse('booking_cart_app:provider_add_availability', kwargs={'service_id': self.service.id}),
            data=post_data
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        
        # Check that availability was created
        self.assertEqual(ServiceAvailability.objects.count(), 1)
        availability = ServiceAvailability.objects.first()
        self.assertEqual(availability.service, self.service)
        self.assertEqual(availability.max_bookings, 5)
