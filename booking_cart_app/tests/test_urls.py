"""
Unit tests for booking_cart_app URLs.

This module contains comprehensive unit tests for URL patterns and routing
in the booking_cart_app, ensuring all URLs resolve correctly and return
appropriate responses.
"""

# Django imports
from django.test import TestCase, Client
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model

# Local imports
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category, Venue, Service, VenueCategory
from decimal import Decimal
from booking_cart_app.models import Booking
from booking_cart_app import views

User = get_user_model()


class BookingCartURLsTest(TestCase):
    """Test URL patterns for booking_cart_app."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa",
            category_description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00")
        )

    def test_cart_url_resolves(self):
        """Test that cart URL resolves correctly."""
        url = reverse('booking_cart_app:cart_view')
        self.assertEqual(url, '/bookings/cart/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.cart_view.__name__)

    def test_add_to_cart_url_resolves(self):
        """Test that add_to_cart URL resolves correctly."""
        url = reverse('booking_cart_app:add_to_cart', kwargs={'service_id': self.service.id})
        expected_url = f'/bookings/add-to-cart-legacy/{self.service.id}/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.add_to_cart_view.__name__)

    def test_remove_from_cart_url_resolves(self):
        """Test that remove_from_cart URL resolves correctly."""
        url = reverse('booking_cart_app:remove_from_cart', kwargs={'item_id': 1})
        self.assertEqual(url, '/bookings/cart/remove/1/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.remove_from_cart_view.__name__)

    def test_update_cart_item_url_resolves(self):
        """Test that update_cart_item URL resolves correctly."""
        url = reverse('booking_cart_app:update_cart_item', kwargs={'item_id': 1})
        self.assertEqual(url, '/bookings/cart/update/1/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.update_cart_item_view.__name__)

    def test_checkout_url_resolves(self):
        """Test that checkout URL resolves correctly."""
        url = reverse('booking_cart_app:checkout')
        self.assertEqual(url, '/bookings/checkout/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.checkout_view.__name__)

    def test_customer_bookings_url_resolves(self):
        """Test that customer_bookings URL resolves correctly."""
        url = reverse('booking_cart_app:booking_list')
        self.assertEqual(url, '/bookings/bookings/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.booking_list_view.__name__)

    def test_booking_detail_url_resolves(self):
        """Test that booking_detail URL resolves correctly."""
        url = reverse('booking_cart_app:booking_detail', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/bookings/{self.booking.slug}/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.booking_detail_view.__name__)

    def test_cancel_booking_url_resolves(self):
        """Test that cancel_booking URL resolves correctly."""
        url = reverse('booking_cart_app:cancel_booking', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/bookings/{self.booking.slug}/cancel/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.cancel_booking_view.__name__)

    def test_provider_bookings_url_resolves(self):
        """Test that provider_bookings URL resolves correctly."""
        url = reverse('booking_cart_app:provider_booking_list')
        self.assertEqual(url, '/bookings/provider/bookings/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.provider_booking_list_view.__name__)

    def test_confirm_booking_url_resolves(self):
        """Test that confirm_booking URL resolves correctly."""
        url = reverse('booking_cart_app:provider_accept_booking', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/provider/booking/{self.booking.slug}/accept/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.provider_accept_booking_view.__name__)

    def test_decline_booking_url_resolves(self):
        """Test that decline_booking URL resolves correctly."""
        url = reverse('booking_cart_app:provider_decline_booking', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/provider/booking/{self.booking.slug}/decline/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.provider_decline_booking_view.__name__)

    def test_create_availability_url_resolves(self):
        """Test that create_availability URL resolves correctly."""
        url = reverse('booking_cart_app:provider_add_availability', kwargs={'service_id': self.service.id})
        expected_url = f'/bookings/provider/availability/{self.service.id}/add/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.provider_add_availability_view.__name__)

    def test_manage_availability_url_resolves(self):
        """Test that manage_availability URL resolves correctly."""
        url = reverse('booking_cart_app:provider_service_availability', kwargs={'service_id': self.service.id})
        expected_url = f'/bookings/provider/availability/{self.service.id}/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.provider_service_availability_view.__name__)

    def test_admin_bookings_url_resolves(self):
        """Test that admin_bookings URL resolves correctly."""
        url = reverse('booking_cart_app:admin_booking_list')
        self.assertEqual(url, '/bookings/admin/bookings/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.admin_booking_list_view.__name__)

    def test_admin_booking_detail_url_resolves(self):
        """Test that admin_booking_detail URL resolves correctly."""
        url = reverse('booking_cart_app:admin_booking_detail', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/admin/bookings/{self.booking.slug}/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.admin_booking_detail_view.__name__)

    def test_resolve_dispute_url_resolves(self):
        """Test that resolve_dispute URL resolves correctly."""
        url = reverse('booking_cart_app:admin_resolve_dispute', kwargs={'booking_slug': self.booking.slug})
        expected_url = f'/bookings/admin/disputes/{self.booking.slug}/resolve/'
        self.assertEqual(url, expected_url)
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.admin_resolve_dispute_view.__name__)

    def test_booking_analytics_url_resolves(self):
        """Test that booking_analytics URL resolves correctly."""
        url = reverse('booking_cart_app:admin_booking_analytics')
        self.assertEqual(url, '/bookings/admin/analytics/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.func.__name__, views.admin_booking_analytics_view.__name__)


class URLAccessTest(TestCase):
    """Test URL access permissions and redirects."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        # Create service provider profile for the provider
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Provider Business',
            phone='+**********',
            contact_name='Test Provider',
            address='123 Provider St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )

    def test_cart_url_requires_authentication(self):
        """Test that cart URL requires authentication."""
        url = reverse('booking_cart_app:cart_view')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_cart_url_accessible_to_authenticated_customer(self):
        """Test that cart URL is accessible to authenticated customer."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('booking_cart_app:cart_view')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_provider_bookings_requires_provider_role(self):
        """Test that provider bookings URL requires provider role."""
        # Test unauthenticated access
        url = reverse('booking_cart_app:provider_booking_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Test customer access (should be redirected)
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to home

        # Test provider access (should be allowed)
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_admin_urls_require_admin_role(self):
        """Test that admin URLs require admin role."""
        admin_urls = [
            'booking_cart_app:admin_booking_list',
            'booking_cart_app:admin_booking_analytics',
        ]

        for url_name in admin_urls:
            url = reverse(url_name)

            # Ensure client is logged out
            self.client.logout()

            # Test unauthenticated access
            response = self.client.get(url)
            # Should redirect to login or return 403/401
            self.assertIn(response.status_code, [302, 401, 403])

            # Test customer access (should be redirected)
            self.client.login(email='<EMAIL>', password='testpass123')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect to home

            # Test provider access (should be redirected)
            self.client.login(email='<EMAIL>', password='testpass123')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect to home

            # Test admin access (should be allowed)
            self.client.login(email='<EMAIL>', password='testpass123')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

    def test_url_namespace_consistency(self):
        """Test that all URLs use consistent namespace."""
        url_names = [
            'cart_view', 'checkout', 'booking_list',
            'provider_booking_list', 'admin_booking_list', 'admin_booking_analytics'
        ]

        for url_name in url_names:
            try:
                url = reverse(f'booking_cart_app:{url_name}')
                self.assertTrue(url.startswith('/bookings/'))
            except Exception as e:
                self.fail(f"URL {url_name} failed to reverse: {e}")

    def test_url_parameter_validation(self):
        """Test URL parameter validation."""
        # Test with invalid service_id
        try:
            url = reverse('booking_cart_app:add_to_cart', kwargs={'service_id': 'invalid'})
            self.fail("Should not accept invalid service_id")
        except Exception:
            pass  # Expected behavior

        # Test with valid service_id
        url = reverse('booking_cart_app:add_to_cart', kwargs={'service_id': 1})
        self.assertEqual(url, '/bookings/add-to-cart-legacy/1/')

    def test_url_trailing_slashes(self):
        """Test that URLs have consistent trailing slashes."""
        url_names = [
            'cart_view', 'checkout', 'booking_list',
            'provider_booking_list', 'admin_booking_list', 'admin_booking_analytics'
        ]

        for url_name in url_names:
            url = reverse(f'booking_cart_app:{url_name}')
            self.assertTrue(url.endswith('/'), f"URL {url_name} should end with slash")
