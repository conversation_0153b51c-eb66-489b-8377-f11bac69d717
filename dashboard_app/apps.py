# --- Third-Party Imports ---
from django.apps import AppConfig


# --- App Configuration ---
class DashboardAppConfig(AppConfig):
    """Configuration for the dashboard_app Django application."""

    default_auto_field = 'django.db.models.BigAutoField'
    name = 'dashboard_app'
    verbose_name = 'Dashboard Application'

    def ready(self):
        """Import signal handlers when the app is ready."""
        try:
            import dashboard_app.signals  # noqa F401
        except ImportError:
            pass
