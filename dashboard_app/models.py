# """Database models for the dashboard application."""

# --- Third-Party Imports ---
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from venues_app.models import Venue


class FavoriteVenue(models.Model):
    """
    Model for storing customer's favorite venues.
    Allows customers to mark venues as favorites for easy access and notifications.
    """

    # Core relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='favorite_venues',
        help_text=_('Customer who favorited this venue')
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='favorited_by',
        help_text=_('Venue that was favorited')
    )

    # Metadata
    added_date = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the venue was added to favorites')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When the favorite was last updated')
    )

    class Meta:
        verbose_name = _('Favorite Venue')
        verbose_name_plural = _('Favorite Venues')
        ordering = ['-added_date']
        unique_together = ['customer', 'venue']
        indexes = [
            models.Index(fields=['customer', '-added_date']),
            models.Index(fields=['venue', '-added_date']),
        ]

    def __str__(self):
        """Return string representation of favorite venue."""
        return f"{self.customer.email} - {self.venue.venue_name}"

    def save(self, *args, **kwargs):
        """Override save to ensure only customers can favorite venues."""
        if not self.customer.is_customer:
            raise ValueError(_('Only customers can favorite venues'))
        super().save(*args, **kwargs)


class UserPreferences(models.Model):
    """
    Model for storing user preferences and quick settings.
    Used for dashboard quick toggles and general user preferences.
    """

    # Core relationship
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dashboard_preferences',
        help_text=_('User these preferences belong to')
    )

    # Communication preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text=_('Receive email notifications for booking updates and promotions')
    )
    sms_reminders = models.BooleanField(
        default=True,
        help_text=_('Receive SMS reminders for upcoming appointments')
    )
    marketing_updates = models.BooleanField(
        default=False,
        help_text=_('Receive marketing emails about special offers and new venues')
    )

    # Dashboard preferences
    dashboard_notifications = models.BooleanField(
        default=True,
        help_text=_('Show notifications in dashboard')
    )
    weekly_summary = models.BooleanField(
        default=True,
        help_text=_('Receive weekly summary emails')
    )
    
    # Privacy preferences
    profile_visibility = models.CharField(
        max_length=20,
        choices=[
            ('public', _('Public')),
            ('private', _('Private')),
            ('friends', _('Friends Only')),
        ],
        default='public',
        help_text=_('Who can see your profile information')
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When preferences were created')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When preferences were last updated')
    )

    class Meta:
        verbose_name = _('User Preferences')
        verbose_name_plural = _('User Preferences')
        indexes = [
            models.Index(fields=['user']),
        ]

    def __str__(self):
        """Return string representation of user preferences."""
        return f"{self.user.email} - Preferences"

    @classmethod
    def get_or_create_for_user(cls, user):
        """Get or create preferences for a user with default values."""
        preferences, created = cls.objects.get_or_create(
            user=user,
            defaults={
                'email_notifications': True,
                'sms_reminders': True,
                'marketing_updates': False,
                'dashboard_notifications': True,
                'weekly_summary': True,
                'profile_visibility': 'public',
            }
        )
        return preferences, created
