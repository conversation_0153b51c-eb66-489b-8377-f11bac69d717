"""Constant messages used throughout dashboard_app."""

# --- Third-Party Imports ---
from django.utils.translation import gettext_lazy as _

CUSTOMER_ONLY_ERROR = _('Only customers can access this dashboard.')
PROVIDER_ONLY_ERROR = _('Only service providers can access this dashboard.')
ADMIN_ONLY_ERROR = _("You don't have access to the admin dashboard.")

# Success messages
FAVORITE_ADDED_SUCCESS = _('Venue added to your favorites successfully.')
FAVORITE_REMOVED_SUCCESS = _('Venue removed from your favorites successfully.')
DASHBOARD_ACCESS_SUCCESS = _('Welcome to your dashboard!')

# Error messages
FAVORITE_ALREADY_EXISTS = _('This venue is already in your favorites.')
VENUE_NOT_FOUND_ERROR = _('The requested venue was not found.')
PERMISSION_DENIED_ERROR = _('You do not have permission to perform this action.')
