"""
Comprehensive logging utilities for dashboard_app.

This module provides structured logging functions for dashboard-related events
including customer activities, favorite venue management, provider analytics,
admin operations, and system monitoring. Built on top of the centralized
logging utilities in utils/logging_utils.py.

Usage:
    from dashboard_app.logging_utils import log_dashboard_access, log_favorite_venue_event

    log_dashboard_access(user, request, dashboard_type='customer')
    log_favorite_venue_event('added', user, venue_name, venue_id, request)
"""

# --- Standard Library Imports ---
import logging
import time
import traceback
from functools import wraps
from typing import Any, Dict, Optional

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

# --- Local App Imports ---
from utils.logging_utils import (
    get_app_logger,
    get_client_info,
    log_audit_event,
    log_error as base_log_error,
    log_performance,
    log_security_event,
    log_user_activity,
)


User = get_user_model()

# Get specialized loggers for dashboard operations
dashboard_logger = get_app_logger('dashboard_app', 'activity')
security_logger = get_app_logger('dashboard_app', 'security')
error_logger = get_app_logger('dashboard_app', 'errors')
performance_logger = get_app_logger('dashboard_app', 'performance')
audit_logger = get_app_logger('dashboard_app', 'audit')


# --- Dashboard Access Logging ---

def log_dashboard_access(
    user: User,
    dashboard_type: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log dashboard access events with enhanced context.

    Args:
        user: User accessing the dashboard
        dashboard_type: Type of dashboard ('customer', 'provider', 'admin')
        request: Django HttpRequest object
        details: Additional dashboard context (metrics, counts, etc.)
    """
    log_user_activity(
        app_name='dashboard_app',
        activity_type=f'{dashboard_type}_dashboard_access',
        user=user,
        request=request,
        details={
            'dashboard_type': dashboard_type,
            'user_role': user.role,
            **(details or {})
        },
        target_object=f'{dashboard_type}_dashboard'
    )


def log_dashboard_activity(
    activity_type: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    target_object: Optional[str] = None
) -> None:
    """
    Log dashboard activity events using centralized logging.

    Args:
        activity_type: Type of activity (dashboard_access, favorite_added, etc.)
        user: User object
        request: Django HttpRequest object
        details: Additional details about the activity
        target_object: Object being acted upon (if applicable)
    """
    log_user_activity(
        app_name='dashboard_app',
        activity_type=activity_type,
        user=user,
        request=request,
        details=details,
        target_object=target_object
    )


def log_favorite_venue_event(
    event_type: str,
    user: User,
    venue_name: str,
    venue_id: int,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log favorite venue management events.
    
    Args:
        event_type: Type of event (favorite_added, favorite_removed)
        user: User who performed the action
        venue_name: Name of the venue
        venue_id: ID of the venue
        request: Django HttpRequest object
        details: Additional event details
    """
    log_dashboard_activity(
        activity_type=f"favorite_venue_{event_type}",
        user=user,
        request=request,
        details={
            'venue_name': venue_name,
            'venue_id': venue_id,
            **(details or {})
        },
        target_object=f"venue_{venue_id}"
    )


def log_booking_status_access(
    user: User,
    booking_count: int,
    filter_type: Optional[str] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log booking status page access.
    
    Args:
        user: User accessing booking status
        booking_count: Number of bookings displayed
        filter_type: Type of filter applied (upcoming, past, all)
        request: Django HttpRequest object
    """
    log_dashboard_activity(
        activity_type="booking_status_access",
        user=user,
        request=request,
        details={
            'booking_count': booking_count,
            'filter_type': filter_type or 'all'
        }
    )


def log_profile_access(
    user: User,
    access_type: str,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log profile management access.
    
    Args:
        user: User accessing profile
        access_type: Type of access (view, edit_redirect)
        request: Django HttpRequest object
    """
    log_dashboard_activity(
        activity_type=f"profile_{access_type}",
        user=user,
        request=request,
        details={
            'profile_type': 'customer' if user.is_customer else 'provider'
        }
    )


def log_dashboard_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log dashboard-specific error events using centralized logging.

    Args:
        error_type: Type of error (validation, database, permission, etc.)
        error_message: Human-readable error message
        user: User object (if applicable)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        details: Additional error details
    """
    base_log_error(
        app_name='dashboard_app',
        error_type=error_type,
        error_message=error_message,
        user=user,
        request=request,
        exception=exception,
        details=details
    )


# Alias for backward compatibility
log_error = log_dashboard_error


def log_dashboard_security_event(
    event_type: str,
    user_email: Optional[str] = None,
    user_id: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    severity: str = 'INFO'
) -> None:
    """
    Log dashboard security-related events using centralized logging.

    Args:
        event_type: Type of security event (unauthorized_access, permission_denied, etc.)
        user_email: Email of the user involved
        user_id: ID of the user involved
        request: Django HttpRequest object
        details: Additional details about the event
        severity: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    from utils.logging_utils import log_security_event
    log_security_event(
        app_name='dashboard_app',
        event_type=event_type,
        user_email=user_email,
        user_id=user_id,
        request=request,
        details=details,
        severity=severity
    )


# Alias for backward compatibility
log_security_event = log_dashboard_security_event


def performance_monitor(operation_name: str):
    """
    Decorator to monitor performance of dashboard functions using centralized logging.

    Args:
        operation_name: Human-readable name for the operation

    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            # Try to extract user and request from args/kwargs
            user = None
            request = None

            for arg in args:
                if isinstance(arg, User):
                    user = arg
                elif isinstance(arg, HttpRequest):
                    request = arg

            for value in kwargs.values():
                if isinstance(value, User):
                    user = value
                elif isinstance(value, HttpRequest):
                    request = value

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Log performance using centralized logging
                log_performance(
                    app_name='dashboard_app',
                    operation=f"{func.__module__}.{func.__name__}",
                    duration=duration,
                    user=user,
                    request=request,
                    details={
                        'operation_name': operation_name,
                        'function_name': func.__name__
                    }
                )

                return result
            except Exception as e:
                duration = time.time() - start_time

                log_dashboard_error(
                    error_type='function_execution',
                    error_message=f"Error in {operation_name}",
                    user=user,
                    request=request,
                    exception=e,
                    details={
                        'function': f"{func.__module__}.{func.__name__}",
                        'duration_before_error': duration,
                        'operation_name': operation_name
                    }
                )
                raise

        return wrapper
    return decorator


def log_provider_dashboard_activity(
    activity_type: str,
    user: User,
    venue_id: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log provider dashboard activity events.

    Args:
        activity_type: Type of activity (dashboard_access, earnings_view, etc.)
        user: Provider user object
        venue_id: ID of the venue (if applicable)
        request: Django HttpRequest object
        details: Additional activity details
    """
    log_dashboard_activity(
        activity_type=f"provider_{activity_type}",
        user=user,
        request=request,
        details={
            'venue_id': venue_id,
            'user_type': 'service_provider',
            **(details or {})
        },
        target_object=f"venue_{venue_id}" if venue_id else None
    )


def log_provider_earnings_access(
    user: User,
    venue_id: int,
    date_range: str,
    total_earnings: str,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log provider earnings report access.

    Args:
        user: Provider user object
        venue_id: ID of the venue
        date_range: Date range for the report
        total_earnings: Total earnings amount
        request: Django HttpRequest object
    """
    log_provider_dashboard_activity(
        activity_type="earnings_access",
        user=user,
        venue_id=venue_id,
        request=request,
        details={
            'date_range': date_range,
            'total_earnings': total_earnings
        }
    )


def log_provider_service_stats_access(
    user: User,
    venue_id: int,
    services_count: int,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log provider service performance stats access.

    Args:
        user: Provider user object
        venue_id: ID of the venue
        services_count: Number of services analyzed
        request: Django HttpRequest object
    """
    log_provider_dashboard_activity(
        activity_type="service_stats_access",
        user=user,
        venue_id=venue_id,
        request=request,
        details={
            'services_count': services_count
        }
    )


# --- Admin Dashboard Logging ---

def log_admin_dashboard_access(
    user: User,
    admin_section: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log admin dashboard section access with enhanced audit trail.

    Args:
        user: Admin user accessing the dashboard
        admin_section: Section being accessed (overview, analytics, system_health, etc.)
        request: Django HttpRequest object
        details: Additional context about the access
    """
    log_audit_event(
        app_name='dashboard_app',
        action=f'admin_{admin_section}_access',
        admin_user=user,
        request=request,
        details={
            'admin_section': admin_section,
            'access_type': 'dashboard_view',
            **(details or {})
        }
    )


def log_admin_data_access(
    user: User,
    data_type: str,
    access_type: str,
    record_count: Optional[int] = None,
    filters_applied: Optional[Dict[str, Any]] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log admin data access for compliance and monitoring.

    Args:
        user: Admin user accessing the data
        data_type: Type of data being accessed (users, bookings, revenue, etc.)
        access_type: Type of access (view, export, search, analytics)
        record_count: Number of records accessed
        filters_applied: Any filters applied to the data access
        request: Django HttpRequest object
    """
    log_user_activity(
        app_name='dashboard_app',
        activity_type=f'admin_data_access_{data_type}',
        user=user,
        request=request,
        details={
            'data_type': data_type,
            'access_type': access_type,
            'record_count': record_count,
            'filters_applied': filters_applied or {},
            'access_category': 'admin_data_access'
        },
        target_object=f'{data_type}_data'
    )


def log_system_health_check(
    user: User,
    health_score: int,
    system_metrics: Dict[str, Any],
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log system health monitoring access and results.

    Args:
        user: Admin user performing health check
        health_score: Overall system health score (0-100)
        system_metrics: System performance metrics
        request: Django HttpRequest object
    """
    log_admin_data_access(
        user=user,
        data_type='system_health',
        access_type='monitoring',
        request=request,
        filters_applied={
            'health_score': health_score,
            'cpu_usage': system_metrics.get('cpu_usage'),
            'memory_usage': system_metrics.get('memory_usage'),
            'disk_usage': system_metrics.get('disk_usage')
        }
    )


def log_analytics_access(
    user: User,
    analytics_type: str,
    date_range: str,
    metrics_accessed: Dict[str, Any],
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log analytics dashboard access with detailed metrics context.

    Args:
        user: User accessing analytics
        analytics_type: Type of analytics (revenue, user, booking, platform)
        date_range: Date range for the analytics
        metrics_accessed: Specific metrics that were accessed
        request: Django HttpRequest object
    """
    log_user_activity(
        app_name='dashboard_app',
        activity_type=f'{analytics_type}_analytics_access',
        user=user,
        request=request,
        details={
            'analytics_type': analytics_type,
            'date_range': date_range,
            'metrics_accessed': metrics_accessed,
            'access_category': 'analytics'
        },
        target_object=f'{analytics_type}_analytics'
    )


def log_dashboard_export(
    user: User,
    export_type: str,
    data_type: str,
    record_count: int,
    export_format: str = 'csv',
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log data export operations from dashboard.

    Args:
        user: User performing the export
        export_type: Type of export (full, filtered, summary)
        data_type: Type of data being exported
        record_count: Number of records exported
        export_format: Format of export (csv, xlsx, pdf)
        request: Django HttpRequest object
    """
    log_audit_event(
        app_name='dashboard_app',
        action=f'data_export_{data_type}',
        admin_user=user,
        request=request,
        details={
            'export_type': export_type,
            'data_type': data_type,
            'record_count': record_count,
            'export_format': export_format,
            'action_category': 'data_export'
        }
    )


def log_dashboard_filter_usage(
    user: User,
    dashboard_section: str,
    filters_applied: Dict[str, Any],
    results_count: int,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log dashboard filter usage for analytics and UX improvement.

    Args:
        user: User applying filters
        dashboard_section: Section where filters were applied
        filters_applied: Dictionary of filters and their values
        results_count: Number of results after filtering
        request: Django HttpRequest object
    """
    log_user_activity(
        app_name='dashboard_app',
        activity_type='dashboard_filter_usage',
        user=user,
        request=request,
        details={
            'dashboard_section': dashboard_section,
            'filters_applied': filters_applied,
            'results_count': results_count,
            'filter_count': len(filters_applied),
            'activity_category': 'filter_usage'
        },
        target_object=f'{dashboard_section}_filters'
    )


# --- Specialized Error and Security Logging ---

def log_unauthorized_dashboard_access(
    user_email: str,
    attempted_dashboard: str,
    user_role: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log unauthorized attempts to access dashboard sections.

    Args:
        user_email: Email of the user attempting unauthorized access
        attempted_dashboard: Dashboard section attempted (customer, provider, admin)
        user_role: Role of the user attempting access
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'attempted_dashboard': attempted_dashboard,
        'user_role': user_role,
        'access_denied_reason': f'{user_role} attempted to access {attempted_dashboard} dashboard',
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_dashboard_security_event(
        event_type='unauthorized_dashboard_access',
        user_email=user_email,
        request=request,
        details=details,
        severity='WARNING'
    )


def log_dashboard_performance_issue(
    operation_name: str,
    duration: float,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    performance_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log dashboard performance issues for monitoring and optimization.

    Args:
        operation_name: Name of the slow operation
        duration: Duration in seconds
        user: User object (if applicable)
        request: Django HttpRequest object
        performance_context: Additional performance context
    """
    severity = 'WARNING' if duration > 3.0 else 'INFO'

    details = {
        'operation_name': operation_name,
        'duration_seconds': duration,
        'performance_category': 'slow_operation' if duration > 3.0 else 'moderate_operation',
        'threshold_exceeded': duration > 3.0
    }

    if performance_context:
        details.update(performance_context)

    log_dashboard_error(
        error_type='performance_issue',
        error_message=f"Dashboard operation '{operation_name}' took {duration:.3f}s",
        user=user,
        request=request,
        details=details
    )


def log_dashboard_data_integrity_check(
    user: User,
    check_type: str,
    results: Dict[str, Any],
    issues_found: int = 0,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log data integrity checks performed through dashboard.

    Args:
        user: Admin user performing the check
        check_type: Type of integrity check performed
        results: Results of the integrity check
        issues_found: Number of issues found
        request: Django HttpRequest object
    """
    log_audit_event(
        app_name='dashboard_app',
        action=f'data_integrity_check_{check_type}',
        admin_user=user,
        request=request,
        details={
            'check_type': check_type,
            'issues_found': issues_found,
            'check_results': results,
            'action_category': 'data_integrity'
        }
    )
