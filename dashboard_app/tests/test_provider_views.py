"""
Unit tests for dashboard_app provider views.

This module contains comprehensive unit tests for provider dashboard views
including provider dashboard, today's bookings, earnings reports, and service performance.
"""


# --- Standard Library Imports ---
from decimal import Decimal
from datetime import timedelta
from unittest.mock import Mock, patch

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.test import Client, TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from dashboard_app.models import FavoriteVenue
from venues_app.models import Service, Venue

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class ProviderDashboardViewTest(TestCase):
    """Test the provider dashboard view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Test Massage',
            short_description='A relaxing massage',
            price_min=Decimal('100.00'),
            duration_minutes=60,
            is_active=True
        )

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()
        
        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # URL for provider dashboard
        self.dashboard_url = reverse('dashboard_app:provider_dashboard')

    def test_provider_dashboard_access_authenticated_provider(self):
        """Test that authenticated providers can access the dashboard."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/dashboard.html')

    def test_provider_dashboard_access_non_provider(self):
        """Test that non-providers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Only service providers can access' in str(message) for message in messages))

    def test_provider_dashboard_context_data(self):
        """Test that provider dashboard context contains expected data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create some test bookings
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='confirmed'
        )
        
        booking_item = BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal('100.00'),
            scheduled_date=timezone.now().date(),
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check context data
        context = response.context
        self.assertIn('provider_profile', context)
        self.assertIn('venue', context)
        self.assertIn('todays_bookings', context)
        self.assertIn('recent_bookings', context)
        self.assertIn('total_bookings', context)
        self.assertIn('pending_bookings', context)
        self.assertIn('confirmed_bookings', context)
        self.assertIn('completed_bookings', context)
        self.assertIn('monthly_earnings', context)
        self.assertIn('team_members_count', context)

    def test_earnings_export_view(self):
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.get(reverse('dashboard_app:provider_earnings_export'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

    def test_provider_dashboard_without_venue(self):
        """Test provider dashboard when provider has no venue."""
        # Create provider without venue
        provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        provider2.role = CustomUser.SERVICE_PROVIDER
        provider2.save()

        provider_profile2 = ServiceProviderProfile.objects.create(
            user=provider2,
            legal_name='Test Spa 2',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Test Avenue',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)

        # Provider should now be able to access dashboard without venue
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/dashboard.html')

        # Check that context contains expected data for provider without venue
        context = response.context
        self.assertIn('provider_profile', context)
        self.assertIn('venue', context)
        self.assertIsNone(context['venue'])  # No venue should be present
        self.assertEqual(context['total_bookings'], 0)
        self.assertEqual(context['pending_bookings'], 0)
        self.assertEqual(context['confirmed_bookings'], 0)
        self.assertEqual(context['completed_bookings'], 0)

    @patch('dashboard_app.views.provider.log_dashboard_access')
    def test_provider_dashboard_logging(self, mock_log_access):
        """Test that provider dashboard access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)

        self.assertEqual(response.status_code, 200)
        mock_log_access.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class ProviderTodaysBookingsViewTest(TestCase):
    """Test the provider today's bookings view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Test Massage',
            short_description='A relaxing massage',
            price_min=Decimal('100.00'),
            duration_minutes=60,
            is_active=True
        )

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()
        
        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # URL for today's bookings
        self.todays_bookings_url = reverse('dashboard_app:provider_todays_bookings')

    def test_todays_bookings_access_authenticated_provider(self):
        """Test that authenticated providers can access today's bookings."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.todays_bookings_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/todays_bookings.html')

    def test_todays_bookings_access_non_provider(self):
        """Test that non-providers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.todays_bookings_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

    def test_todays_bookings_filtering_by_status(self):
        """Test filtering today's bookings by status."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create today's bookings with different statuses
        today = timezone.now().date()
        
        booking1 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='pending'
        )
        
        booking_item1 = BookingItem.objects.create(
            booking=booking1,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal('100.00'),
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('150.00'),
            status='confirmed'
        )
        
        booking_item2 = BookingItem.objects.create(
            booking=booking2,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal('150.00'),
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            duration_minutes=self.service.duration_minutes
        )
        
        # Test filtering by pending status
        response = self.client.get(self.todays_bookings_url, {'status': 'pending'})
        self.assertEqual(response.status_code, 200)
        
        # Test filtering by confirmed status
        response = self.client.get(self.todays_bookings_url, {'status': 'confirmed'})
        self.assertEqual(response.status_code, 200)

    def test_todays_bookings_context_data(self):
        """Test that today's bookings context contains expected data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.get(self.todays_bookings_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check context data
        context = response.context
        self.assertIn('provider_profile', context)
        self.assertIn('venue', context)
        self.assertIn('bookings_by_time', context)
        self.assertIn('today', context)
        self.assertIn('status_filter', context)
        self.assertIn('total_count', context)
        self.assertIn('pending_count', context)
        self.assertIn('confirmed_count', context)
        self.assertIn('completed_count', context)

    @patch('dashboard_app.views.provider.log_dashboard_activity')
    def test_todays_bookings_logging(self, mock_log_activity):
        """Test that today's bookings access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.todays_bookings_url)

        self.assertEqual(response.status_code, 200)
        mock_log_activity.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminDashboardViewTest(TestCase):
    """Test the admin dashboard view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create admin user
        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin.is_staff = True
        self.admin.is_superuser = True
        self.admin.save()

        # Create regular user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()

        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # URL for admin dashboard
        self.admin_dashboard_url = reverse('dashboard_app:admin_dashboard')

    def test_admin_dashboard_access_authenticated_admin(self):
        """Test that authenticated admins can access the dashboard."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.admin_dashboard_url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/dashboard.html')

    def test_admin_dashboard_access_non_admin(self):
        """Test that non-admins are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.admin_dashboard_url)

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any("don't have access to the admin dashboard" in str(message) for message in messages))

    def test_admin_dashboard_context_data(self):
        """Test that admin dashboard context contains expected data."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create some test bookings
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='confirmed'
        )

        response = self.client.get(self.admin_dashboard_url)

        self.assertEqual(response.status_code, 200)

        # Check context data
        context = response.context
        self.assertIn('total_users', context)
        self.assertIn('customer_count', context)
        self.assertIn('provider_count', context)
        self.assertIn('staff_count', context)
        self.assertIn('total_venues', context)
        self.assertIn('pending_venues', context)
        self.assertIn('approved_venues', context)
        self.assertIn('rejected_venues', context)
        self.assertIn('total_bookings', context)
        self.assertIn('pending_bookings', context)
        self.assertIn('confirmed_bookings', context)
        self.assertIn('cancelled_bookings', context)
        self.assertIn('completed_bookings', context)
        self.assertIn('total_revenue', context)
        self.assertIn('recent_bookings', context)
        self.assertIn('recent_reviews', context)

        # Check specific values
        self.assertGreaterEqual(context['total_users'], 4)  # admin, user, provider, customer
        self.assertEqual(context['total_venues'], 1)
        self.assertEqual(context['approved_venues'], 1)
        self.assertEqual(context['total_bookings'], 1)
        self.assertEqual(context['confirmed_bookings'], 1)

    @patch('dashboard_app.views.admin.log_admin_dashboard_access')
    def test_admin_dashboard_logging(self, mock_log_access):
        """Test that admin dashboard access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.admin_dashboard_url)

        self.assertEqual(response.status_code, 200)
        mock_log_access.assert_called_once()

    @patch('dashboard_app.views.admin.log_error')
    def test_admin_dashboard_error_handling(self, mock_log_error):
        """Test error handling in admin dashboard."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Mock an exception in the view
        with patch('dashboard_app.views.admin.CustomUser.objects.count', side_effect=Exception('Test error')):
            response = self.client.get(self.admin_dashboard_url)

            self.assertEqual(response.status_code, 302)
            self.assertRedirects(response, reverse('venues_app:home'))
            mock_log_error.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminPlatformOverviewViewTest(TestCase):
    """Test the admin platform overview view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create admin user
        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin.is_staff = True
        self.admin.is_superuser = True
        self.admin.save()

        # Create regular user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # URL for admin platform overview
        self.platform_overview_url = reverse('dashboard_app:admin_platform_overview')

    def test_platform_overview_access_authenticated_admin(self):
        """Test that authenticated admins can access platform overview."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.platform_overview_url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/platform_overview.html')

    def test_platform_overview_access_non_admin(self):
        """Test that non-admins are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.platform_overview_url)

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

    def test_platform_overview_with_date_range_form(self):
        """Test platform overview with date range filtering."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test with custom date range
        from datetime import date
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()

        response = self.client.get(self.platform_overview_url, {
            'period': 'custom',
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        })

        self.assertEqual(response.status_code, 200)

        # Check that form is in context
        self.assertIn('form', response.context)
        self.assertIn('start_date', response.context)
        self.assertIn('end_date', response.context)

    @patch('dashboard_app.views.admin.log_analytics_access')
    def test_platform_overview_logging(self, mock_log_analytics):
        """Test that platform overview access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.platform_overview_url)

        self.assertEqual(response.status_code, 200)
        mock_log_analytics.assert_called_once()
