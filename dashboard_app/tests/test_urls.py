"""
Unit tests for dashboard_app URLs.

This module contains comprehensive unit tests for all URL patterns in the dashboard_app,
including URL resolution, reverse URL lookup, and URL parameter handling.
"""

# Django imports
from django.test import TestCase
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model

# Local imports
from dashboard_app import views

User = get_user_model()


class DashboardAppURLTest(TestCase):
    """Test the dashboard_app URL patterns."""

    def test_health_check_url(self):
        """Test health check URL resolution and reverse lookup."""
        url = reverse('dashboard_app:health_check')
        self.assertEqual(url, '/dashboard/health/')

        # Test URL resolution
        resolver = resolve('/dashboard/health/')
        self.assertEqual(resolver.func, views.health_check)
        self.assertEqual(resolver.url_name, 'health_check')
        self.assertEqual(resolver.namespace, 'dashboard_app')

    def test_customer_dashboard_url(self):
        """Test customer dashboard URL resolution and reverse lookup."""
        url = reverse('dashboard_app:customer_dashboard')
        self.assertEqual(url, '/dashboard/customer/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/customer/')
        self.assertEqual(resolver.func, views.customer_dashboard_view)
        self.assertEqual(resolver.url_name, 'customer_dashboard')
        self.assertEqual(resolver.namespace, 'dashboard_app')

    def test_customer_booking_status_url(self):
        """Test customer booking status URL resolution and reverse lookup."""
        url = reverse('dashboard_app:customer_booking_status')
        self.assertEqual(url, '/dashboard/customer/booking-status/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/customer/booking-status/')
        self.assertEqual(resolver.func, views.customer_booking_status_view)
        self.assertEqual(resolver.url_name, 'customer_booking_status')

    def test_customer_profile_edit_url(self):
        """Test customer profile edit URL resolution and reverse lookup."""
        url = reverse('dashboard_app:customer_profile_edit')
        self.assertEqual(url, '/dashboard/customer/profile/edit/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/customer/profile/edit/')
        self.assertEqual(resolver.func, views.customer_profile_edit_view)
        self.assertEqual(resolver.url_name, 'customer_profile_edit')

    def test_customer_favorite_venues_url(self):
        """Test customer favorite venues URL resolution and reverse lookup."""
        url = reverse('dashboard_app:customer_favorite_venues')
        self.assertEqual(url, '/dashboard/customer/favorites/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/customer/favorites/')
        self.assertEqual(resolver.func, views.customer_favorite_venues_view)
        self.assertEqual(resolver.url_name, 'customer_favorite_venues')

    def test_add_favorite_venue_url(self):
        """Test add favorite venue URL resolution and reverse lookup."""
        venue_id = 123
        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': venue_id})
        self.assertEqual(url, f'/dashboard/favorites/add/{venue_id}/')

        # Test URL resolution
        resolver = resolve(f'/dashboard/favorites/add/{venue_id}/')
        self.assertEqual(resolver.func, views.add_favorite_venue_view)
        self.assertEqual(resolver.url_name, 'add_favorite_venue')
        self.assertEqual(resolver.kwargs['venue_id'], venue_id)

    def test_remove_favorite_venue_url(self):
        """Test remove favorite venue URL resolution and reverse lookup."""
        venue_id = 456
        url = reverse('dashboard_app:remove_favorite_venue', kwargs={'venue_id': venue_id})
        self.assertEqual(url, f'/dashboard/favorites/remove/{venue_id}/')

        # Test URL resolution
        resolver = resolve(f'/dashboard/favorites/remove/{venue_id}/')
        self.assertEqual(resolver.func, views.remove_favorite_venue_view)
        self.assertEqual(resolver.url_name, 'remove_favorite_venue')
        self.assertEqual(resolver.kwargs['venue_id'], venue_id)

    def test_check_favorite_status_url(self):
        """Test check favorite status URL resolution and reverse lookup."""
        venue_id = 789
        url = reverse('dashboard_app:check_favorite_status', kwargs={'venue_id': venue_id})
        self.assertEqual(url, f'/dashboard/favorites/check/{venue_id}/')

        # Test URL resolution
        resolver = resolve(f'/dashboard/favorites/check/{venue_id}/')
        self.assertEqual(resolver.func, views.check_favorite_status_view)
        self.assertEqual(resolver.url_name, 'check_favorite_status')
        self.assertEqual(resolver.kwargs['venue_id'], venue_id)

    def test_provider_dashboard_url(self):
        """Test provider dashboard URL resolution and reverse lookup."""
        url = reverse('dashboard_app:provider_dashboard')
        self.assertEqual(url, '/dashboard/provider/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/provider/')
        self.assertEqual(resolver.func.view_class, views.ProviderDashboardView)
        self.assertEqual(resolver.url_name, 'provider_dashboard')

    def test_provider_todays_bookings_url(self):
        """Test provider today's bookings URL resolution and reverse lookup."""
        url = reverse('dashboard_app:provider_todays_bookings')
        self.assertEqual(url, '/dashboard/provider/todays-bookings/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/provider/todays-bookings/')
        self.assertEqual(resolver.func, views.provider_todays_bookings_view)
        self.assertEqual(resolver.url_name, 'provider_todays_bookings')

    def test_provider_earnings_reports_url(self):
        """Test provider earnings reports URL resolution and reverse lookup."""
        url = reverse('dashboard_app:provider_earnings_reports')
        self.assertEqual(url, '/dashboard/provider/earnings-reports/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/provider/earnings-reports/')
        self.assertEqual(resolver.func, views.provider_earnings_reports_view)
        self.assertEqual(resolver.url_name, 'provider_earnings_reports')

    def test_provider_service_performance_url(self):
        """Test provider service performance URL resolution and reverse lookup."""
        url = reverse('dashboard_app:provider_service_performance')
        self.assertEqual(url, '/dashboard/provider/service-performance/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/provider/service-performance/')
        self.assertEqual(resolver.func, views.provider_service_performance_view)
        self.assertEqual(resolver.url_name, 'provider_service_performance')

    def test_provider_team_management_url(self):
        """Test provider team management URL resolution and reverse lookup."""
        url = reverse('dashboard_app:provider_team_management')
        self.assertEqual(url, '/dashboard/provider/team-management/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/provider/team-management/')
        self.assertEqual(resolver.func, views.provider_team_management_view)
        self.assertEqual(resolver.url_name, 'provider_team_management')

    def test_admin_dashboard_url(self):
        """Test admin dashboard URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_dashboard')
        self.assertEqual(url, '/dashboard/admin/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/')
        self.assertEqual(resolver.func, views.admin_dashboard)
        self.assertEqual(resolver.url_name, 'admin_dashboard')

    def test_admin_platform_overview_url(self):
        """Test admin platform overview URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_platform_overview')
        self.assertEqual(url, '/dashboard/admin/platform-overview/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/platform-overview/')
        self.assertEqual(resolver.func, views.admin_platform_overview)
        self.assertEqual(resolver.url_name, 'admin_platform_overview')

    def test_admin_user_statistics_url(self):
        """Test admin user statistics URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_user_statistics')
        self.assertEqual(url, '/dashboard/admin/user-statistics/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/user-statistics/')
        self.assertEqual(resolver.func, views.admin_user_statistics)
        self.assertEqual(resolver.url_name, 'admin_user_statistics')

    def test_admin_booking_analytics_url(self):
        """Test admin booking analytics URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_booking_analytics')
        self.assertEqual(url, '/dashboard/admin/booking-analytics/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/booking-analytics/')
        self.assertEqual(resolver.func, views.admin_booking_analytics)
        self.assertEqual(resolver.url_name, 'admin_booking_analytics')

    def test_admin_revenue_tracking_url(self):
        """Test admin revenue tracking URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_revenue_tracking')
        self.assertEqual(url, '/dashboard/admin/revenue-tracking/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/revenue-tracking/')
        self.assertEqual(resolver.func, views.admin_revenue_tracking)
        self.assertEqual(resolver.url_name, 'admin_revenue_tracking')

    def test_admin_system_health_url(self):
        """Test admin system health URL resolution and reverse lookup."""
        url = reverse('dashboard_app:admin_system_health')
        self.assertEqual(url, '/dashboard/admin/system-health/')
        
        # Test URL resolution
        resolver = resolve('/dashboard/admin/system-health/')
        self.assertEqual(resolver.func, views.admin_system_health)
        self.assertEqual(resolver.url_name, 'admin_system_health')

    def test_url_namespace(self):
        """Test that all URLs use the correct namespace."""
        url_names = [
            'health_check',
            'customer_dashboard',
            'customer_booking_status',
            'customer_profile_edit',
            'customer_favorite_venues',
            'provider_dashboard',
            'provider_todays_bookings',
            'provider_earnings_reports',
            'provider_earnings_export',
            'provider_service_performance',
            'provider_team_management',
            'admin_dashboard',
            'admin_platform_overview',
            'admin_user_statistics',
            'admin_booking_analytics',
            'admin_revenue_tracking',
            'admin_revenue_export',
            'admin_system_health',
        ]

        for url_name in url_names:
            url = reverse(f'dashboard_app:{url_name}')
            self.assertIsNotNone(url)
            self.assertTrue(url.startswith('/dashboard/'))

    def test_venue_id_parameter_urls(self):
        """Test URLs that require venue_id parameter."""
        venue_id_urls = [
            'add_favorite_venue',
            'remove_favorite_venue',
            'check_favorite_status',
        ]

        for url_name in venue_id_urls:
            venue_id = 999
            url = reverse(f'dashboard_app:{url_name}', kwargs={'venue_id': venue_id})
            self.assertIsNotNone(url)
            self.assertIn(str(venue_id), url)

            resolver = resolve(url)
            self.assertEqual(resolver.kwargs['venue_id'], venue_id)

    def test_invalid_venue_id_parameter(self):
        """Test that invalid venue_id parameters raise an exception."""
        with self.assertRaises(Exception):
            reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': 'invalid'})

    def test_all_urls_have_names(self):
        """Test that all URL patterns have names."""
        from dashboard_app.urls import urlpatterns
        
        for pattern in urlpatterns:
            self.assertIsNotNone(pattern.name, f"URL pattern {pattern.pattern} should have a name")

    def test_url_patterns_count(self):
        """Test that we have the expected number of URL patterns."""
        from dashboard_app.urls import urlpatterns
        
        # We expect 24 URL patterns based on the current implementation
        expected_count = 24
        actual_count = len(urlpatterns)
        
        self.assertEqual(actual_count, expected_count, 
                        f"Expected {expected_count} URL patterns, but found {actual_count}")

    def test_app_name_is_set(self):
        """Test that the app_name is correctly set."""
        from dashboard_app import urls
        
        self.assertEqual(urls.app_name, 'dashboard_app')

    def test_url_pattern_types(self):
        """Test that URL patterns are of the correct type."""
        from dashboard_app.urls import urlpatterns
        from django.urls.resolvers import URLPattern
        
        for pattern in urlpatterns:
            self.assertIsInstance(pattern, URLPattern, 
                                f"Pattern {pattern} should be a URLPattern instance")
