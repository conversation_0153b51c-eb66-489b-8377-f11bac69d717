"""
Unit tests for dashboard_app models.

This module contains comprehensive unit tests for all model classes in the dashboard_app,
including FavoriteVenue model functionality, validation, and relationships.
"""

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db import IntegrityError

# Local imports
from dashboard_app.models import FavoriteVenue
from venues_app.models import Venue
from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile

User = get_user_model()


class FavoriteVenueModelTest(TestCase):
    """Test the FavoriteVenue model functionality."""

    def setUp(self):
        """Set up test data."""
        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()
        
        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

    def test_create_favorite_venue(self):
        """Test creating a favorite venue."""
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        self.assertEqual(favorite.customer, self.customer)
        self.assertEqual(favorite.venue, self.venue)
        self.assertIsNotNone(favorite.added_date)
        self.assertIsNotNone(favorite.updated_at)

    def test_favorite_venue_str_representation(self):
        """Test the string representation of FavoriteVenue."""
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        expected_str = f"{self.customer.email} - {self.venue.venue_name}"
        self.assertEqual(str(favorite), expected_str)

    def test_unique_together_constraint(self):
        """Test that a customer cannot favorite the same venue twice."""
        # Create first favorite
        FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        # Try to create duplicate favorite
        with self.assertRaises(IntegrityError):
            FavoriteVenue.objects.create(
                customer=self.customer,
                venue=self.venue
            )

    def test_customer_can_favorite_multiple_venues(self):
        """Test that a customer can favorite multiple venues."""
        # Create second venue - need another provider profile since venue is one-to-one
        provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        provider2.role = CustomUser.SERVICE_PROVIDER
        provider2.save()

        provider_profile2 = ServiceProviderProfile.objects.create(
            user=provider2,
            legal_name='Test Spa 2',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Test Avenue',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        venue2 = Venue.objects.create(
            service_provider=provider_profile2,
            venue_name='Test Spa Venue 2',
            short_description='Another test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='456',
            street_name='Test Avenue',
            approval_status='approved',
            visibility='active'
        )

        # Create favorites for both venues
        favorite1 = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        favorite2 = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=venue2
        )

        self.assertEqual(FavoriteVenue.objects.filter(customer=self.customer).count(), 2)
        self.assertNotEqual(favorite1.venue, favorite2.venue)

    def test_multiple_customers_can_favorite_same_venue(self):
        """Test that multiple customers can favorite the same venue."""
        # Create second customer
        customer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        customer2.role = CustomUser.CUSTOMER
        customer2.save()
        CustomerProfile.objects.create(user=customer2)

        # Create favorites for both customers
        favorite1 = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        favorite2 = FavoriteVenue.objects.create(
            customer=customer2,
            venue=self.venue
        )

        self.assertEqual(FavoriteVenue.objects.filter(venue=self.venue).count(), 2)
        self.assertNotEqual(favorite1.customer, favorite2.customer)

    def test_only_customers_can_favorite_venues(self):
        """Test that only customers can favorite venues."""
        # Try to create favorite with non-customer user
        with self.assertRaises(ValueError) as context:
            FavoriteVenue.objects.create(
                customer=self.provider,  # Provider, not customer
                venue=self.venue
            )
        
        self.assertIn('Only customers can favorite venues', str(context.exception))

    def test_favorite_venue_ordering(self):
        """Test that favorite venues are ordered by added_date descending."""
        # Create second venue - need another provider profile since venue is one-to-one
        provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        provider2.role = CustomUser.SERVICE_PROVIDER
        provider2.save()

        provider_profile2 = ServiceProviderProfile.objects.create(
            user=provider2,
            legal_name='Test Spa 2',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Test Avenue',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        venue2 = Venue.objects.create(
            service_provider=provider_profile2,
            venue_name='Test Spa Venue 2',
            short_description='Another test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='456',
            street_name='Test Avenue',
            approval_status='approved',
            visibility='active'
        )

        # Create favorites with some time difference
        favorite1 = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        
        # Simulate time passing
        import time
        time.sleep(0.01)
        
        favorite2 = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=venue2
        )

        # Get ordered favorites
        favorites = FavoriteVenue.objects.filter(customer=self.customer)
        
        # Most recent should be first
        self.assertEqual(favorites.first(), favorite2)
        self.assertEqual(favorites.last(), favorite1)

    def test_favorite_venue_related_names(self):
        """Test the related names work correctly."""
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        # Test customer's favorite_venues related name
        customer_favorites = self.customer.favorite_venues.all()
        self.assertIn(favorite, customer_favorites)

        # Test venue's favorited_by related name
        venue_favorites = self.venue.favorited_by.all()
        self.assertIn(favorite, venue_favorites)

    def test_favorite_venue_deletion_cascade(self):
        """Test that favorite venues are deleted when customer or venue is deleted."""
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        # Delete customer - favorite should be deleted
        customer_id = self.customer.id
        self.customer.delete()
        
        self.assertFalse(FavoriteVenue.objects.filter(customer_id=customer_id).exists())

    def test_favorite_venue_meta_options(self):
        """Test the model meta options."""
        meta = FavoriteVenue._meta
        
        # Test verbose names
        self.assertEqual(meta.verbose_name, 'Favorite Venue')
        self.assertEqual(meta.verbose_name_plural, 'Favorite Venues')
        
        # Test ordering
        self.assertEqual(meta.ordering, ['-added_date'])
        
        # Test unique_together
        self.assertEqual(meta.unique_together, (('customer', 'venue'),))

    def test_favorite_venue_indexes(self):
        """Test that the model has the expected database indexes."""
        meta = FavoriteVenue._meta
        
        # Check that indexes are defined
        self.assertTrue(len(meta.indexes) > 0)
        
        # Check specific index fields
        index_fields = [index.fields for index in meta.indexes]
        self.assertIn(['customer', '-added_date'], index_fields)
        self.assertIn(['venue', '-added_date'], index_fields)

    def test_favorite_venue_auto_timestamps(self):
        """Test that timestamps are automatically set."""
        before_creation = timezone.now()
        
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        
        after_creation = timezone.now()
        
        # Check added_date is set and within expected range
        self.assertIsNotNone(favorite.added_date)
        self.assertGreaterEqual(favorite.added_date, before_creation)
        self.assertLessEqual(favorite.added_date, after_creation)
        
        # Check updated_at is set and within expected range
        self.assertIsNotNone(favorite.updated_at)
        self.assertGreaterEqual(favorite.updated_at, before_creation)
        self.assertLessEqual(favorite.updated_at, after_creation)

    def test_favorite_venue_update_timestamp(self):
        """Test that updated_at is updated when the model is saved."""
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        
        original_updated_at = favorite.updated_at
        
        # Wait a bit and save again
        import time
        time.sleep(0.01)
        favorite.save()
        
        # Refresh from database
        favorite.refresh_from_db()
        
        # updated_at should be different
        self.assertGreater(favorite.updated_at, original_updated_at)
