"""
Integration tests for dashboard_app.

This module contains comprehensive integration tests for complex workflows and 
end-to-end functionality in the dashboard_app, including customer dashboard workflows,
provider dashboard analytics, admin platform monitoring, and cross-app integrations.
"""

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.core.paginator import Paginator
from django.test import Client, TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

# --- Standard Library Imports ---
from datetime import date, timedelta
from decimal import Decimal

# --- Local App Imports ---
from accounts_app.models import CustomerProfile, ServiceProviderProfile, TeamMember
from booking_cart_app.models import Booking, BookingItem
from dashboard_app.models import FavoriteVenue
from venues_app.models import Category, Service, Venue

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class CustomerDashboardIntegrationTest(TestCase):
    """Test the complete customer dashboard integration workflows."""

    def setUp(self):
        """Set up test data for customer dashboard integration tests."""
        self.client = Client()

        # Create test categories
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        # Create a customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        self.customer_profile = CustomerProfile.objects.create(
            user=self.customer,
            first_name='John',
            last_name='Customer',
            phone_number='+**********'
        )

        # Create a service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa Business',
            phone='+**********',
            address='123 Business St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

        # Create test venues
        self.venue1 = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Integration Test Spa',
            short_description='A spa for integration testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='123',
            street_name='Test Street',
            operating_hours='9AM-6PM',
            approval_status='approved',
            visibility='active'
        )
        self.venue1.categories.add(self.category)

        provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass456!',
            role=User.SERVICE_PROVIDER,
        )
        provider_profile2 = ServiceProviderProfile.objects.create(
            user=provider2,
            legal_name='Second Spa Business',
            phone='+**********',
            address='456 Business Ave',
            city='Test City',
            state='CA',
            zip_code='54321',
        )

        self.venue2 = Venue.objects.create(
            service_provider=provider_profile2,
            venue_name='Second Test Venue',
            short_description='Another venue for testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='456',
            street_name='Another Street',
            operating_hours='8AM-8PM',
            approval_status='approved',
            visibility='active'
        )
        self.venue2.categories.add(self.category)

        # Create test services
        self.service1 = Service.objects.create(
            venue=self.venue1,
            service_title='Deep Tissue Massage',
            short_description='Therapeutic massage for muscle tension',
            price_min=Decimal('80.00'),
            price_max=Decimal('120.00'),
            duration_minutes=60,
            is_active=True
        )

        self.service2 = Service.objects.create(
            venue=self.venue1,
            service_title='Facial Treatment',
            short_description='Rejuvenating facial treatment',
            price_min=Decimal('60.00'),
            price_max=Decimal('90.00'),
            duration_minutes=45,
            is_active=True
        )

        # Create test bookings
        self.booking1 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue1,
            status='confirmed',
            total_price=Decimal('100.00'),
            notes='Integration test booking'
        )

        self.booking_item1 = BookingItem.objects.create(
            booking=self.booking1,
            service=self.service1,
            service_title=self.service1.service_title,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            service_price=Decimal('100.00'),
            quantity=1,
            duration_minutes=60
        )

        self.booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue2,
            status='completed',
            total_price=Decimal('80.00'),
            notes='Past booking for testing'
        )

        self.booking_item2 = BookingItem.objects.create(
            booking=self.booking2,
            service=self.service2,
            service_title=self.service2.service_title,
            scheduled_date=timezone.now().date() - timedelta(days=3),
            scheduled_time=timezone.now().time(),
            service_price=Decimal('80.00'),
            quantity=1,
            duration_minutes=60
        )

    def test_complete_customer_dashboard_workflow(self):
        """Test the complete customer dashboard workflow from login to all features."""
        # Step 1: Customer logs in
        login_success = self.client.login(email='<EMAIL>', password='CustomerPass123!')
        self.assertTrue(login_success)

        # Step 2: Access main customer dashboard
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/dashboard.html')

        # Verify dashboard context data
        self.assertIn('customer_profile', response.context)
        self.assertIn('recent_bookings', response.context)
        self.assertIn('upcoming_bookings', response.context)
        self.assertIn('total_bookings', response.context)
        self.assertIn('pending_bookings', response.context)
        self.assertIn('confirmed_bookings', response.context)

        # Check that customer profile is correct
        self.assertEqual(response.context['customer_profile'], self.customer_profile)

        # Check booking statistics
        self.assertEqual(response.context['total_bookings'], 2)
        self.assertEqual(response.context['confirmed_bookings'], 1)

        # Step 3: Access booking status page
        response = self.client.get(reverse('dashboard_app:customer_booking_status'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/booking_status.html')

        # Verify booking status context
        self.assertIn('bookings', response.context)
        self.assertIn('total_count', response.context)
        self.assertIn('pending_count', response.context)
        self.assertIn('confirmed_count', response.context)
        self.assertIn('completed_count', response.context)

        # Check that bookings are displayed
        bookings = response.context['bookings']
        self.assertEqual(len(bookings), 2)
        self.assertIn(self.booking1, bookings)
        self.assertIn(self.booking2, bookings)

        # Step 4: Test booking status filtering
        response = self.client.get(reverse('dashboard_app:customer_booking_status'), {
            'status': 'confirmed'
        })
        self.assertEqual(response.status_code, 200)
        bookings = response.context['bookings']
        self.assertEqual(len(bookings), 1)
        self.assertEqual(bookings[0], self.booking1)

        # Step 5: Test date filtering
        response = self.client.get(reverse('dashboard_app:customer_booking_status'), {
            'date': 'upcoming'
        })
        self.assertEqual(response.status_code, 200)
        bookings = response.context['bookings']
        # Should show bookings with future dates
        upcoming_bookings = [b for b in bookings if b.booking_date.date() >= timezone.now().date()]
        self.assertTrue(len(upcoming_bookings) >= 0)

        # Step 6: Access profile edit (should redirect to accounts_app)
        response = self.client.get(reverse('dashboard_app:customer_profile_edit'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:customer_profile_edit'))

        # Step 7: Access favorite venues page
        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/favorite_venues.html')

        # Verify favorite venues context
        self.assertIn('favorite_venues', response.context)
        self.assertIn('total_favorites', response.context)

        # Initially no favorites
        self.assertEqual(response.context['total_favorites'], 0)

    def test_favorite_venues_ajax_integration(self):
        """Test the complete favorite venues AJAX functionality."""
        # Login as customer
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Step 1: Check initial favorite status (should be false)
        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertFalse(data['is_favorite'])
        self.assertEqual(data['venue_id'], self.venue1.id)

        # Step 2: Add venue to favorites
        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'added')

        # Verify favorite was created in database
        self.assertTrue(FavoriteVenue.objects.filter(
            customer=self.customer,
            venue=self.venue1
        ).exists())

        # Step 3: Check favorite status again (should be true now)
        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertTrue(data['is_favorite'])

        # Step 4: Try to add same venue again (should fail)
        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data['success'])
        self.assertEqual(data['action'], 'exists')

        # Step 5: Remove venue from favorites
        response = self.client.post(reverse('dashboard_app:remove_favorite_venue', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'removed')

        # Verify favorite was removed from database
        self.assertFalse(FavoriteVenue.objects.filter(
            customer=self.customer,
            venue=self.venue1
        ).exists())

        # Step 6: Check favorite status after removal (should be false)
        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertFalse(data['is_favorite'])

        # Step 7: Try to remove non-favorite venue (should fail gracefully)
        response = self.client.post(reverse('dashboard_app:remove_favorite_venue', args=[self.venue1.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data['success'])
        self.assertEqual(data['action'], 'not_found')

    def test_customer_dashboard_pagination_integration(self):
        """Test pagination functionality in customer dashboard views."""
        # Login as customer
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Create multiple favorite venues for pagination testing
        for i in range(15):
            # Create a unique provider for each venue (since each provider can only have one venue)
            provider_user = User.objects.create_user(
                email=f'pagination_provider{i}@example.com',
                password='ProviderPass123!',
                role=User.SERVICE_PROVIDER
            )
            provider_profile = ServiceProviderProfile.objects.create(
                user=provider_user,
                legal_name=f'Test Business {i}',
                phone=f'+155512345{i:02d}',
                address=f'{100 + i} Test St',
                city='Test City',
                state='CA',
                zip_code='12345'
            )
            venue = Venue.objects.create(
                service_provider=provider_profile,
                venue_name=f'Pagination Test Venue {i}',
                short_description=f'Venue {i} for pagination testing',
                state='California',
                county='Los Angeles County',
                city='Los Angeles',
                street_number=str(100 + i),
                street_name='Pagination Street',
                operating_hours='9AM-6PM',
                approval_status='approved',
                visibility='active'
            )
            FavoriteVenue.objects.create(customer=self.customer, venue=venue)

        # Test favorite venues pagination
        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'))
        self.assertEqual(response.status_code, 200)

        # Check pagination context
        self.assertIn('page_obj', response.context)
        page_obj = response.context['page_obj']
        self.assertTrue(page_obj.has_next())  # Should have next page
        self.assertEqual(len(page_obj.object_list), 12)  # 12 per page

        # Test second page
        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'), {'page': 2})
        self.assertEqual(response.status_code, 200)
        page_obj = response.context['page_obj']
        self.assertEqual(len(page_obj.object_list), 3)  # Remaining 3 venues

    def test_customer_dashboard_cross_app_integration(self):
        """Test integration between dashboard_app and other apps."""
        # Login as customer
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Test integration with venues_app - venue data should be accessible
        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'))
        self.assertEqual(response.status_code, 200)

        # Add a venue to favorites
        self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue1.id]))

        # Verify venue data is properly displayed
        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'))
        favorite_venues = response.context['favorite_venues']
        self.assertEqual(len(favorite_venues), 1)
        favorite_venue = favorite_venues[0]
        self.assertEqual(favorite_venue.venue, self.venue1)
        self.assertEqual(favorite_venue.venue.venue_name, 'Integration Test Spa')

        # Test integration with booking_cart_app - booking data should be accessible
        response = self.client.get(reverse('dashboard_app:customer_booking_status'))
        bookings = response.context['bookings']
        self.assertEqual(len(bookings), 2)

        # Verify booking details are accessible
        booking = bookings[0]  # Most recent booking first
        self.assertIsNotNone(booking.venue)
        self.assertIsNotNone(booking.total_price)
        self.assertIn(booking.status, ['pending', 'confirmed', 'completed', 'cancelled'])

        # Test integration with accounts_app - profile redirect should work
        response = self.client.get(reverse('dashboard_app:customer_profile_edit'))
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.endswith(reverse('accounts_app:customer_profile_edit')))


@override_settings(SECURE_SSL_REDIRECT=False)
class ProviderDashboardIntegrationTest(TestCase):
    """Test the complete provider dashboard integration workflows."""

    def setUp(self):
        """Set up test data for provider dashboard integration tests."""
        self.client = Client()

        # Create test categories
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        # Create a service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Integration Test Spa Business',
            phone='+**********',
            address='123 Business St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

        # Create a customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        self.customer_profile = CustomerProfile.objects.create(
            user=self.customer,
            first_name='Jane',
            last_name='Customer',
            phone_number='+**********'
        )

        # Create test venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Provider Integration Test Spa',
            short_description='A spa for provider integration testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='123',
            street_name='Provider Street',
            operating_hours='9AM-6PM',
            approval_status='approved',
            visibility='active'
        )
        self.venue.categories.add(self.category)

        # Link venue to provider profile
        self.provider_profile.venue = self.venue
        self.provider_profile.save()

        # Create test services
        self.service1 = Service.objects.create(
            venue=self.venue,
            service_title='Provider Massage Service',
            short_description='Therapeutic massage service',
            price_min=Decimal('100.00'),
            price_max=Decimal('150.00'),
            duration_minutes=60,
            is_active=True
        )

        self.service2 = Service.objects.create(
            venue=self.venue,
            service_title='Provider Facial Service',
            short_description='Rejuvenating facial service',
            price_min=Decimal('80.00'),
            price_max=Decimal('120.00'),
            duration_minutes=45,
            is_active=True
        )

        # Create test bookings for today and other dates
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        tomorrow = today + timedelta(days=1)

        # Today's bookings
        self.booking_today1 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status='confirmed',
            total_price=Decimal('100.00'),
            notes='Today booking 1'
        )

        self.booking_item_today1 = BookingItem.objects.create(
            booking=self.booking_today1,
            service=self.service1,
            service_title=self.service1.service_title,
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('100.00'),
            quantity=1,
            duration_minutes=60
        )

        self.booking_today2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status='pending',
            total_price=Decimal('80.00'),
            notes='Today booking 2'
        )

        self.booking_item_today2 = BookingItem.objects.create(
            booking=self.booking_today2,
            service=self.service2,
            service_title=self.service2.service_title,
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('80.00'),
            quantity=1,
            duration_minutes=60
        )

        # Past booking
        self.booking_past = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status='completed',
            total_price=Decimal('120.00'),
            notes='Past booking'
        )

        self.booking_item_past = BookingItem.objects.create(
            booking=self.booking_past,
            service=self.service1,
            service_title=self.service1.service_title,
            scheduled_date=yesterday,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('120.00'),
            quantity=1,
            duration_minutes=60
        )

        # Create team members
        self.team_member1 = TeamMember.objects.create(
            service_provider=self.provider_profile,
            name='Team Member1',
            position='Massage Therapist',
            is_active=True
        )

        self.team_member2 = TeamMember.objects.create(
            service_provider=self.provider_profile,
            name='Team Member2',
            position='Receptionist',
            is_active=True
        )

    def test_complete_provider_dashboard_workflow(self):
        """Test the complete provider dashboard workflow from login to all features."""
        # Step 1: Provider logs in
        login_success = self.client.login(email='<EMAIL>', password='ProviderPass123!')
        self.assertTrue(login_success)

        # Step 2: Access main provider dashboard
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/dashboard.html')

        # Verify dashboard context data
        self.assertIn('provider_profile', response.context)
        self.assertIn('venue', response.context)
        self.assertIn('todays_bookings', response.context)
        self.assertIn('recent_bookings', response.context)
        self.assertIn('total_bookings', response.context)
        self.assertIn('pending_bookings', response.context)
        self.assertIn('confirmed_bookings', response.context)
        self.assertIn('completed_bookings', response.context)
        self.assertIn('monthly_earnings', response.context)
        self.assertIn('team_members_count', response.context)

        # Check that provider profile and venue are correct
        self.assertEqual(response.context['provider_profile'], self.provider_profile)
        self.assertEqual(response.context['venue'], self.venue)

        # Check booking statistics
        self.assertEqual(response.context['total_bookings'], 3)  # 2 today + 1 past
        self.assertEqual(response.context['pending_bookings'], 1)
        self.assertEqual(response.context['confirmed_bookings'], 1)
        self.assertEqual(response.context['completed_bookings'], 1)

        # Check team members count
        self.assertEqual(response.context['team_members_count'], 2)

        # Step 3: Access today's bookings page
        response = self.client.get(reverse('dashboard_app:provider_todays_bookings'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/todays_bookings.html')

        # Verify today's bookings context
        self.assertIn('bookings_by_time', response.context)
        self.assertIn('total_count', response.context)
        self.assertIn('pending_count', response.context)
        self.assertIn('confirmed_count', response.context)

        # Check that today's bookings are displayed
        bookings_by_time = response.context['bookings_by_time']
        total_today_items = sum(len(items) for items in bookings_by_time.values())
        self.assertEqual(total_today_items, 2)  # 2 booking items for today

        # Step 4: Test today's bookings filtering
        response = self.client.get(reverse('dashboard_app:provider_todays_bookings'), {
            'status': 'confirmed'
        })
        self.assertEqual(response.status_code, 200)
        bookings_by_time = response.context['bookings_by_time']
        confirmed_items = sum(len(items) for items in bookings_by_time.values())
        self.assertEqual(confirmed_items, 1)  # Only 1 confirmed booking item

        # Step 5: Access earnings reports page
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/earnings_reports.html')

        # Verify earnings context
        self.assertIn('form', response.context)
        self.assertIn('total_earnings', response.context)
        self.assertIn('total_bookings', response.context)
        self.assertIn('avg_booking_value', response.context)
        self.assertIn('earnings_by_service', response.context)
        self.assertIn('daily_earnings', response.context)

        # Check earnings calculations
        total_earnings = response.context['total_earnings']
        self.assertGreater(total_earnings, Decimal('0.00'))

        # Step 6: Test earnings reports with date filtering
        today = timezone.now().date()
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'), {
            'period': 'today'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['start_date'], today)
        self.assertEqual(response.context['end_date'], today)

        # Step 7: Access service performance page
        response = self.client.get(reverse('dashboard_app:provider_service_performance'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/service_performance.html')

        # Verify service performance context
        self.assertIn('service_performance', response.context)
        service_performance = response.context['service_performance']
        self.assertEqual(len(service_performance), 2)  # 2 services

        # Check service performance data structure
        for service_data in service_performance:
            self.assertIn('service', service_data)
            self.assertIn('total_bookings', service_data)
            self.assertIn('total_revenue', service_data)
            self.assertIn('recent_bookings', service_data)
            self.assertIn('revenue_per_booking', service_data)

        # Step 8: Access team management (should redirect to accounts_app)
        response = self.client.get(reverse('dashboard_app:provider_team_management'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:team_member_list'))

    def test_provider_dashboard_date_range_filtering(self):
        """Test date range filtering functionality in provider dashboard views."""
        # Login as provider
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        # Test earnings reports with different date ranges
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        # Test "yesterday" period
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'), {
            'period': 'yesterday'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['start_date'], yesterday)
        self.assertEqual(response.context['end_date'], yesterday)

        # Test "this_week" period
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'), {
            'period': 'this_week'
        })
        self.assertEqual(response.status_code, 200)
        start_date = response.context['start_date']
        end_date = response.context['end_date']
        self.assertLessEqual(start_date, today)
        self.assertEqual(end_date, today)

        # Test custom date range
        custom_start = today - timedelta(days=7)
        custom_end = today
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'), {
            'period': 'custom',
            'start_date': custom_start.isoformat(),
            'end_date': custom_end.isoformat()
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['start_date'], custom_start)
        self.assertEqual(response.context['end_date'], custom_end)

    def test_provider_dashboard_cross_app_integration(self):
        """Test integration between provider dashboard and other apps."""
        # Login as provider
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        # Test integration with venues_app - venue data should be accessible
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        venue = response.context['venue']
        self.assertEqual(venue.venue_name, 'Provider Integration Test Spa')
        self.assertEqual(venue.service_provider, self.provider_profile)

        # Test integration with booking_cart_app - booking data should be accessible
        response = self.client.get(reverse('dashboard_app:provider_todays_bookings'))
        bookings_by_time = response.context['bookings_by_time']

        # Verify booking items are accessible with related data
        for time_slot, items in bookings_by_time.items():
            for item in items:
                self.assertIsNotNone(item.booking)
                self.assertIsNotNone(item.service)
                self.assertEqual(item.booking.venue, self.venue)

        # Test integration with accounts_app - team management redirect should work
        response = self.client.get(reverse('dashboard_app:provider_team_management'))
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.endswith(reverse('accounts_app:team_member_list')))

        # Test service performance integration
        response = self.client.get(reverse('dashboard_app:provider_service_performance'))
        service_performance = response.context['service_performance']

        # Verify service data is properly integrated
        for service_data in service_performance:
            service = service_data['service']
            self.assertEqual(service.venue, self.venue)
            self.assertIn(service, [self.service1, self.service2])
            self.assertGreaterEqual(service_data['total_bookings'], 0)
            self.assertGreaterEqual(service_data['total_revenue'], Decimal('0.00'))


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminDashboardIntegrationTest(TestCase):
    """Test the complete admin dashboard integration workflows."""

    def setUp(self):
        """Set up test data for admin dashboard integration tests."""
        self.client = Client()

        # Create test categories
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        # Create an admin user
        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='AdminPass123!',
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True
        )

        # Create a service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Admin Test Spa Business',
            phone='+**********',
            address='123 Business St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

        # Create a customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        self.customer_profile = CustomerProfile.objects.create(
            user=self.customer,
            first_name='Admin',
            last_name='TestCustomer',
            phone_number='+**********'
        )

        # Create test venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Admin Integration Test Spa',
            short_description='A spa for admin integration testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='123',
            street_name='Admin Street',
            operating_hours='9AM-6PM',
            approval_status='approved',
            visibility='active'
        )
        self.venue.categories.add(self.category)

        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Admin Test Service',
            short_description='Service for admin testing',
            price_min=Decimal('100.00'),
            price_max=Decimal('150.00'),
            duration_minutes=60,
            is_active=True
        )

        # Create test bookings
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        self.booking1 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status='confirmed',
            total_price=Decimal('100.00'),
            notes='Admin test booking 1'
        )

        self.booking_item1 = BookingItem.objects.create(
            booking=self.booking1,
            service=self.service,
            service_title=self.service.service_title,
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('100.00'),
            quantity=1,
            duration_minutes=60
        )

        self.booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status='completed',
            total_price=Decimal('120.00'),
            notes='Admin test booking 2'
        )

        self.booking_item2 = BookingItem.objects.create(
            booking=self.booking2,
            service=self.service,
            service_title=self.service.service_title,
            scheduled_date=yesterday,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('120.00'),
            quantity=1,
            duration_minutes=60
        )

    def test_complete_admin_dashboard_workflow(self):
        """Test the complete admin dashboard workflow from login to all features."""
        # Step 1: Admin logs in
        login_success = self.client.login(email='<EMAIL>', password='AdminPass123!')
        self.assertTrue(login_success)

        # Step 2: Access main admin dashboard
        response = self.client.get(reverse('dashboard_app:admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/dashboard.html')

        # Verify dashboard context data
        self.assertIn('total_users', response.context)
        self.assertIn('customer_count', response.context)
        self.assertIn('provider_count', response.context)
        self.assertIn('staff_count', response.context)
        self.assertIn('total_venues', response.context)
        self.assertIn('pending_venues', response.context)
        self.assertIn('approved_venues', response.context)
        self.assertIn('total_bookings', response.context)
        self.assertIn('pending_bookings', response.context)
        self.assertIn('confirmed_bookings', response.context)
        self.assertIn('cancelled_bookings', response.context)
        self.assertIn('completed_bookings', response.context)
        self.assertIn('total_revenue', response.context)
        self.assertIn('recent_bookings', response.context)

        # Check statistics
        self.assertEqual(response.context['total_users'], 3)  # admin + provider + customer
        self.assertEqual(response.context['customer_count'], 1)
        self.assertEqual(response.context['provider_count'], 1)
        self.assertEqual(response.context['staff_count'], 1)
        self.assertEqual(response.context['total_venues'], 1)
        self.assertEqual(response.context['approved_venues'], 1)
        self.assertEqual(response.context['total_bookings'], 2)
        self.assertEqual(response.context['confirmed_bookings'], 1)
        self.assertEqual(response.context['completed_bookings'], 1)

        # Step 3: Access platform overview
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/platform_overview.html')

        # Verify platform overview context
        self.assertIn('form', response.context)
        self.assertIn('start_date', response.context)
        self.assertIn('end_date', response.context)
        self.assertIn('total_users', response.context)
        self.assertIn('new_users', response.context)
        self.assertIn('total_venues', response.context)
        self.assertIn('new_venues', response.context)
        self.assertIn('total_bookings', response.context)
        self.assertIn('new_bookings', response.context)
        self.assertIn('total_revenue', response.context)
        self.assertIn('period_revenue', response.context)
        self.assertIn('daily_stats', response.context)

        # Step 4: Access user statistics
        response = self.client.get(reverse('dashboard_app:admin_user_statistics'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/user_statistics.html')

        # Verify user statistics context
        self.assertIn('total_users', response.context)
        self.assertIn('customer_count', response.context)
        self.assertIn('provider_count', response.context)
        self.assertIn('staff_count', response.context)
        self.assertIn('new_users_count', response.context)
        self.assertIn('active_users_count', response.context)
        self.assertIn('daily_registrations', response.context)
        self.assertIn('top_customers', response.context)
        self.assertIn('top_providers', response.context)

        # Step 5: Access booking analytics (should redirect to booking_cart_app)
        response = self.client.get(reverse('dashboard_app:admin_booking_analytics'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('booking_cart_app:admin_booking_analytics'))

        # Step 6: Access revenue tracking
        response = self.client.get(reverse('dashboard_app:admin_revenue_tracking'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/revenue_tracking.html')

        # Verify revenue tracking context
        self.assertIn('form', response.context)
        self.assertIn('total_revenue', response.context)
        self.assertIn('daily_revenue', response.context)
        self.assertIn('top_venues', response.context)
        self.assertIn('top_services', response.context)

        # Step 7: Access system health
        response = self.client.get(reverse('dashboard_app:admin_system_health'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/admin/system_health.html')

        # Verify system health context
        self.assertIn('db_stats', response.context)
        self.assertIn('performance_metrics', response.context)
        self.assertIn('recent_events', response.context)
        self.assertIn('app_health', response.context)
        self.assertIn('health_score', response.context)
        self.assertIn('health_status', response.context)

    def test_admin_dashboard_date_range_filtering(self):
        """Test date range filtering functionality in admin dashboard views."""
        # Login as admin
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Test platform overview with different date ranges
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        # Test "today" period
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'), {
            'period': 'today'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['start_date'], today)
        self.assertEqual(response.context['end_date'], today)

        # Test "this_month" period
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'), {
            'period': 'this_month'
        })
        self.assertEqual(response.status_code, 200)
        start_date = response.context['start_date']
        end_date = response.context['end_date']
        self.assertEqual(start_date, today.replace(day=1))
        self.assertEqual(end_date, today)

        # Test custom date range
        custom_start = today - timedelta(days=7)
        custom_end = today
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'), {
            'period': 'custom',
            'start_date': custom_start.isoformat(),
            'end_date': custom_end.isoformat()
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['start_date'], custom_start)
        self.assertEqual(response.context['end_date'], custom_end)

        # Test user statistics with date filtering
        response = self.client.get(reverse('dashboard_app:admin_user_statistics'), {
            'period': 'this_week'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('daily_registrations', response.context)

        # Test revenue tracking with date filtering
        response = self.client.get(reverse('dashboard_app:admin_revenue_tracking'), {
            'period': 'last_week'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('daily_revenue', response.context)

    def test_admin_dashboard_cross_app_integration(self):
        """Test integration between admin dashboard and other apps."""
        # Login as admin
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Test integration with accounts_app - user data should be accessible
        response = self.client.get(reverse('dashboard_app:admin_user_statistics'))
        self.assertEqual(response.status_code, 200)

        # Verify user data is accessible
        self.assertGreater(response.context['total_users'], 0)
        self.assertGreater(response.context['customer_count'], 0)
        self.assertGreater(response.context['provider_count'], 0)

        # Test integration with venues_app - venue data should be accessible
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'))
        self.assertEqual(response.status_code, 200)

        # Verify venue data is accessible
        self.assertGreater(response.context['total_venues'], 0)
        self.assertGreaterEqual(response.context['approved_venues'], 0)

        # Test integration with booking_cart_app - booking data should be accessible
        response = self.client.get(reverse('dashboard_app:admin_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Verify booking data is accessible
        self.assertGreater(response.context['total_bookings'], 0)
        self.assertGreaterEqual(response.context['confirmed_bookings'], 0)
        self.assertGreaterEqual(response.context['completed_bookings'], 0)

        # Test revenue tracking integration
        response = self.client.get(reverse('dashboard_app:admin_revenue_tracking'))
        self.assertEqual(response.status_code, 200)

        # Verify revenue data is accessible
        self.assertIn('total_revenue', response.context)
        self.assertIn('top_venues', response.context)
        self.assertIn('top_services', response.context)


@override_settings(SECURE_SSL_REDIRECT=False)
class DashboardSecurityIntegrationTest(TestCase):
    """Test security and permission integration across dashboard views."""

    def setUp(self):
        """Set up test data for security integration tests."""
        self.client = Client()

        # Create test categories
        self.category = Category.objects.create(
            category_name='Security Test Category',
            category_description='Category for security testing'
        )

        # Create users with different roles
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(
            user=self.customer,
            first_name='Security',
            last_name='Customer',
            phone_number='+**********'
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Security Test Business',
            phone='+**********',
            address='123 Security St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='AdminPass123!',
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True
        )

        # Create test venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Security Test Venue',
            short_description='Venue for security testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='123',
            street_name='Security Street',
            operating_hours='9AM-6PM',
            approval_status='approved',
            visibility='active'
        )
        self.venue.categories.add(self.category)

    def test_role_based_dashboard_access_control(self):
        """Test that users can only access dashboards appropriate to their role."""
        # Test customer access
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Customer should access customer dashboard
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Customer should NOT access provider dashboard
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('service provider' in str(m) for m in messages))

        # Customer should NOT access admin dashboard
        response = self.client.get(reverse('dashboard_app:admin_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('admin' in str(m) for m in messages))

        self.client.logout()

        # Test provider access
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        # Provider should NOT access customer dashboard
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('customer' in str(m) for m in messages))

        # Provider should access provider dashboard
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Provider should NOT access admin dashboard
        response = self.client.get(reverse('dashboard_app:admin_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('admin' in str(m) for m in messages))

        self.client.logout()

        # Test admin access
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Admin should access admin dashboard
        response = self.client.get(reverse('dashboard_app:admin_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Admin should NOT access customer dashboard (not a customer)
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect

        # Admin should NOT access provider dashboard (not a provider)
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect

    def test_ajax_endpoint_security(self):
        """Test security of AJAX endpoints for favorite venues."""
        # Test unauthenticated access
        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]))
        self.assertEqual(response.status_code, 302)  # Redirect to login

        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue.id]))
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Test provider trying to use customer-only endpoints
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        # Test AJAX requests (should return 403)
        response = self.client.post(
            reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]),
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        self.assertEqual(response.status_code, 403)  # Forbidden
        data = response.json()
        self.assertFalse(data['success'])
        self.assertIn('customer', data['message'].lower())

        response = self.client.get(
            reverse('dashboard_app:check_favorite_status', args=[self.venue.id]),
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        self.assertEqual(response.status_code, 403)  # Forbidden
        data = response.json()
        self.assertFalse(data['success'])
        self.assertIn('customer', data['message'].lower())

        # Test non-AJAX requests (should redirect with 302)
        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]))
        self.assertEqual(response.status_code, 302)  # Redirect

        self.client.logout()

        # Test admin trying to use customer-only endpoints
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Test AJAX requests (should return 403)
        response = self.client.post(
            reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]),
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        self.assertEqual(response.status_code, 403)  # Forbidden

        response = self.client.get(
            reverse('dashboard_app:check_favorite_status', args=[self.venue.id]),
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        self.assertEqual(response.status_code, 403)  # Forbidden

        # Test non-AJAX requests (should redirect with 302)
        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]))
        self.assertEqual(response.status_code, 302)  # Redirect

        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue.id]))
        self.assertEqual(response.status_code, 302)  # Redirect

        self.client.logout()

        # Test customer access (should work)
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        response = self.client.get(reverse('dashboard_app:check_favorite_status', args=[self.venue.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])

        response = self.client.post(reverse('dashboard_app:add_favorite_venue', args=[self.venue.id]))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])

    def test_cross_dashboard_navigation_security(self):
        """Test security when navigating between different dashboard types."""
        # Login as customer
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Customer accesses customer dashboard
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Customer tries to access provider features
        response = self.client.get(reverse('dashboard_app:provider_todays_bookings'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Customer tries to access admin features
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        response = self.client.get(reverse('dashboard_app:admin_system_health'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        self.client.logout()

        # Login as provider
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        # Provider accesses provider dashboard
        response = self.client.get(reverse('dashboard_app:provider_dashboard'))
        self.assertEqual(response.status_code, 200)

        # Provider tries to access customer features
        response = self.client.get(reverse('dashboard_app:customer_booking_status'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        response = self.client.get(reverse('dashboard_app:customer_favorite_venues'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Provider tries to access admin features
        response = self.client.get(reverse('dashboard_app:admin_user_statistics'))
        self.assertEqual(response.status_code, 302)  # Should redirect

        response = self.client.get(reverse('dashboard_app:admin_revenue_tracking'))
        self.assertEqual(response.status_code, 302)  # Should redirect
