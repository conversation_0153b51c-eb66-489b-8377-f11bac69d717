from django.test import TestCase, Client
from django.urls import reverse


class HealthCheckTests(TestCase):
    def setUp(self):
        self.client = Client()

    def test_health_check(self):
        response = self.client.get(reverse('dashboard_app:health_check'), secure=True)
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content.decode(), {'status': 'ok'})
