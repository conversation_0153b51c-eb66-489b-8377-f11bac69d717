# """Middleware for restricting dashboard access by user role."""

# --- Third-Party Imports ---
from django.contrib import messages
from django.shortcuts import redirect

# --- Local App Imports ---
from .constants import ADMIN_ONLY_ERROR, CUSTOMER_ONLY_ERROR, PROVIDER_ONLY_ERROR
from .logging_utils import log_unauthorized_dashboard_access


# --- Dashboard Access Middleware ---
class DashboardAccessMiddleware:
    """Log and redirect unauthorized dashboard access attempts."""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        path = request.path
        user = request.user

        if path.startswith('/dashboard/') and path != '/dashboard/health/':
            if not user.is_authenticated:
                return redirect('accounts_app:customer_login')
            if path.startswith('/dashboard/customer') and not user.is_customer:
                self._log(request, user, 'customer', CUSTOMER_ONLY_ERROR)
                return redirect('venues_app:home')
            if path.startswith('/dashboard/provider') and not user.is_service_provider:
                self._log(request, user, 'provider', PROVIDER_ONLY_ERROR)
                return redirect('venues_app:home')
            if path.startswith('/dashboard/admin') and not user.is_staff:
                self._log(request, user, 'admin', ADMIN_ONLY_ERROR)
                return redirect('venues_app:home')
        return self.get_response(request)

    def _log(self, request, user, dashboard_type, error_msg):
        log_unauthorized_dashboard_access(
            user_email=user.email,
            attempted_dashboard=dashboard_type,
            user_role=user.role,
            request=request,
            additional_details={'user_id': user.id},
        )
        messages.error(request, error_msg)
