"""Views package for dashboard_app.

This package splits the original monolithic views module into
customer, provider, and admin modules while maintaining backwards
compatibility. All view functions and helper imports are re-exported
here so existing imports continue to work."""

# Re-export logging utilities for test patching compatibility
# --- Local App Imports ---
from ..logging_utils import (
    log_admin_dashboard_access,
    log_admin_data_access,
    log_analytics_access,
    log_booking_status_access,
    log_dashboard_access,
    log_dashboard_activity,
    log_dashboard_filter_usage,
    log_error,
    log_favorite_venue_event,
    log_profile_access,
    log_provider_dashboard_activity,
    log_provider_earnings_access,
    log_provider_service_stats_access,
    log_security_event,
    log_system_health_check,
    performance_monitor,
)

# Re-export models used in tests for patching
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Service, Venue

# Import view functions grouped by feature area
from .common import health_check
from .customer import (
    customer_dashboard_view,
    customer_booking_status_view,
    customer_profile_edit_view,
    customer_profile_quick_update,
    customer_settings_toggle,
    customer_favorite_venues_view,
    customer_booking_reschedule_request,
    add_favorite_venue_view,
    remove_favorite_venue_view,
    check_favorite_status_view,
)
from .provider import (
    ProviderDashboardView,
    provider_todays_bookings_view,
    provider_earnings_reports_view,
    provider_service_performance_view,
    provider_earnings_export,
    provider_team_management_view,
)
from .admin import (
    admin_dashboard,
    admin_platform_overview,
    admin_user_statistics,
    admin_booking_analytics,
    admin_revenue_tracking,
    admin_revenue_export,
    admin_system_health,
)

__all__ = [
    # Logging utilities
    'log_dashboard_activity', 'log_dashboard_access', 'log_favorite_venue_event',
    'log_booking_status_access', 'log_profile_access', 'log_error', 'log_security_event',
    'performance_monitor', 'log_provider_dashboard_activity', 'log_provider_earnings_access',
    'log_provider_service_stats_access', 'log_admin_dashboard_access', 'log_admin_data_access',
    'log_system_health_check', 'log_analytics_access', 'log_dashboard_filter_usage',
    # Models
    'CustomUser', 'CustomerProfile', 'ServiceProviderProfile', 'TeamMember',
    'Booking', 'BookingItem', 'Venue', 'Service',
    # Common views
    'health_check',
    # Customer views
    'customer_dashboard_view', 'customer_booking_status_view',
    'customer_profile_edit_view', 'customer_profile_quick_update', 'customer_settings_toggle',
    'customer_favorite_venues_view', 'customer_booking_reschedule_request',
    'add_favorite_venue_view', 'remove_favorite_venue_view',
    'check_favorite_status_view',
    # Provider views
    'ProviderDashboardView', 'provider_todays_bookings_view',
    'provider_earnings_reports_view', 'provider_service_performance_view',
    'provider_earnings_export', 'provider_team_management_view',
    # Admin views
    'admin_dashboard', 'admin_platform_overview', 'admin_user_statistics',
    'admin_booking_analytics', 'admin_revenue_tracking', 'admin_revenue_export',
    'admin_system_health',
]
