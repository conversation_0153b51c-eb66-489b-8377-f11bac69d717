import bleach
import html
import re

ALLOWED_TAGS = [
    'b', 'i', 'u', 'em', 'strong', 'br', 'ul', 'ol', 'li', 'p'
]

# Tags that should be completely removed (including their content)
DANGEROUS_TAGS = ['script', 'style', 'iframe', 'object', 'embed']


def sanitize_html(value: str) -> str:
    """Sanitize user provided HTML input."""
    if not value:
        return value

    # First, remove dangerous tags and their content completely
    for tag in DANGEROUS_TAGS:
        pattern = f'<{tag}[^>]*>.*?</{tag}>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)
        # Also remove self-closing dangerous tags
        pattern = f'<{tag}[^>]*/?>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE)

    # Then clean with bleach to handle remaining tags
    return bleach.clean(value, tags=ALLOWED_TAGS, strip=True)


def sanitize_text(value: str) -> str:
    """Sanitize plain text input to prevent XSS attacks."""
    if not value:
        return value
    
    # Escape HTML entities to prevent XSS
    sanitized = html.escape(value)
    
    # Remove null bytes and other dangerous characters
    sanitized = sanitized.replace('\x00', '')
    
    # Normalize whitespace
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()
    
    return sanitized


def sanitize_filename(value: str) -> str:
    """Sanitize filename to prevent path traversal attacks."""
    if not value:
        return value
    
    # Remove dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', value)
    
    # Remove path traversal attempts
    sanitized = sanitized.replace('..', '')
    
    # Limit length
    sanitized = sanitized[:255]
    
    return sanitized.strip()


def sanitize_url(value: str) -> str:
    """Sanitize URL input to prevent XSS and malicious redirects."""
    if not value:
        return value
    
    # Basic URL validation
    if not value.startswith(('http://', 'https://', 'ftp://', 'mailto:')):
        # Prepend https:// if no scheme provided
        value = f'https://{value}'
    
    # Remove dangerous schemes
    dangerous_schemes = ['javascript:', 'data:', 'vbscript:', 'file:']
    for scheme in dangerous_schemes:
        if value.lower().startswith(scheme):
            return ''
    
    return html.escape(value)
