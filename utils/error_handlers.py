"""
Custom error handlers for CozyWish project.

This module provides professional error handling views for 404, 500, 403, and 400 errors.
All error events are logged using the centralized logging system.
"""

from django.shortcuts import render
from django.http import HttpResponseNotFound, HttpResponseServerError, HttpResponseForbidden, HttpResponseBadRequest
from django.template import TemplateDoesNotExist
from django.conf import settings
from utils.logging_utils import log_error
import logging

logger = logging.getLogger(__name__)


def custom_404_view(request, exception=None):
    """
    Custom 404 error handler.
    
    Logs the 404 error and renders a user-friendly error page.
    """
    # Log the 404 error
    log_error(
        app_name='utils',
        error_type='page_not_found',
        error_message=f'404 error for path: {request.path}',
        request=request,
        details={
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    try:
        response = render(request, '404.html', {
            'request_path': request.path,
            'support_email': '<EMAIL>',
        })
        response.status_code = 404
        return response
    except TemplateDoesNotExist:
        # Fallback if template doesn't exist
        return HttpResponseNotFound(
            '<h1>Page Not Found</h1>'
            '<p>The page you requested could not be found.</p>'
            '<p><a href="/">Return to Home</a></p>'
        )


def custom_500_view(request):
    """
    Custom 500 error handler.
    
    Logs the 500 error and renders a user-friendly error page.
    """
    # Log the 500 error
    log_error(
        app_name='utils',
        error_type='server_error',
        error_message=f'500 error for path: {request.path}',
        request=request,
        details={
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    try:
        response = render(request, '500.html', {
            'request_path': request.path,
            'support_email': '<EMAIL>',
            'debug': settings.DEBUG,
        })
        response.status_code = 500
        return response
    except TemplateDoesNotExist:
        # Fallback if template doesn't exist
        return HttpResponseServerError(
            '<h1>Server Error</h1>'
            '<p>An internal server error occurred. Please try again later.</p>'
            '<p><a href="/">Return to Home</a></p>'
        )


def custom_403_view(request, exception=None):
    """
    Custom 403 error handler.
    
    Logs the 403 error and renders a user-friendly error page.
    """
    # Log the 403 error
    log_error(
        app_name='utils',
        error_type='permission_denied',
        error_message=f'403 error for path: {request.path}',
        user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
        request=request,
        details={
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    try:
        response = render(request, '403.html', {
            'request_path': request.path,
            'support_email': '<EMAIL>',
        })
        response.status_code = 403
        return response
    except TemplateDoesNotExist:
        # Fallback if template doesn't exist
        return HttpResponseForbidden(
            '<h1>Permission Denied</h1>'
            '<p>You do not have permission to access this page.</p>'
            '<p><a href="/">Return to Home</a></p>'
        )


def custom_400_view(request, exception=None):
    """
    Custom 400 error handler.
    
    Logs the 400 error and renders a user-friendly error page.
    """
    # Log the 400 error
    log_error(
        app_name='utils',
        error_type='bad_request',
        error_message=f'400 error for path: {request.path}',
        request=request,
        details={
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    try:
        response = render(request, '400.html', {
            'request_path': request.path,
            'support_email': '<EMAIL>',
        })
        response.status_code = 400
        return response
    except TemplateDoesNotExist:
        # Fallback if template doesn't exist
        return HttpResponseBadRequest(
            '<h1>Bad Request</h1>'
            '<p>Your request could not be processed.</p>'
            '<p><a href="/">Return to Home</a></p>'
        )
