"""
Centralized logging utilities for CozyWish project.

This module provides structured logging functions that can be used across
all Django apps in the CozyWish project. All logs are sent to stdout/stderr
for Render's centralized monitoring system.

Usage:
    from utils.logging_utils import get_app_logger, log_error, log_security_event
    
    # Get a logger for your app
    logger = get_app_logger('venues_app')
    logger.info("Venue created successfully")
    
    # Log specific event types
    log_error('venues_app', 'validation_error', 'Invalid venue data', user=request.user)
    log_security_event('venues_app', 'unauthorized_access', user_email='<EMAIL>')
"""

import logging
import traceback
from typing import Optional, Dict, Any
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.http import HttpRequest

User = get_user_model()


def get_app_logger(app_name: str, logger_type: str = '') -> logging.Logger:
    """
    Get a logger for a specific app and logger type.
    
    Args:
        app_name: Name of the Django app (e.g., 'venues_app', 'booking_cart_app')
        logger_type: Type of logger ('security', 'activity', 'errors', 'performance', 'audit')
    
    Returns:
        Configured logger instance
    """
    if logger_type:
        logger_name = f"{app_name}.{logger_type}"
    else:
        logger_name = app_name
    
    return logging.getLogger(logger_name)


def get_client_info(request: Optional[HttpRequest]) -> Dict[str, Any]:
    """
    Extract client information from Django request.
    
    Args:
        request: Django HttpRequest object
    
    Returns:
        Dictionary containing client information
    """
    if not request:
        return {
            'ip_address': 'unknown',
            'user_agent': 'unknown',
            'path': 'unknown',
            'method': 'unknown'
        }
    
    # Get real IP address (considering proxies)
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip_address = x_forwarded_for.split(',')[0].strip()
    else:
        ip_address = request.META.get('REMOTE_ADDR', 'unknown')
    
    return {
        'ip_address': ip_address,
        'user_agent': request.META.get('HTTP_USER_AGENT', 'unknown'),
        'path': request.path,
        'method': request.method
    }


def log_security_event(
    app_name: str,
    event_type: str,
    user_email: Optional[str] = None,
    user_id: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    severity: str = 'INFO'
) -> None:
    """
    Log security-related events.
    
    Args:
        app_name: Name of the Django app
        event_type: Type of security event
        user_email: Email of the user involved
        user_id: ID of the user involved
        request: Django HttpRequest object
        details: Additional details about the event
        severity: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    logger = get_app_logger(app_name, 'security')
    client_info = get_client_info(request)
    
    log_data = {
        'event_type': event_type,
        'timestamp': timezone.now().isoformat(),
        'user_email': user_email,
        'user_id': user_id,
        'severity': severity,
        **client_info
    }
    
    if details:
        log_data.update(details)
    
    message = f"SECURITY EVENT: {event_type}"
    if user_email:
        message += f" | User: {user_email}"
    
    getattr(logger, severity.lower(), logger.info)(message, extra=log_data)


def log_user_activity(
    app_name: str,
    activity_type: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    target_object: Optional[str] = None
) -> None:
    """
    Log user activity events.
    
    Args:
        app_name: Name of the Django app
        activity_type: Type of activity
        user: User object
        request: Django HttpRequest object
        details: Additional details about the activity
        target_object: Object being acted upon (if applicable)
    """
    logger = get_app_logger(app_name, 'activity')
    client_info = get_client_info(request)
    
    log_data = {
        'activity_type': activity_type,
        'timestamp': timezone.now().isoformat(),
        'user_email': user.email if user else None,
        'user_id': user.id if user else None,
        'user_role': user.role if user else None,
        'target_object': target_object,
        **client_info
    }
    
    if details:
        log_data.update(details)
    
    message = f"USER ACTIVITY: {activity_type}"
    if user:
        message += f" | User: {user.email}"
    if target_object:
        message += f" | Target: {target_object}"
    
    logger.info(message, extra=log_data)


def log_error(
    app_name: str,
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log error events.
    
    Args:
        app_name: Name of the Django app
        error_type: Type of error
        error_message: Error message
        user: User object (if applicable)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        details: Additional error details
    """
    logger = get_app_logger(app_name, 'errors')
    client_info = get_client_info(request)
    
    log_data = {
        'error_type': error_type,
        'error_message': error_message,
        'timestamp': timezone.now().isoformat(),
        'user_email': user.email if user else None,
        'user_id': user.id if user else None,
        **client_info
    }
    
    if exception:
        log_data.update({
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc()
        })
    
    if details:
        log_data.update(details)
    
    message = f"ERROR: {error_type} - {error_message}"
    if user:
        message += f" | User: {user.email}"
    
    logger.error(message, extra=log_data)


def log_performance(
    app_name: str,
    operation: str,
    duration: float,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log performance metrics for operations.
    
    Args:
        app_name: Name of the Django app
        operation: Name of the operation
        duration: Duration in seconds
        user: User object (if applicable)
        request: Django HttpRequest object
        details: Additional performance details
    """
    logger = get_app_logger(app_name, 'performance')
    client_info = get_client_info(request)
    
    log_data = {
        'operation': operation,
        'duration_seconds': duration,
        'timestamp': timezone.now().isoformat(),
        'user_email': user.email if user else None,
        'user_id': user.id if user else None,
        **client_info
    }
    
    if details:
        log_data.update(details)
    
    message = f"PERFORMANCE: {operation} took {duration:.3f}s"
    if user:
        message += f" | User: {user.email}"
    
    logger.debug(message, extra=log_data)


def log_audit_event(
    app_name: str,
    action: str,
    admin_user: User,
    target_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    changes: Optional[Dict[str, Any]] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log audit trail events for administrative actions.
    
    Args:
        app_name: Name of the Django app
        action: Administrative action performed
        admin_user: User performing the action
        target_user: User being acted upon (if applicable)
        request: Django HttpRequest object
        changes: Dictionary of changes made
        details: Additional audit details
    """
    logger = get_app_logger(app_name, 'audit')
    client_info = get_client_info(request)
    
    log_data = {
        'action': action,
        'timestamp': timezone.now().isoformat(),
        'admin_email': admin_user.email,
        'admin_id': admin_user.id,
        'target_email': target_user.email if target_user else None,
        'target_id': target_user.id if target_user else None,
        'changes': changes,
        **client_info
    }
    
    if details:
        log_data.update(details)
    
    message = f"AUDIT: {action} by {admin_user.email}"
    if target_user:
        message += f" on {target_user.email}"
    
    logger.info(message, extra=log_data)
