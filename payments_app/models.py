"""
Models for the payments_app Django application.

This module contains models for handling payments, refund requests, and payment-related
functionality for the CozyWish platform.
"""

# Standard library imports
import uuid
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models, transaction
from django.db.models import F
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Local imports
from booking_cart_app.models import Booking


class Payment(models.Model):
    """
    Model for storing payment transaction information.
    Links payments to bookings and tracks Stripe payment details.
    """
    
    # Payment status choices
    PENDING = 'pending'
    PROCESSING = 'processing'
    SUCCEEDED = 'succeeded'
    FAILED = 'failed'
    CANCELLED = 'cancelled'
    REQUIRES_ACTION = 'requires_action'
    REFUNDED = 'refunded'
    PARTIALLY_REFUNDED = 'partially_refunded'
    
    STATUS_CHOICES = [
        (PENDING, _('Pending')),
        (PROCESSING, _('Processing')),
        (SUCCEEDED, _('Succeeded')),
        (FAILED, _('Failed')),
        (CANCELLED, _('Cancelled')),
        (REQUIRES_ACTION, _('Requires Action')),
        (REFUNDED, _('Refunded')),
        (PARTIALLY_REFUNDED, _('Partially Refunded')),
    ]
    
    # Payment method choices
    CREDIT_CARD = 'credit_card'
    DEBIT_CARD = 'debit_card'
    STRIPE = 'stripe'
    
    PAYMENT_METHOD_CHOICES = [
        (CREDIT_CARD, _('Credit Card')),
        (DEBIT_CARD, _('Debit Card')),
        (STRIPE, _('Stripe')),
    ]
    
    payment_id = models.UUIDField(
        default=uuid.uuid4,
        editable=False,
        unique=True,
        help_text=_('Unique payment identifier')
    )
    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='payments',
        help_text=_('Booking this payment is for')
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments',
        help_text=_('Customer who made the payment')
    )
    provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments',
        limit_choices_to={'role': 'service_provider'},
        help_text=_('Service provider receiving the payment')
    )
    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Total amount paid by customer')
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        default=STRIPE,
        help_text=_('Payment method used')
    )
    stripe_payment_intent_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text=_('Stripe Payment Intent ID (placeholder for future implementation)')
    )
    stripe_charge_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text=_('Stripe Charge ID (placeholder for future implementation)')
    )
    payment_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Current status of the payment')
    )
    payment_date = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the payment was initiated')
    )
    completed_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the payment was completed')
    )
    failure_reason = models.TextField(
        blank=True,
        help_text=_('Reason for payment failure (if applicable)')
    )
    refunded_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Total amount refunded')
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['customer', '-payment_date']),
            models.Index(fields=['provider', '-payment_date']),
            models.Index(fields=['payment_status']),
            models.Index(fields=['stripe_payment_intent_id']),
            models.Index(fields=['booking']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['booking'],
                condition=models.Q(payment_status='succeeded'),
                name='unique_booking_successful_payment'
            )
        ]
    
    def __str__(self):
        return f"Payment {self.payment_id} - {self.customer.email} - ${self.amount_paid}"
    
    def clean(self):
        """Validate payment data."""
        super().clean()
        
        # Validate refunded amount doesn't exceed paid amount
        if self.refunded_amount > self.amount_paid:
            raise ValidationError(_('Refunded amount cannot exceed paid amount'))
        
        # Validate customer is actually a customer
        if hasattr(self.customer, 'role') and self.customer.role != 'customer':
            raise ValidationError(_('Payment customer must have customer role'))
        
        # Validate provider is actually a service provider
        if hasattr(self.provider, 'role') and self.provider.role != 'service_provider':
            raise ValidationError(_('Payment provider must have service provider role'))
    
    def save(self, *args, **kwargs):
        """Override save to set completed_date when payment succeeds."""
        if self.payment_status == self.SUCCEEDED and not self.completed_date:
            self.completed_date = timezone.now()
        super().save(*args, **kwargs)
    
    @property
    def is_refundable(self):
        """Check if payment can be refunded."""
        return (
            self.payment_status in [self.SUCCEEDED, self.PARTIALLY_REFUNDED] and
            self.refunded_amount < self.amount_paid
        )
    
    @property
    def remaining_refundable_amount(self):
        """Calculate remaining amount that can be refunded."""
        return self.amount_paid - self.refunded_amount
    
    @property
    def is_fully_refunded(self):
        """Check if payment is fully refunded."""
        return self.refunded_amount >= self.amount_paid
    
    def process_refund(self, refund_amount, reason=''):
        """Process a refund for this payment with concurrency protection."""
        if not self.is_refundable:
            raise ValidationError(_('Payment is not refundable'))

        if refund_amount <= 0:
            raise ValidationError(_('Refund amount must be positive'))

        if refund_amount > self.remaining_refundable_amount:
            raise ValidationError(_('Refund amount exceeds remaining refundable amount'))

        with transaction.atomic():
            payment = Payment.objects.select_for_update().get(pk=self.pk)
            if refund_amount > payment.remaining_refundable_amount:
                raise ValidationError(_('Refund amount exceeds remaining refundable amount'))

            payment.refunded_amount = F('refunded_amount') + refund_amount
            payment.save(update_fields=['refunded_amount'])

            payment.refresh_from_db()

            # Update payment status based on refund amount
            if payment.is_fully_refunded:
                payment.payment_status = Payment.REFUNDED
            else:
                payment.payment_status = Payment.PARTIALLY_REFUNDED

            payment.save(update_fields=['payment_status'])

            # Refresh again to get the updated refunded_amount
            payment.refresh_from_db()

            # Update self to reflect the changes
            self.refunded_amount = payment.refunded_amount
            self.payment_status = payment.payment_status


class RefundRequest(models.Model):
    """
    Model for storing customer refund requests.
    Allows customers to request refunds with reasons and admin review.
    """
    
    # Request status choices
    PENDING = 'pending'
    APPROVED = 'approved'
    DECLINED = 'declined'
    PROCESSED = 'processed'
    
    STATUS_CHOICES = [
        (PENDING, _('Pending')),
        (APPROVED, _('Approved')),
        (DECLINED, _('Declined')),
        (PROCESSED, _('Processed')),
    ]
    
    # Refund reason choices
    SERVICE_NOT_PROVIDED = 'service_not_provided'
    POOR_SERVICE_QUALITY = 'poor_service_quality'
    BOOKING_CANCELLED = 'booking_cancelled'
    TECHNICAL_ISSUE = 'technical_issue'
    OTHER = 'other'
    
    REASON_CHOICES = [
        (SERVICE_NOT_PROVIDED, _('Service Not Provided')),
        (POOR_SERVICE_QUALITY, _('Poor Service Quality')),
        (BOOKING_CANCELLED, _('Booking Cancelled')),
        (TECHNICAL_ISSUE, _('Technical Issue')),
        (OTHER, _('Other')),
    ]
    
    refund_request_id = models.UUIDField(
        default=uuid.uuid4,
        editable=False,
        unique=True,
        help_text=_('Unique refund request identifier')
    )
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='refund_requests',
        help_text=_('Payment to be refunded')
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='refund_requests',
        help_text=_('Customer requesting the refund')
    )
    reason_category = models.CharField(
        max_length=30,
        choices=REASON_CHOICES,
        help_text=_('Category of refund reason')
    )
    reason_description = models.TextField(
        max_length=1000,
        help_text=_('Detailed description of refund reason')
    )
    requested_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Amount requested for refund')
    )
    request_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Current status of refund request')
    )
    admin_notes = models.TextField(
        blank=True,
        help_text=_('Admin notes regarding the refund request')
    )
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_refund_requests',
        limit_choices_to={'is_staff': True},
        help_text=_('Admin who reviewed this request')
    )
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the request was reviewed')
    )
    processed_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Amount actually processed for refund')
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Refund Request')
        verbose_name_plural = _('Refund Requests')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', '-created_at']),
            models.Index(fields=['request_status']),
            models.Index(fields=['payment']),
            models.Index(fields=['reviewed_by']),
        ]
    
    def __str__(self):
        return f"Refund Request {self.refund_request_id} - {self.customer.email} - ${self.requested_amount}"
    
    def clean(self):
        """Validate refund request data."""
        super().clean()

        # Validate customer matches payment customer
        if self.payment_id and self.customer and self.payment and self.customer != self.payment.customer:
            raise ValidationError(_('Refund request customer must match payment customer'))

        # Validate requested amount doesn't exceed refundable amount
        if self.payment_id and self.requested_amount and self.payment and self.requested_amount > self.payment.remaining_refundable_amount:
            raise ValidationError(_('Requested amount exceeds remaining refundable amount'))
    
    def approve(self, admin_user, notes=''):
        """Approve the refund request."""
        if self.request_status != self.PENDING:
            raise ValidationError(_('Only pending requests can be approved'))
        
        self.request_status = self.APPROVED
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.admin_notes = notes
        self.save()
    
    def decline(self, admin_user, notes=''):
        """Decline the refund request."""
        if self.request_status != self.PENDING:
            raise ValidationError(_('Only pending requests can be declined'))
        
        self.request_status = self.DECLINED
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.admin_notes = notes
        self.save()
    
    def process_refund(self, processed_amount=None):
        """Process the approved refund."""
        if self.request_status != self.APPROVED:
            raise ValidationError(_('Only approved requests can be processed'))
        
        if processed_amount is None:
            processed_amount = self.requested_amount
        
        # Process the refund on the payment
        self.payment.process_refund(processed_amount, f"Refund request: {self.reason_category}")
        
        # Update refund request status
        self.processed_amount = processed_amount
        self.request_status = self.PROCESSED
        self.save()
