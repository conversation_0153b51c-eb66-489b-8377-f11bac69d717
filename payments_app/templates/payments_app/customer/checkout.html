{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Stripe Checkout" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="checkout-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="h2">{% trans "Stripe Checkout" %}</h1>
            <p class="text-muted">{% trans "Complete your booking payment securely" %}</p>
        </div>

        <!-- Booking Summary -->
        <div class="booking-summary">
            <h3>{% trans "Booking Summary" %}</h3>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans "Venue:" %}</strong> {{ booking.venue.venue_name }}</p>
                    <p><strong>{% trans "Service:" %}</strong> {{ booking.service.service_title }}</p>
                    <p><strong>{% trans "Date:" %}</strong> {{ booking.booking_date }}</p>
                    <p><strong>{% trans "Time:" %}</strong> {{ booking.booking_time }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "Customer:" %}</strong> {{ booking.customer.email }}</p>
                    <p><strong>{% trans "Booking ID:" %}</strong> {{ booking.booking_id }}</p>
                    <p><strong>{% trans "Status:" %}</strong> {{ booking.get_status_display }}</p>
                </div>
            </div>
            
            <!-- Price Breakdown -->
            <div class="price-breakdown">
                <div class="row">
                    <div class="col-6">{% trans "Subtotal:" %}</div>
                    <div class="col-6 text-end">${{ price_breakdown.subtotal }}</div>
                </div>
                {% if price_breakdown.discount > 0 %}
                <div class="row">
                    <div class="col-6">{% trans "Discount:" %}</div>
                    <div class="col-6 text-end text-success">-${{ price_breakdown.discount }}</div>
                </div>
                {% endif %}
                {% if price_breakdown.tax > 0 %}
                <div class="row">
                    <div class="col-6">{% trans "Tax:" %}</div>
                    <div class="col-6 text-end">${{ price_breakdown.tax }}</div>
                </div>
                {% endif %}
                {% if price_breakdown.service_fee > 0 %}
                <div class="row">
                    <div class="col-6">{% trans "Service Fee:" %}</div>
                    <div class="col-6 text-end">${{ price_breakdown.service_fee }}</div>
                </div>
                {% endif %}
                <hr>
                <div class="row">
                    <div class="col-6"><strong>{% trans "Total:" %}</strong></div>
                    <div class="col-6 text-end total-amount">${{ total_amount }}</div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="payment-form">
            <h3>{% trans "Payment Information" %}</h3>
            <form method="post">
                {% csrf_token %}
                
                <!-- Payment Method -->
                <div class="mb-3">
                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">
                        {% trans "Payment Method" %}
                    </label>
                    {{ form.payment_method }}
                    {% if form.payment_method.errors %}
                        <div class="text-danger">{{ form.payment_method.errors }}</div>
                    {% endif %}
                </div>

                <!-- Save Payment Method -->
                <div class="mb-3 form-check">
                    {{ form.save_payment_method }}
                    <label for="{{ form.save_payment_method.id_for_label }}" class="form-check-label">
                        {% trans "Save payment method for future use" %}
                    </label>
                    {% if form.save_payment_method.errors %}
                        <div class="text-danger">{{ form.save_payment_method.errors }}</div>
                    {% endif %}
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-3 form-check">
                    {{ form.terms_accepted }}
                    <label for="{{ form.terms_accepted.id_for_label }}" class="form-check-label">
                        {% trans "I accept the" %} 
                        <a href="#" target="_blank">{% trans "Terms and Conditions" %}</a>
                        {% trans "and" %}
                        <a href="#" target="_blank">{% trans "Privacy Policy" %}</a>
                    </label>
                    {% if form.terms_accepted.errors %}
                        <div class="text-danger">{{ form.terms_accepted.errors }}</div>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-credit-card me-2"></i>
                        {% trans "Pay" %} ${{ total_amount }}
                    </button>
                </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="text-center mt-4">
            <small class="text-muted">
                <i class="fas fa-lock me-1"></i>
                {% trans "Your payment information is secure and encrypted" %}
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Placeholder for Stripe integration
    console.log('Stripe Checkout placeholder loaded');
</script>
{% endblock %}
