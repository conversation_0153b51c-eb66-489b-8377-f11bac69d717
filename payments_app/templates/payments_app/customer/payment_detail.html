{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payment Detail" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="payment-detail-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'payments_app:payment_history' %}">{% trans "Payment History" %}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{% trans "Payment Detail" %}</li>
                    </ol>
                </nav>
                <h1 class="h2">{% trans "Payment Detail" %}</h1>
                <p class="text-muted">{% trans "Payment ID:" %} {{ payment.payment_id }}</p>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="card payment-card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>{% trans "Payment Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Payment ID:" %}</strong>
                            <div class="text-muted">{{ payment.payment_id }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Amount Paid:" %}</strong>
                            <div class="text-primary h5">${{ payment.amount_paid }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Payment Method:" %}</strong>
                            <div>{{ payment.get_payment_method_display }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Payment Date:" %}</strong>
                            <div>{{ payment.payment_date|date:"F d, Y \a\t g:i A" }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Status:" %}</strong>
                            <div>
                                <span class="badge status-badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% elif payment.payment_status == 'pending' %}warning{% else %}secondary{% endif %}">
                                    {{ payment.get_payment_status_display }}
                                </span>
                            </div>
                        </div>
                        {% if payment.completed_date %}
                        <div class="info-row">
                            <strong>{% trans "Completed Date:" %}</strong>
                            <div>{{ payment.completed_date|date:"F d, Y \a\t g:i A" }}</div>
                        </div>
                        {% endif %}
                        {% if payment.failure_reason %}
                        <div class="info-row">
                            <strong>{% trans "Failure Reason:" %}</strong>
                            <div class="text-danger">{{ payment.failure_reason }}</div>
                        </div>
                        {% endif %}
                        {% if payment.stripe_payment_intent_id %}
                        <div class="info-row">
                            <strong>{% trans "Stripe Payment Intent:" %}</strong>
                            <div class="text-muted">{{ payment.stripe_payment_intent_id }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Information -->
        <div class="card payment-card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "Booking Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Booking ID:" %}</strong>
                            <div>{{ payment.booking.booking_id }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Venue:" %}</strong>
                            <div>{{ payment.booking.venue.venue_name }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Service:" %}</strong>
                            <div>{{ payment.booking.service.service_title|default:"N/A" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Location:" %}</strong>
                            <div>{{ payment.booking.venue.city }}, {{ payment.booking.venue.state }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Booking Date:" %}</strong>
                            <div>{{ payment.booking.booking_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Booking Time:" %}</strong>
                            <div>{{ payment.booking.booking_time|time:"g:i A" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Booking Status:" %}</strong>
                            <div>
                                <span class="badge bg-{% if payment.booking.status == 'confirmed' %}success{% elif payment.booking.status == 'cancelled' %}danger{% else %}warning{% endif %}">
                                    {{ payment.booking.get_status_display }}
                                </span>
                            </div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Total Price:" %}</strong>
                            <div class="text-success h6">${{ payment.booking.total_price }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Provider Information -->
        <div class="card payment-card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-tie me-2"></i>{% trans "Service Provider" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Provider:" %}</strong>
                            <div>{{ payment.provider.email }}</div>
                        </div>
                        {% if payment.provider.serviceproviderprofile %}
                        <div class="info-row">
                            <strong>{% trans "Business Name:" %}</strong>
                            <div>{{ payment.provider.serviceproviderprofile.business_name }}</div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Contact:" %}</strong>
                            <div>{{ payment.provider.email }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Refund Information -->
        {% if payment.refunded_amount > 0 or refund_requests.exists %}
        <div class="card payment-card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-undo me-2"></i>{% trans "Refund Information" %}
                </h5>
            </div>
            <div class="card-body">
                {% if payment.refunded_amount > 0 %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Refunded Amount:" %}</strong>
                            <div class="text-warning h5">${{ payment.refunded_amount }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Remaining Amount:" %}</strong>
                            <div class="text-success h6">${{ payment.remaining_refundable_amount }}</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if refund_requests.exists %}
                <h6>{% trans "Refund Requests:" %}</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Request ID" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for refund in refund_requests %}
                            <tr>
                                <td>{{ refund.refund_request_id|truncatechars:12 }}</td>
                                <td>${{ refund.requested_amount }}</td>
                                <td>
                                    <span class="badge bg-{% if refund.request_status == 'processed' %}success{% elif refund.request_status == 'declined' %}danger{% else %}warning{% endif %}">
                                        {{ refund.get_request_status_display }}
                                    </span>
                                </td>
                                <td>{{ refund.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <a href="{% url 'payments_app:refund_detail' refund.refund_request_id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "View" %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
            <div class="row">
                <div class="col-12">
                    <a href="{% url 'payments_app:payment_history' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to History" %}
                    </a>
                    
                    {% if payment.payment_status == 'succeeded' %}
                    <a href="{% url 'payments_app:payment_receipt' payment.payment_id %}" 
                       class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-receipt me-2"></i>{% trans "View Receipt" %}
                    </a>
                    {% endif %}

                    {% if can_request_refund %}
                    <a href="{% url 'payments_app:refund_request' payment.payment_id %}" 
                       class="btn btn-warning">
                        <i class="fas fa-undo me-2"></i>{% trans "Request Refund" %}
                    </a>
                    {% endif %}

                    <a href="{% url 'booking_cart_app:booking_detail' payment.booking.slug %}" 
                       class="btn btn-outline-info">
                        <i class="fas fa-calendar-check me-2"></i>{% trans "View Booking" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Add any interactive functionality here
    console.log('Payment detail page loaded');
</script>
{% endblock %}
