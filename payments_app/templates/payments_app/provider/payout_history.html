{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payout History" %} - Provider - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="payout-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'payments_app:provider_earnings_overview' %}">{% trans "Earnings" %}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{% trans "Payout History" %}</li>
                    </ol>
                </nav>
                <h1 class="h2">{% trans "Payout History" %}</h1>
                <p class="text-muted">{% trans "Track your payouts and account balance" %}</p>
            </div>
        </div>

        <!-- Balance Overview -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card balance-card payout-card">
                    <div class="card-body text-center">
                        <h3>${{ available_balance|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Available Balance" %}</p>
                        <small class="opacity-75">{% trans "Ready for payout" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card balance-card payout-card">
                    <div class="card-body text-center">
                        <h3>${{ pending_balance|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Pending Balance" %}</p>
                        <small class="opacity-75">{% trans "Processing payments" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stripe Connect Integration -->
        <div class="card stripe-connect-card payout-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title mb-2">
                            <i class="fab fa-stripe me-2"></i>{% trans "Stripe Connect" %}
                        </h5>
                        <p class="mb-0">
                            {% trans "Automatic payouts are handled through Stripe Connect. Payouts are typically processed within 2-7 business days depending on your bank." %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <button class="btn btn-light" disabled>
                                <i class="fas fa-link me-2"></i>{% trans "Connected" %}
                            </button>
                            <button class="btn btn-outline-light" disabled>
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payout Schedule -->
        <div class="card payout-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>{% trans "Payout Schedule" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                            <h6>{% trans "Automatic Payouts" %}</h6>
                            <p class="text-muted small">{% trans "Every 7 days" %}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                            <h6>{% trans "Minimum Payout" %}</h6>
                            <p class="text-muted small">$25.00</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                            <h6>{% trans "Next Payout" %}</h6>
                            <p class="text-muted small">{{ next_payout_date|date:"M d, Y" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payout History -->
        <div class="card payout-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>{% trans "Recent Payouts" %}
                </h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        {% trans "Filter" %}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?period=week">{% trans "Last Week" %}</a></li>
                        <li><a class="dropdown-item" href="?period=month">{% trans "Last Month" %}</a></li>
                        <li><a class="dropdown-item" href="?period=quarter">{% trans "Last Quarter" %}</a></li>
                        <li><a class="dropdown-item" href="?period=year">{% trans "Last Year" %}</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                {% if payout_history %}
                    {% for payout in payout_history %}
                    <div class="payout-item">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="fw-bold">{{ payout.payout_date|date:"M d, Y" }}</div>
                                <small class="text-muted">{{ payout.payout_date|time:"g:i A" }}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold text-success">${{ payout.amount|floatformat:2 }}</div>
                                <small class="text-muted">{% trans "Gross Amount" %}</small>
                            </div>
                            <div class="col-md-3">
                                <span class="badge bg-{% if payout.status == 'completed' %}success{% elif payout.status == 'pending' %}warning{% else %}danger{% endif %}">
                                    {{ payout.get_status_display }}
                                </span>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#payoutModal{{ payout.id }}">
                                    {% trans "Details" %}
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <!-- Placeholder for no payouts -->
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <h5>{% trans "No Payouts Yet" %}</h5>
                        <p class="text-muted">{% trans "Your payouts will appear here once you start receiving payments." %}</p>
                        <div class="mt-3">
                            <p class="small text-muted">
                                {% trans "Payouts are automatically processed when your available balance reaches $25.00 or more." %}
                            </p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Payout Summary -->
        <div class="card payout-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>{% trans "Payout Summary" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${{ total_payouts|floatformat:2 }}</h4>
                            <small class="text-muted">{% trans "Total Payouts" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>{{ payout_count }}</h4>
                            <small class="text-muted">{% trans "Number of Payouts" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${{ average_payout|floatformat:2 }}</h4>
                            <small class="text-muted">{% trans "Average Payout" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>{{ last_payout_date|date:"M d"|default:"N/A" }}</h4>
                            <small class="text-muted">{% trans "Last Payout" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="card payout-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>{% trans "Need Help?" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{% trans "Payout Questions" %}</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="text-decoration-none">{% trans "When will I receive my payout?" %}</a></li>
                            <li><a href="#" class="text-decoration-none">{% trans "How are platform fees calculated?" %}</a></li>
                            <li><a href="#" class="text-decoration-none">{% trans "Can I change my payout schedule?" %}</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>{% trans "Contact Support" %}</h6>
                        <p class="text-muted">{% trans "If you have questions about your payouts, our support team is here to help." %}</p>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Placeholder for payout history interactions
    console.log('Payout history loaded');
</script>
{% endblock %}
