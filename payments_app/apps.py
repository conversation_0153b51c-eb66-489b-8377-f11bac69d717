"""App configuration for payments_app."""

# --- Third-Party Imports ---
from django.apps import AppConfig


class PaymentsAppConfig(AppConfig):
    """Configuration for the payments_app Django application."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'payments_app'
    verbose_name = 'Payments'
    
    def ready(self):
        """Import signals when the app is ready."""
        try:
            import payments_app.signals
        except ImportError:
            pass
