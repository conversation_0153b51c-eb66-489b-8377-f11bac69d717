"""Service provider payment views."""

# --- Standard Library Imports ---
from decimal import Decimal
import hashlib
import json

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import etag
from django.views.generic import CreateView, DetailView, Form<PERSON>iew, List<PERSON>iew, TemplateView


# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..forms import PaymentSearchForm, RefundRequestForm, RefundSearchForm, StripeCheckoutForm
from ..logging_utils import (
    log_admin_disputed_payments_viewed,
    log_admin_payment_analytics_viewed,
    log_admin_refund_detail_viewed,
    log_admin_refund_management_viewed,
    log_customer_payment_history_viewed,
    log_customer_refund_history_viewed,
    log_payment_completed,
    log_payment_error,
    log_payment_failed,
    log_payment_initiated,
    log_provider_earnings_viewed,
    log_provider_payment_detail_viewed,
    log_provider_payment_history_viewed,
    log_provider_payout_history_viewed,
    log_payout_processed,
    log_refund_approved,
    log_refund_declined,
    log_refund_processed,
    log_refund_requested,
    log_stripe_event,
    performance_monitor,
)
from ..models import Payment, RefundRequest
from ..utils import quantize_money

from .common import (
    PAYMENT_INITIATED_SUCCESS, PAYMENT_COMPLETED_SUCCESS, PAYMENT_FAILED_ERROR,
    REFUND_REQUESTED_SUCCESS, REFUND_REQUEST_ERROR, INVALID_PAYMENT_ERROR, INVALID_BOOKING_ERROR,
    send_payment_receipt_email_async, send_payout_email_async, EMAIL_NOTIFICATIONS_ENABLED,
    payment_receipt_etag, payment_receipt_view, stripe_webhook_view
)


# --- Provider Payment Views ---


class ProviderPaymentMixin:
    """Mixin to ensure only service providers can access provider payment views."""



    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in to access this page.'))
            return redirect('accounts_app:service_provider_login')

        if not request.user.is_service_provider:
            messages.error(request, _('Only service providers can access this page.'))
            return redirect('venues_app:home')

        return super().dispatch(request, *args, **kwargs)


class ProviderEarningsOverviewView(ProviderPaymentMixin, TemplateView):
    """
    Provider earnings overview dashboard showing Stripe payouts and earnings analytics.
    Displays daily/weekly/monthly earnings with charts and statistics.
    """

    template_name = 'payments_app/provider/earnings_overview.html'

    @performance_monitor('provider_earnings_overview')
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        cache_key = f"provider_earnings_{self.request.user.id}"
        cached = cache.get(cache_key)
        if cached:
            context.update(cached)
            return context

        # Get all payments received by this provider
        received_payments = Payment.objects.filter(
            provider=self.request.user,
            payment_status=Payment.SUCCEEDED
        ).select_related('booking', 'booking__venue', 'customer')

        # Calculate earnings statistics
        from datetime import datetime, timedelta

        now = timezone.now()
        today = now.date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        year_ago = today - timedelta(days=365)

        # Total earnings
        total_earnings = received_payments.aggregate(
            total=Sum('amount_paid')
        )['total'] or 0

        # Recent earnings
        weekly_earnings = received_payments.filter(
            payment_date__date__gte=week_ago
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        monthly_earnings = received_payments.filter(
            payment_date__date__gte=month_ago
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        yearly_earnings = received_payments.filter(
            payment_date__date__gte=year_ago
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        # Payment statistics
        payment_stats = received_payments.aggregate(
            total_payments=Count('id'),
            average_payment=Avg('amount_paid')
        )

        # Recent payments for display
        recent_payments = received_payments.order_by('-payment_date')[:10]

        # Pending payouts (placeholder for Stripe integration)
        # In real implementation, this would fetch from Stripe API
        platform_fee_rate = Decimal(str(settings.PLATFORM_FEE_RATE))
        pending_payouts = {
            'amount': quantize_money(total_earnings * (Decimal('1') - platform_fee_rate)),
            'count': 1,
            'next_payout_date': today + timedelta(days=1)  # Daily payouts
        }

        context_data = {
            'total_earnings': total_earnings,
            'weekly_earnings': weekly_earnings,
            'monthly_earnings': monthly_earnings,
            'yearly_earnings': yearly_earnings,
            'payment_stats': payment_stats,
            'recent_payments': recent_payments,
            'pending_payouts': pending_payouts,
            'platform_fee_rate': platform_fee_rate,
        }
        context.update(context_data)
        cache.set(cache_key, context_data, 300)

        if EMAIL_NOTIFICATIONS_ENABLED:
            send_payout_email_async(self.request.user, pending_payouts)

        # Log provider earnings view
        log_provider_earnings_viewed(
            user=self.request.user,
            request=self.request,
            additional_data={
                'total_earnings': str(total_earnings),
                'monthly_earnings': str(monthly_earnings),
                'total_payments': payment_stats['total_payments']
            }
        )

        return context


class ProviderPaymentHistoryView(ProviderPaymentMixin, ListView):
    """
    Provider payment history view showing all payments received from customers.
    Includes search and filtering capabilities.
    """

    model = Payment
    template_name = 'payments_app/provider/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get(self, request, *args, **kwargs):
        self.object_list = self.get_queryset()
        fmt = request.GET.get('format')
        if fmt in ['csv', 'pdf']:
            return self.download_history(self.object_list, fmt)
        context = self.get_context_data()
        return self.render_to_response(context)

    def get_queryset(self):
        queryset = Payment.objects.filter(
            provider=self.request.user
        ).select_related('booking', 'booking__venue', 'customer')

        # Apply search and filters
        from ..forms import ProviderPaymentSearchForm
        form = ProviderPaymentSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get('search_query')
            if search_query:
                queryset = queryset.filter(
                    Q(payment_id__icontains=search_query) |
                    Q(booking__booking_id__icontains=search_query) |
                    Q(customer__email__icontains=search_query) |
                    Q(customer__first_name__icontains=search_query) |
                    Q(customer__last_name__icontains=search_query)
                )

            status = form.cleaned_data.get('status')
            if status:
                queryset = queryset.filter(payment_status=status)

            payment_method = form.cleaned_data.get('payment_method')
            if payment_method:
                queryset = queryset.filter(payment_method=payment_method)

            date_from = form.cleaned_data.get('date_from')
            if date_from:
                queryset = queryset.filter(payment_date__date__gte=date_from)

            date_to = form.cleaned_data.get('date_to')
            if date_to:
                queryset = queryset.filter(payment_date__date__lte=date_to)

            amount_min = form.cleaned_data.get('amount_min')
            if amount_min:
                queryset = queryset.filter(amount_paid__gte=amount_min)

            amount_max = form.cleaned_data.get('amount_max')
            if amount_max:
                queryset = queryset.filter(amount_paid__lte=amount_max)

        sort_field = self.request.GET.get('sort', 'payment_date')
        direction = self.request.GET.get('dir', 'desc')
        allowed = {
            'payment_id': 'payment_id',
            'payment_date': 'payment_date',
            'amount_paid': 'amount_paid',
            'payment_status': 'payment_status',
        }
        sort = allowed.get(sort_field, 'payment_date')
        if direction != 'asc':
            sort = '-' + sort

        return queryset.order_by(sort)

    def download_history(self, queryset, fmt):
        if fmt == 'csv':
            import csv
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename=provider_payment_history.csv'
            writer = csv.writer(response)
            writer.writerow(['Payment ID', 'Date', 'Customer', 'Amount', 'Status'])
            for p in queryset:
                customer = p.customer.get_full_name() or p.customer.email
                writer.writerow([p.payment_id, p.payment_date.strftime('%Y-%m-%d'), customer, p.amount_paid, p.get_payment_status_display()])
            return response
        elif fmt == 'pdf':
            from io import BytesIO
            from reportlab.lib.pagesizes import letter
            from reportlab.pdfgen import canvas
            buffer = BytesIO()
            pdf = canvas.Canvas(buffer, pagesize=letter)
            y = 750
            pdf.drawString(50, y, 'Payment History')
            y -= 20
            for p in queryset:
                customer = p.customer.get_full_name() or p.customer.email
                line = f"{p.payment_id} | {p.payment_date.strftime('%Y-%m-%d')} | {customer} | ${p.amount_paid} | {p.get_payment_status_display()}"
                pdf.drawString(50, y, line)
                y -= 15
                if y < 50:
                    pdf.showPage()
                    y = 750
            pdf.save()
            buffer.seek(0)
            response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=provider_payment_history.pdf'
            return response
        raise Http404

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from ..forms import ProviderPaymentSearchForm
        form = ProviderPaymentSearchForm(self.request.GET)
        context['search_form'] = form

        active = []
        if form.is_valid():
            cd = form.cleaned_data
            if cd.get('search_query'):
                active.append(_('Search: ') + cd['search_query'])
            if cd.get('status'):
                active.append(_('Status: ') + dict(Payment.STATUS_CHOICES).get(cd['status'], cd['status']))
            if cd.get('payment_method'):
                active.append(_('Method: ') + dict(Payment.PAYMENT_METHOD_CHOICES).get(cd['payment_method'], cd['payment_method']))
            if cd.get('date_from'):
                active.append(_('From: ') + cd['date_from'].strftime('%Y-%m-%d'))
            if cd.get('date_to'):
                active.append(_('To: ') + cd['date_to'].strftime('%Y-%m-%d'))
            if cd.get('amount_min'):
                active.append(_('Min: ') + str(cd['amount_min']))
            if cd.get('amount_max'):
                active.append(_('Max: ') + str(cd['amount_max']))
        context['active_filters'] = active

        # Calculate summary statistics
        all_payments = Payment.objects.filter(provider=self.request.user)

        context['payment_stats'] = {
            'total_payments': all_payments.count(),
            'successful_payments': all_payments.filter(payment_status=Payment.SUCCEEDED).count(),
            'failed_payments': all_payments.filter(payment_status=Payment.FAILED).count(),
            'refunded_payments': all_payments.filter(payment_status__in=[Payment.REFUNDED, Payment.PARTIALLY_REFUNDED]).count(),
            'total_earnings': all_payments.filter(payment_status=Payment.SUCCEEDED).aggregate(
                total=Sum('amount_paid')
            )['total'] or 0,
            'average_payment': all_payments.filter(payment_status=Payment.SUCCEEDED).aggregate(
                avg=Avg('amount_paid')
            )['avg'] or 0,
        }

        # Log provider payment history view
        log_provider_payment_history_viewed(
            user=self.request.user,
            request=self.request,
            filters=dict(self.request.GET.items())
        )

        return context


class ProviderPaymentDetailView(ProviderPaymentMixin, DetailView):
    """
    Provider payment detail view showing detailed information about a specific payment.
    """

    model = Payment
    template_name = 'payments_app/provider/payment_detail.html'
    context_object_name = 'payment'
    pk_url_kwarg = 'payment_id'

    def get_object(self, queryset=None):
        payment = get_object_or_404(
            Payment,
            payment_id=self.kwargs.get('payment_id'),
            provider=self.request.user
        )
        return payment

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        payment = self.object

        # Add related refund requests
        context['refund_requests'] = payment.refund_requests.all().order_by('-created_at')

        # Calculate net earnings (after platform fee)
        platform_fee_rate = Decimal(str(settings.PLATFORM_FEE_RATE))
        if payment.payment_status == Payment.SUCCEEDED:
            context['platform_fee'] = quantize_money(payment.amount_paid * platform_fee_rate)
            context['net_earnings'] = quantize_money(payment.amount_paid * (Decimal('1') - platform_fee_rate))
        else:
            context['platform_fee'] = 0
            context['net_earnings'] = 0

        context['platform_fee_rate'] = platform_fee_rate

        # Log provider payment detail view
        log_provider_payment_detail_viewed(
            provider_user=self.request.user,
            payment=payment,
            request=self.request
        )

        return context


class ProviderPayoutHistoryView(ProviderPaymentMixin, TemplateView):
    """
    Provider payout history view showing Stripe payout information.
    This is a placeholder for future Stripe integration.
    """

    template_name = 'payments_app/provider/payout_history.html'

    @performance_monitor('provider_payout_history')
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Placeholder payout data (in real implementation, this would come from Stripe API)
        from datetime import datetime, timedelta
        from django.utils import timezone

        now = timezone.now()

        # Calculate total earnings for payout calculation
        total_earnings = Payment.objects.filter(
            provider=self.request.user,
            payment_status=Payment.SUCCEEDED
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        platform_fee_rate = Decimal(str(settings.PLATFORM_FEE_RATE))
        net_earnings = quantize_money(total_earnings * (Decimal('1') - platform_fee_rate))

        # Mock payout data
        mock_payouts = [
            {
                'payout_id': 'po_1234567890',
                'amount': quantize_money(net_earnings * Decimal('0.3')),
                'status': 'paid',
                'arrival_date': now.date() - timedelta(days=1),
                'created_date': now.date() - timedelta(days=2),
                'description': 'Weekly payout for services',
                'method': 'bank_account',
                'bank_account_last4': '1234'
            },
            {
                'payout_id': 'po_0987654321',
                'amount': quantize_money(net_earnings * Decimal('0.4')),
                'status': 'in_transit',
                'arrival_date': now.date() + timedelta(days=1),
                'created_date': now.date() - timedelta(days=1),
                'description': 'Weekly payout for services',
                'method': 'bank_account',
                'bank_account_last4': '1234'
            },
            {
                'payout_id': 'po_1122334455',
                'amount': quantize_money(net_earnings * Decimal('0.3')),
                'status': 'pending',
                'arrival_date': now.date() + timedelta(days=2),
                'created_date': now.date(),
                'description': 'Weekly payout for services',
                'method': 'bank_account',
                'bank_account_last4': '1234'
            }
        ]

        context.update({
            'payouts': mock_payouts,
            'total_payout_amount': quantize_money(sum(p['amount'] for p in mock_payouts)),
            'pending_amount': quantize_money(sum(p['amount'] for p in mock_payouts if p['status'] == 'pending')),
            'stripe_dashboard_url': 'https://dashboard.stripe.com/payouts',  # Placeholder
        })

        # Log provider payout history view
        log_provider_payout_history_viewed(
            provider_user=self.request.user,
            request=self.request
        )

        for payout in mock_payouts:
            if payout['status'] == 'paid':
                log_payout_processed(
                    provider_user=self.request.user,
                    payout_amount=payout['amount'],
                    payout_id=payout['payout_id'],
                    request=self.request
                )

        return context


# Alias for backward compatibility with tests
ProviderEarningsView = ProviderEarningsOverviewView
