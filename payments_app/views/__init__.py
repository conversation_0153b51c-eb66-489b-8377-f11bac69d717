"""View package for payments_app organized into feature modules."""

# --- Local App Imports ---
from ..logging_utils import log_payment_initiated

from .common import (
    PAYMENT_INITIATED_SUCCESS,
    PAYMENT_COMPLETED_SUCCESS,
    PAYMENT_FAILED_ERROR,
    REFUND_REQUESTED_SUCCESS,
    REFUND_REQUEST_ERROR,
    INVALID_PAYMENT_ERROR,
    INVALID_BOOKING_ERROR,
    send_payment_receipt_email_async,
    send_payout_email_async,
    EMAIL_NOTIFICATIONS_ENABLED,
    payment_receipt_etag,
    payment_receipt_view,
    stripe_webhook_view,
)


# --- Customer Views ---
from .customer import (
    CustomerPaymentMixin,
    StripeCheckoutView,
    PaymentProcessView,
    PaymentSuccessView,
    PaymentCancelView,
    PaymentHistoryView,
    PaymentDetailView,
    RefundRequestView,
    RefundConfirmationView,
    RefundHistoryView,
    RefundDetailView,

)

# --- Provider Views ---
from .provider import (
    ProviderPaymentMixin,
    ProviderEarningsOverviewView,
    ProviderEarningsView,
    ProviderPaymentHistoryView,
    ProviderPaymentDetailView,
    ProviderPayoutHistoryView,

)

# --- Admin Views ---
from .admin import (
    AdminPaymentMixin,
    AdminPaymentListView,
    AdminPaymentDetailView,
    AdminRefundManagementView,
    AdminRefundListView,
    AdminRefundDetailView,
    admin_approve_refund,
    admin_decline_refund,
    AdminDisputedPaymentsView,
    AdminPaymentAnalyticsView,
)

__all__ = [
    # Common utilities
'log_payment_initiated',
    'PAYMENT_INITIATED_SUCCESS', 'PAYMENT_COMPLETED_SUCCESS', 'PAYMENT_FAILED_ERROR',
    'REFUND_REQUESTED_SUCCESS', 'REFUND_REQUEST_ERROR', 'INVALID_PAYMENT_ERROR', 'INVALID_BOOKING_ERROR',
    'send_payment_receipt_email_async', 'send_payout_email_async', 'EMAIL_NOTIFICATIONS_ENABLED',
    'payment_receipt_etag', 'payment_receipt_view', 'stripe_webhook_view',
    # Customer views
    'CustomerPaymentMixin', 'StripeCheckoutView', 'PaymentProcessView', 'PaymentSuccessView', 'PaymentCancelView',
    'PaymentHistoryView', 'PaymentDetailView', 'RefundRequestView', 'RefundConfirmationView', 'RefundHistoryView',
    'RefundDetailView',
    # Provider views
    'ProviderPaymentMixin', 'ProviderEarningsOverviewView', 'ProviderEarningsView', 'ProviderPaymentHistoryView',
    'ProviderPaymentDetailView', 'ProviderPayoutHistoryView',
    # Admin views
    'AdminPaymentMixin', 'AdminPaymentListView', 'AdminPaymentDetailView', 'AdminRefundManagementView',
    'AdminRefundListView', 'AdminRefundDetailView', 'admin_approve_refund', 'admin_decline_refund',
    'AdminDisputedPaymentsView', 'AdminPaymentAnalyticsView',
]
