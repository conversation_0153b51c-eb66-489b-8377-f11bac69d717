"""Common mixins and utilities for payments_app forms."""

# --- Third-Party Imports ---
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


# --- Validation Mixins ---


class RefundAmountValidationMixin:
    """Shared validation logic for refund amounts."""

    def validate_refund_amount(self, amount, payment):
        if amount is None:
            raise ValidationError(_('Please enter a refund amount.'))
        if amount <= 0:
            raise ValidationError(_('Refund amount must be greater than zero.'))
        if payment and amount > payment.remaining_refundable_amount:
            raise ValidationError(
                _(f'Refund amount exceeds ${payment.remaining_refundable_amount} (remaining refundable amount).')
            )
        return amount
