from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import json

from payments_app.models import Payment
from accounts_app.models import CustomUser
from booking_cart_app.models import Booking
from venues_app.models import Venue, Category


class StripeWebhookTest(TestCase):
    def setUp(self):
        self.customer = CustomUser.objects.create_user(
            email='<EMAIL>', password='testpass', role='customer'
        )
        self.provider = CustomUser.objects.create_user(
            email='<EMAIL>', password='testpass', role='service_provider'
        )
        from accounts_app.models import ServiceProviderProfile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Biz',
            business_phone_number='+**********',
            contact_person_name='Contact',
            business_address='123 St',
            city='City',
            state='CA',
            zip_code='12345'
        )
        self.category = Category.objects.create(name='Cat', description='Cat desc')
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Venue',
            short_description='Desc',
            state='State',
            county='County',
            city='City',
            street_number='1',
            street_name='St',
            operating_hours='9-5',
            tags='tag',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('50.00'),
            status='pending'
        )
        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('50.00'),
            payment_method=Payment.STRIPE,
            payment_status=Payment.PENDING,
            stripe_payment_intent_id='pi_test'
        )
        self.url = reverse('payments_app:stripe_webhook')

    def post_event(self, event_type, data_object=None):
        payload = {
            'id': 'evt_test',
            'type': event_type,
            'data': {'object': data_object or {'id': 'pi_test'}},
        }
        return self.client.post(
            self.url,
            data=json.dumps(payload),
            content_type='application/json',
            HTTP_HOST='www.cozywish.com',
            secure=True
        )

    def test_payment_intent_succeeded(self):
        response = self.post_event('payment_intent.succeeded')
        self.assertEqual(response.status_code, 200)
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.payment_status, Payment.SUCCEEDED)
        self.assertIsNotNone(self.payment.completed_date)

    def test_payment_intent_failed(self):
        response = self.post_event('payment_intent.payment_failed', {'id': 'pi_test', 'last_payment_error': {'message': 'fail'}})
        self.assertEqual(response.status_code, 200)
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.payment_status, Payment.FAILED)
        self.assertEqual(self.payment.failure_reason, 'fail')

    def test_charge_refunded(self):
        self.payment.payment_status = Payment.SUCCEEDED
        self.payment.completed_date = timezone.now()
        self.payment.save()
        response = self.post_event('charge.refunded', {'payment_intent': 'pi_test'})
        self.assertEqual(response.status_code, 200)
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.payment_status, Payment.REFUNDED)
        self.assertEqual(self.payment.refunded_amount, self.payment.amount_paid)
