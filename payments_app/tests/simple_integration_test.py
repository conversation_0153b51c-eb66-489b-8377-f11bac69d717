"""
Simple integration test to verify the payments_app integration test structure.
"""

from django.test import TestCase
from django.apps import apps


class SimpleIntegrationTestCase(TestCase):
    """Simple test case to verify that the integration test setup works correctly."""

    def test_app_installed(self):
        """Test that the payments_app is installed."""
        self.assertTrue(apps.is_installed('payments_app'))

    def test_integration_models_exist(self):
        """Test that the required models for integration testing exist."""
        from payments_app.models import Payment, RefundRequest
        from booking_cart_app.models import Booking, Cart, CartItem
        from venues_app.models import Category, Venue, Service
        from accounts_app.models import CustomerProfile, ServiceProviderProfile
        
        # Verify models can be imported
        self.assertTrue(Payment)
        self.assertTrue(RefundRequest)
        self.assertTrue(Booking)
        self.assertTrue(Cart)
        self.assertTrue(CartItem)
        self.assertTrue(Category)
        self.assertTrue(Venue)
        self.assertTrue(Service)
        self.assertTrue(CustomerProfile)
        self.assertTrue(ServiceProviderProfile)
        
        print("✅ All integration test models imported successfully!")

    def test_integration_test_classes_exist(self):
        """Test that integration test classes are properly defined."""
        try:
            from payments_app.tests.test_integration import (
                PaymentIntegrationBaseTest,
                CustomerPaymentIntegrationTest,
                RefundIntegrationTest,
                ProviderPaymentIntegrationTest,
                AdminPaymentIntegrationTest,
                CrossAppIntegrationTest,
                PaymentSecurityIntegrationTest
            )
            
            # Verify test classes can be imported
            self.assertTrue(PaymentIntegrationBaseTest)
            self.assertTrue(CustomerPaymentIntegrationTest)
            self.assertTrue(RefundIntegrationTest)
            self.assertTrue(ProviderPaymentIntegrationTest)
            self.assertTrue(AdminPaymentIntegrationTest)
            self.assertTrue(CrossAppIntegrationTest)
            self.assertTrue(PaymentSecurityIntegrationTest)
            
            print("✅ All integration test classes imported successfully!")
            
        except ImportError as e:
            self.fail(f"Failed to import integration test classes: {e}")
