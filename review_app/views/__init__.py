"""Expose all review_app views for backward compatibility."""

# --- Local App Imports ---
from .common import (
    get_venue_review_stats,
    has_completed_booking,
    is_admin,
    is_customer,
    is_service_provider,
)

# --- Customer Views ---
from .customer import (
    venue_reviews_view,
    submit_review_view,
    review_success_view,
    edit_review_view,
    delete_review_view,
    flag_review_view,
    customer_review_history_view,
    customer_review_detail_view,
    vote_review_helpfulness_view,
    save_review_draft_view,
    edit_review_draft_view,
    publish_review_draft_view,
    delete_review_draft_view,
    submit_customer_response_view,
    edit_customer_response_view,
    delete_customer_response_view,
)

# --- Provider Views ---
from .provider import (
    provider_respond_to_review_view,
    provider_edit_response_view,
    ProviderVenueReviewsView,
    provider_review_summary_view,
    provider_review_detail_view,
)

# --- Admin Views ---
from .admin import (
    admin_review_moderation_view,
    admin_moderate_review_view,
    admin_manage_flags_view,
    admin_flag_resolution_view,
)

__all__ = [
    # common helpers
    'is_admin',
    'is_customer',
    'is_service_provider',
    'has_completed_booking',
    'get_venue_review_stats',
    # customer views
    'venue_reviews_view',
    'submit_review_view',
    'review_success_view',
    'edit_review_view',
    'delete_review_view',
    'flag_review_view',
    'customer_review_history_view',
    'customer_review_detail_view',
    'vote_review_helpfulness_view',
    'save_review_draft_view',
    'edit_review_draft_view',
    'publish_review_draft_view',
    'delete_review_draft_view',
    'submit_customer_response_view',
    'edit_customer_response_view',
    'delete_customer_response_view',
    # provider views
    'provider_respond_to_review_view',
    'provider_edit_response_view',
    'ProviderVenueReviewsView',
    'provider_review_summary_view',
    'provider_review_detail_view',
    # admin views
    'admin_review_moderation_view',
    'admin_moderate_review_view',
    'admin_manage_flags_view',
    'admin_flag_resolution_view',
]
