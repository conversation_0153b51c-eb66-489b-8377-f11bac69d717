"""
Logging utilities specifically for the review_app.

This module provides specialized logging functions for review-related operations,
building on top of the centralized logging utilities in utils/logging_utils.py.

Usage:
    from review_app.logging_utils import log_review_creation, log_review_response
    
    log_review_creation(user, review, request)
    log_review_response(user, review, response, request)
"""

from typing import Optional, Dict, Any
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone
from django.template.loader import render_to_string
import os
from django.template import TemplateDoesNotExist

# Conditional imports to avoid circular dependencies during testing
try:
    from notifications_app.utils import send_notification_email_async
except ImportError:
    # Fallback for testing or when notifications_app is not available
    def send_notification_email_async(*args, **kwargs):
        pass

try:
    from utils.logging_utils import (
        get_app_logger, log_user_activity, log_error, log_security_event,
        log_audit_event, log_performance
    )
except ImportError:
    # Fallback implementations for testing
    import logging
    
    def get_app_logger(app_name, logger_type='main'):
        return logging.getLogger(f'{app_name}.{logger_type}')
    
    def log_user_activity(app_name, activity_type, user=None, request=None, details=None, target_object=None):
        pass
    
    def log_error(app_name, error_type, error_message, user=None, request=None, exception=None, details=None):
        pass
    
    def log_security_event(app_name, event_type, user_email=None, user_id=None, request=None, details=None, severity='INFO'):
        pass
    
    def log_audit_event(app_name, event_type, user=None, request=None, details=None):
        pass
    
    def log_performance(app_name, operation, duration=None, user=None, request=None, details=None):
        pass

# Import performance monitor decorator
def performance_monitor(operation_name: str):
    """
    Decorator to monitor performance of review operations.

    Args:
        operation_name: Name of the operation being monitored

    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Log performance metrics
                user = None
                request = None
                if args:
                    first_arg = args[0]
                    if hasattr(first_arg, 'user') and hasattr(first_arg.user, 'is_authenticated') and first_arg.user.is_authenticated:
                        user = first_arg.user
                    if hasattr(first_arg, 'META'):
                        request = first_arg

                log_performance(
                    app_name='review_app',
                    operation=operation_name,
                    duration=duration,
                    user=user,
                    request=request,
                    details={'function_name': func.__name__}
                )

                return result
            except Exception as e:
                duration = time.time() - start_time

                # Log performance even for failed operations
                user = None
                request = None
                if args:
                    first_arg = args[0]
                    if hasattr(first_arg, 'user') and hasattr(first_arg.user, 'is_authenticated') and first_arg.user.is_authenticated:
                        user = first_arg.user
                    if hasattr(first_arg, 'META'):
                        request = first_arg

                log_performance(
                    app_name='review_app',
                    operation=f"{operation_name}_failed",
                    duration=duration,
                    user=user,
                    request=request,
                    details={'function_name': func.__name__, 'error': str(e)}
                )

                raise

        return wrapper
    return decorator

from django.conf import settings

User = get_user_model()

# Get app-specific loggers
logger = get_app_logger('review_app')
activity_logger = get_app_logger('review_app', 'activity')
security_logger = get_app_logger('review_app', 'security')
audit_logger = get_app_logger('review_app', 'audit')
performance_logger = get_app_logger('review_app', 'performance')


def log_review_creation(
    user: User,
    review: Any,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when a customer creates a new review.
    
    Args:
        user: User who created the review
        review: Review object that was created
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review.id,
        'venue_id': review.venue.id,
        'venue_name': review.venue.venue_name,
        'rating': review.rating,
        'review_length': len(review.written_review),
        'timestamp': timezone.now().isoformat()
    }
    
    if additional_details:
        details.update(additional_details)
    
    log_user_activity(
        app_name='review_app',
        activity_type='review_creation',
        user=user,
        request=request,
        details=details
    )
    
    activity_logger.info(
        f"REVIEW: review_created | User: {user.email} | "
        f"Venue: {review.venue.venue_name} | Rating: {review.rating}/5",
        extra=details
    )


def log_review_response(
    user: User,
    review: Any,
    response: Any,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when a provider responds to a review.

    Args:
        user: Provider who created the response
        review: Review being responded to
        response: ReviewResponse object that was created
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review.id,
        'response_id': response.id,
        'venue_id': review.venue.id,
        'venue_name': review.venue.venue_name,
        'original_rating': review.rating,
        'response_length': len(response.response_text),
        'customer_email': review.customer.email,
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_user_activity(
        app_name='review_app',
        activity_type='review_response',
        user=user,
        request=request,
        details=details
    )

    activity_logger.info(
        f"REVIEW: response_created | Provider: {user.email} | "
        f"Venue: {review.venue.venue_name} | Original Rating: {review.rating}/5",
        extra=details
    )


def log_review_response_update(
    user: User,
    review: Any,
    response: Any,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when a provider updates their response to a review.

    Args:
        user: Provider who updated the response
        review: Review being responded to
        response: ReviewResponse object that was updated
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review.id,
        'response_id': response.id,
        'venue_id': review.venue.id,
        'venue_name': review.venue.venue_name,
        'original_rating': review.rating,
        'response_length': len(response.response_text),
        'customer_email': review.customer.email,
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_user_activity(
        app_name='review_app',
        activity_type='review_response_updated',
        user=user,
        request=request,
        details=details
    )

    activity_logger.info(
        f"REVIEW: response_updated | Provider: {user.email} | "
        f"Venue: {review.venue.venue_name} | Original Rating: {review.rating}/5",
        extra=details
    )


def log_review_flag(
    user: User,
    review: Any,
    flag: Any,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when a customer flags a review.

    Args:
        user: User who flagged the review
        review: Review that was flagged
        flag: ReviewFlag object that was created
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review.id,
        'flag_id': flag.id,
        'venue_id': review.venue.id,
        'venue_name': review.venue.venue_name,
        'flag_reason': flag.reason,
        'original_rating': review.rating,
        'review_author': review.customer.email,
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    # Log user activity
    log_user_activity(
        app_name='review_app',
        activity_type='review_flag',
        user=user,
        request=request,
        details=details
    )

    # Log security event
    log_security_event(
        app_name='review_app',
        event_type='review_flagged',
        user_email=user.email,
        user_id=user.id,
        request=request,
        details=details
    )

    security_logger.warning(
        f"REVIEW: review_flagged | Flagged by: {user.email} | "
        f"Venue: {review.venue.venue_name} | Reason: {flag.reason}",
        extra=details
    )


def log_review_flag_resolution(
    admin_user: User,
    flag: Any,
    action: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when an admin resolves a review flag.
    
    Args:
        admin_user: Admin who resolved the flag
        flag: ReviewFlag object that was resolved
        action: Action taken (approved, rejected, resolved)
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'flag_id': flag.id,
        'review_id': flag.review.id,
        'venue_id': flag.review.venue.id,
        'venue_name': flag.review.venue.venue_name,
        'flag_reason': flag.reason,
        'resolution_action': action,
        'flagged_by': flag.flagged_by.email,
        'admin_notes': flag.admin_notes,
        'timestamp': timezone.now().isoformat()
    }
    
    if additional_details:
        details.update(additional_details)
    
    log_audit_event(
        app_name='review_app',
        action='flag_resolution',
        admin_user=admin_user,
        request=request,
        details=details
    )
    
    audit_logger.info(
        f"REVIEW: flag_resolved | Admin: {admin_user.email} | "
        f"Action: {action} | Venue: {flag.review.venue.venue_name}",
        extra=details
    )


def log_review_moderation(
    admin_user: User,
    review: Any,
    action: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log when an admin moderates a review (approve/disapprove).
    
    Args:
        admin_user: Admin who moderated the review
        review: Review object that was moderated
        action: Action taken (approved, disapproved, edited)
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review.id,
        'venue_id': review.venue.id,
        'venue_name': review.venue.venue_name,
        'moderation_action': action,
        'review_rating': review.rating,
        'review_author': review.customer.email,
        'is_approved': review.is_approved,
        'is_flagged': review.is_flagged,
        'timestamp': timezone.now().isoformat()
    }
    
    if additional_details:
        details.update(additional_details)
    
    log_audit_event(
        app_name='review_app',
        action='review_moderation',
        admin_user=admin_user,
        request=request,
        details=details
    )
    
    audit_logger.info(
        f"REVIEW: review_moderated | Admin: {admin_user.email} | "
        f"Action: {action} | Venue: {review.venue.venue_name}",
        extra=details
    )


def log_review_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    review_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log review-related errors with context.
    
    Args:
        error_type: Type of error
        error_message: Error message
        user: User object (if applicable)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        review_context: Additional context about the review operation
    """
    details = {'error_category': 'review_operation'}
    
    if review_context:
        details.update(review_context)
    
    log_error(
        app_name='review_app',
        error_type=error_type,
        error_message=error_message,
        user=user,
        request=request,
        exception=exception,
        details=details
    )


def log_unauthorized_review_access(
    user_email: str,
    attempted_action: str,
    review_id: int,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log unauthorized attempts to access or modify reviews.
    
    Args:
        user_email: Email of the user attempting unauthorized access
        attempted_action: The action that was attempted
        review_id: ID of the review being accessed
        request: Django HttpRequest object
        additional_details: Additional context information
    """
    details = {
        'review_id': review_id,
        'attempted_action': attempted_action,
        'timestamp': timezone.now().isoformat()
    }
    
    if additional_details:
        details.update(additional_details)
    
    log_security_event(
        app_name='review_app',
        event_type='unauthorized_access',
        user_email=user_email,
        request=request,
        details=details
    )


def send_review_notification_email(review):
    """
    Send email notification to provider when a new review is received.

    Args:
        review: Review object that was created
    """
    try:
        if review.venue.service_provider.user.email:
            # Enhanced subject with rating context
            if review.rating >= 4:
                subject = f"🌟 Excellent {review.rating}-Star Review for '{review.venue.venue_name}'"
            elif review.rating >= 3:
                subject = f"⭐ New {review.rating}-Star Review for '{review.venue.venue_name}'"
            else:
                subject = f"📝 {review.rating}-Star Review for '{review.venue.venue_name}' - Response Suggested"
            
            # Try HTML template first, fallback to text
            try:
                message = render_to_string('emails/new_review.html', {
                    'review': review,
                    'provider': review.venue.service_provider,
                    'venue': review.venue,
                    'request': {'scheme': 'https', 'get_host': lambda: 'cozywish.com'}  # Default for emails
                })
                is_html = True
            except TemplateDoesNotExist:
                # Fallback to enhanced text template
                message = f"""
Hello {review.venue.service_provider.business_name},

🌟 Great news! You have received a new {review.rating}-star review for {review.venue.venue_name}.

REVIEW DETAILS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Customer: {review.customer.get_full_name() or review.customer.email}
Rating: {review.rating}/5 stars {"★" * review.rating}{"☆" * (5-review.rating)}
Date: {review.created_at.strftime('%B %d, %Y at %I:%M %p')}

{f'Review: "{review.written_review}"' if review.written_review else 'The customer provided a star rating without written feedback.'}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💡 QUICK RESPONSE TIPS:
• Thank the customer for their feedback
• Address any specific concerns mentioned  
• Keep your response professional and courteous
• Invite them to visit again or contact you directly
• Respond promptly to show you value feedback

Why respond? Responding to reviews shows professionalism, builds customer relationships, and demonstrates that you value feedback. This can lead to improved customer satisfaction and more bookings.

You can view all reviews and respond from your provider dashboard:
{os.getenv('SITE_URL', 'https://cozywish.com')}/reviews/provider/

Thank you for using CozyWish!

Best regards,
The CozyWish Team

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
CozyWish - Making every stay memorable through trusted connections
© 2024 CozyWish. All rights reserved.
                """
                is_html = False
            
            send_notification_email_async(
                review.venue.service_provider.user,
                subject,
                message,
                is_html=is_html
            )

            activity_logger.info(
                f"REVIEW: enhanced_notification_email_sent | Provider: {review.venue.service_provider.user.email} | "
                f"Venue: {review.venue.venue_name} | Rating: {review.rating}",
                extra={
                    'review_id': review.id,
                    'venue_id': review.venue.id,
                    'provider_email': review.venue.service_provider.user.email,
                    'rating': review.rating,
                    'has_written_review': bool(review.written_review),
                    'timestamp': timezone.now().isoformat()
                }
            )
    except Exception as e:
        log_review_error(
            error_type='email_error',
            error_message=f'Failed to send enhanced review notification email: {str(e)}',
            exception=e,
            review_context={
                'review_id': review.id,
                'venue_id': review.venue.id
            }
        )


def send_review_response_notification_email(review, response):
    """
    Send email notification to customer when provider responds to their review.

    Args:
        review: Review object that was responded to
        response: ReviewResponse object that was created
    """
    try:
        if review.customer.email:
            # Enhanced subject based on review context
            provider_name = review.venue.service_provider.business_name
            if review.rating >= 4:
                subject = f"🎉 {provider_name} Responded to Your Great Review!"
            elif review.rating >= 3:
                subject = f"💬 {provider_name} Responded to Your Review"
            else:
                subject = f"🤝 {provider_name} Addressed Your Feedback"
            
            # Try HTML template first, fallback to text
            try:
                message = render_to_string('emails/review_response.html', {
                    'review': review,
                    'response': response,
                    'customer': review.customer,
                    'venue': review.venue,
                    'provider': review.venue.service_provider,
                    'request': {'scheme': 'https', 'get_host': lambda: 'cozywish.com'}  # Default for emails
                })
                is_html = True
            except TemplateDoesNotExist:
                # Fallback to enhanced text template
                response_preview = response.response_text[:150] + "..." if len(response.response_text) > 150 else response.response_text
                message = f"""
Hello {review.customer.get_full_name() or review.customer.email},

💬 Great news! {provider_name} has personally responded to your {review.rating}-star review of {review.venue.venue_name}.

CONVERSATION THREAD:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📝 YOUR REVIEW ({review.created_at.strftime('%B %d, %Y')}):
Rating: {review.rating}/5 stars {"★" * review.rating}{"☆" * (5-review.rating)}
{f'Review: "{review.written_review}"' if review.written_review else f'You provided a {review.rating}-star rating without written feedback.'}

💼 PROVIDER RESPONSE ({response.created_at.strftime('%B %d, %Y at %I:%M %p')}):
"{response_preview}"

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🌟 This shows their commitment to customer satisfaction! 

You can:
• View the full conversation: {os.getenv('SITE_URL', 'https://cozywish.com')}/reviews/customer/{review.id}/
• Visit the venue page: {os.getenv('SITE_URL', 'https://cozywish.com')}/venues/{review.venue.slug}/
• Continue the dialogue if needed

Providers value your feedback! If you have additional questions or comments about their response, you can contact them directly or consider leaving a follow-up review after your next visit.

Thank you for choosing CozyWish! Your reviews help other travelers make informed decisions and encourage providers to maintain high service standards.

Best regards,
The CozyWish Team

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
CozyWish - Building trust through transparent reviews and genuine connections
© 2024 CozyWish. All rights reserved.
                """
                is_html = False
            
            send_notification_email_async(
                review.customer,
                subject,
                message,
                is_html=is_html
            )

            activity_logger.info(
                f"REVIEW: enhanced_response_email_sent | Customer: {review.customer.email} | "
                f"Venue: {review.venue.venue_name} | Provider: {provider_name}",
                extra={
                    'review_id': review.id,
                    'response_id': response.id,
                    'venue_id': review.venue.id,
                    'customer_email': review.customer.email,
                    'provider_name': provider_name,
                    'rating': review.rating,
                    'response_time_hours': (response.created_at - review.created_at).total_seconds() / 3600,
                    'timestamp': timezone.now().isoformat()
                }
            )
    except Exception as e:
        log_review_error(
            error_type='email_error',
            error_message=f'Failed to send enhanced review response notification email: {str(e)}',
            exception=e,
            review_context={
                'review_id': review.id,
                'response_id': response.id,
                'venue_id': review.venue.id
            }
        )
