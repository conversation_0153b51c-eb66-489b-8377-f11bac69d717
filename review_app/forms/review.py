# --- Third-Party Imports ---
import bleach
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import Review, ReviewDraft, CustomerReviewResponse


class ReviewForm(forms.ModelForm):
    """Form for customers to create reviews for venues."""

    class Meta:
        model = Review
        fields = ['rating', 'written_review']
        widgets = {
            'rating': forms.Select(
                choices=Review.RATING_CHOICES,
                attrs={
                    'class': 'form-select',
                    'required': True
                }
            ),
            'written_review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': _('Share your experience with this venue... (optional)'),
                'maxlength': 1000,
                'required': False
            })
        }
        labels = {
            'rating': _('Your Rating'),
            'written_review': _('Your Review (Optional)')
        }
        help_texts = {
            'rating': _('Rate your overall experience from 1 to 5 stars'),
            'written_review': _('Tell others about your experience (maximum 1000 characters, optional)')
        }

    def __init__(self, *args, **kwargs):
        self.customer = kwargs.pop('customer', None)
        self.venue = kwargs.pop('venue', None)
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes and validation
        for field in self.fields.values():
            if hasattr(field.widget, 'attrs'):
                field.widget.attrs.update({'class': field.widget.attrs.get('class', '') + ' form-control'})

    def clean_written_review(self):
        written_review = self.cleaned_data.get('written_review')
        
        # Written review is now optional
        if not written_review or not written_review.strip():
            return ''
            
        # If provided, it should be at least 10 characters
        if len(written_review.strip()) < 10:
            raise ValidationError(_('If provided, review must be at least 10 characters long.'))
            
        # Content moderation
        inappropriate_words = ['spam', 'fake', 'scam']
        review_lower = written_review.lower()
        for word in inappropriate_words:
            if word in review_lower:
                raise ValidationError(_('Please keep your review professional and appropriate.'))
                
        # Ensure character limit
        if len(written_review) > 1000:
            raise ValidationError(_('Review must be 1000 characters or less.'))
            
        cleaned = bleach.clean(written_review, tags=[], strip=True)
        return cleaned.strip()

    def clean_rating(self):
        rating = self.cleaned_data.get('rating')
        if not rating or rating < 1 or rating > 5:
            raise ValidationError(_('Please select a rating between 1 and 5 stars.'))
        return rating

    def save(self, commit=True):
        review = super().save(commit=False)
        if self.customer:
            review.customer = self.customer
        if self.venue:
            review.venue = self.venue
        if commit:
            review.save()
        return review


class ReviewEditForm(forms.ModelForm):
    """Form for customers to edit their existing reviews."""

    class Meta:
        model = Review
        fields = ['rating', 'written_review']
        widgets = {
            'rating': forms.Select(
                choices=Review.RATING_CHOICES,
                attrs={'class': 'form-select', 'required': True}
            ),
            'written_review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'maxlength': 1000,
                'required': False,
                'placeholder': _('Share your experience with this venue... (optional)')
            })
        }
        labels = {
            'rating': _('Your Rating'),
            'written_review': _('Your Review (Optional)')
        }

    def clean_written_review(self):
        written_review = self.cleaned_data.get('written_review')
        
        # Written review is now optional
        if not written_review or not written_review.strip():
            return ''
            
        # If provided, it should be at least 10 characters
        if len(written_review.strip()) < 10:
            raise ValidationError(_('If provided, review must be at least 10 characters long.'))
            
        # Ensure character limit
        if len(written_review) > 1000:
            raise ValidationError(_('Review must be 1000 characters or less.'))
            
        cleaned = bleach.clean(written_review, tags=[], strip=True)
        return cleaned.strip()

    def clean_rating(self):
        rating = self.cleaned_data.get('rating')
        if not rating or rating < 1 or rating > 5:
            raise ValidationError(_('Please select a rating between 1 and 5 stars.'))
        return rating


class ReviewDraftForm(forms.ModelForm):
    """Form for customers to save and edit review drafts."""

    class Meta:
        model = ReviewDraft
        fields = ['rating', 'written_review']
        widgets = {
            'rating': forms.Select(
                choices=[(None, 'Select a rating...')] + Review.RATING_CHOICES,
                attrs={
                    'class': 'form-select',
                    'required': False
                }
            ),
            'written_review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': _('Start writing your review... (draft will be saved automatically)'),
                'maxlength': 1000,
                'required': False
            })
        }
        labels = {
            'rating': _('Your Rating (Optional)'),
            'written_review': _('Your Review (Optional)')
        }
        help_texts = {
            'rating': _('You can save without rating and complete it later'),
            'written_review': _('Write as much or as little as you want - save as draft anytime')
        }

    def __init__(self, *args, **kwargs):
        self.customer = kwargs.pop('customer', None)
        self.venue = kwargs.pop('venue', None)
        super().__init__(*args, **kwargs)

    def clean_written_review(self):
        written_review = self.cleaned_data.get('written_review')
        
        if not written_review:
            return ''
            
        # Content moderation for drafts (lighter than final review)
        if len(written_review) > 1000:
            raise ValidationError(_('Review must be 1000 characters or less.'))
            
        cleaned = bleach.clean(written_review, tags=[], strip=True)
        return cleaned.strip()

    def clean_rating(self):
        rating = self.cleaned_data.get('rating')
        if rating and (rating < 1 or rating > 5):
            raise ValidationError(_('Rating must be between 1 and 5 stars.'))
        return rating

    def save(self, commit=True):
        draft = super().save(commit=False)
        if self.customer:
            draft.customer = self.customer
        if self.venue:
            draft.venue = self.venue
        if commit:
            draft.save()
        return draft


class CustomerReviewResponseForm(forms.ModelForm):
    """Form for customers to respond to provider responses."""

    class Meta:
        model = CustomerReviewResponse
        fields = ['response_text']
        widgets = {
            'response_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Respond to the provider\'s message... (optional, max 300 characters)'),
                'maxlength': 300,
                'required': True
            })
        }
        labels = {
            'response_text': _('Your Response')
        }
        help_texts = {
            'response_text': _('Keep your response professional and constructive (maximum 300 characters)')
        }

    def __init__(self, *args, **kwargs):
        self.customer = kwargs.pop('customer', None)
        self.provider_response = kwargs.pop('provider_response', None)
        super().__init__(*args, **kwargs)

    def clean_response_text(self):
        response_text = self.cleaned_data.get('response_text')
        
        if not response_text or not response_text.strip():
            raise ValidationError(_('Response text is required.'))
            
        # Minimum length check
        if len(response_text.strip()) < 5:
            raise ValidationError(_('Response must be at least 5 characters long.'))
            
        # Content moderation
        inappropriate_words = ['spam', 'fake', 'scam', 'idiot', 'stupid']
        response_lower = response_text.lower()
        for word in inappropriate_words:
            if word in response_lower:
                raise ValidationError(_('Please keep your response professional and appropriate.'))
                
        # Ensure character limit
        if len(response_text) > 300:
            raise ValidationError(_('Response must be 300 characters or less.'))
            
        cleaned = bleach.clean(response_text, tags=[], strip=True)
        return cleaned.strip()

    def save(self, commit=True):
        response = super().save(commit=False)
        if self.customer:
            response.customer = self.customer
        if self.provider_response:
            response.provider_response = self.provider_response
        if commit:
            response.save()
        return response
