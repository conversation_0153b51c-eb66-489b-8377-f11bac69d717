# Generated by Django 5.2.3 on 2025-06-30 15:22

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('review_app', '0003_make_written_review_optional'),
        ('venues_app', '0017_add_service_tags_system'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerReviewResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_text', models.TextField(help_text='Customer response to provider response (max 300 characters)', max_length=300)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the response was posted')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the response was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who wrote this response', on_delete=django.db.models.deletion.CASCADE, related_name='customer_review_responses', to=settings.AUTH_USER_MODEL)),
                ('provider_response', models.OneToOneField(help_text='Provider response being responded to', on_delete=django.db.models.deletion.CASCADE, related_name='customer_response', to='review_app.reviewresponse')),
            ],
            options={
                'verbose_name': 'Customer Review Response',
                'verbose_name_plural': 'Customer Review Responses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewDraft',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(blank=True, help_text='Star rating from 1 to 5 (optional in draft)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('written_review', models.TextField(blank=True, help_text='Written review content (max 1000 characters, optional)', max_length=1000)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the draft was first created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the draft was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who created this draft', on_delete=django.db.models.deletion.CASCADE, related_name='review_drafts', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='review_drafts', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Review Draft',
                'verbose_name_plural': 'Review Drafts',
                'ordering': ['-updated_at'],
                'indexes': [models.Index(fields=['customer'], name='review_app__custome_aa9086_idx'), models.Index(fields=['venue'], name='review_app__venue_i_c7bcba_idx'), models.Index(fields=['updated_at'], name='review_app__updated_ab7c4c_idx')],
                'unique_together': {('customer', 'venue')},
            },
        ),
    ]
