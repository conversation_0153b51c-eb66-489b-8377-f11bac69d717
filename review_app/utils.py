"""
Utility functions for review_app.

This module contains helper functions for review operations, calculations,
and data processing used throughout the review_app.
"""

# --- Standard Library Imports ---
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Tuple

# --- Third-Party Imports ---
from django.db.models import Avg, Count, Q
from django.utils import timezone

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from notifications_app.utils import run_async
from .models import Review, ReviewFlag


def calculate_average_rating(venue) -> float:
    """
    Calculate the average rating for a venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        Average rating as float, 0.0 if no reviews
    """
    result = Review.objects.filter(
        venue=venue,
        is_approved=True
    ).aggregate(avg_rating=Avg('rating'))
    
    return result['avg_rating'] or 0.0


def update_venue_average_rating(venue_id: int) -> None:
    """Asynchronously compute and update a venue's average rating if field exists."""
    try:
        from venues_app.models import Venue
        venue = Venue.objects.get(id=venue_id)
        avg = calculate_average_rating(venue)
        if hasattr(venue, 'average_rating'):
            venue.average_rating = avg or 0
            venue.save(update_fields=['average_rating'])
    except Exception:
        pass


def async_update_venue_average_rating(venue_id: int) -> None:
    """Trigger asynchronous update of a venue's average rating."""
    run_async(update_venue_average_rating, venue_id)


def get_rating_distribution(venue) -> Dict[int, int]:
    """
    Get the distribution of ratings for a venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        Dictionary with rating (1-5) as key and count as value
    """
    distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
    
    ratings = Review.objects.filter(
        venue=venue,
        is_approved=True
    ).values('rating').annotate(count=Count('rating'))
    
    for rating_data in ratings:
        distribution[rating_data['rating']] = rating_data['count']
    
    return distribution


def get_review_statistics(venue) -> Dict[str, Any]:
    """
    Get comprehensive review statistics for a venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        Dictionary with statistics including total, average, distribution
    """
    reviews = Review.objects.filter(venue=venue, is_approved=True)
    total_reviews = reviews.count()
    average_rating = calculate_average_rating(venue)
    rating_distribution = get_rating_distribution(venue)
    
    return {
        'total_reviews': total_reviews,
        'average_rating': average_rating,
        'rating_distribution': rating_distribution
    }


def filter_reviews_by_rating(venue, rating: int):
    """
    Filter reviews by specific rating.
    
    Args:
        venue: Venue instance
        rating: Rating value (1-5)
        
    Returns:
        QuerySet of reviews with specified rating
    """
    return Review.objects.filter(
        venue=venue,
        rating=rating,
        is_approved=True
    ).order_by('-created_at')


def get_recent_reviews(venue, limit: int = 5):
    """
    Get recent reviews for a venue.
    
    Args:
        venue: Venue instance
        limit: Maximum number of reviews to return
        
    Returns:
        List of recent Review instances
    """
    return list(Review.objects.filter(
        venue=venue,
        is_approved=True
    ).order_by('-created_at')[:limit])


def check_user_can_review(user, venue) -> bool:
    """
    Check if a user can review a venue.

    Args:
        user: User instance
        venue: Venue instance

    Returns:
        True if user can review, False otherwise
    """
    from django.contrib.auth import get_user_model
    User = get_user_model()

    # Check if user is a customer
    if not hasattr(user, 'role') or user.role != User.CUSTOMER:
        return False

    # Check if user is the venue provider
    if hasattr(venue, 'service_provider') and venue.service_provider.user == user:
        return False

    # Check if user already reviewed this venue
    existing_review = Review.objects.filter(
        customer=user,
        venue=venue
    ).exists()

    return not existing_review


def get_flagged_reviews():
    """
    Get all flagged reviews.
    
    Returns:
        QuerySet of flagged Review instances
    """
    return Review.objects.filter(is_flagged=True).order_by('-created_at')


def get_pending_flags():
    """
    Get all pending review flags.
    
    Returns:
        QuerySet of pending ReviewFlag instances
    """
    return ReviewFlag.objects.filter(status=ReviewFlag.PENDING).order_by('-created_at')


def calculate_review_sentiment(review_text: str) -> str:
    """
    Calculate sentiment of review text (basic implementation).
    
    Args:
        review_text: Review text content
        
    Returns:
        Sentiment as string: 'positive', 'negative', or 'neutral'
    """
    # Basic sentiment analysis using keyword matching
    positive_words = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'perfect']
    negative_words = ['terrible', 'awful', 'horrible', 'bad', 'worst', 'hate', 'disappointing']
    
    text_lower = review_text.lower()
    
    positive_count = sum(1 for word in positive_words if word in text_lower)
    negative_count = sum(1 for word in negative_words if word in text_lower)
    
    if positive_count > negative_count:
        return 'positive'
    elif negative_count > positive_count:
        return 'negative'
    else:
        return 'neutral'


def format_rating_display(rating: float) -> str:
    """
    Format rating for display.
    
    Args:
        rating: Rating value
        
    Returns:
        Formatted rating string
    """
    return f"{rating:.1f}"


def get_review_response_rate(venue) -> float:
    """
    Calculate review response rate for a venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        Response rate as percentage
    """
    total_reviews = Review.objects.filter(venue=venue, is_approved=True).count()
    
    if total_reviews == 0:
        return 0.0
    
    responded_reviews = Review.objects.filter(
        venue=venue,
        is_approved=True,
        response__isnull=False
    ).count()
    
    return (responded_reviews / total_reviews) * 100


def get_review_trends(venue, days: int = 30) -> Dict[str, Any]:
    """
    Get review trends over time.
    
    Args:
        venue: Venue instance
        days: Number of days for current period
        
    Returns:
        Dictionary with trend data
    """
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    previous_start = start_date - timedelta(days=days)
    
    current_reviews = Review.objects.filter(
        venue=venue,
        is_approved=True,
        created_at__gte=start_date,
        created_at__lt=end_date
    ).count()
    
    previous_reviews = Review.objects.filter(
        venue=venue,
        is_approved=True,
        created_at__gte=previous_start,
        created_at__lt=start_date
    ).count()
    
    if previous_reviews > 0:
        change_percentage = ((current_reviews - previous_reviews) / previous_reviews) * 100
    else:
        change_percentage = 100 if current_reviews > 0 else 0
    
    return {
        'current_period': current_reviews,
        'previous_period': previous_reviews,
        'change_percentage': change_percentage
    }


def validate_review_content(content: str) -> Tuple[bool, Optional[str]]:
    """
    Validate review content for inappropriate material.
    
    Args:
        content: Review content to validate
        
    Returns:
        Tuple of (is_valid, reason)
    """
    # Basic validation - can be extended with more sophisticated checks
    if not content or not content.strip():
        return False, "Review content cannot be empty"
    
    if len(content) > 1000:
        return False, "Review content exceeds maximum length"
    
    # Add more validation rules as needed
    return True, None


def get_similar_reviews(review, limit: int = 5):
    """
    Find similar reviews for a given review.
    
    Args:
        review: Review instance
        limit: Maximum number of similar reviews
        
    Returns:
        List of similar Review instances
    """
    return list(Review.objects.filter(
        venue=review.venue,
        is_approved=True
    ).exclude(id=review.id).order_by('-created_at')[:limit])


def export_reviews_data(venue) -> List[Dict[str, Any]]:
    """
    Export reviews data for a venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        List of dictionaries with review data
    """
    reviews = Review.objects.filter(venue=venue, is_approved=True).order_by('-created_at')
    
    exported_data = []
    for review in reviews:
        exported_data.append({
            'id': review.id,
            'customer_email': review.customer.email,
            'rating': review.rating,
            'written_review': review.written_review,
            'created_at': review.created_at.isoformat(),
            'has_response': hasattr(review, 'response') and review.response is not None
        })
    
    return exported_data


def bulk_update_review_status(review_ids: List[int], **kwargs) -> int:
    """
    Bulk update review status.
    
    Args:
        review_ids: List of review IDs to update
        **kwargs: Fields to update
        
    Returns:
        Number of reviews updated
    """
    return Review.objects.filter(id__in=review_ids).update(**kwargs)


def is_new_on_cozywish(venue) -> bool:
    """
    Check if venue should display 'New on CozyWish' badge.

    A venue is considered "new" if it has fewer than 5 approved reviews.

    Args:
        venue: Venue instance

    Returns:
        True if venue should show "New on CozyWish" badge
    """
    review_count = Review.objects.filter(
        venue=venue,
        is_approved=True
    ).count()

    return review_count < 5


def get_venue_badge_info(venue) -> Dict[str, Any]:
    """
    Get badge information for a venue (New on CozyWish, Top Rated, etc.).

    Args:
        venue: Venue instance

    Returns:
        Dictionary with badge information
    """
    badge_info = {
        'has_badge': False,
        'badge_type': None,
        'badge_text': None,
        'badge_class': None
    }

    # Check for "New on CozyWish" badge
    if is_new_on_cozywish(venue):
        badge_info.update({
            'has_badge': True,
            'badge_type': 'new',
            'badge_text': 'New on CozyWish',
            'badge_class': 'bg-info'
        })
        return badge_info

    # Check for "Top Rated" badge (4.5+ stars with 10+ reviews)
    stats = get_review_statistics(venue)
    if stats['total_reviews'] >= 10 and stats['average_rating'] >= 4.5:
        badge_info.update({
            'has_badge': True,
            'badge_type': 'top_rated',
            'badge_text': 'Top Rated',
            'badge_class': 'bg-warning'
        })
        return badge_info

    return badge_info


def generate_review_summary_report(venue) -> Dict[str, Any]:
    """
    Generate comprehensive review summary report.

    Args:
        venue: Venue instance

    Returns:
        Dictionary with summary report data
    """
    stats = get_review_statistics(venue)

    recent_reviews_count = Review.objects.filter(
        venue=venue,
        is_approved=True,
        created_at__gte=timezone.now() - timedelta(days=30)
    ).count()

    flagged_reviews_count = Review.objects.filter(
        venue=venue,
        is_flagged=True
    ).count()
    
    response_rate = get_review_response_rate(venue)
    
    return {
        'venue_name': venue.venue_name,
        'total_reviews': stats['total_reviews'],
        'average_rating': stats['average_rating'],
        'rating_distribution': stats['rating_distribution'],
        'recent_reviews_count': recent_reviews_count,
        'flagged_reviews_count': flagged_reviews_count,
        'response_rate': response_rate
    }


def get_provider_venue(user):
    """Return the venue for a service provider user if it exists."""
    try:
        from accounts_app.models import ServiceProviderProfile
        from venues_app.models import Venue

        profile = ServiceProviderProfile.objects.get(user=user)
        # Get the venue where this profile is the service provider
        venue = Venue.objects.filter(service_provider=profile).first()
        return venue
    except ServiceProviderProfile.DoesNotExist:
        return None
