{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>{{ page_title }}</h1>
            <p class="lead">Review analytics and summary for {{ venue.venue_name }}</p>
            
            {% if overall_stats.total_reviews > 0 %}
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ overall_stats.average_rating|floatformat:1 }}</h3>
                                <p>Average Rating</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ overall_stats.total_reviews }}</h3>
                                <p>Total Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.response_rate|floatformat:0 }}%</h3>
                                <p>Response Rate</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.pending_responses }}</h3>
                                <p>Pending Responses</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if rating_distribution %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Rating Distribution</h5>
                                </div>
                                <div class="card-body">
                                    {% for rating, data in rating_distribution.items %}
                                        <div class="rating-bar mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ rating }} stars</span>
                                                <span>{{ data.count }} ({{ data.percentage|floatformat:1 }}%)</span>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" style="width: {{ data.percentage }}%"></div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Review Insights</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><strong>Most Common Rating:</strong> {{ review_stats.most_common_rating }} stars</li>
                                        <li><strong>Reviews This Month:</strong> {{ review_stats.reviews_this_month }}</li>
                                        <li><strong>Reviews Last Month:</strong> {{ review_stats.reviews_last_month }}</li>
                                        <li><strong>Flagged Reviews:</strong> {{ review_stats.flagged_count }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                {% if recent_reviews %}
                    <div class="card">
                        <div class="card-header">
                            <h5>Recent Reviews</h5>
                        </div>
                        <div class="card-body">
                            {% for review in recent_reviews %}
                                <div class="review-item border-bottom pb-3 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>{{ review.customer.first_name|default:"Anonymous" }}</strong>
                                            <span class="rating">{{ review.rating }}/5 stars</span>
                                        </div>
                                        <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                    </div>
                                    <p class="mt-2">{{ review.written_review|truncatewords:20 }}</p>
                                    {% if not review.response %}
                                        <a href="{% url 'review_app:provider_respond_to_review' review.slug %}" class="btn btn-sm btn-primary">Respond</a>
                                    {% endif %}
                                </div>
                            {% endfor %}
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-outline-primary">View All Reviews</a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <h3>No reviews yet</h3>
                    <p>Your venue hasn't received any reviews yet.</p>
                    <p>Once customers start leaving reviews, you'll see detailed analytics here.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
