{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Submit Review - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>{{ page_title }}</h2>
                    <p class="mb-0">Share your experience at {{ venue.venue_name }}</p>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.rating.id_for_label }}" class="form-label">Rating *</label>
                            {{ form.rating }}
                            {% if form.rating.errors %}
                                <div class="text-danger">
                                    {% for error in form.rating.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.written_review.id_for_label }}" class="form-label">Your Review *</label>
                            {{ form.written_review }}
                            {% if form.written_review.errors %}
                                <div class="text-danger">
                                    {% for error in form.written_review.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Share your honest experience to help other customers.
                            </small>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:venue_reviews' venue.id %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Submit Review</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5>{{ venue.venue_name }}</h5>
                        <p>{{ venue.short_description }}</p>
                        <p><strong>Location:</strong> {{ venue.city }}, {{ venue.state }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
