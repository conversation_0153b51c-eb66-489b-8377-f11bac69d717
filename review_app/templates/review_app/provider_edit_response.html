{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Edit Response - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>Edit Response</h2>
                    <p class="mb-0">Update your response to a customer review</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Customer Review:</strong>
                        <div class="mt-2 p-3 bg-light">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ response.review.customer.first_name|default:"Anonymous" }}</strong>
                                    <span class="rating">{{ response.review.rating }}/5 stars</span>
                                </div>
                                <small class="text-muted">{{ response.review.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mt-2">{{ response.review.written_review }}</p>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.response_text.id_for_label }}" class="form-label">Your Response *</label>
                            {{ form.response_text }}
                            {% if form.response_text.errors %}
                                <div class="text-danger">
                                    {% for error in form.response_text.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Update your response. Changes will be visible to all customers.
                            </small>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6>Current Response:</h6>
                                    <div class="current-response p-3 bg-light">
                                        <p>{{ response.response_text }}</p>
                                        <small class="text-muted">Last updated: {{ response.updated_at|date:"M d, Y" }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-3">
                            <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Response</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
