{% extends 'review_app/base_review.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ venue.venue_name }} - Reviews{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h1>{{ venue.venue_name }} Reviews</h1>
            
            {% if reviews %}
                <div class="mb-4">
                    <h3>Average Rating: {{ average_rating|floatformat:1 }}/5</h3>
                    <p>Total Reviews: {{ total_reviews }}</p>
                    
                    {% if rating_distribution %}
                        <div class="rating-distribution">
                            {% for rating, percentage in rating_distribution.items %}
                                <div class="rating-bar">
                                    {{ rating }} stars: {{ percentage }}%
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="reviews-list">
                    {% for review in reviews %}
                        <div class="review-item card mb-3">
                            <div class="card-body">
                                <div class="review-header d-flex justify-content-between">
                                    <div>
                                        <strong>{{ review.customer.first_name|default:"Anonymous" }}</strong>
                                        <span class="rating">{{ review.rating }}/5 stars</span>
                                    </div>
                                    <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                </div>
                                <p class="review-text mt-2">{{ review.written_review }}</p>
                                
                                {% if review.response %}
                                    <div class="provider-response mt-3 p-3 bg-light">
                                        <strong>Response from {{ venue.venue_name }}:</strong>
                                        <p>{{ review.response.response_text }}</p>
                                        <small class="text-muted">{{ review.response.created_at|date:"M d, Y" }}</small>
                                    </div>
                                {% endif %}
                                
                                {% if user.is_authenticated and user != review.customer %}
                                    <div class="review-actions mt-2">
                                        <a href="{% url 'review_app:flag_review' review.slug %}" class="btn btn-sm btn-outline-warning">Flag Review</a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                {% if reviews.has_other_pages %}
                    <nav aria-label="Reviews pagination">
                        <ul class="pagination">
                            {% if reviews.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">{{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                            </li>
                            
                            {% if reviews.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="no-reviews text-center py-5">
                    <h3>No reviews yet</h3>
                    <p>Be the first to review {{ venue.venue_name }}!</p>
                </div>
            {% endif %}
            
            {% if can_review %}
                <div class="mt-4">
                    <a href="{% url 'review_app:submit_review' venue.id %}" class="btn btn-primary">Write a Review</a>
                </div>
            {% endif %}
        </div>
        
        <div class="col-md-4">
            <div class="venue-info card">
                <div class="card-body">
                    <h5>{{ venue.venue_name }}</h5>
                    <p>{{ venue.short_description }}</p>
                    <p><strong>Location:</strong> {{ venue.city }}, {{ venue.state }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
