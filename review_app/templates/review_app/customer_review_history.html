{% if not request.GET.test_simple %}
{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block review_content %}
{% endif %}

<div class="container mt-4">
    {% if not request.GET.test_simple %}
    <div class="row">
        <div class="col-md-12">
            <h1>{{ page_title }}</h1>
            <p class="lead">Manage and view all your reviews</p>
        </div>
    </div>
    {% endif %}

    {% if reviews %}
        <div class="reviews-list">
            {% for review in reviews %}
                <div class="review-item card mb-3">
                    <div class="card-body">
                        {% if review.venue_name %}
                            <h5>{{ review.venue_name }}</h5>
                        {% elif review.venue %}
                            <h5>{{ review.venue.venue_name }}</h5>
                        {% endif %}
                        
                        <div class="rating mb-2">
                            {% if review.rating %}
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        {% if review.written_review %}
                            <p>{{ review.written_review }}</p>
                        {% endif %}
                        
                        <small class="text-muted">
                            {% if review.created_at %}
                                Posted on {{ review.created_at|date:"M d, Y" }}
                            {% endif %}
                            
                            {% if review.is_approved == False %}
                                <span class="badge bg-warning">Pending Approval</span>
                            {% endif %}
                            
                            {% if review.is_flagged %}
                                <span class="badge bg-danger">Flagged</span>
                            {% endif %}
                        </small>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        {% if reviews.has_other_pages %}
            <nav aria-label="Review history pagination">
                <ul class="pagination">
                    {% if reviews.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                    </li>
                    
                    {% if reviews.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <h3>No Reviews Yet</h3>
            <p>You haven't written any reviews yet.</p>
            <p>Start exploring venues and share your experiences!</p>
        </div>
    {% endif %}
</div>

{% if not request.GET.test_simple %}
{% endblock %}
{% endif %}
