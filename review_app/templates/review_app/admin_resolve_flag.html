{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h2>{{ page_title }}</h2>
                    <p class="mb-0">Resolve flag for review</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="flag-details">
                                <h5>Flag Details</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="mb-2">
                                            <strong>Flagged by:</strong> {{ flag.flagged_by.email }}
                                        </div>
                                        <div class="mb-2">
                                            <strong>Date flagged:</strong> {{ flag.created_at|date:"M d, Y H:i" }}
                                        </div>
                                        <div class="mb-2">
                                            <strong>Current status:</strong> 
                                            <span class="badge badge-{{ flag.status|lower }}">{{ flag.get_status_display }}</span>
                                        </div>
                                        <div class="mb-3">
                                            <strong>Reason:</strong>
                                            <p class="mt-2">{{ flag.reason }}</p>
                                        </div>
                                        
                                        {% if flag.admin_notes %}
                                            <div class="mb-2">
                                                <strong>Previous admin notes:</strong>
                                                <p class="mt-2">{{ flag.admin_notes }}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>Flagged Review</h5>
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <div>
                                                    <strong>Customer:</strong> {{ flag.review.customer.first_name|default:"Anonymous" }}
                                                </div>
                                                <div>
                                                    <strong>Rating:</strong> {{ flag.review.rating }}/5 stars
                                                </div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Venue:</strong> {{ flag.review.venue.venue_name }}
                                            </div>
                                            <div class="mb-2">
                                                <strong>Review Date:</strong> {{ flag.review.created_at|date:"M d, Y" }}
                                            </div>
                                            <div class="mb-3">
                                                <strong>Review Text:</strong>
                                                <p class="mt-2">{{ flag.review.written_review }}</p>
                                            </div>
                                            
                                            {% if flag.review.response %}
                                                <div class="provider-response">
                                                    <strong>Provider Response:</strong>
                                                    <p class="mt-2">{{ flag.review.response.response_text }}</p>
                                                    <small class="text-muted">{{ flag.review.response.created_at|date:"M d, Y" }}</small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="resolution-actions">
                                <h5>Resolution Actions</h5>
                                
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Action</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="action" value="resolve" id="resolve">
                                            <label class="form-check-label" for="resolve">
                                                Resolve Flag (Valid concern)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="action" value="dismiss" id="dismiss">
                                            <label class="form-check-label" for="dismiss">
                                                Dismiss Flag (Invalid concern)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="admin_notes" class="form-label">Admin Notes</label>
                                        <textarea class="form-control" name="admin_notes" id="admin_notes" rows="4" placeholder="Add notes about your resolution decision..."></textarea>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">Submit Resolution</button>
                                        <a href="{% url 'review_app:admin_manage_flags' %}" class="btn btn-secondary">Cancel</a>
                                    </div>
                                </form>
                                
                                <div class="mt-4">
                                    <h6>Flag Information</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Status:</strong> {{ flag.get_status_display }}</li>
                                        <li><strong>Review Approved:</strong> {{ flag.review.is_approved|yesno:"Yes,No" }}</li>
                                        <li><strong>Review Flagged:</strong> {{ flag.review.is_flagged|yesno:"Yes,No" }}</li>
                                        {% if flag.reviewed_by %}
                                            <li><strong>Reviewed by:</strong> {{ flag.reviewed_by.email }}</li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
