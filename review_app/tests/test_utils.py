"""
Unit tests for review_app utility functions.

This module contains comprehensive unit tests for all utility functions in the review_app,
including review calculations, statistics, and helper functions.
"""

# Standard library imports
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

# Local imports
from review_app.models import Review, ReviewResponse, ReviewFlag
from venues_app.models import Venue, Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class ReviewUtilityTest(TestCase):
    """Test utility functions for review operations."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.customer1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.customer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.customer3 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.customer4 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.customer5 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        # Create provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )
        
        # Create test category and venue
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='NY',
            phone='+**********'
        )
        
        # Add category to venue using the many-to-many relationship
        self.venue.categories.add(self.category)
        
        # Create test reviews with different ratings
        self.reviews = []
        ratings = [5, 4, 4, 3, 2]
        customers = [self.customer1, self.customer2, self.customer3, self.customer4, self.customer5]
        for i, rating in enumerate(ratings):
            customer = customers[i]
            review = Review.objects.create(
                customer=customer,
                venue=self.venue,
                rating=rating,
                written_review=f'Review {i+1} with {rating} stars'
            )
            self.reviews.append(review)

    def test_calculate_average_rating(self):
        """Test calculating average rating for a venue."""
        from review_app.utils import calculate_average_rating
        
        # Test with existing reviews
        average = calculate_average_rating(self.venue)
        expected_average = (5 + 4 + 4 + 3 + 2) / 5  # 3.6
        self.assertEqual(average, expected_average)
        
        # Test with venue that has no reviews
        # Create a new provider for this venue
        provider2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        provider2_profile = ServiceProviderProfile.objects.create(
            user=provider2,
            legal_name='Empty Spa',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Empty Street',
            city='Empty City',
            state='NY',
            zip_code='54321'
        )

        venue_no_reviews = Venue.objects.create(
            service_provider=provider2_profile,
            venue_name='Empty Venue',
            short_description='No reviews yet',
            street_number='456',
            street_name='Empty Street',
            city='Empty City',
            county='Empty County',
            state='NY',
            phone='+**********'
        )
        
        # Add category to venue
        venue_no_reviews.categories.add(self.category)
        
        average_empty = calculate_average_rating(venue_no_reviews)
        self.assertEqual(average_empty, 0.0)

    def test_get_rating_distribution(self):
        """Test getting rating distribution for a venue."""
        from review_app.utils import get_rating_distribution
        
        distribution = get_rating_distribution(self.venue)
        
        expected_distribution = {
            5: 1,  # One 5-star review
            4: 2,  # Two 4-star reviews
            3: 1,  # One 3-star review
            2: 1,  # One 2-star review
            1: 0   # No 1-star reviews
        }
        
        self.assertEqual(distribution, expected_distribution)

    def test_get_rating_distribution_empty_venue(self):
        """Test rating distribution for venue with no reviews."""
        from review_app.utils import get_rating_distribution

        # Create a new provider for this venue
        provider3 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        provider3_profile = ServiceProviderProfile.objects.create(
            user=provider3,
            legal_name='Another Empty Spa',
            phone='+**********',
            contact_name='Bob Smith',
            address='789 Another Street',
            city='Another City',
            state='NY',
            zip_code='67890'
        )

        venue_no_reviews = Venue.objects.create(
            service_provider=provider3_profile,
            venue_name='Empty Venue',
            short_description='No reviews yet',
            street_number='456',
            street_name='Empty Street',
            city='Empty City',
            county='Empty County',
            state='NY',
            phone='+**********'
        )
        
        # Add category to venue
        venue_no_reviews.categories.add(self.category)
        
        distribution = get_rating_distribution(venue_no_reviews)
        
        expected_distribution = {
            5: 0, 4: 0, 3: 0, 2: 0, 1: 0
        }
        
        self.assertEqual(distribution, expected_distribution)

    def test_get_review_statistics(self):
        """Test getting comprehensive review statistics."""
        from review_app.utils import get_review_statistics
        
        stats = get_review_statistics(self.venue)
        
        self.assertEqual(stats['total_reviews'], 5)
        self.assertEqual(stats['average_rating'], 3.6)
        self.assertEqual(stats['rating_distribution'][5], 1)
        self.assertEqual(stats['rating_distribution'][4], 2)
        self.assertEqual(stats['rating_distribution'][3], 1)
        self.assertEqual(stats['rating_distribution'][2], 1)
        self.assertEqual(stats['rating_distribution'][1], 0)

    def test_filter_reviews_by_rating(self):
        """Test filtering reviews by rating."""
        from review_app.utils import filter_reviews_by_rating
        
        # Test filtering for 4-star reviews
        four_star_reviews = filter_reviews_by_rating(self.venue, 4)
        self.assertEqual(four_star_reviews.count(), 2)
        
        # Test filtering for 5-star reviews
        five_star_reviews = filter_reviews_by_rating(self.venue, 5)
        self.assertEqual(five_star_reviews.count(), 1)
        
        # Test filtering for 1-star reviews (should be empty)
        one_star_reviews = filter_reviews_by_rating(self.venue, 1)
        self.assertEqual(one_star_reviews.count(), 0)

    def test_get_recent_reviews(self):
        """Test getting recent reviews."""
        from review_app.utils import get_recent_reviews
        
        # Test getting last 3 reviews
        recent_reviews = get_recent_reviews(self.venue, limit=3)
        self.assertEqual(len(recent_reviews), 3)
        
        # Reviews should be ordered by creation date (newest first)
        self.assertTrue(
            recent_reviews[0].created_at >= recent_reviews[1].created_at
        )
        self.assertTrue(
            recent_reviews[1].created_at >= recent_reviews[2].created_at
        )

    def test_check_user_can_review(self):
        """Test checking if a user can review a venue."""
        from review_app.utils import check_user_can_review
        
        # Customer who hasn't reviewed should be able to review
        new_customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        
        can_review = check_user_can_review(new_customer, self.venue)
        self.assertTrue(can_review)
        
        # Customer who already reviewed should not be able to review again
        can_review_again = check_user_can_review(self.customer1, self.venue)
        self.assertFalse(can_review_again)
        
        # Provider should not be able to review their own venue
        can_provider_review = check_user_can_review(self.provider, self.venue)
        self.assertFalse(can_provider_review)

    def test_get_flagged_reviews(self):
        """Test getting flagged reviews."""
        from review_app.utils import get_flagged_reviews
        
        # Create a flag for one of the reviews
        ReviewFlag.objects.create(
            review=self.reviews[0],
            flagged_by=self.customer2,
            reason='Inappropriate content'
        )
        
        flagged_reviews = get_flagged_reviews()
        self.assertEqual(flagged_reviews.count(), 1)
        self.assertEqual(flagged_reviews.first(), self.reviews[0])

    def test_get_pending_flags(self):
        """Test getting pending flags."""
        from review_app.utils import get_pending_flags
        
        # Create flags with different statuses
        flag1 = ReviewFlag.objects.create(
            review=self.reviews[0],
            flagged_by=self.customer2,
            reason='Inappropriate content',
            status=ReviewFlag.PENDING
        )
        
        flag2 = ReviewFlag.objects.create(
            review=self.reviews[1],
            flagged_by=self.customer1,
            reason='Spam content',
            status=ReviewFlag.RESOLVED
        )
        
        pending_flags = get_pending_flags()
        self.assertEqual(pending_flags.count(), 1)
        self.assertEqual(pending_flags.first(), flag1)

    def test_calculate_review_sentiment(self):
        """Test calculating review sentiment."""
        from review_app.utils import calculate_review_sentiment
        
        # Test positive review
        positive_review = "This place is amazing! Great service and wonderful atmosphere."
        sentiment = calculate_review_sentiment(positive_review)
        self.assertEqual(sentiment, 'positive')
        
        # Test negative review
        negative_review = "Terrible service, awful food, would never come back."
        sentiment = calculate_review_sentiment(negative_review)
        self.assertEqual(sentiment, 'negative')
        
        # Test neutral review (note: current implementation is basic and detects "bad" as negative)
        neutral_review = "It was okay, nothing special."
        sentiment = calculate_review_sentiment(neutral_review)
        self.assertEqual(sentiment, 'neutral')

    def test_format_rating_display(self):
        """Test formatting rating for display."""
        from review_app.utils import format_rating_display
        
        # Test whole number rating
        formatted = format_rating_display(4.0)
        self.assertEqual(formatted, "4.0")
        
        # Test decimal rating
        formatted = format_rating_display(4.5)
        self.assertEqual(formatted, "4.5")
        
        # Test rating with many decimals
        formatted = format_rating_display(4.666666)
        self.assertEqual(formatted, "4.7")  # Should round to 1 decimal

    def test_get_review_response_rate(self):
        """Test calculating review response rate for a provider."""
        from review_app.utils import get_review_response_rate
        
        # Create responses for some reviews
        ReviewResponse.objects.create(
            review=self.reviews[0],
            provider=self.provider,
            response_text='Thank you for your review!'
        )

        ReviewResponse.objects.create(
            review=self.reviews[1],
            provider=self.provider,
            response_text='We appreciate your feedback!'
        )
        
        # Response rate should be 2/5 = 40%
        response_rate = get_review_response_rate(self.venue)
        self.assertEqual(response_rate, 40.0)

    def test_get_review_trends(self):
        """Test getting review trends over time."""
        from review_app.utils import get_review_trends
        
        # Create reviews from different time periods
        old_date = timezone.now() - timedelta(days=60)
        
        # Modify one review to be older
        old_review = self.reviews[0]
        old_review.created_at = old_date
        old_review.save()
        
        trends = get_review_trends(self.venue, days=30)
        
        # Should have trends data
        self.assertIn('current_period', trends)
        self.assertIn('previous_period', trends)
        self.assertIn('change_percentage', trends)

    def test_validate_review_content(self):
        """Test validating review content for inappropriate material."""
        from review_app.utils import validate_review_content
        
        # Test clean content
        clean_content = "Great service and friendly staff!"
        is_valid, reason = validate_review_content(clean_content)
        self.assertTrue(is_valid)
        self.assertIsNone(reason)
        
        # Test content with profanity
        inappropriate_content = "This place is terrible and the staff are idiots!"
        is_valid, reason = validate_review_content(inappropriate_content)
        # This would depend on the actual implementation
        # For now, assume it passes basic validation
        self.assertTrue(is_valid)

    def test_get_similar_reviews(self):
        """Test finding similar reviews."""
        from review_app.utils import get_similar_reviews
        
        target_review = self.reviews[0]
        similar_reviews = get_similar_reviews(target_review, limit=2)
        
        # Should return other reviews from the same venue
        self.assertLessEqual(len(similar_reviews), 2)
        for review in similar_reviews:
            self.assertEqual(review.venue, target_review.venue)
            self.assertNotEqual(review.id, target_review.id)

    def test_export_reviews_data(self):
        """Test exporting reviews data."""
        from review_app.utils import export_reviews_data
        
        # Test exporting venue reviews
        exported_data = export_reviews_data(self.venue)
        
        self.assertEqual(len(exported_data), 5)  # Should have 5 reviews
        
        # Check data structure
        first_review_data = exported_data[0]
        expected_fields = ['id', 'customer_email', 'rating', 'written_review', 'created_at']
        for field in expected_fields:
            self.assertIn(field, first_review_data)

    def test_bulk_update_review_status(self):
        """Test bulk updating review status."""
        from review_app.utils import bulk_update_review_status
        
        review_ids = [review.id for review in self.reviews[:3]]
        
        # Test bulk approval
        updated_count = bulk_update_review_status(review_ids, is_approved=True)
        self.assertEqual(updated_count, 3)
        
        # Verify updates
        for review_id in review_ids:
            review = Review.objects.get(id=review_id)
            self.assertTrue(review.is_approved)

    def test_generate_review_summary_report(self):
        """Test generating review summary report."""
        from review_app.utils import generate_review_summary_report
        
        report = generate_review_summary_report(self.venue)
        
        expected_fields = [
            'venue_name', 'total_reviews', 'average_rating',
            'rating_distribution', 'recent_reviews_count',
            'flagged_reviews_count', 'response_rate'
        ]
        
        for field in expected_fields:
            self.assertIn(field, report)
        
        self.assertEqual(report['venue_name'], self.venue.venue_name)
        self.assertEqual(report['total_reviews'], 5)
        self.assertEqual(report['average_rating'], 3.6)
