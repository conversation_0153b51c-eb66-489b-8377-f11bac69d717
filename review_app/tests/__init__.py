"""
Tests package for review_app.

This package contains all unit and integration tests for the review_app.
All test modules are imported here to ensure proper test discovery.
"""

# Patch asynchronous notification runner to execute synchronously during tests
from unittest.mock import patch

patch('notifications_app.utils.run_async', lambda func, *args, **kwargs: func(*args, **kwargs)).start()

# Import all test modules to ensure they are discovered by Django's test runner
from .test_models import (
    ReviewModelTest, ReviewResponseModelTest, ReviewFlagModelTest
)
from .test_forms import (
    ReviewFormTest, ReviewResponseFormTest, ReviewFlagFormTest, ReviewEditFormTest
)
from .test_views import (
    CustomerViewsTest, ProviderViewsTest, AdminViewsTest
)
from .test_urls import (
    ReviewAppURLsTest, URLAccessTest
)
from .test_utils import (
    ReviewUtilityTest
)
from .simple_test import (
    SimpleTestCase
)
from .test_integration import (
    ReviewIntegrationTest, ReviewEdgeCasesIntegrationTest, ReviewPerformanceIntegrationTest
)
