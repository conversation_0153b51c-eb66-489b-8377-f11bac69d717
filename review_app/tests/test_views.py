"""
Unit tests for review_app views.

This module contains comprehensive unit tests for all view functions and classes in the review_app,
including customer views, provider views, and admin views.
"""

# Standard library imports
import tempfile
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core import mail
from django.utils import timezone
from datetime import timedelta

# Local imports
from review_app.models import Review, ReviewResponse, ReviewFlag
from review_app.forms import ReviewForm, ReviewResponseForm, ReviewFlagForm, ReviewEditForm
from venues_app.models import Venue, Category
from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking, BookingItem, Service

User = get_user_model()


class CustomerViewsTest(TestCase):
    """Test customer-related views in review_app."""

    def setUp(self):
        """Set up test data."""
        # Disconnect signals that cause notification issues during testing
        from django.db.models.signals import post_save, post_delete
        from review_app.models import Review
        from review_app.signals import handle_review_save, handle_review_delete
        from notifications_app.signals import handle_review_notifications
        
        post_save.disconnect(handle_review_save, sender=Review)
        post_delete.disconnect(handle_review_delete, sender=Review)
        post_save.disconnect(handle_review_notifications, sender='review_app.Review')
        
        self.client = Client()
        
        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        
        self.customer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Business',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )
        
        # Create test category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='NY',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue through many-to-many relationship
        self.venue.categories.add(self.category)
        
        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Relaxing Massage',
            short_description='A 60-minute relaxing massage',
            duration_minutes=60,
            price_min=100.00
        )
        
        # Create test booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=100.00,
            status=Booking.COMPLETED
        )
        
        self.booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=100.00,
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60
        )
        
        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review='Great experience! The staff was very friendly and professional.',
            is_approved=True
        )

    def test_venue_reviews_view_get(self):
        """Test GET request to venue reviews view."""
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.venue.venue_name)
        self.assertContains(response, self.review.written_review)
        self.assertIn('reviews', response.context)
        self.assertIn('venue', response.context)

    def test_venue_reviews_view_nonexistent_venue(self):
        """Test venue reviews view with nonexistent venue."""
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': 99999})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)



    def test_submit_review_view_get_unauthenticated(self):
        """Test GET request to submit review view when not authenticated."""
        url = reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_submit_review_view_post_valid(self):
        """Test POST request to submit review view with valid data."""
        # Delete existing review to allow new one
        self.review.delete()
        
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id})
        
        form_data = {
            'rating': 5,
            'written_review': 'Excellent service! Highly recommend this place.'
        }
        
        response = self.client.post(url, data=form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful submission
        
        # Check that review was created
        review = Review.objects.get(customer=self.customer, venue=self.venue)
        self.assertEqual(review.rating, 5)
        self.assertEqual(review.written_review, 'Excellent service! Highly recommend this place.')



    def test_submit_review_view_duplicate_review(self):
        """Test submitting a review when one already exists."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id})
        
        form_data = {
            'rating': 5,
            'written_review': 'Another review attempt'
        }
        
        response = self.client.post(url, data=form_data)
        
        # Should redirect with error message
        self.assertEqual(response.status_code, 302)
        
        # Check that no new review was created
        review_count = Review.objects.filter(customer=self.customer, venue=self.venue).count()
        self.assertEqual(review_count, 1)

    def test_edit_review_view_get_owner(self):
        """Test GET request to edit review view as review owner."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:edit_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Review')
        self.assertIn('form', response.context)
        self.assertIn('review', response.context)

    def test_edit_review_view_get_not_owner(self):
        """Test GET request to edit review view as non-owner."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:edit_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_edit_review_view_post_valid(self):
        """Test POST request to edit review view with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:edit_review', kwargs={'review_slug': self.review.slug})
        
        form_data = {
            'rating': 3,
            'written_review': 'Updated review content with new thoughts.'
        }
        
        response = self.client.post(url, data=form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful update
        
        # Check that review was updated
        self.review.refresh_from_db()
        self.assertEqual(self.review.rating, 3)
        self.assertEqual(self.review.written_review, 'Updated review content with new thoughts.')

    def test_flag_review_view_get_authenticated(self):
        """Test GET request to flag review view when authenticated."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:flag_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Flag Review')
        self.assertIn('form', response.context)
        self.assertIn('review', response.context)

    def test_flag_review_view_get_own_review(self):
        """Test GET request to flag own review (should be forbidden)."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:flag_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_flag_review_view_post_valid(self):
        """Test POST request to flag review view with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:flag_review', kwargs={'review_slug': self.review.slug})
        
        form_data = {
            'reason': ReviewFlag.INAPPROPRIATE_CONTENT,
            'reason_text': 'This review contains inappropriate language.'
        }
        
        response = self.client.post(url, data=form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful flagging
        
        # Check that flag was created
        flag = ReviewFlag.objects.get(review=self.review, flagged_by=self.customer2)
        self.assertEqual(flag.reason, ReviewFlag.INAPPROPRIATE_CONTENT)
        self.assertEqual(flag.reason_text, 'This review contains inappropriate language.')
        
        # Check that review is now flagged
        self.review.refresh_from_db()
        self.assertTrue(self.review.is_flagged)



    def test_customer_review_history_view_unauthenticated(self):
        """Test customer review history view when not authenticated."""
        url = reverse('review_app:customer_review_history')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect to login



    def test_venue_reviews_view_pagination(self):
        """Test venue reviews view with pagination."""
        # Create multiple reviews
        for i in range(15):
            customer = User.objects.create_user(
                email=f'pagination_customer{i}@example.com',
                password='testpass123',
                role=User.CUSTOMER
            )
            Review.objects.create(
                customer=customer,
                venue=self.venue,
                rating=4,
                written_review=f'Review number {i}',
                is_approved=True
            )
        
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('is_paginated', response.context)
        
        # Test pagination with page parameter
        response = self.client.get(url + '?page=2')
        self.assertEqual(response.status_code, 200)

    def test_venue_reviews_view_filtering(self):
        """Test venue reviews view with rating filtering."""
        # Create reviews with different ratings
        for rating in [1, 2, 3, 5]:
            customer = User.objects.create_user(
                email=f'customer_rating_{rating}@example.com',
                password='testpass123',
                role=User.CUSTOMER
            )
            Review.objects.create(
                customer=customer,
                venue=self.venue,
                rating=rating,
                written_review=f'Review with {rating} stars',
                is_approved=True
            )
        
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id})
        
        # Test filtering by rating
        response = self.client.get(url + '?rating=5')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Review with 5 stars')
        self.assertNotContains(response, 'Review with 1 stars')

    def test_venue_reviews_view_sorting(self):
        """Test venue reviews view sorting options."""
        for rating in [1, 5, 3]:
            customer = User.objects.create_user(
                email=f'sort_customer_{rating}@example.com',
                password='testpass123',
                role=User.CUSTOMER
            )
            Review.objects.create(
                customer=customer,
                venue=self.venue,
                rating=rating,
                written_review=f'Sort review {rating}',
                is_approved=True
            )

        url = reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id})

        response = self.client.get(url + '?sort=rating_desc')
        self.assertEqual(response.status_code, 200)
        reviews = list(response.context['reviews'])
        ratings = [r.rating for r in reviews]
        self.assertEqual(ratings, sorted(ratings, reverse=True))



    def test_review_view_context_data(self):
        """Test that views provide correct context data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Test venue reviews view context
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        self.assertIn('venue', response.context)
        self.assertIn('reviews', response.context)
        self.assertIn('average_rating', response.context)
        self.assertIn('total_reviews', response.context)
        self.assertIn('rating_distribution', response.context)

    def test_review_view_permissions(self):
        """Test view permissions for different user types."""
        # Test provider trying to submit review
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        # Providers should not be able to submit reviews
        self.assertEqual(response.status_code, 403)

    def test_review_view_messages(self):
        """Test that views display appropriate messages."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Test successful flag submission
        url = reverse('review_app:flag_review', kwargs={'review_slug': self.review.slug})
        form_data = {
            'reason': ReviewFlag.INAPPROPRIATE_CONTENT,
            'reason_text': 'Inappropriate content'
        }
        response = self.client.post(url, data=form_data, follow=True)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('flagged' in str(message) for message in messages))

    def test_review_view_error_handling(self):
        """Test error handling in review views."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Test with nonexistent review ID
        url = reverse('review_app:edit_review', kwargs={'review_slug': 'does-not-exist'})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)


class ProviderViewsTest(TestCase):
    """Test provider-related views in review_app."""

    def setUp(self):
        """Set up test data."""
        # Disconnect signals that cause notification issues during testing
        from django.db.models.signals import post_save, post_delete
        from review_app.models import Review
        from review_app.signals import handle_review_save, handle_review_delete
        from notifications_app.signals import handle_review_notifications
        
        post_save.disconnect(handle_review_save, sender=Review)
        post_delete.disconnect(handle_review_delete, sender=Review)
        post_save.disconnect(handle_review_notifications, sender='review_app.Review')
        
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        self.another_provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        # Create service provider profiles
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        self.another_provider_profile = ServiceProviderProfile.objects.create(
            user=self.another_provider,
            legal_name='Another Spa',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Another Street',
            city='Another City',
            state='CA',
            zip_code='67890'
        )

        # Create test category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='NY',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Add category to venue through many-to-many relationship
        self.venue.categories.add(self.category)

        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review='Great experience! The staff was very friendly and professional.',
            is_approved=True
        )

    def test_provider_venue_reviews_view_authenticated(self):
        """Test provider venue reviews view when authenticated."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Venue Reviews')
        self.assertIn('reviews', response.context)
        self.assertContains(response, self.review.written_review)

    def test_provider_venue_reviews_view_unauthenticated(self):
        """Test provider venue reviews view when not authenticated."""
        url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_provider_venue_reviews_view_wrong_user_type(self):
        """Test provider venue reviews view with customer account."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_provider_respond_to_review_view_get(self):
        """Test GET request to provider respond to review view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_respond_to_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Respond to Review')
        self.assertIn('form', response.context)
        self.assertIn('review', response.context)

    def test_provider_respond_to_review_view_wrong_provider(self):
        """Test provider responding to review for different venue."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_respond_to_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_provider_respond_to_review_view_post_valid(self):
        """Test POST request to provider respond to review view with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_respond_to_review', kwargs={'review_slug': self.review.slug})

        form_data = {
            'response_text': 'Thank you for your wonderful review! We appreciate your feedback.'
        }

        response = self.client.post(url, data=form_data)

        self.assertEqual(response.status_code, 302)  # Redirect after successful submission

        # Check that response was created
        review_response = ReviewResponse.objects.get(review=self.review)
        self.assertEqual(
            review_response.response_text,
            'Thank you for your wonderful review! We appreciate your feedback.'
        )

    def test_provider_respond_to_review_view_already_responded(self):
        """Test provider trying to respond to review that already has a response."""
        # Create existing response
        ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider_user,
            response_text='Existing response'
        )

        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_respond_to_review', kwargs={'review_slug': self.review.slug})

        response = self.client.get(url)

        # Should redirect with message about existing response
        self.assertEqual(response.status_code, 302)

    def test_provider_edit_response_view_get(self):
        """Test GET request to provider edit response view."""
        # Create response first
        review_response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider_user,
            response_text='Original response text'
        )

        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_edit_response', kwargs={'response_id': review_response.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Response')
        self.assertIn('form', response.context)
        self.assertIn('response', response.context)

    def test_provider_edit_response_view_post_valid(self):
        """Test POST request to provider edit response view with valid data."""
        # Create response first
        review_response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider_user,
            response_text='Original response text'
        )

        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_edit_response', kwargs={'response_id': review_response.id})

        form_data = {
            'response_text': 'Updated response text with more details.'
        }

        response = self.client.post(url, data=form_data)

        self.assertEqual(response.status_code, 302)  # Redirect after successful update

        # Check that response was updated
        review_response.refresh_from_db()
        self.assertEqual(review_response.response_text, 'Updated response text with more details.')

    def test_provider_review_summary_view(self):
        """Test provider review summary view."""
        # Create additional reviews for better summary
        for i in range(5):
            customer = User.objects.create_user(
                email=f'summary_customer{i}@example.com',
                password='testpass123',
                role=User.CUSTOMER
            )
            Review.objects.create(
                customer=customer,
                venue=self.venue,
                rating=4 + (i % 2),  # Mix of 4 and 5 star ratings
                written_review=f'Review number {i}',
                is_approved=True
            )

        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:provider_review_summary')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Review Summary')
        self.assertIn('average_rating', response.context)
        self.assertIn('total_reviews', response.context)
        self.assertIn('rating_distribution', response.context)
        self.assertIn('recent_reviews', response.context)



    def test_provider_views_context_data(self):
        """Test that provider views provide correct context data."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test venue reviews view context
        url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(url)

        self.assertIn('reviews', response.context)
        self.assertIn('venue', response.context)
        self.assertIn('review_stats', response.context)

    def test_provider_views_permissions(self):
        """Test provider view permissions."""
        # Test customer trying to access provider views
        self.client.login(email='<EMAIL>', password='testpass123')

        urls = [
            reverse('review_app:provider_venue_reviews'),
            reverse('review_app:provider_review_summary'),
        ]

        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 403)


class AdminViewsTest(TestCase):
    """Test admin-related views in review_app."""

    def setUp(self):
        """Set up test data."""
        # Disconnect signals that cause notification issues during testing
        from django.db.models.signals import post_save, post_delete
        from review_app.models import Review
        from review_app.signals import handle_review_save, handle_review_delete
        from notifications_app.signals import handle_review_notifications
        
        post_save.disconnect(handle_review_save, sender=Review)
        post_delete.disconnect(handle_review_delete, sender=Review)
        post_save.disconnect(handle_review_notifications, sender='review_app.Review')
        
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.flagger = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create test category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='NY',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Add category to venue through many-to-many relationship
        self.venue.categories.add(self.category)

        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review='Great experience! The staff was very friendly and professional.',
            is_approved=True
        )

        # Create test flag
        self.flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=self.flagger,
            reason='Inappropriate content'
        )

    def test_admin_review_moderation_view_authenticated(self):
        """Test admin review moderation view when authenticated as admin."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_review_moderation')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Review Moderation')
        self.assertIn('reviews', response.context)
        self.assertIn('stats', response.context)

    def test_admin_review_moderation_view_non_admin(self):
        """Test admin review moderation view when not admin."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_review_moderation')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)  # Forbidden, not redirect

    def test_admin_moderate_review_view_get(self):
        """Test GET request to admin moderate review view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_moderate_review', kwargs={'review_slug': self.review.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Moderate Review')
        self.assertIn('review', response.context)

    def test_admin_moderate_review_view_post_valid(self):
        """Test POST request to admin moderate review view with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_moderate_review', kwargs={'review_slug': self.review.slug})

        form_data = {
            'action': 'approve',
            'admin_notes': 'Review approved after moderation'
        }

        response = self.client.post(url, data=form_data)

        self.assertEqual(response.status_code, 302)  # Redirect after successful moderation

        # Check that review was updated
        self.review.refresh_from_db()
        self.assertTrue(self.review.is_approved)
        self.assertFalse(self.review.is_flagged)

    def test_admin_flag_resolution_view_get(self):
        """Test GET request to admin flag resolution view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_resolve_flag', kwargs={'flag_id': self.flag.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Resolve Flag')
        self.assertIn('flag', response.context)
        self.assertIn('review', response.context)

    def test_admin_flag_resolution_view_post_resolve(self):
        """Test POST request to admin flag resolution view to resolve flag."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_resolve_flag', kwargs={'flag_id': self.flag.id})

        form_data = {
            'action': 'resolve',
            'admin_notes': 'Flag reviewed - content is appropriate'
        }

        response = self.client.post(url, data=form_data)

        self.assertEqual(response.status_code, 302)  # Redirect after successful resolution

        # Check that flag was resolved
        self.flag.refresh_from_db()
        self.assertEqual(self.flag.status, ReviewFlag.RESOLVED)
        self.assertEqual(self.flag.reviewed_by, self.admin)
        self.assertEqual(self.flag.admin_notes, 'Flag reviewed - content is appropriate')

    def test_admin_flag_resolution_invalid_action(self):
        """Test flag resolution with invalid action."""
        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_resolve_flag', kwargs={'flag_id': self.flag.id})

        form_data = {
            'action': 'invalid_action',
            'admin_notes': 'Test notes'
        }

        response = self.client.post(url, data=form_data)

        # Should stay on same page or redirect to form
        self.assertIn(response.status_code, [200, 302])

    def test_admin_flag_resolution_already_resolved(self):
        """Test flag resolution when flag is already resolved."""
        # Resolve the flag first
        self.flag.status = ReviewFlag.RESOLVED
        self.flag.save()

        self.client.login(email='<EMAIL>', password='testpass123')
        url = reverse('review_app:admin_resolve_flag', kwargs={'flag_id': self.flag.id})

        form_data = {
            'action': 'resolve',
            'admin_notes': 'Already resolved'
        }

        response = self.client.post(url, data=form_data)

        # Should handle gracefully
        self.assertIn(response.status_code, [200, 302])

    def test_admin_views_context_data(self):
        """Test that admin views provide correct context data."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test moderation view context
        url = reverse('review_app:admin_review_moderation')
        response = self.client.get(url)

        self.assertIn('reviews', response.context)
        self.assertIn('stats', response.context)

    def test_admin_views_permissions(self):
        """Test admin view permissions."""
        # Test customer trying to access admin views
        self.client.login(email='<EMAIL>', password='testpass123')

        urls = [
            reverse('review_app:admin_review_moderation'),
            reverse('review_app:admin_moderate_review', kwargs={'review_slug': self.review.slug}),
        ]

        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 403)  # Should be forbidden, not redirect
