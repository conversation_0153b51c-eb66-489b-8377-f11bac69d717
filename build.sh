#!/usr/bin/env bash
set -o errexit


# --- Build Script for CozyWish Application ---
echo "🔍  Step 1: Installing dependencies from requirements.txt..."
pip install -r requirements.txt
echo "✅  Dependencies installed successfully."



# --- Static Files Setup ---
echo "🎨  Step 2: Collecting static files..."
python manage.py collectstatic --no-input --clear
echo "✅  Static files collected."



# --- Database Setup ---
echo "🔧  Step 3: Making migrations..."
python manage.py makemigrations
echo "✅  Migrations made."

echo "🚀  Step 4: Applying database migrations..."
python manage.py migrate
echo "✅  Database migrated successfully."



# --- URL Resolution Test ---
echo "🔍  Step 5: Testing URL resolution..."
python manage.py test_urls --url-name=home
if [ $? -eq 0 ]; then
    echo "✅  URL resolution test passed."
else
    echo "⚠️   URL resolution test failed, but continuing..."
fi



# --- Completion Message ---
echo "🎉  Build script completed successfully! All steps done. 🎉"

