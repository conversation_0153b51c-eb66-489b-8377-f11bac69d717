===== DJANG<PERSON> APPS FEATURE =====


=== 01 accounts_app ===
# Customer Features
- Sign up with email and password
- Login and logout (session-based)
- Forgot/reset password via email
- View and update profile
- Change password from dashboard
- Deactivate or delete account
- Login with Google and Apple

# Service Provider Features
- Sign up with email, password, and business details
- Email verification
- Login and logout (session-based)
- Forgot/reset password via email
- View and update business profile
- Change password from dashboard
- Deactivate or delete account
- Manage team/staff (add, remove, deactivate)

# Admin Features:
- View all users (customers and service providers)
- Activate, deactivate, or delete any account
- Activate/deactivate accounts
- Reset passwords for users if needed
- Access alerts for unusual login attempts
- See login history for each user



=== 02. venues_app ===
# Customer Features:
- Search venues by name, category, or location (state, county, city)
- Filter results by promotions, discounts, price, venue type (e.g., male/female only)
- View detailed venue information
- Browse list of services offered by the venue

# Service Provider Features:
- Create and manage one venue only
- Add up to 7 services under their venue
- Edit or update venue details and photos
- Add, edit, or remove services
- Offer special discounts on services

# Admin Features:
- Review and approve new or updated venues before they go live
- Manage service categories (add, edit, delete)
- Assign venues to the right categories for better search results
- Flag or remove inappropriate listings or offers
- Activate or deactivate any venue or service



=== 03. discount_app ===
# Customer Features
- See all active discounts on venues and services

# Service Provider Features
- Create one-time or limited-time discounts for their venue
- Set custom discounts for each service (up to 7 services)
- Manage when discounts start and end

# Admin Features:
- Create and manage platform-wide discount campaigns
- Approve or reject provider discounts if needed
- Set basic rules for discount usage
- Monitor how well discounts are performing



=== 04. booking_cart_app ===
# Customer Features
- Add one or more services to booking cart
- Cart items expire automatically after 24 hours
- See real-time availability before booking
- Cancel booking within 6 hours if needed
- Get booking confirmation by email or notification
- View all past bookings and their current status

# Service Provider Features
- Set available time slots for each service
- See and manage upcoming bookings
- Accept or decline booking requests
- Limit how many bookings can happen at the same time (up to 10)
- Get notified when new bookings arrive

# Admin Features:
- Monitor all bookings across the platform
- Resolve booking-related issues or disputes
- Access reports and analytics on booking trends
- Update basic booking rules or settings



=== 05. payments_app ===
# Customer Features
- Pay securely using card or wallet
- See total price before confirming payment
- Get payment receipt by email
- View past payment history
- Refund request option for canceled bookings

# Service Provider Features
- Receive payments for confirmed bookings
- View daily, weekly, and monthly earnings
- Manage refund requests from customers
- Get notifications when payments are received
- Update payment details (e.g., bank info)

# Admin Features:
- Track all payments and transactions
- Approve or decline refund requests
- View earnings reports by service provider
- Manage payment settings and payout rules
- Handle failed or disputed payments



=== 06. dashboard_app ===
# Customer Features
- View all past and upcoming bookings
- Check current booking status
- Manage favorite venues list
- Edit personal profile details

# Service Provider Features
- View today’s bookings at a glance
- Track earnings in a simple report
- See which services are performing best
- Monitor discount usage and results
- Manage staff or team members

# Admin Features:
- View overall platform activity
- See customer and provider statistics
- Track bookings across the platform
- Monitor total revenue
- Check system status and performance



=== 07. review_app ===
# Customer Features:
- Leave a star rating and written review after a completed service
- Read reviews on each provider’s profile
- Reviews are shown with the most recent first
- Flag any review they believe is fake or inappropriate

# Service Provider Features:
- Read all reviews received from customers
- See average rating and total review count on profile
- Respond publicly to any review
- Get notified when a new review is posted

# Admin Features:
- Remove or edit reviews that break the rules
- Review and take action on reported reviews
- Highlight “New on CozyWish” for providers with no or few reviews



=== 08. notifications_app ===
# Customer Features
- Get alerts for booking confirmations
- Get updates if booking status changes
- Receive messages when providers respond
- Get notified when a payment is successful
- Receive important updates from the platform
- See number of unread notifications
- View full notification history

# Service Provider Features
- Get alerts for new bookings
- Receive cancellation notifications
- Get notified when customers leave a review
- Receive updates when payments are made
- See platform-wide announcements
- Track unread notifications
- View full notification history

# Admin Features:
- Send announcements to all users or selected groups
- View notification delivery status and history



=== 09. admin_app === 
# Admin Features
- Manage users (view, edit, delete, reset passwords)
- Approve/reject service providers
- Create/edit static pages (About, Terms, FAQs, etc.)
- Edit homepage hero section (title, image, button text)
- Manage homepage blocks (e.g., “How It Works”, “Top Deals”)
- Upload/manage media files
- Create blog categories and posts
- Manage banners and announcements
- Set SEO tags for pages
- Turn pages on/off (publish/unpublish)
- Manage page URLs
- Perform bulk actions (users, content, venues)
- View platform metrics and reports
- Monitor system health and admin activity

