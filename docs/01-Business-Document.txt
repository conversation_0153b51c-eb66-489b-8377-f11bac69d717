===== BUSINESS OVERVIEW =====
CozyWish is USA based local consumer focused e-commerce marketplace for Spa and wellness services.
It will known for dedicated marketplace for flush, off-peak, discount driven deals for fil up empty sit.
CozyWish will be clone version of Gowabi for USA market.



===== COMPETITOR ANALYSIS =====
1. https://www.fresha.com
2. https://www.gowabi.com/en

Gowabi:
GoWabi is Thailand-based beauty and wellness marketplace
known for its heavy focus on deals, off-peak discounts, and merchant tools.

Fresha:
Fresha is a global beauty platform that offers free SaaS to salons and spas
while monetizing through commissions on new-client bookings and payment processing fees.

How CozyWish Differs:
1. Gowabi targets audience in Southeast Asia, CozyWish focuses on local USA market only.
2. Fresha offers extensive back office features as SaaS, where CozyWish only marketplace.
3. CozyWish is not SaaS tool, it is discounted off-peak e-commerce marketplace.



===== REVENUE MODEL =====
1. CozyWish charges small commission for every successful booking made through the platform.
2. Service providers advertising and promotions



===== COMMON-TERMS =====
- Customer: Individuals who purchase services are typically referred to as customers.
- Service Providers: Businesses offering services are commonly referred to as service providers.
- Venues: This term refers to the physical location or establishment where beauty and wellness treatments are provided.
- Service: This term pertains to the specific treatments or offerings provided by these venues



===== RESTRICTIONS =====
- Solo developer
- Limited budget and time
- MVP building
- Soft launch



===== APPLICATION BUILDING TECHNOLOGY =====
1. Python
2. Django Framework
3. postgresql Database (SQLite for local development) (Render Postgresql)
4. Bootstrap5 (Front end)
5. AWS S3 (Media Images)
6. https://render.com/ (Hosting)
7. GoDaddy (Domain)
8. Google Workspace for Email setup (Business email)
9. Stripe (Payment)
10. SendGrid (Email sending service)



===== DJANGO APPS =====
1. accounts_app
2. venues_app
3. discount_app
4. booking_cart_app
5. payments_app
6. dashboard_app
7. review_app
8. notifications_app
9. admin_app

