"""Tests for venue information management features."""

import pytest
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.test import Client, RequestFactory
from django.urls import reverse
from model_bakery import baker

from accounts_app.models import CustomUser, ServiceProviderProfile
from venues_app.forms import VenueFAQForm, VenueAmenityForm, OperatingHoursForm
from venues_app.models import (
    Venue, VenueFAQ, VenueAmenity, OperatingHours, Category
)


pytestmark = pytest.mark.django_db


# --- Contact Information Tests ---

def test_venue_contact_fields_creation():
    """Test venue can be created with contact information."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(
        Venue,
        service_provider=provider,
        phone='(*************',
        email='<EMAIL>',
        website_url='https://www.venue.com'
    )
    
    assert venue.phone == '(*************'
    assert venue.email == '<EMAIL>'
    assert venue.website_url == 'https://www.venue.com'


def test_venue_contact_fields_optional():
    """Test venue contact fields are optional."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    assert venue.phone == ''
    assert venue.email == ''
    assert venue.website_url == ''


# --- VenueAmenity Model Tests ---

def test_venue_amenity_creation():
    """Test VenueAmenity model creation."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    amenity = VenueAmenity.objects.create(
        venue=venue,
        amenity_type=VenueAmenity.WIFI,
        custom_name='High-Speed Wi-Fi',
        description='Complimentary high-speed internet access',
        is_active=True
    )
    
    assert amenity.venue == venue
    assert amenity.amenity_type == VenueAmenity.WIFI
    assert amenity.custom_name == 'High-Speed Wi-Fi'
    assert amenity.description == 'Complimentary high-speed internet access'
    assert amenity.is_active is True
    assert str(amenity) == f"{venue.venue_name} - High-Speed Wi-Fi"


def test_venue_amenity_display_name():
    """Test amenity display name logic."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    # With custom name
    amenity1 = VenueAmenity.objects.create(
        venue=venue,
        amenity_type=VenueAmenity.WIFI,
        custom_name='Premium Wi-Fi'
    )
    assert str(amenity1) == f"{venue.venue_name} - Premium Wi-Fi"
    
    # Without custom name
    amenity2 = VenueAmenity.objects.create(
        venue=venue,
        amenity_type=VenueAmenity.PARKING
    )
    assert str(amenity2) == f"{venue.venue_name} - Parking Available"


def test_venue_amenity_unique_constraint():
    """Test venue can't have duplicate amenity types."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    VenueAmenity.objects.create(venue=venue, amenity_type=VenueAmenity.WIFI)
    
    with pytest.raises(Exception):  # IntegrityError for unique constraint
        VenueAmenity.objects.create(venue=venue, amenity_type=VenueAmenity.WIFI)


def test_venue_amenity_max_limit_validation():
    """Test venue amenity maximum limit validation."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    # Create 15 amenities (max limit)
    amenity_types = list(VenueAmenity.AMENITY_CHOICES)
    for i in range(15):
        VenueAmenity.objects.create(
            venue=venue,
            amenity_type=amenity_types[i][0]
        )
    
    # Try to create 16th amenity
    amenity = VenueAmenity(
        venue=venue,
        amenity_type='custom_type'  # This would fail anyway due to choices
    )
    
    with pytest.raises(ValidationError):
        amenity.clean()


# --- OperatingHours Model Tests ---

def test_operating_hours_creation():
    """Test OperatingHours model creation."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    from datetime import time
    hours = OperatingHours.objects.create(
        venue=venue,
        day=0,  # Monday
        opening=time(9, 0),
        closing=time(17, 0),
        is_closed=False
    )
    
    assert hours.venue == venue
    assert hours.day == 0
    assert hours.opening == time(9, 0)
    assert hours.closing == time(17, 0)
    assert hours.is_closed is False
    assert str(hours) == f"{venue.venue_name} - Monday: 09:00:00-17:00:00"


def test_operating_hours_closed_day():
    """Test operating hours for closed day."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    hours = OperatingHours.objects.create(
        venue=venue,
        day=6,  # Sunday
        is_closed=True
    )
    
    assert hours.is_closed is True
    assert hours.opening is None
    assert hours.closing is None
    assert str(hours) == f"{venue.venue_name} - Sunday: Closed"


def test_operating_hours_unique_constraint():
    """Test venue can't have duplicate operating hours for same day."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    from datetime import time
    OperatingHours.objects.create(
        venue=venue,
        day=0,
        opening=time(9, 0),
        closing=time(17, 0)
    )
    
    with pytest.raises(Exception):  # IntegrityError for unique constraint
        OperatingHours.objects.create(
            venue=venue,
            day=0,
            opening=time(10, 0),
            closing=time(18, 0)
        )


# --- VenueFAQ Model Tests ---

def test_venue_faq_creation():
    """Test VenueFAQ model creation."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    faq = VenueFAQ.objects.create(
        venue=venue,
        question='What are your hours?',
        answer='We are open Monday through Friday from 9 AM to 6 PM.',
        order=1,
        is_active=True
    )
    
    assert faq.venue == venue
    assert faq.question == 'What are your hours?'
    assert faq.answer == 'We are open Monday through Friday from 9 AM to 6 PM.'
    assert faq.order == 1
    assert faq.is_active is True
    assert str(faq) == f"{venue.venue_name} - FAQ 1: What are your hours?"


def test_venue_faq_max_limit_validation():
    """Test venue FAQ maximum limit validation."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    # Create 5 FAQs (max limit)
    for i in range(5):
        VenueFAQ.objects.create(
            venue=venue,
            question=f'Question {i+1}?',
            answer=f'Answer {i+1}',
            order=i+1
        )
    
    # Try to create 6th FAQ
    faq = VenueFAQ(
        venue=venue,
        question='Question 6?',
        answer='Answer 6',
        order=6
    )
    
    with pytest.raises(ValidationError):
        faq.clean()


def test_venue_faq_unique_order_constraint():
    """Test venue can't have duplicate FAQ orders."""
    provider = baker.make(ServiceProviderProfile)
    venue = baker.make(Venue, service_provider=provider)
    
    VenueFAQ.objects.create(
        venue=venue,
        question='Question 1?',
        answer='Answer 1',
        order=1
    )
    
    with pytest.raises(Exception):  # IntegrityError for unique constraint
        VenueFAQ.objects.create(
            venue=venue,
            question='Question 2?',
            answer='Answer 2',
            order=1
        )


# --- Form Tests ---

def test_venue_faq_form_valid():
    """Test VenueFAQForm with valid data."""
    data = {
        'question': 'How Can I Book An Appointment Online 24/7?',
        'answer': 'You can book appointments online through our website booking system or call us directly at our phone number.',
    }
    form = VenueFAQForm(data=data)
    if not form.is_valid():
        print(f"Form errors: {form.errors}")
    assert form.is_valid()


def test_venue_faq_form_required_fields():
    """Test VenueFAQForm with missing required fields."""
    form = VenueFAQForm(data={})
    assert not form.is_valid()
    required_fields = ['question', 'answer']
    for field in required_fields:
        assert field in form.errors


def test_venue_amenity_form_valid():
    """Test VenueAmenityForm with valid data."""
    data = {
        'amenity_type': VenueAmenity.WIFI,
        'custom_name': 'High-Speed Wi-Fi',
        'description': 'Complimentary internet access',
        'is_active': True,
    }
    form = VenueAmenityForm(data=data)
    assert form.is_valid()


def test_operating_hours_form_valid():
    """Test OperatingHoursForm with valid data."""
    from datetime import time
    data = {
        'day': 0,
        'opening': time(9, 0),
        'closing': time(17, 0),
        'is_closed': False,
    }
    form = OperatingHoursForm(data=data)
    assert form.is_valid()


def test_operating_hours_form_closed_day():
    """Test OperatingHoursForm for closed day."""
    data = {
        'day': 6,
        'is_closed': True,
    }
    form = OperatingHoursForm(data=data)
    assert form.is_valid()


def test_operating_hours_form_validation_error():
    """Test OperatingHoursForm validation for invalid times."""
    from datetime import time
    data = {
        'day': 0,
        'opening': time(17, 0),  # Opening after closing
        'closing': time(9, 0),
        'is_closed': False,
    }
    form = OperatingHoursForm(data=data)
    assert not form.is_valid()
    assert 'Opening time must be before closing time' in str(form.errors)


# --- View Tests ---

def test_manage_faqs_view_requires_login():
    """Test manage_faqs view requires login."""
    client = Client()
    response = client.get(reverse('venues_app:manage_faqs'))
    assert response.status_code == 302  # Redirect to login


def test_manage_faqs_view_requires_service_provider():
    """Test manage_faqs view requires service provider profile."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    client = Client()
    client.force_login(user)

    response = client.get(reverse('venues_app:manage_faqs'))
    assert response.status_code == 302  # Redirect to home


def test_manage_faqs_view_success():
    """Test manage_faqs view for service provider with venue."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=provider)

    client = Client()
    client.force_login(user)

    response = client.get(reverse('venues_app:manage_faqs'))
    assert response.status_code == 200
    assert 'venue' in response.context
    assert response.context['venue'] == venue


def test_manage_amenities_view_success():
    """Test manage_amenities view for service provider with venue."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=provider)

    client = Client()
    client.force_login(user)

    response = client.get(reverse('venues_app:manage_amenities'))
    assert response.status_code == 200
    assert 'venue' in response.context
    assert response.context['venue'] == venue


def test_manage_operating_hours_view_success():
    """Test manage_operating_hours view for service provider with venue."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=provider)

    client = Client()
    client.force_login(user)

    response = client.get(reverse('venues_app:manage_operating_hours'))
    assert response.status_code == 200
    assert 'venue' in response.context
    assert response.context['venue'] == venue


def test_edit_faq_permission_check():
    """Test edit_faq view only allows venue owner to edit."""
    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider1 = baker.make(ServiceProviderProfile, user=user1)
    venue1 = baker.make(Venue, service_provider=provider1)
    faq = baker.make(VenueFAQ, venue=venue1)

    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider2 = baker.make(ServiceProviderProfile, user=user2)
    venue2 = baker.make(Venue, service_provider=provider2)

    client = Client()
    client.force_login(user2)

    response = client.get(reverse('venues_app:edit_faq', kwargs={'faq_id': faq.id}))
    assert response.status_code == 404  # FAQ not found for this user's venue


def test_delete_faq_permission_check():
    """Test delete_faq view only allows venue owner to delete."""
    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider1 = baker.make(ServiceProviderProfile, user=user1)
    venue1 = baker.make(Venue, service_provider=provider1)
    faq = baker.make(VenueFAQ, venue=venue1)

    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider2 = baker.make(ServiceProviderProfile, user=user2)
    venue2 = baker.make(Venue, service_provider=provider2)

    client = Client()
    client.force_login(user2)

    response = client.get(reverse('venues_app:delete_faq', kwargs={'faq_id': faq.id}))
    assert response.status_code == 404  # FAQ not found for this user's venue


def test_edit_amenity_permission_check():
    """Test edit_amenity view only allows venue owner to edit."""
    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider1 = baker.make(ServiceProviderProfile, user=user1)
    venue1 = baker.make(Venue, service_provider=provider1)
    amenity = baker.make(VenueAmenity, venue=venue1)

    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider2 = baker.make(ServiceProviderProfile, user=user2)
    venue2 = baker.make(Venue, service_provider=provider2)

    client = Client()
    client.force_login(user2)

    response = client.get(reverse('venues_app:edit_amenity', kwargs={'amenity_id': amenity.id}))
    assert response.status_code == 404  # Amenity not found for this user's venue


def test_delete_amenity_permission_check():
    """Test delete_amenity view only allows venue owner to delete."""
    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider1 = baker.make(ServiceProviderProfile, user=user1)
    venue1 = baker.make(Venue, service_provider=provider1)
    amenity = baker.make(VenueAmenity, venue=venue1)

    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider2 = baker.make(ServiceProviderProfile, user=user2)
    venue2 = baker.make(Venue, service_provider=provider2)

    client = Client()
    client.force_login(user2)

    response = client.get(reverse('venues_app:delete_amenity', kwargs={'amenity_id': amenity.id}))
    assert response.status_code == 404  # Amenity not found for this user's venue


# --- Approval Status Reset Tests ---

def test_venue_approval_reset_on_significant_change():
    """Test venue approval status resets when significant changes are made."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(
        Venue,
        service_provider=provider,
        approval_status=Venue.APPROVED,
        venue_name='Original Name'
    )

    # Make significant change
    venue.venue_name = 'New Name'
    venue.save()

    venue.refresh_from_db()
    assert venue.approval_status == Venue.PENDING


def test_venue_approval_reset_on_contact_change():
    """Test venue approval status resets when contact info changes."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(
        Venue,
        service_provider=provider,
        approval_status=Venue.APPROVED,
        phone='(*************'
    )

    # Make contact change
    venue.phone = '(*************'
    venue.save()

    venue.refresh_from_db()
    assert venue.approval_status == Venue.PENDING


def test_venue_approval_no_reset_on_minor_change():
    """Test venue approval status doesn't reset for minor changes."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(
        Venue,
        service_provider=provider,
        approval_status=Venue.APPROVED,
        tags='spa, massage'
    )

    # Make minor change (tags are not significant)
    venue.tags = 'spa, massage, relaxation'
    venue.save()

    venue.refresh_from_db()
    assert venue.approval_status == Venue.APPROVED


# --- Integration Tests ---

def test_faq_add_edit_delete_workflow():
    """Test complete FAQ workflow: add, edit, delete."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=provider)

    client = Client()
    client.force_login(user)

    # Test adding FAQ
    response = client.post(reverse('venues_app:manage_faqs'), {
        'question': 'How Can I Schedule My Appointment Online 24/7?',
        'answer': 'You can easily schedule appointments through our online booking system available on our website.',
    })
    assert response.status_code == 302  # Redirect after success
    
    faq = VenueFAQ.objects.filter(venue=venue).first()
    assert faq is not None
    assert faq.question == 'How Can I Schedule My Appointment Online 24/7?'
    assert 'You can easily schedule appointments through our online booking system available on our website.' in faq.answer


def test_amenity_add_edit_delete_workflow():
    """Test complete amenity management workflow."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=provider)

    client = Client()
    client.force_login(user)

    # Add amenity
    response = client.post(reverse('venues_app:manage_amenities'), {
        'amenity_type': VenueAmenity.WIFI,
        'custom_name': 'High-Speed Wi-Fi',
        'description': 'Complimentary internet access',
        'is_active': True,
    })
    assert response.status_code == 302  # Redirect after success

    amenity = VenueAmenity.objects.get(venue=venue)
    assert amenity.amenity_type == VenueAmenity.WIFI
    assert amenity.custom_name == 'High-Speed Wi-Fi'

    # Edit amenity
    response = client.post(reverse('venues_app:edit_amenity', kwargs={'amenity_id': amenity.id}), {
        'amenity_type': VenueAmenity.WIFI,
        'custom_name': 'Premium Wi-Fi',
        'description': 'High-speed wireless internet',
        'is_active': True,
    })
    assert response.status_code == 302  # Redirect after success

    amenity.refresh_from_db()
    assert amenity.custom_name == 'Premium Wi-Fi'

    # Delete amenity
    response = client.post(reverse('venues_app:delete_amenity', kwargs={'amenity_id': amenity.id}))
    assert response.status_code == 302  # Redirect after success

    assert not VenueAmenity.objects.filter(venue=venue).exists()
