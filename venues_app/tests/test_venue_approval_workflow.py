"""
Comprehensive tests for the Venue Approval Workflow.

Tests cover:
1. New venues appear in admin approval queue
2. Admin can view venue details for approval
3. Admin can approve venues with notes
4. Admin can reject venues with reasons
5. Provider receives notification of approval/rejection
6. Approved venues become visible to customers
7. Rejected venues remain hidden from customers
"""

# --- Standard Library Imports ---
from unittest.mock import patch

# --- Django Imports ---
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.contrib.messages.storage.fallback import FallbackStorage
from django.contrib.sessions.middleware import SessionMiddleware

# --- Third-Party Imports ---
import pytest

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Venue, Category
from notifications_app.models import Notification

User = get_user_model()


@pytest.mark.django_db
class TestVenueApprovalWorkflow:
    """Test suite for venue approval workflow functionality."""

    @pytest.fixture
    def admin_user(self):
        """Create an admin user for testing."""
        return User.objects.create_user(
            email='<EMAIL>',
            password='P@ssw0rd123',
            is_staff=True,
            is_superuser=True
        )

    @pytest.fixture
    def service_provider_user(self):
        """Create a service provider user for testing."""
        return User.objects.create_user(
            email='<EMAIL>',
            password='P@ssw0rd123',
            role=User.SERVICE_PROVIDER
        )

    @pytest.fixture
    def service_provider_profile(self, service_provider_user):
        """Create a service provider profile for testing."""
        return ServiceProviderProfile.objects.create(
            user=service_provider_user,
            legal_name='Test Spa',
            phone='+15550123',
            contact_name='Test Provider',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )

    @pytest.fixture
    def category(self):
        """Create a category for testing."""
        return Category.objects.create(
            category_name='Spa & Wellness',
            slug='spa-wellness',
            is_active=True
        )

    @pytest.fixture
    def pending_venue(self, service_provider_profile, category):
        """Create a pending venue for testing."""
        venue = Venue.objects.create(
            service_provider=service_provider_profile,
            venue_name='Test Venue',
            short_description='A test venue for approval testing',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            county='Test County',
            state='CA',
            phone='555-0123',
            email='<EMAIL>',
            approval_status=Venue.PENDING
        )
        venue.categories.add(category)
        return venue

    def test_new_venues_appear_in_admin_approval_queue(self, client, admin_user, pending_venue):
        """Test that new venues appear in admin approval queue."""
        client.login(email=admin_user.email, password='P@ssw0rd123')
        
        # Test admin dashboard shows pending venues
        response = client.get(reverse('venues_app:admin_venue_approval_dashboard'))
        assert response.status_code == 200
        assert pending_venue.venue_name in response.content.decode()
        
        # Test pending venues list
        response = client.get(reverse('venues_app:admin_pending_venues'))
        assert response.status_code == 200
        assert pending_venue.venue_name in response.content.decode()

    def test_admin_can_view_venue_details_for_approval(self, client, admin_user, pending_venue):
        """Test that admin can view venue details for approval."""
        client.login(email=admin_user.email, password='P@ssw0rd123')
        
        # Test venue detail view
        response = client.get(reverse('venues_app:admin_venue_detail', kwargs={'venue_id': pending_venue.id}))
        assert response.status_code == 200
        assert pending_venue.venue_name in response.content.decode()
        assert pending_venue.service_provider.business_name in response.content.decode()
        assert pending_venue.short_description in response.content.decode()
        
        # Test venue approval view
        response = client.get(reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id}))
        assert response.status_code == 200
        assert 'Venue Approval Review' in response.content.decode()
        assert pending_venue.venue_name in response.content.decode()

    @patch('venues_app.signals.run_async')
    def test_admin_can_approve_venues_with_notes(self, mock_run_async, client, admin_user, pending_venue):
        """Test that admin can approve venues with notes."""
        client.login(email=admin_user.email, password='P@ssw0rd123')
        
        approval_notes = 'Great venue, approved!'
        
        response = client.post(
            reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id}),
            {
                'action': 'approve',
                'admin_notes': approval_notes
            }
        )
        
        # Check redirect
        assert response.status_code == 302
        assert response.url == reverse('venues_app:admin_pending_venues')
        
        # Check venue was updated
        pending_venue.refresh_from_db()
        assert pending_venue.approval_status == Venue.APPROVED
        assert pending_venue.admin_notes == approval_notes
        assert pending_venue.approved_at is not None
        assert pending_venue.rejected_at is None
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        assert any('approved successfully' in str(m) for m in messages)
        
        # Check notification was triggered
        mock_run_async.assert_called()

    @patch('venues_app.signals.run_async')
    def test_admin_can_reject_venues_with_reasons(self, mock_run_async, client, admin_user, pending_venue):
        """Test that admin can reject venues with reasons."""
        client.login(email=admin_user.email, password='P@ssw0rd123')
        
        rejection_reason = 'Incomplete information provided'
        
        response = client.post(
            reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id}),
            {
                'action': 'reject',
                'admin_notes': rejection_reason
            }
        )
        
        # Check redirect
        assert response.status_code == 302
        assert response.url == reverse('venues_app:admin_pending_venues')
        
        # Check venue was updated
        pending_venue.refresh_from_db()
        assert pending_venue.approval_status == Venue.REJECTED
        assert pending_venue.admin_notes == rejection_reason
        assert pending_venue.rejected_at is not None
        assert pending_venue.approved_at is None
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        assert any('rejected successfully' in str(m) for m in messages)
        
        # Check notification was triggered
        mock_run_async.assert_called()

    def test_admin_cannot_reject_without_reason(self, client, admin_user, pending_venue):
        """Test that admin cannot reject venues without providing a reason."""
        client.login(email=admin_user.email, password='P@ssw0rd123')
        
        response = client.post(
            reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id}),
            {
                'action': 'reject',
                'admin_notes': ''  # Empty reason
            }
        )
        
        # Check redirect back to approval page
        assert response.status_code == 302
        assert response.url == reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id})
        
        # Check venue was not updated
        pending_venue.refresh_from_db()
        assert pending_venue.approval_status == Venue.PENDING
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        assert any('Please provide a reason for rejection' in str(m) for m in messages)

    def test_approved_venues_visible_to_customers(self, client, pending_venue):
        """Test that approved venues become visible to customers."""
        # Initially venue should not be visible
        response = client.get(reverse('venues_app:venue_search'))
        assert response.status_code == 200
        assert pending_venue.venue_name not in response.content.decode()
        
        # Approve the venue
        pending_venue.approval_status = Venue.APPROVED
        pending_venue.approved_at = timezone.now()
        pending_venue.save()
        
        # Now venue should be visible
        response = client.get(reverse('venues_app:venue_search'))
        assert response.status_code == 200
        assert pending_venue.venue_name in response.content.decode()
        
        # Test venue detail page is accessible
        response = client.get(reverse('venues_app:venue_detail', kwargs={'venue_slug': pending_venue.slug}))
        assert response.status_code == 200
        assert pending_venue.venue_name in response.content.decode()

    def test_rejected_venues_hidden_from_customers(self, client, pending_venue):
        """Test that rejected venues remain hidden from customers."""
        # Reject the venue (with proper timestamp to satisfy database constraint)
        pending_venue.approval_status = Venue.REJECTED
        pending_venue.rejected_at = timezone.now()
        pending_venue.admin_notes = 'Test rejection'
        pending_venue.save()
        
        # Venue should not be visible in search
        response = client.get(reverse('venues_app:venue_search'))
        assert response.status_code == 200
        assert pending_venue.venue_name not in response.content.decode()
        
        # Venue detail page should return 404
        response = client.get(reverse('venues_app:venue_detail', kwargs={'venue_slug': pending_venue.slug}))
        assert response.status_code == 404


    def test_non_admin_cannot_access_approval_views(self, client, service_provider_user, pending_venue):
        """Test that non-admin users cannot access approval views."""
        client.login(email=service_provider_user.email, password='P@ssw0rd123')
        
        # Test admin dashboard - should redirect to home
        response = client.get(reverse('venues_app:admin_venue_approval_dashboard'))
        assert response.status_code == 302  # Redirect to home
        
        # Test pending venues list - should redirect to home
        response = client.get(reverse('venues_app:admin_pending_venues'))
        assert response.status_code == 302  # Redirect to home
        
        # Test venue approval view - should redirect to home
        response = client.get(reverse('venues_app:admin_venue_approval', kwargs={'venue_id': pending_venue.id}))
        assert response.status_code == 302  # Redirect to home

    def test_venue_timestamps_updated_correctly(self, pending_venue):
        """Test that venue timestamps are updated correctly during approval workflow."""
        original_created_at = pending_venue.created_at
        
        # Approve venue
        pending_venue.approval_status = Venue.APPROVED
        pending_venue.approved_at = timezone.now()
        pending_venue.save()
        
        pending_venue.refresh_from_db()
        assert pending_venue.approved_at is not None
        assert pending_venue.rejected_at is None
        assert pending_venue.created_at == original_created_at
        
        # Reject venue (with proper timestamp to satisfy database constraint)
        pending_venue.approval_status = Venue.REJECTED
        pending_venue.rejected_at = timezone.now()
        pending_venue.approved_at = None  # Clear approved timestamp
        pending_venue.save()
        
        pending_venue.refresh_from_db()
        assert pending_venue.approved_at is None
        assert pending_venue.rejected_at is not None
        
        # Back to pending
        pending_venue.approval_status = Venue.PENDING
        pending_venue.rejected_at = None  # Clear rejected timestamp
        pending_venue.save()
        
        pending_venue.refresh_from_db()
        assert pending_venue.approved_at is None
        assert pending_venue.rejected_at is None

    @patch('venues_app.signals.run_async')
    def test_bulk_admin_actions_trigger_notifications(self, mock_run_async, admin_user, pending_venue):
        """Test that bulk admin actions properly trigger notifications."""
        from venues_app.admin import VenueAdmin
        from django.contrib.admin.sites import AdminSite
        from django.http import HttpRequest
        
        # Create admin instance
        admin_site = AdminSite()
        venue_admin = VenueAdmin(Venue, admin_site)
        
        # Create mock request with proper message support
        request = HttpRequest()
        request.user = admin_user
        
        # Add session middleware support
        SessionMiddleware(lambda x: None).process_request(request)
        request.session.save()
        
        # Add message framework support
        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)
        
        # Test bulk approve
        queryset = Venue.objects.filter(id=pending_venue.id)
        venue_admin.approve_venues(request, queryset)
        
        pending_venue.refresh_from_db()
        assert pending_venue.approval_status == Venue.APPROVED
        assert pending_venue.approved_at is not None
        assert 'Bulk approved by' in pending_venue.admin_notes
        
        # Reset venue to pending and clear admin_notes for proper reject test
        pending_venue.approval_status = Venue.PENDING
        pending_venue.approved_at = None
        pending_venue.admin_notes = ''  # Clear admin_notes so reject will set them
        pending_venue.save()
        
        # Test bulk reject
        venue_admin.reject_venues(request, queryset)
        
        pending_venue.refresh_from_db()
        assert pending_venue.approval_status == Venue.REJECTED
        assert pending_venue.rejected_at is not None
        assert 'Bulk rejected by' in pending_venue.admin_notes
        
        # Check notifications were triggered
        assert mock_run_async.call_count >= 2
