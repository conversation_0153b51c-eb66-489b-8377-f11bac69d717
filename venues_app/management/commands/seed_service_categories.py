"""Management command to seed the database with default service categories."""

from django.core.management.base import BaseCommand
from django.db import transaction
from venues_app.models import ServiceCategory


class Command(BaseCommand):
    """Seed service categories with essential service types."""
    
    help = 'Seed the database with default service categories'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing service categories before seeding',
        )

    def handle(self, *args, **options):
        """Execute the seeding command."""
        self.stdout.write(self.style.SUCCESS('🚀 Starting service categories seeding...'))
        
        if options['reset']:
            self.reset_categories()
        
        self.create_core_service_categories()
        
        self.stdout.write(self.style.SUCCESS('✅ Service categories seeding completed successfully!'))

    def reset_categories(self):
        """Reset existing service categories."""
        self.stdout.write('🗑️  Resetting existing service categories...')
        
        deleted_count = ServiceCategory.objects.all().count()
        ServiceCategory.objects.all().delete()
        
        self.stdout.write(f'   ✅ Deleted {deleted_count} existing service categories')

    def create_core_service_categories(self):
        """Create essential service categories."""
        self.stdout.write('🏷️ Creating core service categories...')
        
        # Define the core service categories
        categories_data = [
            {
                'name': 'Massage Therapy',
                'description': 'Therapeutic and relaxation massage services including Swedish, deep tissue, hot stone, and specialized bodywork treatments.',
                'icon_class': 'fas fa-hand-paper',
                'color_code': '#4CAF50',
                'sort_order': 1,
                'is_active': True
            },
            {
                'name': 'Facial Treatments',
                'description': 'Professional skincare treatments including cleansing facials, anti-aging treatments, and specialized skin therapies.',
                'icon_class': 'fas fa-smile',
                'color_code': '#FF9800',
                'sort_order': 2,
                'is_active': True
            },
            {
                'name': 'Hair Services',
                'description': 'Complete hair care services including cuts, styling, coloring, treatments, and specialized hair procedures.',
                'icon_class': 'fas fa-cut',
                'color_code': '#9C27B0',
                'sort_order': 3,
                'is_active': True
            },
            {
                'name': 'Nail Care',
                'description': 'Professional nail services including manicures, pedicures, nail art, gel treatments, and nail health care.',
                'icon_class': 'fas fa-hand-sparkles',
                'color_code': '#E91E63',
                'sort_order': 4,
                'is_active': True
            },
            {
                'name': 'Body Treatments',
                'description': 'Full body wellness treatments including body wraps, scrubs, detox treatments, and body contouring services.',
                'icon_class': 'fas fa-spa',
                'color_code': '#00BCD4',
                'sort_order': 5,
                'is_active': True
            },
            {
                'name': 'Wellness & Therapy',
                'description': 'Holistic wellness services including acupuncture, reflexology, aromatherapy, and alternative healing treatments.',
                'icon_class': 'fas fa-leaf',
                'color_code': '#8BC34A',
                'sort_order': 6,
                'is_active': True
            },
            {
                'name': 'Fitness & Training',
                'description': 'Personal training, group fitness classes, yoga sessions, pilates, and specialized fitness programs.',
                'icon_class': 'fas fa-dumbbell',
                'color_code': '#FF5722',
                'sort_order': 7,
                'is_active': True
            },
            {
                'name': 'Beauty Services',
                'description': 'Comprehensive beauty treatments including makeup application, eyebrow services, lash extensions, and cosmetic treatments.',
                'icon_class': 'fas fa-magic',
                'color_code': '#F44336',
                'sort_order': 8,
                'is_active': True
            },
            {
                'name': 'Medical Spa',
                'description': 'Medical aesthetic treatments including laser procedures, injectables, skin rejuvenation, and medically supervised treatments.',
                'icon_class': 'fas fa-stethoscope',
                'color_code': '#2196F3',
                'sort_order': 9,
                'is_active': True
            },
            {
                'name': 'Specialized Therapy',
                'description': 'Specialized therapeutic services including physical therapy, rehabilitation, sports therapy, and medical treatments.',
                'icon_class': 'fas fa-user-md',
                'color_code': '#607D8B',
                'sort_order': 10,
                'is_active': True
            },
            {
                'name': 'Consultation',
                'description': 'Professional consultation services including wellness consultations, beauty advice, and service planning sessions.',
                'icon_class': 'fas fa-comments',
                'color_code': '#795548',
                'sort_order': 11,
                'is_active': True
            },
            {
                'name': 'Packages & Combos',
                'description': 'Service packages and combination treatments offering multiple services at special rates.',
                'icon_class': 'fas fa-gift',
                'color_code': '#3F51B5',
                'sort_order': 12,
                'is_active': True
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for category_data in categories_data:
            with transaction.atomic():
                category, created = ServiceCategory.objects.get_or_create(
                    name=category_data['name'],
                    defaults=category_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'   ✅ Created: {category.name}')
                else:
                    # Update existing category with new data
                    for key, value in category_data.items():
                        if key != 'name':  # Don't update the name
                            setattr(category, key, value)
                    category.save()
                    updated_count += 1
                    self.stdout.write(f'   🔄 Updated: {category.name}')
        
        self.stdout.write(f'   ✅ Created {created_count} new service categories')
        self.stdout.write(f'   🔄 Updated {updated_count} existing service categories')

    def create_additional_categories(self):
        """Create additional specialized categories if needed."""
        self.stdout.write('🎯 Creating additional service categories...')
        
        additional_categories = [
            {
                'name': 'Couples Services',
                'description': 'Romantic spa experiences and treatments designed for couples.',
                'icon_class': 'fas fa-heart',
                'color_code': '#E91E63',
                'sort_order': 20,
                'is_active': True
            },
            {
                'name': 'Prenatal Services',
                'description': 'Specialized treatments safe for expecting mothers.',
                'icon_class': 'fas fa-baby',
                'color_code': '#FFB6C1',
                'sort_order': 21,
                'is_active': True
            },
            {
                'name': 'Men\'s Services',
                'description': 'Services specifically tailored for men\'s grooming and wellness needs.',
                'icon_class': 'fas fa-male',
                'color_code': '#455A64',
                'sort_order': 22,
                'is_active': True
            },
            {
                'name': 'Teen Services',
                'description': 'Age-appropriate treatments designed for teenagers.',
                'icon_class': 'fas fa-user-friends',
                'color_code': '#FF7043',
                'sort_order': 23,
                'is_active': True
            }
        ]
        
        for category_data in additional_categories:
            with transaction.atomic():
                category, created = ServiceCategory.objects.get_or_create(
                    name=category_data['name'],
                    defaults=category_data
                )
                
                if created:
                    self.stdout.write(f'   ✅ Created additional: {category.name}') 