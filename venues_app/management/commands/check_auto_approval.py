from django.core.management.base import BaseCommand
from django.utils import timezone
from venues_app.models import Venue


class Command(BaseCommand):
    help = 'Check and apply auto-approval for eligible pending venues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show which venues would be auto-approved without making changes',
        )
        parser.add_argument(
            '--venue-id',
            type=int,
            help='Check specific venue by ID',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        venue_id = options.get('venue_id')

        # Get venues to check
        if venue_id:
            try:
                venues = [Venue.objects.get(id=venue_id, approval_status=Venue.PENDING)]
                self.stdout.write(f"Checking specific venue ID: {venue_id}")
            except Venue.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Venue with ID {venue_id} not found or not pending")
                )
                return
        else:
            venues = Venue.objects.filter(
                approval_status=Venue.PENDING,
                is_deleted=False
            ).select_related('service_provider', 'us_city').prefetch_related('categories', 'images', 'services')
            self.stdout.write(f"Checking {venues.count()} pending venues for auto-approval eligibility")

        approved_count = 0
        eligible_count = 0

        for venue in venues:
            meets_criteria, reasons = venue.check_auto_approval_eligibility()
            
            if meets_criteria:
                eligible_count += 1
                
                if dry_run:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✓ ELIGIBLE: {venue.venue_name} (ID: {venue.id}) - "
                            f"Would be auto-approved"
                        )
                    )
                else:
                    success = venue.apply_auto_approval_if_eligible()
                    if success:
                        approved_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"✓ APPROVED: {venue.venue_name} (ID: {venue.id}) - "
                                f"Automatically approved"
                            )
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f"⚠ FAILED: {venue.venue_name} (ID: {venue.id}) - "
                                f"Eligible but approval failed"
                            )
                        )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"✗ NOT ELIGIBLE: {venue.venue_name} (ID: {venue.id})"
                    )
                )
                for reason in reasons[:3]:  # Show first 3 reasons
                    self.stdout.write(f"    - {reason}")
                if len(reasons) > 3:
                    self.stdout.write(f"    ... and {len(reasons) - 3} more issues")

        # Summary
        self.stdout.write("\n" + "="*60)
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"DRY RUN COMPLETE: {eligible_count} venues would be auto-approved"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"AUTO-APPROVAL COMPLETE: {approved_count}/{eligible_count} eligible venues were approved"
                )
            )

        # Show progress distribution
        if not venue_id:
            self.stdout.write("\nProgress Distribution:")
            progress_stats = {}
            for venue in venues:
                progress = venue.get_approval_progress()
                percentage = progress['completion_percentage']
                range_key = f"{(percentage // 10) * 10}-{(percentage // 10) * 10 + 9}%"
                progress_stats[range_key] = progress_stats.get(range_key, 0) + 1
            
            for range_key, count in sorted(progress_stats.items()):
                self.stdout.write(f"  {range_key}: {count} venues")

        self.stdout.write("\nTip: Run with --dry-run to preview changes before applying them") 