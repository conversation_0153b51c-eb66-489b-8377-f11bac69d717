"""Management command to seed service tags for better searchability."""

from django.core.management.base import BaseCommand
from venues_app.models import ServiceTag


class Command(BaseCommand):
    help = 'Seed the database with default service tags for better searchability'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing service tags before seeding',
        )

    def handle(self, *args, **options):
        if options['clear']:
            deleted_count = ServiceTag.objects.all().count()
            ServiceTag.objects.all().delete()
            self.stdout.write(
                self.style.WARNING(f'Deleted {deleted_count} existing service tags.')
            )

        # Service tags organized by type
        service_tags = {
            # Keywords (general descriptive terms)
            ServiceTag.KEYWORD: [
                'relaxing', 'therapeutic', 'rejuvenating', 'luxury', 'wellness',
                'healing', 'professional', 'expert', 'customized', 'holistic',
                'natural', 'organic', 'premium', 'signature', 'specialty'
            ],
            
            # Techniques
            ServiceTag.TECHNIQUE: [
                'deep-tissue', 'swedish', 'hot-stone', 'aromatherapy', 'reflexology',
                'prenatal', 'sports-massage', 'trigger-point', 'myofascial', 'shiatsu',
                'lymphatic-drainage', 'cupping', 'acupressure', 'thai-massage', 'couples-massage',
                'microdermabrasion', 'chemical-peel', 'hydrafacial', 'oxygen-facial', 'anti-aging',
                'collagen', 'dermaplane', 'led-therapy', 'radiofrequency', 'ultrasonic'
            ],
            
            # Benefits
            ServiceTag.BENEFIT: [
                'pain-relief', 'stress-relief', 'muscle-tension', 'circulation', 'flexibility',
                'detox', 'skin-rejuvenation', 'anti-aging', 'hydrating', 'brightening',
                'acne-treatment', 'scar-reduction', 'wrinkle-reduction', 'firming', 'toning',
                'recovery', 'rehabilitation', 'injury-prevention', 'performance-enhancement'
            ],
            
            # Body Parts / Focus Areas
            ServiceTag.BODY_PART: [
                'face', 'neck', 'shoulders', 'back', 'legs', 'feet', 'hands', 'arms',
                'full-body', 'scalp', 'eye-area', 'chest', 'abdomen', 'glutes',
                'calves', 'thighs', 'upper-body', 'lower-body', 'core'
            ],
            
            # Duration
            ServiceTag.DURATION: [
                'express', 'quick', '30-minute', '60-minute', '90-minute', '2-hour',
                'half-day', 'full-day', 'extended', 'intensive', 'brief', 'standard'
            ],
            
            # Intensity
            ServiceTag.INTENSITY: [
                'gentle', 'mild', 'moderate', 'firm', 'deep', 'intense',
                'light-pressure', 'medium-pressure', 'strong-pressure', 'customizable',
                'beginner-friendly', 'advanced', 'therapeutic-level'
            ]
        }

        created_count = 0
        updated_count = 0

        for tag_type, tag_names in service_tags.items():
            for name in tag_names:
                tag, created = ServiceTag.objects.get_or_create(
                    name=name,
                    defaults={
                        'tag_type': tag_type,
                        'description': f'{tag_type.replace("_", " ").title()} tag for {name}',
                        'is_active': True
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'  Created: {name} ({tag_type})')
                else:
                    # Update tag type if different
                    if tag.tag_type != tag_type:
                        tag.tag_type = tag_type
                        tag.save()
                        updated_count += 1
                        self.stdout.write(f'  Updated: {name} -> {tag_type}')

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSeed completed successfully!\n'
                f'Created: {created_count} new service tags\n'
                f'Updated: {updated_count} existing service tags\n'
                f'Total service tags: {ServiceTag.objects.count()}'
            )
        )

        # Update usage counts for existing tags
        self.stdout.write('Updating usage counts...')
        for tag in ServiceTag.objects.all():
            tag.update_usage_count()
        
        self.stdout.write(
            self.style.SUCCESS('Usage counts updated successfully!')
        ) 