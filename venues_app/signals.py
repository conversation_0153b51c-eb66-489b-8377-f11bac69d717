# --- Django Imports ---
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

# --- Local App Imports ---
from notifications_app.utils import run_async
from .models import FlaggedVenue, Venue, VenueImage, VenueCategory, VenueAmenity

# Store original venue state for comparison
_venue_original_state = {}


@receiver(pre_save, sender=Venue)
def store_venue_original_state(sender, instance, **kwargs):
    """Store original venue state before save for comprehensive comparison."""
    if instance.pk:
        try:
            original = Venue.objects.get(pk=instance.pk)
            
            # Store comprehensive original state
            original_state = {
                'approval_status': original.approval_status,
                'approved_at': original.approved_at,
                'rejected_at': getattr(original, 'rejected_at', None),
                
                # Basic fields
                'venue_name': original.venue_name,
                'short_description': original.short_description,
                'phone': original.phone,
                'email': original.email,
                'website_url': original.website_url,
                
                # Location fields
                'street_number': original.street_number,
                'street_name': original.street_name,
                'city': original.city,
                'county': original.county,
                'state': original.state,
                'zip_code': getattr(original, 'zip_code', ''),
                
                # Social media
                'instagram_url': getattr(original, 'instagram_url', ''),
                'facebook_url': getattr(original, 'facebook_url', ''),
                'twitter_url': getattr(original, 'twitter_url', ''),
                'linkedin_url': getattr(original, 'linkedin_url', ''),
                
                # Other fields
                'venue_type': getattr(original, 'venue_type', ''),
                'tags': original.tags,
                'opening_notes': getattr(original, 'opening_notes', ''),
                'latitude': original.latitude,
                'longitude': original.longitude,
                'visibility': original.visibility,
                
                # Related data
                'categories': list(original.categories.values_list('id', flat=True)),
                'amenities': list(original.amenities.filter(is_active=True).values_list('amenity_type', flat=True))
            }
            
            _venue_original_state[instance.pk] = original_state
        except Venue.DoesNotExist:
            pass


@receiver(post_save, sender=Venue)
def venue_status_changed(sender, instance, created, **kwargs):
    """Handle venue status changes and send enhanced notifications."""
    try:
        from notifications_app.utils import (
            notify_venue_submitted_for_approval,
            notify_venue_approved,
            notify_venue_rejected,
            notify_venue_changes_detected  # New notification function
        )

        # Pre-load the relationship to avoid issues in async context
        try:
            provider_user = instance.service_provider.user
            if not provider_user:
                return
        except Exception:
            return

        if created:
            # New venue created - notify provider that it was submitted for approval
            if instance.approval_status == 'pending':
                run_async(notify_venue_submitted_for_approval, instance)
        else:
            # Existing venue updated - check for status changes and modifications
            original_state = _venue_original_state.get(instance.pk, {})

            # Check if status changed to approved
            if (instance.approval_status == 'approved' and
                original_state.get('approval_status') != 'approved'):
                run_async(notify_venue_approved, instance)

            # Check if status changed to rejected
            elif (instance.approval_status == 'rejected' and
                  original_state.get('approval_status') != 'rejected'):
                rejection_reason = getattr(instance, 'admin_notes', 'No specific reason provided')
                run_async(notify_venue_rejected, instance, rejection_reason)
            
            # Check for significant changes that triggered re-approval
            elif (instance.approval_status == 'pending' and
                  original_state.get('approval_status') == 'approved'):
                
                # Analyze what changed
                from .utils import analyze_venue_changes, get_change_comparison_data
                
                # Get changed fields
                changed_fields = []
                for field in ['venue_name', 'short_description', 'phone', 'email', 'website_url',
                             'street_number', 'street_name', 'city', 'county', 'state', 'zip_code',
                             'instagram_url', 'facebook_url', 'twitter_url', 'linkedin_url',
                             'venue_type', 'tags', 'opening_notes', 'visibility']:
                    old_value = original_state.get(field, '')
                    new_value = getattr(instance, field, '')
                    if old_value != new_value:
                        changed_fields.append(field)
                
                if changed_fields or original_state.get('categories') != list(instance.categories.values_list('id', flat=True)):
                    # Get detailed change analysis
                    change_analysis = analyze_venue_changes(instance, changed_fields, original_state)
                    comparison_data = get_change_comparison_data(instance, original_state)
                    
                    # Notify about specific changes
                    run_async(notify_venue_changes_detected, instance, change_analysis, comparison_data)

            # Clean up stored state
            if instance.pk in _venue_original_state:
                del _venue_original_state[instance.pk]

    except ImportError:
        # notifications_app not available yet - skip notifications
        pass


@receiver(post_save, sender=FlaggedVenue)
def venue_flagged(sender, instance, created, **kwargs):
    """Handle venue flagging and send notifications."""
    try:
        from notifications_app.utils import notify_venue_flagged, notify_venue_flag_reviewed

        # Pre-load the relationship to avoid issues in async context
        try:
            # Force evaluation of the relationship in the main thread
            provider_user = instance.venue.service_provider.user
            if not provider_user:
                return  # Skip if no user found
        except Exception:
            # Skip notification if we can't access the provider user
            return

        if created:
            # New flag created - notify admins
            run_async(notify_venue_flagged, instance)
        else:
            # Existing flag updated - check if it was reviewed
            if instance.status in ['reviewed', 'resolved'] and instance.reviewed_at:
                # Check if this is a new review (reviewed_at was just set)
                old_instance = FlaggedVenue.objects.filter(pk=instance.pk).first()
                if old_instance and old_instance.reviewed_at != instance.reviewed_at:
                    run_async(notify_venue_flag_reviewed, instance)
    except ImportError:
        # notifications_app not available yet - skip notifications
        pass


@receiver(pre_save, sender=Venue)
def venue_pre_save_enhanced(sender, instance, **kwargs):
    """Enhanced venue pre-save operations with improved change detection."""
    # Set approved_at timestamp when status changes to approved
    if instance.approval_status == 'approved' and not instance.approved_at:
        instance.approved_at = timezone.now()
    elif instance.approval_status != 'approved':
        instance.approved_at = None

    # Enhanced change detection and approval status reset
    if instance.pk:  # Only for existing venues
        try:
            original = Venue.objects.get(pk=instance.pk)
            original_state = _venue_original_state.get(instance.pk, {})
            
            # Get changed fields
            changed_fields = []
            field_mapping = {
                'venue_name': 'venue_name',
                'short_description': 'short_description', 
                'phone': 'phone',
                'email': 'email',
                'website_url': 'website_url',
                'street_number': 'street_number',
                'street_name': 'street_name',
                'city': 'city',
                'county': 'county',
                'state': 'state',
                'zip_code': 'zip_code',
                'instagram_url': 'instagram_url',
                'facebook_url': 'facebook_url',
                'twitter_url': 'twitter_url',
                'linkedin_url': 'linkedin_url',
                'venue_type': 'venue_type',
                'tags': 'tags',
                'opening_notes': 'opening_notes',
                'visibility': 'visibility'
            }
            
            for field, original_field in field_mapping.items():
                old_value = getattr(original, field, '')
                new_value = getattr(instance, field, '')
                if old_value != new_value:
                    changed_fields.append(field)
            
            # Use enhanced change analysis
            if changed_fields and original.approval_status == 'approved':
                from .utils import requires_reapproval, analyze_venue_changes
                
                if requires_reapproval(instance, changed_fields, original_state):
                    # Get change analysis for detailed logging
                    change_analysis = analyze_venue_changes(instance, changed_fields, original_state)
                    
                    instance.approval_status = 'pending'
                    instance.approved_at = None

                    # Enhanced status log with change details
                    if not hasattr(instance, '_skip_status_log'):
                        severity_summary = change_analysis.get('severity_breakdown', {})
                        change_detail = f"Changes detected - Critical: {severity_summary.get('critical', 0)}, Major: {severity_summary.get('major', 0)}, Moderate: {severity_summary.get('moderate', 0)}, Minor: {severity_summary.get('minor', 0)}"
                        
                        instance.status_log.append({
                            'status': 'pending',
                            'timestamp': timezone.now().isoformat(),
                            'reason': f'Re-approval required due to significant changes',
                            'change_details': change_detail,
                            'changed_fields': changed_fields,
                            'approval_recommendation': change_analysis.get('approval_recommendation'),
                            'by': 'system'
                        })

        except Venue.DoesNotExist:
            pass


@receiver(pre_save, sender=FlaggedVenue)
def flagged_venue_pre_save(sender, instance, **kwargs):
    """Handle flagged venue pre-save operations."""
    # Set reviewed_at timestamp when status changes to reviewed or resolved
    if instance.status in ['reviewed', 'resolved'] and not instance.reviewed_at:
        instance.reviewed_at = timezone.now()
    elif instance.status == 'pending':
        instance.reviewed_at = None


@receiver(post_delete, sender=VenueImage)
def delete_venue_image_files(sender, instance, **kwargs):
    """Remove image files when a VenueImage is deleted."""
    try:
        from utils.image_service import ImageService
        if instance.image:
            ImageService.delete_image(instance.image.name)
    except Exception:
        pass


@receiver(post_delete, sender=Venue)
def delete_venue_main_image(sender, instance, **kwargs):
    """Remove main image file when a Venue is deleted."""
    try:
        from utils.image_service import ImageService
        if instance.main_image:
            ImageService.delete_image(instance.main_image.name)
    except Exception:
        pass


# Enhanced category change tracking
@receiver(post_save, sender=VenueCategory)
@receiver(post_delete, sender=VenueCategory)
def venue_category_changed(sender, instance, **kwargs):
    """Track venue category changes for re-approval consideration."""
    venue = instance.venue
    if venue.approval_status == 'approved':
        # Category changes are considered major changes
        venue._category_change_pending = True
        venue.save()  # This will trigger the venue pre_save signal


# Enhanced amenity change tracking  
@receiver(post_save, sender=VenueAmenity)
@receiver(post_delete, sender=VenueAmenity) 
def venue_amenity_changed(sender, instance, **kwargs):
    """Track venue amenity changes for re-approval consideration."""
    venue = instance.venue
    if venue.approval_status == 'approved':
        # Amenity changes are considered moderate changes
        venue._amenity_change_pending = True
        venue.save()  # This will trigger the venue pre_save signal