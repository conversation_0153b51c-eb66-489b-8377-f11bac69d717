"""
Migration to enhance USCity model with better indexes and constraints for improved performance.
"""
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0007_make_venue_image_order_constraint_deferrable'),
    ]

    operations = [
        # Add search_vector field to USCity
        migrations.AddField(
            model_name='uscity',
            name='search_vector',
            field=models.TextField(
                blank=True,
                db_index=True,
                help_text='Precomputed search text for faster queries',
                verbose_name='search vector'
            ),
        ),
        
        # Make latitude and longitude nullable
        migrations.AlterField(
            model_name='uscity',
            name='latitude',
            field=models.DecimalField(
                blank=True,
                decimal_places=7,
                help_text='Latitude coordinate',
                max_digits=10,
                null=True,
                verbose_name='latitude'
            ),
        ),
        migrations.AlterField(
            model_name='uscity',
            name='longitude',
            field=models.DecimalField(
                blank=True,
                decimal_places=7,
                help_text='Longitude coordinate',
                max_digits=10,
                null=True,
                verbose_name='longitude'
            ),
        ),
        
        # Make zip_codes field blank
        migrations.AlterField(
            model_name='uscity',
            name='zip_codes',
            field=models.TextField(
                blank=True,
                help_text='Space-separated list of ZIP codes',
                verbose_name='ZIP codes'
            ),
        ),
        
        # Add individual indexes for better query performance
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS venues_app_uscity_city_idx ON venues_app_uscity (city);",
            reverse_sql="DROP INDEX IF EXISTS venues_app_uscity_city_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS venues_app_uscity_state_id_idx ON venues_app_uscity (state_id);",
            reverse_sql="DROP INDEX IF EXISTS venues_app_uscity_state_id_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS venues_app_uscity_state_name_idx ON venues_app_uscity (state_name);",
            reverse_sql="DROP INDEX IF EXISTS venues_app_uscity_state_name_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS venues_app_uscity_county_name_idx ON venues_app_uscity (county_name);",
            reverse_sql="DROP INDEX IF EXISTS venues_app_uscity_county_name_idx;"
        ),
        
        # Add composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_state_county_idx ON venues_app_uscity (state_name, county_name);",
            reverse_sql="DROP INDEX IF EXISTS uscity_state_county_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_state_city_idx ON venues_app_uscity (state_name, city);",
            reverse_sql="DROP INDEX IF EXISTS uscity_state_city_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_full_location_idx ON venues_app_uscity (state_name, county_name, city);",
            reverse_sql="DROP INDEX IF EXISTS uscity_full_location_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_state_abbrev_county_idx ON venues_app_uscity (state_id, county_name);",
            reverse_sql="DROP INDEX IF EXISTS uscity_state_abbrev_county_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_state_abbrev_city_idx ON venues_app_uscity (state_id, city);",
            reverse_sql="DROP INDEX IF EXISTS uscity_state_abbrev_city_idx;"
        ),
        
        # Add text search indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_city_state_search_idx ON venues_app_uscity (city, state_id);",
            reverse_sql="DROP INDEX IF EXISTS uscity_city_state_search_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_county_state_search_idx ON venues_app_uscity (county_name, state_id);",
            reverse_sql="DROP INDEX IF EXISTS uscity_county_state_search_idx;"
        ),
        
        # Add coordinate indexes for location-based queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS uscity_coordinates_idx ON venues_app_uscity (latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS uscity_coordinates_idx;"
        ),
        
        # Add unique constraint for city-county-state combination
        migrations.RunSQL(
            "CREATE UNIQUE INDEX IF NOT EXISTS unique_city_county_state ON venues_app_uscity (city, county_name, state_name);",
            reverse_sql="DROP INDEX IF EXISTS unique_city_county_state;"
        ),
        
        # Populate search_vector field for existing records
        migrations.RunSQL(
            """
            UPDATE venues_app_uscity 
            SET search_vector = LOWER(CONCAT(city, ' ', state_name, ' ', state_id, ' ', county_name))
            WHERE search_vector = '' OR search_vector IS NULL;
            """,
            reverse_sql="UPDATE venues_app_uscity SET search_vector = '';"
        ),
    ] 