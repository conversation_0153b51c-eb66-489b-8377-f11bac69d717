# Generated by Django 5.2.3 on 2025-06-29 18:43

import venues_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0011_enhanced_venue_constraints'),
    ]

    operations = [
        migrations.AddField(
            model_name='venueimage',
            name='file_size',
            field=models.PositiveIntegerField(blank=True, help_text='Image file size in bytes', null=True, verbose_name='file size'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='format',
            field=models.CharField(blank=True, help_text='Image file format (JPEG, PNG, WebP)', max_length=10, verbose_name='image format'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='height',
            field=models.PositiveIntegerField(blank=True, help_text='Image height in pixels', null=True, verbose_name='image height'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='original_filename',
            field=models.CharField(blank=True, help_text='Original filename when uploaded', max_length=255, verbose_name='original filename'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='upload_session_id',
            field=models.CharField(blank=True, help_text='Session ID for tracking upload batches', max_length=100, verbose_name='upload session ID'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='width',
            field=models.PositiveIntegerField(blank=True, help_text='Image width in pixels', null=True, verbose_name='image width'),
        ),
        migrations.AlterField(
            model_name='venue',
            name='main_image',
            field=models.ImageField(blank=True, help_text='Main featured image for the venue (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.', null=True, upload_to=venues_app.models.get_venue_main_image_path, verbose_name='main image'),
        ),
        migrations.AlterField(
            model_name='venueimage',
            name='image',
            field=models.ImageField(help_text='Venue image file (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.', upload_to=venues_app.models.get_venue_gallery_image_path, verbose_name='image'),
        ),
    ]
