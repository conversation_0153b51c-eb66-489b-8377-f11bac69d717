# Generated by Django 5.2.3 on 2025-06-30 04:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0016_fix_service_category_references'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Tag name (e.g., relaxing, deep-tissue, pain-relief)', max_length=50, unique=True, verbose_name='tag name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=60, null=True, unique=True, verbose_name='slug')),
                ('tag_type', models.CharField(choices=[('keyword', 'Keyword'), ('technique', 'Technique'), ('benefit', 'Benefit'), ('body_part', 'Body Part'), ('duration', 'Duration'), ('intensity', 'Intensity')], default='keyword', help_text='Type of tag for better organization', max_length=20, verbose_name='tag type')),
                ('description', models.TextField(blank=True, help_text='Optional description of what this tag represents', max_length=200, verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tag is available for use', verbose_name='active status')),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='Number of times this tag has been used (auto-updated)', verbose_name='usage count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Service Tag',
                'verbose_name_plural': 'Service Tags',
                'ordering': ['tag_type', 'name'],
            },
        ),
        migrations.RemoveIndex(
            model_name='servicecategory',
            name='venues_app__name_c1896a_idx',
        ),
        migrations.AddIndex(
            model_name='servicetag',
            index=models.Index(fields=['is_active'], name='venues_app__is_acti_a997f4_idx'),
        ),
        migrations.AddIndex(
            model_name='servicetag',
            index=models.Index(fields=['tag_type'], name='venues_app__tag_typ_23f395_idx'),
        ),
        migrations.AddIndex(
            model_name='servicetag',
            index=models.Index(fields=['usage_count'], name='venues_app__usage_c_5b3832_idx'),
        ),
        migrations.AddField(
            model_name='service',
            name='tags',
            field=models.ManyToManyField(blank=True, help_text='Tags for better searchability (e.g., relaxing, deep-tissue, pain-relief)', related_name='services', to='venues_app.servicetag', verbose_name='service tags'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['venue', 'is_active'], name='venues_app__venue_i_896798_idx'),
        ),
    ]
