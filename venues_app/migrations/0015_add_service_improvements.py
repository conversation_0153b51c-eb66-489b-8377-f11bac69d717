# Generated by Django 5.2.3 on 2025-06-29 20:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0014_add_service_categorization_and_enhancements'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='custom_slug',
            field=models.SlugField(blank=True, help_text='Custom URL slug (optional). Leave blank to auto-generate from title.', max_length=255, verbose_name='custom slug'),
        ),
        migrations.AlterField(
            model_name='service',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated or custom)', max_length=255, null=True, verbose_name='slug'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['venue', 'slug'], name='venues_app__venue_i_bf62a6_idx'),
        ),
        migrations.AddConstraint(
            model_name='service',
            constraint=models.UniqueConstraint(condition=models.Q(('slug__isnull', False)), fields=('venue', 'slug'), name='unique_service_slug_per_venue'),
        ),
    ]
