# Generated by Django 5.2.3 on 2025-06-29 17:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0009_merge_20250629_1705'),
    ]

    operations = [
        migrations.CreateModel(
            name='HolidaySchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the holiday or special event', max_length=100, verbose_name='holiday name')),
                ('date', models.DateField(help_text='Date of the special schedule', verbose_name='date')),
                ('opening', models.TimeField(blank=True, help_text='Opening time for this special day', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Closing time for this special day', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the venue is closed on this special day', verbose_name='closed')),
                ('is_24_hours', models.BooleanField(default=False, help_text='Whether the venue is open 24 hours on this special day', verbose_name='24 hours')),
                ('is_overnight', models.BooleanField(default=False, help_text='Whether closing time is next day', verbose_name='overnight hours')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this special schedule', verbose_name='notes')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this special schedule is active', verbose_name='active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Holiday Schedule',
                'verbose_name_plural': 'Holiday Schedules',
                'ordering': ['date'],
            },
        ),
        migrations.RemoveIndex(
            model_name='uscity',
            name='venues_app__city_f629c9_idx',
        ),
        migrations.RemoveIndex(
            model_name='uscity',
            name='venues_app__state_n_569810_idx',
        ),
        migrations.RemoveIndex(
            model_name='uscity',
            name='venues_app__county__cc5321_idx',
        ),
        migrations.RemoveIndex(
            model_name='uscity',
            name='venues_app__state_i_cc2426_idx',
        ),
        migrations.AddField(
            model_name='operatinghours',
            name='is_24_hours',
            field=models.BooleanField(default=False, help_text='Whether the venue is open 24 hours on this day', verbose_name='24 hours'),
        ),
        migrations.AddField(
            model_name='operatinghours',
            name='is_overnight',
            field=models.BooleanField(default=False, help_text='Whether closing time is next day (e.g., open until 2 AM)', verbose_name='overnight hours'),
        ),
        migrations.AlterField(
            model_name='uscity',
            name='city',
            field=models.CharField(db_index=True, help_text='City name', max_length=100, verbose_name='city'),
        ),
        migrations.AlterField(
            model_name='uscity',
            name='county_name',
            field=models.CharField(db_index=True, help_text='County name', max_length=100, verbose_name='county name'),
        ),
        migrations.AlterField(
            model_name='uscity',
            name='state_id',
            field=models.CharField(db_index=True, help_text='State abbreviation (e.g., NY, CA)', max_length=2, verbose_name='state abbreviation'),
        ),
        migrations.AlterField(
            model_name='uscity',
            name='state_name',
            field=models.CharField(db_index=True, help_text='Full state name', max_length=100, verbose_name='state name'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['state_name', 'county_name'], name='uscity_state_county_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['state_name', 'city'], name='uscity_state_city_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['state_name', 'county_name', 'city'], name='uscity_full_location_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['state_id', 'county_name'], name='uscity_state_abbrev_county_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['state_id', 'city'], name='uscity_state_abbrev_city_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['city', 'state_id'], name='uscity_city_state_search_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['county_name', 'state_id'], name='uscity_county_state_search_idx'),
        ),
        migrations.AddIndex(
            model_name='uscity',
            index=models.Index(fields=['latitude', 'longitude'], name='uscity_coordinates_idx'),
        ),
        migrations.AddConstraint(
            model_name='uscity',
            constraint=models.UniqueConstraint(fields=('city', 'county_name', 'state_name'), name='unique_city_county_state'),
        ),
        migrations.AddField(
            model_name='holidayschedule',
            name='venue',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holiday_schedules', to='venues_app.venue', verbose_name='venue'),
        ),
        migrations.AlterUniqueTogether(
            name='holidayschedule',
            unique_together={('venue', 'date')},
        ),
    ]
