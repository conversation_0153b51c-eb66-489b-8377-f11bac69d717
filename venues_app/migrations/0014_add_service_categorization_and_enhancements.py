# Generated by Django 5.2.3 on 2025-06-29 20:14

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0013_add_social_media_and_verification_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceOperatingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], help_text='Day of the week (0=Monday, 6=Sunday)', verbose_name='day of week')),
                ('opening', models.TimeField(blank=True, help_text='Service available from this time', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Service available until this time', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the service is unavailable on this day', verbose_name='closed')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Service Operating Hours',
                'verbose_name_plural': 'Service Operating Hours',
                'ordering': ['day'],
            },
        ),
        migrations.AddField(
            model_name='service',
            name='has_custom_availability',
            field=models.BooleanField(default=False, help_text='Whether this service has different availability from venue hours', verbose_name='custom availability'),
        ),
        migrations.AddField(
            model_name='service',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Whether this service should be highlighted', verbose_name='featured service'),
        ),
        migrations.AddField(
            model_name='service',
            name='max_advance_booking_days',
            field=models.PositiveIntegerField(default=60, help_text='Maximum days in advance customers can book', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)], verbose_name='max advance booking (days)'),
        ),
        migrations.AddField(
            model_name='service',
            name='min_advance_booking_hours',
            field=models.PositiveIntegerField(default=2, help_text='Minimum hours in advance required for booking', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='min advance booking (hours)'),
        ),
        migrations.AddField(
            model_name='service',
            name='requires_booking',
            field=models.BooleanField(default=True, help_text='Whether this service requires advance booking', verbose_name='requires booking'),
        ),
        migrations.AddField(
            model_name='service',
            name='service_image',
            field=models.ImageField(blank=True, help_text='Optional image for this service (JPEG, PNG, WebP - max 2MB)', null=True, upload_to='services/images/', verbose_name='service image'),
        ),
        migrations.AddField(
            model_name='venue',
            name='amenities_updated_at',
            field=models.DateTimeField(blank=True, help_text='When amenities were last updated', null=True, verbose_name='amenities updated at'),
        ),
        migrations.AddField(
            model_name='venue',
            name='completeness_score',
            field=models.PositiveIntegerField(default=0, help_text='Calculated completeness score (0-100)', verbose_name='completeness score'),
        ),
        migrations.AddField(
            model_name='venue',
            name='contact_updated_at',
            field=models.DateTimeField(blank=True, help_text='When contact information was last updated', null=True, verbose_name='contact updated at'),
        ),
        migrations.AddField(
            model_name='venue',
            name='description_updated_at',
            field=models.DateTimeField(blank=True, help_text='When venue description was last updated', null=True, verbose_name='description updated at'),
        ),
        migrations.AddField(
            model_name='venue',
            name='hours_updated_at',
            field=models.DateTimeField(blank=True, help_text='When operating hours were last updated', null=True, verbose_name='hours updated at'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_amenities',
            field=models.BooleanField(default=True, help_text='Show venue amenities and features', verbose_name='show amenities'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_contact_info',
            field=models.BooleanField(default=True, help_text='Show phone, email, and website to customers', verbose_name='show contact information'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_faqs',
            field=models.BooleanField(default=True, help_text='Show frequently asked questions', verbose_name='show FAQs'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_operating_hours',
            field=models.BooleanField(default=True, help_text='Show opening hours to customers', verbose_name='show operating hours'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_social_media',
            field=models.BooleanField(default=True, help_text='Show social media links', verbose_name='show social media'),
        ),
        migrations.AddField(
            model_name='venue',
            name='show_team_members',
            field=models.BooleanField(default=True, help_text='Show service provider team members', verbose_name='show team members'),
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Service category name (e.g., Massage, Facial, Hair)', max_length=100, unique=True, verbose_name='category name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=120, null=True, unique=True, verbose_name='slug')),
                ('description', models.TextField(blank=True, help_text='Optional description of the service category', verbose_name='description')),
                ('icon_class', models.CharField(blank=True, help_text='CSS icon class for display (e.g., fas fa-spa)', max_length=50, verbose_name='icon class')),
                ('color_code', models.CharField(blank=True, help_text='Hex color code for category display (e.g., #FF5722)', max_length=7, verbose_name='color code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active and visible', verbose_name='active status')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Display order for category listings', verbose_name='sort order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Service Category',
                'verbose_name_plural': 'Service Categories',
                'ordering': ['sort_order', 'name'],
                'indexes': [models.Index(fields=['name'], name='venues_app__name_c1896a_idx'), models.Index(fields=['is_active'], name='venues_app__is_acti_0cbaef_idx'), models.Index(fields=['sort_order'], name='venues_app__sort_or_b662c2_idx')],
            },
        ),
        migrations.AddField(
            model_name='service',
            name='service_category',
            field=models.ForeignKey(blank=True, help_text='Category that best describes this service', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='venues_app.servicecategory', verbose_name='service category'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['service_category'], name='venues_app__service_4307fe_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['is_featured'], name='venues_app__is_feat_eb3022_idx'),
        ),
        migrations.AddField(
            model_name='serviceoperatinghours',
            name='service',
            field=models.ForeignKey(help_text='Service these hours apply to', on_delete=django.db.models.deletion.CASCADE, related_name='service_hours', to='venues_app.service', verbose_name='service'),
        ),
        migrations.AlterUniqueTogether(
            name='serviceoperatinghours',
            unique_together={('service', 'day')},
        ),
    ]
