# Generated by Django 5.2.3 on 2025-07-01 16:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0003_serviceproviderprofile_venue_creation_tutorial_completed'),
        ('venues_app', '0017_add_service_tags_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='VenueCreationDraft',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('venue_name', models.CharField(blank=True, help_text='Name of the venue/business', max_length=255, null=True, verbose_name='venue name')),
                ('short_description', models.TextField(blank=True, help_text='Brief description of the venue', max_length=500, null=True, verbose_name='description')),
                ('state', models.CharField(blank=True, choices=[('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'), ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'), ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'), ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'), ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'), ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'), ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'), ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'), ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'), ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'), ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'), ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'), ('WI', 'Wisconsin'), ('WY', 'Wyoming')], help_text='State where venue is located', max_length=2, null=True, verbose_name='state')),
                ('county', models.CharField(blank=True, help_text='County where venue is located', max_length=100, null=True, verbose_name='county')),
                ('city', models.CharField(blank=True, help_text='City where venue is located', max_length=100, null=True, verbose_name='city')),
                ('street_number', models.CharField(blank=True, help_text='Street number', max_length=20, null=True, verbose_name='street number')),
                ('street_name', models.CharField(blank=True, help_text='Street name', max_length=255, null=True, verbose_name='street name')),
                ('phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20, null=True, verbose_name='phone')),
                ('email', models.EmailField(blank=True, help_text='Contact email address', max_length=254, null=True, verbose_name='email')),
                ('website_url', models.URLField(blank=True, help_text='Website URL', null=True, verbose_name='website URL')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL', null=True, verbose_name='Instagram URL')),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook page URL', null=True, verbose_name='Facebook URL')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL', null=True, verbose_name='Twitter URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL', null=True, verbose_name='LinkedIn URL')),
                ('categories_data', models.JSONField(blank=True, default=list, help_text='Selected category IDs stored as JSON', verbose_name='categories data')),
                ('current_step', models.CharField(default='basic', help_text='Current wizard step', max_length=20, verbose_name='current step')),
                ('completed_steps', models.JSONField(blank=True, default=list, help_text='List of completed wizard steps', verbose_name='completed steps')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the draft was first created', verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the draft was last updated', verbose_name='updated at')),
                ('service_provider', models.OneToOneField(help_text='Service provider creating the venue', on_delete=django.db.models.deletion.CASCADE, related_name='venue_creation_draft', to='accounts_app.serviceproviderprofile', verbose_name='service provider')),
            ],
            options={
                'verbose_name': 'venue creation draft',
                'verbose_name_plural': 'venue creation drafts',
                'db_table': 'venues_venue_creation_draft',
            },
        ),
    ]
