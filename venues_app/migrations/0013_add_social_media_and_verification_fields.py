# Generated by Django 5.2.3 on 2025-06-29 19:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0012_venueimage_file_size_venueimage_format_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='venue',
            name='email_verification_token',
            field=models.CharField(blank=True, help_text='Token for email verification', max_length=64, verbose_name='email verification token'),
        ),
        migrations.AddField(
            model_name='venue',
            name='email_verified',
            field=models.BooleanField(default=False, help_text='Whether the venue email address has been verified', verbose_name='email verified'),
        ),
        migrations.AddField(
            model_name='venue',
            name='facebook_url',
            field=models.URLField(blank=True, help_text='Facebook page URL for the venue', verbose_name='Facebook URL'),
        ),
        migrations.AddField(
            model_name='venue',
            name='instagram_url',
            field=models.URLField(blank=True, help_text='Instagram profile URL for the venue', verbose_name='Instagram URL'),
        ),
        migrations.AddField(
            model_name='venue',
            name='linkedin_url',
            field=models.URLField(blank=True, help_text='LinkedIn business page URL for the venue', verbose_name='LinkedIn URL'),
        ),
        migrations.AddField(
            model_name='venue',
            name='twitter_url',
            field=models.URLField(blank=True, help_text='Twitter profile URL for the venue', verbose_name='Twitter URL'),
        ),
    ]
