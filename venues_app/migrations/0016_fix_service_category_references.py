# Generated by Django 5.2.3 on 2025-06-29 20:45

from django.db import migrations


def fix_service_category_references(apps, schema_editor):
    """Fix invalid service category references by setting them to NULL."""
    Service = apps.get_model('venues_app', 'Service')
    ServiceCategory = apps.get_model('venues_app', 'ServiceCategory')
    
    # Get all services
    services = Service.objects.all()
    
    for service in services:
        # Check if service has a valid category reference
        if service.service_category_id:
            try:
                # Try to get the category
                ServiceCategory.objects.get(id=service.service_category_id)
            except ServiceCategory.DoesNotExist:
                # If category doesn't exist, set to NULL
                service.service_category_id = None
                service.save(update_fields=['service_category_id'])


def reverse_fix_service_category_references(apps, schema_editor):
    """This migration cannot be reversed as it cleans up invalid data."""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0015_add_service_improvements'),
    ]

    operations = [
        migrations.RunPython(
            fix_service_category_references,
            reverse_fix_service_category_references,
        ),
    ]
