# Generated by Django 5.2.3 on 2025-06-29 17:45

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0003_serviceproviderprofile_venue_creation_tutorial_completed'),
        ('venues_app', '0010_holidayschedule_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='venue',
            name='venues_app__venue_n_e2ef62_idx',
        ),
        migrations.RemoveIndex(
            model_name='venue',
            name='venues_app__approva_4dd942_idx',
        ),
        migrations.RemoveIndex(
            model_name='venue',
            name='venues_app__visibil_24d4b7_idx',
        ),
        migrations.RemoveIndex(
            model_name='venue',
            name='venues_app__service_3877cd_idx',
        ),
        migrations.RemoveIndex(
            model_name='venue',
            name='venues_app__is_dele_126c6c_idx',
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['latitude', 'longitude'], name='venues_app__latitud_34f5b0_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['created_at'], name='venues_app__created_9391a8_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['tags'], name='venues_app__tags_4ba727_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['service_provider', 'is_deleted'], name='venues_app__service_78e600_idx'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('service_provider',), name='unique_venue_per_service_provider'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('venue_name', 'city', 'state'), name='unique_venue_name_per_location'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('approval_status__in', ['draft', 'pending', 'rejected']), ('approved_at__isnull', False), _connector='OR'), name='approved_venues_have_timestamp'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('approval_status__in', ['draft', 'pending', 'approved']), ('rejected_at__isnull', False), _connector='OR'), name='rejected_venues_have_timestamp'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('latitude__isnull', True), models.Q(('latitude__gte', -90), ('latitude__lte', 90)), _connector='OR'), name='valid_latitude_range'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('longitude__isnull', True), models.Q(('longitude__gte', -180), ('longitude__lte', 180)), _connector='OR'), name='valid_longitude_range'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('venue_name', ''), _negated=True), name='venue_name_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('short_description', ''), _negated=True), name='venue_description_not_empty'),
        ),
    ]
