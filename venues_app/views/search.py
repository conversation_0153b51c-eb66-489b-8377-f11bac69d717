"""Views for venue search and detail pages."""

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Max, Min, Q, Prefetch, Avg, Count
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone

# --- Local App Imports ---
from ..forms import FlaggedVenueForm, VenueFilterForm, VenueSearchForm
from ..models import Category, FlaggedVenue, Service, ServiceCategory, USCity, Venue, ServiceTag


def venue_search(request):
    """Enhanced search and filter venues with service category filtering."""
    venues = Venue.objects.filter(
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
    ).exclude(
        approval_status=Venue.DRAFT
    ).select_related('service_provider').prefetch_related(
        'categories',
        Prefetch('services', queryset=Service.objects.filter(is_active=True).select_related('service_category').order_by('service_title')),
        'images'
    )

    venues = venues.annotate(
        min_service_price=Min('services__price_min'),
        max_service_price=Max('services__price_max'),
        avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True)),
        review_count=Count('reviews', filter=Q(reviews__is_approved=True)),
    )

    search_form = VenueSearchForm(request.GET or None)
    filter_form = VenueFilterForm(request.GET or None)

    applied_filters = {}

    if search_form.is_valid():
        query = search_form.cleaned_data.get('query')
        location = search_form.cleaned_data.get('location')
        category = search_form.cleaned_data.get('category')

        if query:
            venues = venues.filter(
                Q(venue_name__icontains=query)
                | Q(short_description__icontains=query)
                | Q(tags__icontains=query)
                | Q(services__service_title__icontains=query)
                | Q(services__short_description__icontains=query)
                | Q(services__service_category__name__icontains=query)  # Search by service category
                | Q(services__tags__name__icontains=query)  # Search by service tags
                | Q(categories__category_name__icontains=query)
                | Q(service_provider__legal_name__icontains=query)
                | Q(service_provider__display_name__icontains=query)
            ).distinct()
            applied_filters['query'] = query

        if location:
            city_matches = USCity.objects.filter(
                Q(city__icontains=location)
                | Q(state_name__icontains=location)
                | Q(county_name__icontains=location)
                | Q(state_id__iexact=location)
            )
            if city_matches.exists():
                venues = venues.filter(
                    Q(us_city__in=city_matches)
                    | Q(city__icontains=location)
                    | Q(state__icontains=location)
                    | Q(county__icontains=location)
                ).distinct()
            else:
                venues = venues.filter(
                    Q(city__icontains=location)
                    | Q(state__icontains=location)
                    | Q(county__icontains=location)
                )
            applied_filters['location'] = location

        if category:
            venues = venues.filter(categories=category)
            applied_filters['category'] = category.category_name

    if filter_form.is_valid():
        sort_by = filter_form.cleaned_data.get('sort_by')
        venue_type = filter_form.cleaned_data.get('venue_type')
        has_discount = filter_form.cleaned_data.get('has_discount')

        state = filter_form.cleaned_data.get('state')
        county = filter_form.cleaned_data.get('county')
        city = filter_form.cleaned_data.get('city')
        categories = filter_form.cleaned_data.get('categories')
        
        # NEW: Service category filtering
        service_categories = filter_form.cleaned_data.get('service_categories')

        if venue_type:
            venues = venues.filter(venue_type=venue_type)
            applied_filters['venue_type'] = dict(filter_form.VENUE_TYPE_CHOICES).get(venue_type)

        if has_discount:
            # Filter venues that have active discounts (either venue-level or service-level)
            now = timezone.now()
            
            venues = venues.filter(
                Q(
                    # Venue-level discounts
                    venue_discounts__start_date__lte=now,
                    venue_discounts__end_date__gte=now,
                    venue_discounts__is_approved=True
                ) | Q(
                    # Service-level discounts
                    services__service_discounts__start_date__lte=now,
                    services__service_discounts__end_date__gte=now,
                    services__service_discounts__is_approved=True
                )
            ).distinct()
            applied_filters['has_discount'] = 'Has Discounts'

        if state:
            venues = venues.filter(Q(state__iexact=state) | Q(us_city__state_name__iexact=state))
            applied_filters['state'] = state

        if county:
            venues = venues.filter(Q(county__iexact=county) | Q(us_city__county_name__iexact=county))
            applied_filters['county'] = county

        if city:
            venues = venues.filter(Q(city__iexact=city) | Q(us_city__city__iexact=city))
            applied_filters['city'] = city

        if categories:
            venues = venues.filter(categories__in=categories).distinct()
            applied_filters['categories'] = [cat.name for cat in categories]
        
        # NEW: Filter by service categories
        if service_categories:
            venues = venues.filter(services__service_category__in=service_categories).distinct()
            applied_filters['service_categories'] = [cat.name for cat in service_categories]

        # NEW: Filter by service tags
        service_tags = filter_form.cleaned_data.get('service_tags')
        if service_tags:
            venues = venues.filter(services__tags__in=service_tags).distinct()
            applied_filters['service_tags'] = [tag.name for tag in service_tags]

        if sort_by:
            if sort_by == 'rating_high':
                venues = venues.order_by('-created_at')
            elif sort_by == 'rating_low':
                venues = venues.order_by('created_at')
            elif sort_by == 'price_high':
                venues = venues.order_by('-min_service_price')
            elif sort_by == 'price_low':
                venues = venues.order_by('min_service_price')
            elif sort_by == 'newest':
                venues = venues.order_by('-created_at')
            elif sort_by == 'discount':
                venues = venues.order_by('-min_service_price')
            elif sort_by == 'name':
                venues = venues.order_by('venue_name')
        else:
            venues = venues.order_by('-created_at', 'venue_name')

    venues = venues.distinct()
    total_venues = venues.count()

    paginator = Paginator(venues, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    categories = Category.objects.filter(is_active=True).order_by('category_name')
    service_categories = ServiceCategory.objects.filter(is_active=True).order_by('sort_order', 'name')  # NEW
    service_tags = ServiceTag.objects.filter(is_active=True).order_by('tag_type', 'name')  # NEW
    states = USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')

    price_range = Service.objects.aggregate(
        min_price=Min('price_min'),
        max_price=Max('price_max'),
    )

    context = {
        'page_obj': page_obj,
        'venues': page_obj.object_list,
        'search_form': search_form,
        'filter_form': filter_form,
        'categories': categories,
        'service_categories': service_categories,  # NEW
        'service_tags': service_tags,  # NEW
        'states': states,
        'applied_filters': applied_filters,
        'total_venues': total_venues,
        'price_range': price_range,
        'is_search_results': bool(request.GET),
    }

    return render(request, 'venues_app/venue_search.html', context)


def location_autocomplete(request):
    """Enhanced AJAX endpoint for location autocomplete suggestions with improved fuzzy matching and caching."""
    query = request.GET.get('q', '').strip()
    if len(query) < 2:
        return JsonResponse({'suggestions': []})

    # Rate limiting to prevent abuse
    client_ip = request.META.get('REMOTE_ADDR')
    rate_limit_key = f"location_autocomplete_rate_{client_ip}"
    requests_count = cache.get(rate_limit_key, 0)
    
    if requests_count > 60:  # Max 60 requests per minute
        return JsonResponse({
            'error': 'Rate limit exceeded. Please try again later.',
            'suggestions': []
        }, status=429)
    
    cache.set(rate_limit_key, requests_count + 1, 60)  # Reset every minute

    try:
        # Use enhanced fuzzy matching from utils
        from ..utils import get_location_suggestions_with_fuzzy_matching
        
        # Check cache with versioned key for better cache management
        cache_key = f"location_autocomplete_v3_{query.lower()}"
        suggestions = cache.get(cache_key)
        
        if suggestions is None:
            # Get enhanced suggestions with fuzzy matching and better performance
            suggestions = get_location_suggestions_with_fuzzy_matching(query, limit=10)
            
            # Cache for 30 minutes with some randomization to prevent cache stampede
            import random
            cache_timeout = 1800 + random.randint(-300, 300)  # 25-35 minutes
            cache.set(cache_key, suggestions, cache_timeout)

        # Add metadata for client-side optimization
        response_data = {
            'suggestions': suggestions,
            'query': query,
            'count': len(suggestions),
            'cached': suggestions is not None,
            'timestamp': timezone.now().isoformat()
        }

        return JsonResponse(response_data)

    except Exception as e:
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Location autocomplete error for query '{query}': {str(e)}")
        
        # Return a graceful error response
        return JsonResponse({
            'error': 'Unable to fetch location suggestions. Please try again.',
            'suggestions': [],
            'query': query
        }, status=500)


def category_venues(request, category_slug):
    """Redirect to venue search filtered by category."""
    try:
        category = Category.objects.get(slug=category_slug, is_active=True)
        return redirect(f"{reverse('venues_app:venue_search')}?category={category.id}")
    except Category.DoesNotExist:
        messages.error(request, 'Category not found.')
        return redirect('venues_app:venue_search')


def get_location_data(request):
    """AJAX endpoint for hierarchical location data."""
    location_type = request.GET.get('type')
    state = request.GET.get('state')
    county = request.GET.get('county')

    data = {'options': []}

    if location_type == 'counties' and state:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            counties = USCity.objects.filter(state_name__iexact=state_name).values_list('county_name', flat=True).distinct().order_by('county_name')
            data['options'] = [{'value': county, 'label': county} for county in counties]
    elif location_type == 'cities' and state and county:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            cities = USCity.objects.filter(state_name__iexact=state_name, county_name__iexact=county).values_list('city', flat=True).distinct().order_by('city')
            data['options'] = [{'value': city, 'label': city} for city in cities]
    elif location_type == 'cities' and state:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            cities = USCity.objects.filter(state_name__iexact=state_name).values_list('city', flat=True).distinct().order_by('city')
            data['options'] = [{'value': city, 'label': city} for city in cities]

    return JsonResponse(data)


def venue_detail(request, venue_slug):
    """Display detailed information about a specific venue."""
    venue = (
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('services', 'faqs', 'images', 'categories', 'service_provider__team')
        .filter(slug=venue_slug)
        .first()
    )
    if not venue or venue.approval_status != Venue.APPROVED or venue.visibility != Venue.ACTIVE or venue.approval_status == Venue.DRAFT:
        return render(request, 'venues_app/venue_not_found.html', status=404)

    # Respect visibility settings for customer-facing data
    services = venue.services.filter(is_active=True).order_by('service_title')
    faqs = venue.faqs.filter(is_active=True).order_by('order') if venue.show_faqs else venue.faqs.none()
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    
    # Get active team members only if visible
    team_members = venue.service_provider.team.filter(is_active=True).order_by('name') if venue.show_team_members else venue.service_provider.team.none()

    user_has_flagged = False
    if request.user.is_authenticated:
        user_has_flagged = FlaggedVenue.objects.filter(venue=venue, flagged_by=request.user).exists()

    price_range = None
    if services.exists():
        min_price = min(service.price_min for service in services)
        max_price = max(service.price_max or service.price_min for service in services)
        price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    # Calculate completeness score for display (optional for customers)
    completeness_score = venue.calculate_completeness_score()
    
    # Get information freshness indicators 
    freshness_info = venue.get_information_freshness()

    context = {
        'venue': venue,
        'services': services,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'team_members': team_members,
        'user_has_flagged': user_has_flagged,
        'price_range': price_range,
        'can_flag': request.user.is_authenticated and not user_has_flagged,
        'completeness_score': completeness_score,
        'freshness_info': freshness_info,
    }
    return render(request, 'venues_app/venue_detail.html', context)


def service_detail(request, venue_slug, service_slug):
    """Display detailed information about a specific service."""
    venue = get_object_or_404(Venue, slug=venue_slug)
    service = get_object_or_404(Service, venue=venue, slug=service_slug, is_active=True)
    
    # Get related services from the same venue (excluding current service)
    related_services = venue.services.filter(
        is_active=True
    ).exclude(
        id=service.id
    ).select_related('service_category')[:4]  # Limit to 4 related services
    
    # Get venue operating hours
    operating_hours = venue.operating_hours_set.all().order_by('day')
    
    context = {
        'venue': venue,
        'service': service,
        'related_services': related_services,
        'opening_hours': operating_hours,
    }
    
    return render(request, 'venues_app/service_detail.html', context)


@login_required
def flag_venue(request, venue_slug):
    """Allow customers to flag inappropriate venues."""
    venue = get_object_or_404(
        Venue.objects.select_related('service_provider'),
        slug=venue_slug,
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
    )

    rate_key = f"venue_flag_rate_{request.user.id}"
    attempts = cache.get(rate_key, 0)
    if attempts >= 3:
        messages.error(request, 'You have reached the flagging limit. Please try again later.')
        return redirect('venues_app:venue_detail', venue_slug=venue_slug)

    existing_flag = FlaggedVenue.objects.filter(venue=venue, flagged_by=request.user).first()
    if existing_flag:
        messages.info(request, 'You have already flagged this venue. Our team will review it.')
        return redirect('venues_app:venue_detail', venue_slug=venue_slug)

    if request.method == 'POST':
        form = FlaggedVenueForm(request.POST, venue=venue, user=request.user)
        if form.is_valid():
            form.save()
            cache.set(rate_key, attempts + 1, 3600)
            messages.success(request, 'Thank you for your report. Our team will review this venue and take appropriate action.')
            return redirect('venues_app:venue_detail', venue_slug=venue_slug)
        messages.error(request, 'Please correct the errors below.')
    else:
        form = FlaggedVenueForm(venue=venue, user=request.user)

    return render(request, 'venues_app/flag_venue.html', {'venue': venue, 'form': form})


def services_by_category(request, category_slug):
    """Show services filtered by category."""
    service_category = get_object_or_404(ServiceCategory, slug=category_slug, is_active=True)
    
    # Get all services in this category from approved venues
    services = Service.objects.filter(
        service_category=service_category,
        is_active=True,
        venue__approval_status=Venue.APPROVED,
        venue__visibility=Venue.ACTIVE
    ).select_related(
        'venue', 'venue__service_provider', 'service_category'
    ).prefetch_related(
        'venue__categories', 'venue__images'
    ).order_by('venue__venue_name', 'service_title')
    
    # Get unique venues offering services in this category
    venues_with_services = services.values('venue').distinct().count()
    
    # Apply location filter if provided
    location = request.GET.get('location', '').strip()
    if location:
        services = services.filter(
            Q(venue__city__icontains=location) |
            Q(venue__state__icontains=location) |
            Q(venue__county__icontains=location) |
            Q(venue__us_city__city__icontains=location) |
            Q(venue__us_city__state_name__icontains=location)
        )
    
    # Apply price filter if provided
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    if min_price:
        try:
            services = services.filter(price_min__gte=float(min_price))
        except ValueError:
            pass
    if max_price:
        try:
            services = services.filter(price_min__lte=float(max_price))
        except ValueError:
            pass
    
    # Sort options
    sort_by = request.GET.get('sort', 'venue_name')
    if sort_by == 'price_low':
        services = services.order_by('price_min')
    elif sort_by == 'price_high':
        services = services.order_by('-price_min')
    elif sort_by == 'duration':
        services = services.order_by('duration_minutes')
    elif sort_by == 'newest':
        services = services.order_by('-created_at')
    else:
        services = services.order_by('venue__venue_name', 'service_title')
    
    # Pagination
    paginator = Paginator(services, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get other categories for navigation
    other_categories = ServiceCategory.objects.filter(
        is_active=True
    ).exclude(id=service_category.id).order_by('sort_order', 'name')[:10]
    
    context = {
        'service_category': service_category,
        'page_obj': page_obj,
        'services': page_obj.object_list,
        'venues_with_services': venues_with_services,
        'other_categories': other_categories,
        'location': location,
        'min_price': min_price,
        'max_price': max_price,
        'sort_by': sort_by,
    }
    
    return render(request, 'venues_app/services_by_category.html', context)


def services_by_tag(request, tag_slug):
    """Show services filtered by tag."""
    service_tag = get_object_or_404(ServiceTag, slug=tag_slug, is_active=True)
    
    # Get all services with this tag from approved venues
    services = Service.objects.filter(
        tags=service_tag,
        is_active=True,
        venue__approval_status=Venue.APPROVED,
        venue__visibility=Venue.ACTIVE
    ).select_related(
        'venue', 'venue__service_provider', 'service_category'
    ).prefetch_related(
        'venue__categories', 'venue__images', 'tags'
    ).order_by('venue__venue_name', 'service_title')
    
    # Get unique venues offering services with this tag
    venues_with_services = services.values('venue').distinct().count()
    
    # Apply location filter if provided
    location = request.GET.get('location', '').strip()
    if location:
        services = services.filter(
            Q(venue__city__icontains=location) |
            Q(venue__state__icontains=location) |
            Q(venue__county__icontains=location) |
            Q(venue__us_city__city__icontains=location) |
            Q(venue__us_city__state_name__icontains=location)
        )
    
    # Apply price filter if provided
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    if min_price:
        try:
            services = services.filter(price_min__gte=float(min_price))
        except ValueError:
            pass
    if max_price:
        try:
            services = services.filter(price_min__lte=float(max_price))
        except ValueError:
            pass
    
    # Apply category filter if provided
    category_filter = request.GET.get('category')
    if category_filter:
        try:
            category = ServiceCategory.objects.get(id=category_filter, is_active=True)
            services = services.filter(service_category=category)
        except ServiceCategory.DoesNotExist:
            pass
    
    # Sort options
    sort_by = request.GET.get('sort', 'venue_name')
    if sort_by == 'price_low':
        services = services.order_by('price_min')
    elif sort_by == 'price_high':
        services = services.order_by('-price_min')
    elif sort_by == 'duration':
        services = services.order_by('duration_minutes')
    elif sort_by == 'newest':
        services = services.order_by('-created_at')
    else:
        services = services.order_by('venue__venue_name', 'service_title')
    
    # Pagination
    paginator = Paginator(services, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get related tags for navigation
    related_tags = ServiceTag.objects.filter(
        is_active=True,
        tag_type=service_tag.tag_type
    ).exclude(id=service_tag.id).order_by('name')[:10]
    
    # Get available categories for filtering
    available_categories = ServiceCategory.objects.filter(
        is_active=True,
        services__in=services
    ).distinct().order_by('sort_order', 'name')
    
    context = {
        'service_tag': service_tag,
        'page_obj': page_obj,
        'services': page_obj.object_list,
        'venues_with_services': venues_with_services,
        'related_tags': related_tags,
        'available_categories': available_categories,
        'location': location,
        'min_price': min_price,
        'max_price': max_price,
        'category_filter': category_filter,
        'sort_by': sort_by,
    }
    
    return render(request, 'venues_app/services_by_tag.html', context)

