# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone

# --- Local App Imports ---
from ..models import Category, FlaggedVenue, Venue, ServiceCategory
from ..forms.service import ServiceCategoryForm


# --- Venue Approval Views ---
@login_required
def admin_venue_approval_dashboard(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    total_venues = Venue.objects.count()
    pending_venues = Venue.objects.filter(approval_status=Venue.PENDING).count()
    approved_venues = Venue.objects.filter(approval_status=Venue.APPROVED).count()
    rejected_venues = Venue.objects.filter(approval_status=Venue.REJECTED).count()

    recent_pending = Venue.objects.filter(
        approval_status=Venue.PENDING
    ).select_related('service_provider').order_by('-created_at')[:10]

    recently_approved = Venue.objects.filter(
        approval_status=Venue.APPROVED
    ).select_related('service_provider').order_by('-approved_at')[:5]

    context = {
        'total_venues': total_venues,
        'pending_venues': pending_venues,
        'approved_venues': approved_venues,
        'rejected_venues': rejected_venues,
        'recent_pending': recent_pending,
        'recently_approved': recently_approved,
    }
    return render(request, 'venues_app/admin/approval_dashboard.html', context)


# --- Venue Listing Views ---
@login_required
def admin_venue_list(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    venues = Venue.objects.select_related('service_provider').order_by('-created_at')
    status_filter = request.GET.get('status')
    if status_filter and status_filter in ['pending', 'approved', 'rejected']:
        venues = venues.filter(approval_status=status_filter)

    search_query = request.GET.get('search')
    if search_query:
        venues = venues.filter(
            Q(venue_name__icontains=search_query)
            | Q(service_provider__user__email__icontains=search_query)
            | Q(city__icontains=search_query)
            | Q(state__icontains=search_query)
        )

    paginator = Paginator(venues, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'venues': page_obj.object_list,
        'status_filter': status_filter,
        'search_query': search_query,
    }
    return render(request, 'venues_app/admin/venue_list.html', context)


# --- Pending Venue Views ---
@login_required
def admin_pending_venues(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    pending_venues = Venue.objects.filter(
        approval_status=Venue.PENDING
    ).select_related('service_provider').order_by('-created_at')

    paginator = Paginator(pending_venues, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'pending_venues': page_obj.object_list,
    }
    return render(request, 'venues_app/admin/pending_venues.html', context)


# --- Venue Detail & Approval ---
@login_required
def admin_venue_detail(request, venue_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    venue = get_object_or_404(Venue.objects.select_related('service_provider'), id=venue_id)

    venue_images = venue.images.all().order_by('order')
    venue_faqs = venue.faqs.all().order_by('order')
    venue_services = venue.services.all().order_by('service_title')
    venue_categories = venue.categories.all()

    context = {
        'venue': venue,
        'venue_images': venue_images,
        'venue_faqs': venue_faqs,
        'venue_services': venue_services,
        'venue_categories': venue_categories,
    }
    return render(request, 'venues_app/admin/venue_detail.html', context)


@login_required
def admin_venue_changes_comparison(request, venue_id):
    """Show detailed side-by-side comparison of venue changes for admin review."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    venue = get_object_or_404(Venue, id=venue_id)
    
    # Get the most recent status log entry with change details
    change_log_entry = None
    for log_entry in reversed(venue.status_log):
        if (log_entry.get('status') == 'pending' and 
            log_entry.get('reason', '').startswith('Re-approval required') and
            'changed_fields' in log_entry):
            change_log_entry = log_entry
            break
    
    if not change_log_entry:
        messages.info(request, 'No detailed change information available for this venue.')
        return redirect('venues_app:admin_venue_detail', venue_id=venue.id)
    
    # Try to reconstruct change comparison from stored data
    try:
        from .utils import analyze_venue_changes, get_change_comparison_data
        
        # Get change analysis from stored data
        changed_fields = change_log_entry.get('changed_fields', [])
        approval_recommendation = change_log_entry.get('approval_recommendation', 'required')
        change_details = change_log_entry.get('change_details', '')
        
        # For demo purposes, create a comparison structure
        # In production, you'd want to store the original data more comprehensively
        comparison_data = {
            'has_changes': bool(changed_fields),
            'changed_fields': changed_fields,
            'approval_recommendation': approval_recommendation,
            'change_details': change_details,
            'timestamp': change_log_entry.get('timestamp'),
            'total_changes': len(changed_fields)
        }
        
        # Get change severity analysis
        change_analysis = analyze_venue_changes(venue, changed_fields)
        
    except Exception as e:
        messages.error(request, f'Error analyzing venue changes: {str(e)}')
        return redirect('venues_app:admin_venue_detail', venue_id=venue.id)
    
    context = {
        'venue': venue,
        'comparison_data': comparison_data,
        'change_analysis': change_analysis,
        'change_log_entry': change_log_entry,
        'page_title': f'Change Analysis: {venue.venue_name}'
    }
    
    return render(request, 'venues_app/admin/venue_changes_comparison.html', context)


@login_required
def admin_venue_approval(request, venue_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    venue = get_object_or_404(Venue, id=venue_id)
    
    # Get change analysis if venue was recently modified
    change_analysis = None
    change_log_entry = None
    
    if venue.approval_status == Venue.PENDING:
        # Check if this is a re-approval case (look for change details in status log)
        for log_entry in reversed(venue.status_log):
            if (log_entry.get('status') == 'pending' and 
                log_entry.get('reason', '').startswith('Re-approval required') and
                'changed_fields' in log_entry):
                change_log_entry = log_entry
                
                # Get detailed change analysis
                try:
                    from .utils import analyze_venue_changes
                    changed_fields = log_entry.get('changed_fields', [])
                    change_analysis = analyze_venue_changes(venue, changed_fields)
                except Exception:
                    pass
                break
    
    # Get enhanced approval timeline data
    approval_timeline = venue.get_approval_timeline()
    impact_preview = venue.get_approval_impact_preview()
    
    # Get notification preview data
    notification_preview = _get_notification_preview_data(venue, change_analysis)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        admin_notes = request.POST.get('admin_notes', '').strip()

        if action == 'approve':
            venue.approval_status = Venue.APPROVED
            venue.approved_at = timezone.now()
            venue.rejected_at = None  # Clear rejection timestamp
            venue.admin_notes = admin_notes
            
            # Enhanced status log for approval with change context
            approval_log = {
                'status': 'approved',
                'timestamp': timezone.now().isoformat(),
                'approved_by': request.user.username,
                'admin_notes': admin_notes
            }
            
            if change_analysis:
                approval_log.update({
                    'approval_type': 're_approval',
                    'changes_reviewed': change_analysis.get('total_changes', 0),
                    'change_severity': change_analysis.get('approval_recommendation', 'standard')
                })
            else:
                approval_log['approval_type'] = 'initial_approval'
            
            venue.status_log.append(approval_log)
            venue.save()
            
            messages.success(request, f'Venue "{venue.venue_name}" has been approved successfully.')
            
        elif action == 'reject':
            if not admin_notes:
                messages.error(request, 'Please provide a reason for rejection.')
                return redirect('venues_app:admin_venue_approval', venue_id=venue.id)
                
            venue.approval_status = Venue.REJECTED
            venue.rejected_at = timezone.now()
            venue.approved_at = None  # Clear approval timestamp
            venue.admin_notes = admin_notes
            
            # Enhanced status log for rejection with change context
            rejection_log = {
                'status': 'rejected',
                'timestamp': timezone.now().isoformat(),
                'rejected_by': request.user.username,
                'rejection_reason': admin_notes
            }
            
            if change_analysis:
                rejection_log.update({
                    'rejection_type': 're_approval_rejected',
                    'changes_rejected': change_analysis.get('total_changes', 0),
                    'change_severity': change_analysis.get('approval_recommendation', 'standard')
                })
            else:
                rejection_log['rejection_type'] = 'initial_rejection'
            
            venue.status_log.append(rejection_log)
            venue.save()
            
            messages.success(request, f'Venue "{venue.venue_name}" has been rejected successfully.')
            
        elif action == 'request_more_info':
            # New action for requesting additional information
            if not admin_notes:
                messages.error(request, 'Please specify what additional information is needed.')
                return redirect('venues_app:admin_venue_approval', venue_id=venue.id)
            
            # Send notification to provider requesting more info
            try:
                from notifications_app.utils import run_async, notify_venue_info_requested
                run_async(notify_venue_info_requested, venue, admin_notes)
                
                # Log the info request
                venue.status_log.append({
                    'status': 'info_requested',
                    'timestamp': timezone.now().isoformat(),
                    'requested_by': request.user.username,
                    'info_requested': admin_notes
                })
                venue.save()
                
                messages.info(request, f'Information request sent to {venue.service_provider.business_name}.')
                
            except Exception as e:
                messages.warning(request, 'Approval status updated, but notification may have failed.')
        
        return redirect('venues_app:admin_pending_venues')

    context = {
        'venue': venue,
        'change_analysis': change_analysis,
        'change_log_entry': change_log_entry,
        'has_change_details': bool(change_analysis),
        # Enhanced approval workflow data
        'approval_timeline': approval_timeline,
        'impact_preview': impact_preview,
        'notification_preview': notification_preview,
    }
    
    return render(request, 'venues_app/admin/venue_approval.html', context)


def _get_notification_preview_data(venue, change_analysis):
    """Generate preview data for approval notifications."""
    provider = venue.service_provider
    
    # Prepare different notification scenarios
    notifications = {
        'approval': {
            'title': f'Great news! {venue.venue_name} has been approved',
            'message': f'Your venue is now live and visible to customers. Start receiving bookings today!',
            'type': 'success',
            'icon': 'fas fa-check-circle',
            'action_text': 'View Live Venue',
            'action_url': f'/venues/{venue.slug}/'
        },
        'rejection': {
            'title': f'Update needed for {venue.venue_name}',
            'message': f'We need a few updates before your venue can go live. Check the details and resubmit when ready.',
            'type': 'warning',
            'icon': 'fas fa-exclamation-triangle',
            'action_text': 'View Feedback & Edit',
            'action_url': f'/provider/venues/{venue.id}/'
        },
        'info_request': {
            'title': f'Additional information needed for {venue.venue_name}',
            'message': f'We need some additional details to complete your venue review. This helps us approve you faster!',
            'type': 'info',
            'icon': 'fas fa-question-circle',
            'action_text': 'Provide Information',
            'action_url': f'/provider/venues/{venue.id}/edit/'
        }
    }
    
    # Add change-specific context if available
    if change_analysis:
        total_changes = change_analysis.get('total_changes', 0)
        severity = change_analysis.get('approval_recommendation', 'standard')
        
        notifications['reapproval'] = {
            'title': f'{venue.venue_name} changes under review',
            'message': f'Your recent changes ({total_changes} updates) are being reviewed. Your venue remains live during this process.',
            'type': 'info',
            'icon': 'fas fa-sync-alt',
            'action_text': 'View Changes',
            'action_url': f'/provider/venues/{venue.id}/'
        }
    
    return notifications


# --- Category Management Views ---
@login_required
def admin_category_list(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    categories = Category.objects.all().order_by('category_name')
    return render(request, 'venues_app/admin/category_list.html', {'categories': categories})


@login_required
def admin_category_create(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        if not name:
            messages.error(request, 'Category name is required.')
        elif Category.objects.filter(category_name__iexact=name).exists():
            messages.error(request, 'A category with this name already exists.')
        else:
            category = Category.objects.create(category_name=name, category_description=description, is_active=is_active)
            messages.success(request, f'Category "{category.category_name}" has been created successfully.')
            return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_form.html', {'action': 'Create'})


@login_required
def admin_category_edit(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        if not name:
            messages.error(request, 'Category name is required.')
        elif Category.objects.filter(category_name__iexact=name).exclude(id=category.id).exists():
            messages.error(request, 'A category with this name already exists.')
        else:
            category.category_name = name
            category.category_description = description
            category.is_active = is_active
            category.save()
            messages.success(request, f'Category "{category.category_name}" has been updated successfully.')
            return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_form.html', {'category': category, 'action': 'Edit'})


@login_required
def admin_category_delete(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    category = get_object_or_404(Category, id=category_id)
    venue_count = category.venues.count()

    if request.method == 'POST':
        if venue_count > 0:
            messages.error(request, f'Cannot delete category "{category.name}" because it has {venue_count} venues assigned to it.')
        else:
            category_name = category.name
            category.delete()
            messages.success(request, f'Category "{category_name}" has been deleted successfully.')
        return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_delete.html', {'category': category, 'venue_count': venue_count})


@login_required
def admin_category_toggle_status(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    category = get_object_or_404(Category, id=category_id)
    category.is_active = not category.is_active
    category.save()

    status = 'activated' if category.is_active else 'deactivated'
    messages.success(request, f'Category "{category.name}" has been {status}.')
    return redirect('venues_app:admin_category_list')


# --- Flagged Venues ---
@login_required
def admin_flagged_venues(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')
        
    flagged_venues = FlaggedVenue.objects.select_related('venue', 'flagged_by', 'reviewed_by').order_by('-created_at')
    return render(request, 'venues_app/admin/flagged_venues.html', {'flagged_venues': flagged_venues})


# --- Service Category Management Views ---
@login_required
def admin_service_category_list(request):
    """List all service categories for admin management."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    search_query = request.GET.get('search', '').strip()
    status_filter = request.GET.get('status')
    
    service_categories = ServiceCategory.objects.all()
    
    if search_query:
        service_categories = service_categories.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    if status_filter == 'active':
        service_categories = service_categories.filter(is_active=True)
    elif status_filter == 'inactive':
        service_categories = service_categories.filter(is_active=False)
    
    service_categories = service_categories.order_by('sort_order', 'name')
    
    # Get statistics
    total_categories = ServiceCategory.objects.count()
    active_categories = ServiceCategory.objects.filter(is_active=True).count()
    inactive_categories = total_categories - active_categories
    
    context = {
        'service_categories': service_categories,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_categories': total_categories,
        'active_categories': active_categories,
        'inactive_categories': inactive_categories,
    }
    return render(request, 'venues_app/admin/service_category_list.html', context)


@login_required
def admin_service_category_create(request):
    """Create a new service category."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    if request.method == 'POST':
        form = ServiceCategoryForm(request.POST)
        if form.is_valid():
            service_category = form.save()
            messages.success(request, f'Service category "{service_category.name}" has been created successfully.')
            return redirect('venues_app:admin_service_category_list')
    else:
        form = ServiceCategoryForm()

    context = {
        'form': form,
        'action': 'Create',
        'title': 'Create Service Category'
    }
    return render(request, 'venues_app/admin/service_category_form.html', context)


@login_required
def admin_service_category_edit(request, category_id):
    """Edit an existing service category."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    service_category = get_object_or_404(ServiceCategory, id=category_id)

    if request.method == 'POST':
        form = ServiceCategoryForm(request.POST, instance=service_category)
        if form.is_valid():
            form.save()
            messages.success(request, f'Service category "{service_category.name}" has been updated successfully.')
            return redirect('venues_app:admin_service_category_list')
    else:
        form = ServiceCategoryForm(instance=service_category)

    context = {
        'form': form,
        'service_category': service_category,
        'action': 'Edit',
        'title': f'Edit Service Category: {service_category.name}'
    }
    return render(request, 'venues_app/admin/service_category_form.html', context)


@login_required
def admin_service_category_delete(request, category_id):
    """Delete a service category."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    service_category = get_object_or_404(ServiceCategory, id=category_id)
    service_count = service_category.services.count()

    if request.method == 'POST':
        if service_count > 0:
            messages.error(request, f'Cannot delete service category "{service_category.name}" because it has {service_count} services assigned to it.')
        else:
            category_name = service_category.name
            service_category.delete()
            messages.success(request, f'Service category "{category_name}" has been deleted successfully.')
        return redirect('venues_app:admin_service_category_list')

    context = {
        'service_category': service_category,
        'service_count': service_count,
        'title': f'Delete Service Category: {service_category.name}'
    }
    return render(request, 'venues_app/admin/service_category_delete.html', context)


@login_required
def admin_service_category_toggle_status(request, category_id):
    """Toggle active status of a service category."""
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('admin_app:home')

    service_category = get_object_or_404(ServiceCategory, id=category_id)
    service_category.is_active = not service_category.is_active
    service_category.save()

    status = 'activated' if service_category.is_active else 'deactivated'
    messages.success(request, f'Service category "{service_category.name}" has been {status}.')
    return redirect('venues_app:admin_service_category_list')

