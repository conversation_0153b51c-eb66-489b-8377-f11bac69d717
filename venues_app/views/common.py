"""Shared helpers and constants for ``venues_app`` views."""

# --- Django Imports ---
from django.contrib import messages
from django.shortcuts import redirect, render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse
from django.contrib.auth import get_user_model


MAX_SERVICES_PER_VENUE = 50
MAX_FAQS_PER_VENUE = 20


class ServiceProviderRequiredMixin(LoginRequiredMixin):
    """Mixin to require user to be a service provider."""
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not hasattr(request.user, 'role') or request.user.role != 'service_provider':
            messages.error(request, 'Only service providers can access this page.')
            return redirect('/')
        
        return super().dispatch(request, *args, **kwargs)


def home_view(request):
    """Home view for venues app."""
    # Basic context for the home page
    context = {
        'top_venues': [],
        'popular_categories': [],
        'featured_services': [],
    }
    return render(request, 'venues_app/home.html', context)


def login_view(request):
    """Login view placeholder for venues app."""
    # Redirect to main login page
    return redirect('accounts_app:customer_login')

