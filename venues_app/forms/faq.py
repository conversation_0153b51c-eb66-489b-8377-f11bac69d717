# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
import re

# --- Local App Imports ---
from ..models import VenueFAQ


try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class VenueFAQForm(forms.ModelForm):
    """Enhanced form for creating and editing venue FAQs with validation and templates."""

    class Meta:
        model = VenueFAQ
        fields = ['question', 'answer']
        widgets = {
            'question': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your frequently asked question (minimum 10 characters)',
                'maxlength': 255,
                'data-character-count': 'true',
                'data-min-length': '10',
                'data-max-length': '255',
            }),
            'answer': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Provide a clear and helpful answer (minimum 20 characters, maximum 500 characters)',
                'maxlength': 500,
                'data-character-count': 'true',
                'data-min-length': '20',
                'data-max-length': '500',
            }),
        }
        help_texts = {
            'question': _('Enter a clear, specific question that customers commonly ask (10-255 characters)'),
            'answer': _('Provide a comprehensive answer that addresses the question thoroughly (20-500 characters)'),
        }

    def clean_question(self):
        """Enhanced question validation with minimum length and quality checks."""
        question = self.cleaned_data.get('question', '').strip()
        
        if not question:
            raise ValidationError(_('Question is required.'))
            
        question = sanitize_html(question)
        
        if len(question) < 10:
            raise ValidationError(_('Question must be at least 10 characters long.'))
            
        if len(question) > 255:
            raise ValidationError(_('Question cannot exceed 255 characters.'))
        
        # Quality checks
        if not question.endswith('?'):
            raise ValidationError(_('Question should end with a question mark (?).'))
            
        # Check for low-quality questions
        low_quality_patterns = [
            r'^test+\??$',
            r'^(.)\1{3,}',  # Repeated characters (4 or more)
            r'^[a-z\s]+\??$',  # Only lowercase letters and spaces
        ]
        
        for pattern in low_quality_patterns:
            if re.match(pattern, question, re.IGNORECASE):
                raise ValidationError(
                    _('Please provide a more meaningful and professional question.')
                )
        
        # Ensure minimum word count
        word_count = len(question.split())
        if word_count < 3:
            raise ValidationError(_('Question must contain at least 3 words.'))
            
        return question

    def clean_answer(self):
        """Enhanced answer validation with minimum length and quality checks."""
        answer = self.cleaned_data.get('answer', '').strip()
        
        if not answer:
            raise ValidationError(_('Answer is required.'))
            
        answer = sanitize_html(answer)
        
        if len(answer) < 20:
            raise ValidationError(_('Answer must be at least 20 characters long.'))
            
        if len(answer) > 500:
            raise ValidationError(_('Answer cannot exceed 500 characters.'))
        
        # Quality checks
        low_quality_patterns = [
            r'^test+$',
            r'^(.)\1{5,}',  # Repeated characters (6 or more)
            r'^[a-z\s]+$',  # Only lowercase letters and spaces
        ]
        
        for pattern in low_quality_patterns:
            if re.match(pattern, answer, re.IGNORECASE):
                raise ValidationError(
                    _('Please provide a more detailed and professional answer.')
                )
        
        # Ensure minimum word count
        word_count = len(answer.split())
        if word_count < 5:
            raise ValidationError(_('Answer must contain at least 5 words.'))
            
        return answer

    @staticmethod
    def get_faq_templates():
        """Get predefined FAQ templates for different venue types."""
        return {
            'spa': [
                {
                    'question': 'What should I bring for my spa appointment?',
                    'answer': 'We provide all towels, robes, and slippers. Please arrive 15 minutes early to complete intake forms and relax before your treatment.'
                },
                {
                    'question': 'Do you offer couple\'s treatments?',
                    'answer': 'Yes, we have private couples suites available for side-by-side treatments. Please book in advance as these are popular.'
                },
                {
                    'question': 'What is your cancellation policy?',
                    'answer': 'We require 24-hour notice for cancellations or changes. Same-day cancellations may incur a 50% service charge.'
                },
                {
                    'question': 'Do you offer gift certificates?',
                    'answer': 'Yes, gift certificates are available for purchase online or in-person. They make perfect gifts and never expire.'
                },
                {
                    'question': 'Are your products available for purchase?',
                    'answer': 'Yes, we carry a full line of professional skincare and wellness products used in our treatments.'
                }
            ],
            'salon': [
                {
                    'question': 'Do I need an appointment?',
                    'answer': 'Appointments are recommended to guarantee your preferred time slot. Walk-ins are welcome based on availability.'
                },
                {
                    'question': 'What forms of payment do you accept?',
                    'answer': 'We accept cash, all major credit cards, and digital payments. Gratuity can be added to card payments.'
                },
                {
                    'question': 'Do you offer bridal services?',
                    'answer': 'Yes, we specialize in bridal hair and makeup. We offer trial runs and can accommodate wedding parties of all sizes.'
                },
                {
                    'question': 'How often should I get my hair cut?',
                    'answer': 'For healthy hair maintenance, we recommend a trim every 6-8 weeks. Your stylist can provide personalized recommendations.'
                },
                {
                    'question': 'Do you use organic or natural products?',
                    'answer': 'We offer both conventional and organic product lines. Please let us know your preferences when booking.'
                }
            ],
            'massage': [
                {
                    'question': 'What types of massage do you offer?',
                    'answer': 'We offer Swedish, deep tissue, hot stone, prenatal, and sports massage. Each session is customized to your needs.'
                },
                {
                    'question': 'How should I prepare for my massage?',
                    'answer': 'Arrive 10 minutes early to complete forms. Avoid heavy meals 2 hours before. Communicate any health concerns or preferences.'
                },
                {
                    'question': 'What should I expect during my first visit?',
                    'answer': 'Your therapist will review your health history and discuss your goals. You\'ll be draped professionally throughout the session.'
                },
                {
                    'question': 'How often should I get a massage?',
                    'answer': 'For general wellness, monthly massages are beneficial. For specific issues, weekly or bi-weekly sessions may be recommended.'
                },
                {
                    'question': 'Do you offer membership or package deals?',
                    'answer': 'Yes, we offer discounted packages and monthly membership plans. Ask about our current promotions.'
                }
            ],
            'fitness': [
                {
                    'question': 'Do you offer personal training?',
                    'answer': 'Yes, our certified trainers provide one-on-one and small group training sessions. Initial consultations include fitness assessments.'
                },
                {
                    'question': 'What equipment do you have available?',
                    'answer': 'We have a full range of cardio machines, free weights, resistance equipment, and functional training areas.'
                },
                {
                    'question': 'Do you offer group fitness classes?',
                    'answer': 'Yes, we offer yoga, pilates, spinning, HIIT, and dance classes. Check our schedule for times and instructor specialties.'
                },
                {
                    'question': 'What are your membership options?',
                    'answer': 'We offer monthly, quarterly, and annual memberships with various access levels. Day passes and trial periods are also available.'
                },
                {
                    'question': 'Do you have shower and locker facilities?',
                    'answer': 'Yes, we provide clean locker rooms with showers, towels, and basic amenities for your convenience.'
                }
            ],
            'wellness': [
                {
                    'question': 'What wellness services do you provide?',
                    'answer': 'We offer holistic health consultations, nutritional counseling, stress management, and various alternative therapy modalities.'
                },
                {
                    'question': 'Do you accept insurance?',
                    'answer': 'Some services may be covered by HSA/FSA accounts. Please check with your insurance provider for specific coverage details.'
                },
                {
                    'question': 'How do I choose the right service for me?',
                    'answer': 'We offer complimentary consultations to discuss your wellness goals and recommend the best approach for your needs.'
                },
                {
                    'question': 'Are your practitioners licensed?',
                    'answer': 'Yes, all our wellness practitioners are fully licensed and certified in their respective specialties with ongoing education.'
                },
                {
                    'question': 'Do you offer virtual or remote sessions?',
                    'answer': 'Yes, many of our wellness consultations and coaching sessions are available via secure video platforms.'
                }
            ]
        }

    @staticmethod
    def get_template_by_venue_category(categories):
        """Get appropriate FAQ templates based on venue categories."""
        templates = VenueFAQForm.get_faq_templates()
        
        # Map category names to template keys
        category_mapping = {
            'spa': 'spa',
            'salon': 'salon', 
            'massage': 'massage',
            'fitness': 'fitness',
            'wellness': 'wellness',
            'beauty': 'salon',
            'health': 'wellness',
            'therapy': 'wellness'
        }
        
        # Get the first matching template
        for category in categories:
            category_name = category.category_name.lower()
            for key, template_key in category_mapping.items():
                if key in category_name:
                    return templates.get(template_key, [])
        
        # Return generic templates if no match
        return templates.get('spa', [])  # Default to spa templates
