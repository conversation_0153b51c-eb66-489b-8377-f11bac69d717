"""Enhanced forms for venue operating hours management with preset templates."""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from datetime import time

# --- Local App Imports ---
from ..models import OperatingHours, HolidaySchedule


class ScheduleTemplateForm(forms.Form):
    """Form for selecting preset schedule templates."""
    
    TEMPLATE_CHOICES = [
        ('', _('Select a preset schedule (optional)')),
        ('business_hours', _('Business Hours (Mon-Fri 9 AM - 5 PM)')),
        ('retail_hours', _('Retail Hours (Mon-Sat 10 AM - 8 PM, Sun 12 PM - 6 PM)')),
        ('spa_hours', _('Spa Hours (Mon-Sat 9 AM - 7 PM, Sun 10 AM - 6 PM)')),
        ('restaurant_hours', _('Restaurant Hours (Daily 11 AM - 10 PM)')),
        ('gym_hours', _('Gym Hours (Mon-Fri 5 AM - 11 PM, Weekends 7 AM - 9 PM)')),
        ('salon_hours', _('Salon Hours (Tue-Sat 9 AM - 6 PM, Closed Sun-Mon)')),
        ('medical_hours', _('Medical Hours (Mon-Fri 8 AM - 6 PM, Sat 9 AM - 1 PM)')),
        ('extended_hours', _('Extended Hours (Daily 7 AM - 10 PM)')),
        ('24_7', _('24/7 (Always Open)')),
        ('custom', _('Custom Schedule')),
    ]
    
    template = forms.ChoiceField(
        choices=TEMPLATE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control template-selector',
            'id': 'schedule-template'
        }),
        help_text=_('Choose a preset schedule to quickly set up your hours')
    )


class EnhancedOperatingHoursForm(forms.ModelForm):
    """Enhanced form for managing individual operating hours with better time options."""
    
    # Generate comprehensive time choices (every 15 minutes)
    TIME_CHOICES = [('', _('Select time'))]
    for hour in range(24):
        for minute in [0, 15, 30, 45]:
            time_obj = time(hour, minute)
            if hour == 0:
                display_hour = 12
                am_pm = 'AM'
            elif hour < 12:
                display_hour = hour
                am_pm = 'AM'
            elif hour == 12:
                display_hour = 12
                am_pm = 'PM'
            else:
                display_hour = hour - 12
                am_pm = 'PM'
            
            if minute == 0:
                time_display = f'{display_hour}:00 {am_pm}'
            else:
                time_display = f'{display_hour}:{minute:02d} {am_pm}'
                
            TIME_CHOICES.append((time_obj.strftime('%H:%M'), time_display))

    class Meta:
        model = OperatingHours
        fields = ['day', 'opening', 'closing', 'is_closed', 'is_24_hours', 'is_overnight']
        widgets = {
            'day': forms.HiddenInput(),
            'opening': forms.Select(attrs={
                'class': 'form-control time-select',
            }),
            'closing': forms.Select(attrs={
                'class': 'form-control time-select',
            }),
            'is_closed': forms.CheckboxInput(attrs={
                'class': 'form-check-input day-closed-checkbox',
            }),
            'is_24_hours': forms.CheckboxInput(attrs={
                'class': 'form-check-input day-24hours-checkbox',
            }),
            'is_overnight': forms.CheckboxInput(attrs={
                'class': 'form-check-input day-overnight-checkbox',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set time choices
        self.fields['opening'].widget.choices = self.TIME_CHOICES
        self.fields['closing'].widget.choices = self.TIME_CHOICES
        
        # Add help text
        self.fields['is_overnight'].help_text = _('Check this if you close after midnight (e.g., 10 PM to 2 AM)')
        self.fields['is_24_hours'].help_text = _('Check this if you are open 24 hours on this day')

    def clean(self):
        """Enhanced validation with overnight support."""
        cleaned_data = super().clean()
        opening = cleaned_data.get('opening')
        closing = cleaned_data.get('closing')
        is_closed = cleaned_data.get('is_closed')
        is_24_hours = cleaned_data.get('is_24_hours')
        is_overnight = cleaned_data.get('is_overnight')

        # If closed, clear other fields
        if is_closed:
            cleaned_data['opening'] = None
            cleaned_data['closing'] = None
            cleaned_data['is_24_hours'] = False
            cleaned_data['is_overnight'] = False
            return cleaned_data
            
        # If 24 hours, clear time fields
        if is_24_hours:
            cleaned_data['opening'] = None
            cleaned_data['closing'] = None
            cleaned_data['is_overnight'] = False
            return cleaned_data
            
        # Regular hours validation
        if not opening or not closing:
            raise ValidationError(_('Opening and closing times are required when not closed or 24 hours'))
            
        # Convert string times to time objects for comparison
        if isinstance(opening, str):
            opening = time.fromisoformat(opening)
        if isinstance(closing, str):
            closing = time.fromisoformat(closing)
            
        # For overnight hours, closing can be before opening
        if not is_overnight and opening >= closing:
            raise ValidationError(_('Opening time must be before closing time. If you close after midnight, check "Overnight hours"'))

        return cleaned_data


class SimplifiedOperatingHoursForm(forms.Form):
    """Simplified form for managing all operating hours with templates."""
    
    # Include template selector
    template = forms.ChoiceField(
        choices=ScheduleTemplateForm.TEMPLATE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control template-selector mb-4',
            'id': 'schedule-template'
        }),
        help_text=_('Choose a preset schedule or select "Custom Schedule" to set individual days')
    )
    
    # Days of the week
    DAYS = [
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    ]
    
    # Generate time choices (every 30 minutes for simplicity)
    TIME_CHOICES = [('', _('Select time'))]
    for hour in range(24):
        for minute in [0, 30]:
            time_obj = time(hour, minute)
            if hour == 0:
                display_hour = 12
                am_pm = 'AM'
            elif hour < 12:
                display_hour = hour
                am_pm = 'AM'
            elif hour == 12:
                display_hour = 12
                am_pm = 'PM'
            else:
                display_hour = hour - 12
                am_pm = 'PM'
            
            if minute == 0:
                time_display = f'{display_hour}:00 {am_pm}'
            else:
                time_display = f'{display_hour}:{minute:02d} {am_pm}'
                
            TIME_CHOICES.append((time_obj.strftime('%H:%M'), time_display))

    def __init__(self, *args, **kwargs):
        self.venue = kwargs.pop('venue', None)
        super().__init__(*args, **kwargs)
        
        # Create fields for each day
        for day_key, day_name in self.DAYS:
            # Status field (closed, regular, 24h, overnight)
            self.fields[f'{day_key}_status'] = forms.ChoiceField(
                choices=[
                    ('closed', _('Closed')),
                    ('regular', _('Regular Hours')),
                    ('24hours', _('24 Hours')),
                    ('overnight', _('Overnight Hours')),
                ],
                initial='closed',
                widget=forms.Select(attrs={
                    'class': 'form-control day-status-select',
                    'data-day': day_key
                }),
                label=f'{day_name} Status'
            )
            
            # Opening time
            self.fields[f'{day_key}_opening'] = forms.ChoiceField(
                choices=self.TIME_CHOICES,
                required=False,
                widget=forms.Select(attrs={
                    'class': 'form-control time-select',
                    'data-day': day_key,
                    'data-type': 'opening'
                }),
                label=f'{day_name} Opening'
            )
            
            # Closing time
            self.fields[f'{day_key}_closing'] = forms.ChoiceField(
                choices=self.TIME_CHOICES,
                required=False,
                widget=forms.Select(attrs={
                    'class': 'form-control time-select',
                    'data-day': day_key,
                    'data-type': 'closing'
                }),
                label=f'{day_name} Closing'
            )
            
            # Copy hours button for each day
            self.fields[f'{day_key}_copy_from'] = forms.ChoiceField(
                choices=[('', _('Copy from...'))] + [(k, n) for k, n in self.DAYS if k != day_key],
                required=False,
                widget=forms.Select(attrs={
                    'class': 'form-control copy-hours-select',
                    'data-target-day': day_key,
                    'data-copy-trigger': 'true'
                }),
                label=f'Copy to {day_name}'
            )
        
        # Load existing data if venue provided
        if self.venue:
            self._load_existing_hours()

    def _load_existing_hours(self):
        """Load existing operating hours into the form."""
        existing_hours = {oh.day: oh for oh in self.venue.operating_hours_set.all()}
        
        for i, (day_key, day_name) in enumerate(self.DAYS):
            if i in existing_hours:
                oh = existing_hours[i]
                if oh.is_closed:
                    self.fields[f'{day_key}_status'].initial = 'closed'
                elif oh.is_24_hours:
                    self.fields[f'{day_key}_status'].initial = '24hours'
                elif oh.is_overnight:
                    self.fields[f'{day_key}_status'].initial = 'overnight'
                    self.fields[f'{day_key}_opening'].initial = oh.opening.strftime('%H:%M') if oh.opening else ''
                    self.fields[f'{day_key}_closing'].initial = oh.closing.strftime('%H:%M') if oh.closing else ''
                else:
                    self.fields[f'{day_key}_status'].initial = 'regular'
                    self.fields[f'{day_key}_opening'].initial = oh.opening.strftime('%H:%M') if oh.opening else ''
                    self.fields[f'{day_key}_closing'].initial = oh.closing.strftime('%H:%M') if oh.closing else ''

    def clean(self):
        """Validate the entire schedule."""
        cleaned_data = super().clean()
        
        # Validate each day
        for day_key, day_name in self.DAYS:
            status = cleaned_data.get(f'{day_key}_status')
            opening = cleaned_data.get(f'{day_key}_opening')
            closing = cleaned_data.get(f'{day_key}_closing')
            
            if status in ['regular', 'overnight']:
                if not opening or not closing:
                    raise ValidationError(
                        f'{day_name}: Opening and closing times are required for regular/overnight hours'
                    )
                
                # Convert to time objects for comparison
                opening_time = time.fromisoformat(opening)
                closing_time = time.fromisoformat(closing)
                
                # For regular hours, opening must be before closing
                if status == 'regular' and opening_time >= closing_time:
                    raise ValidationError(
                        f'{day_name}: Opening time must be before closing time for regular hours'
                    )

        return cleaned_data

    def save(self, venue):
        """Save the operating hours to the venue."""
        if not self.is_valid():
            return False
            
        # Delete existing hours
        venue.operating_hours_set.all().delete()
        
        # Create new hours
        for i, (day_key, day_name) in enumerate(self.DAYS):
            status = self.cleaned_data[f'{day_key}_status']
            opening = self.cleaned_data.get(f'{day_key}_opening')
            closing = self.cleaned_data.get(f'{day_key}_closing')
            
            if status == 'closed':
                OperatingHours.objects.create(
                    venue=venue,
                    day=i,
                    is_closed=True
                )
            elif status == '24hours':
                OperatingHours.objects.create(
                    venue=venue,
                    day=i,
                    is_24_hours=True
                )
            elif status in ['regular', 'overnight']:
                OperatingHours.objects.create(
                    venue=venue,
                    day=i,
                    opening=time.fromisoformat(opening) if opening else None,
                    closing=time.fromisoformat(closing) if closing else None,
                    is_overnight=(status == 'overnight')
                )
        
        return True


class HolidayScheduleForm(forms.ModelForm):
    """Form for managing holiday schedules."""
    
    class Meta:
        model = HolidaySchedule
        fields = ['name', 'date', 'opening', 'closing', 'is_closed', 'is_24_hours', 'is_overnight', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('e.g., Christmas Day, New Year\'s Eve')
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'opening': forms.Select(attrs={
                'class': 'form-control time-select',
            }),
            'closing': forms.Select(attrs={
                'class': 'form-control time-select',
            }),
            'is_closed': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
            }),
            'is_24_hours': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
            }),
            'is_overnight': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Additional notes about this special schedule')
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set time choices (same as enhanced form)
        TIME_CHOICES = [('', _('Select time'))]
        for hour in range(24):
            for minute in [0, 15, 30, 45]:
                time_obj = time(hour, minute)
                if hour == 0:
                    display_hour = 12
                    am_pm = 'AM'
                elif hour < 12:
                    display_hour = hour
                    am_pm = 'AM'
                elif hour == 12:
                    display_hour = 12
                    am_pm = 'PM'
                else:
                    display_hour = hour - 12
                    am_pm = 'PM'
                
                if minute == 0:
                    time_display = f'{display_hour}:00 {am_pm}'
                else:
                    time_display = f'{display_hour}:{minute:02d} {am_pm}'
                    
                TIME_CHOICES.append((time_obj.strftime('%H:%M'), time_display))
        
        self.fields['opening'].widget.choices = TIME_CHOICES
        self.fields['closing'].widget.choices = TIME_CHOICES


# Legacy support - keep the old formset for backward compatibility if needed
OperatingHoursForm = EnhancedOperatingHoursForm

class OperatingHoursFormSet(forms.BaseFormSet):
    """Enhanced formset for managing all 7 days of operating hours."""

    def clean(self):
        """Validate the entire formset."""
        if any(self.errors):
            return

        # Check for duplicate days
        days = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                day = form.cleaned_data.get('day')
                if day in days:
                    raise ValidationError(_('Each day can only have one set of operating hours.'))
                days.append(day)


# Create the formset factory with enhanced form
OperatingHoursFormSetFactory = forms.formset_factory(
    EnhancedOperatingHoursForm,
    formset=OperatingHoursFormSet,
    extra=7,  # One for each day of the week
    max_num=7,
    validate_max=True,
    can_delete=False
)



