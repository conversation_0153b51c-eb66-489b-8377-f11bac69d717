from django import template

register = template.Library()

@register.filter
def replace(value, arg):
    """Replace parts of a string.
    
    Usage: {{ "hello_world"|replace:"_"," " }}
    Result: "hello world"
    """
    # Handle comma-separated arguments: "search_string","replacement_string"
    if ',' in arg:
        parts = [part.strip().strip('"\'') for part in arg.split(',')]
        if len(parts) == 2:
            search_for, replace_with = parts
            return str(value).replace(search_for, replace_with)
    
    # Handle colon-separated arguments: "search_string:replacement_string"
    elif ':' in arg:
        search_for, replace_with = arg.split(':', 1)
        return str(value).replace(search_for, replace_with)
    
    return value

@register.filter
def underscore_to_spaces(value):
    """Replace underscores with spaces.
    
    Usage: {{ "hello_world"|underscore_to_spaces }}
    Result: "hello world"
    """
    return str(value).replace('_', ' ') 