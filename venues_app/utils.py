from django.db.models import Avg, Count, Min, Max, Q
from .models import Category, Service, USCity, Venue
# --- Enhanced Location & Validation Imports ---
import difflib
from typing import List, Dict, Optional, Tuple
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from functools import lru_cache
import re
from decimal import Decimal
from urllib.parse import urlparse
import requests
from requests.exceptions import RequestException, Timeout
from django.utils import timezone
from datetime import timedelta


def build_venue_search_query(query_params):

    venues = Venue.objects.filter(
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    ).exclude(
        approval_status=Venue.DRAFT
    ).select_related('service_provider', 'us_city').prefetch_related('categories', 'services')
    
    # Add annotations for sorting and filtering
    venues = venues.annotate(
        avg_rating=Avg('review_app_reviews__rating'),
        review_count=Count('review_app_reviews'),
        min_service_price=Min('services__price_min'),
        max_service_price=Max('services__price_max'),
        has_discount=Count('services', filter=Q(services__discounted_price__isnull=False)) > 0
    )
    
    return venues


def get_location_suggestions_with_fuzzy_matching(query: str, limit: int = 10) -> List[Dict]:
    """
    Enhanced location suggestions with optimized fuzzy matching for better performance.
    Uses database-level filtering and caching for improved user experience.
    
    Args:
        query: Search query string
        limit: Maximum number of suggestions to return
        
    Returns:
        List of suggestion dictionaries with enhanced matching and relevance scoring
    """
    if not query or len(query) < 2:
        return []
    
    query = query.strip().lower()
    
    # Check cache first for performance
    cache_key = f"location_suggestions_v2_{query}_{limit}"
    cached_suggestions = cache.get(cache_key)
    if cached_suggestions is not None:
        return cached_suggestions
    
    suggestions = []
    
    # Use database-level filtering instead of loading all records
    # This is much more efficient for large datasets
    base_queryset = USCity.objects.select_related().only(
        'city', 'state_id', 'state_name', 'county_name', 
        'latitude', 'longitude', 'city_id'
    )
    
    # Exact matches get highest priority
    exact_city_matches = base_queryset.filter(
        Q(city__iexact=query) | Q(city__istartswith=query)
    ).distinct()[:limit//2]
    
    # State matches
    exact_state_matches = base_queryset.filter(
        Q(state_name__iexact=query) | Q(state_id__iexact=query) |
        Q(state_name__istartswith=query) | Q(state_id__istartswith=query)
    ).distinct()[:limit//4]
    
    # County matches
    exact_county_matches = base_queryset.filter(
        Q(county_name__iexact=query) | Q(county_name__istartswith=query)
    ).distinct()[:limit//4]
    
    # Fuzzy matches for cities
    fuzzy_city_matches = base_queryset.filter(
        Q(city__icontains=query) & ~Q(city__istartswith=query)
    ).distinct()[:limit]
    
    # Process exact city matches
    seen_labels = set()
    for city in exact_city_matches:
        label = f"{city.city}, {city.state_id}"
        if label not in seen_labels:
            seen_labels.add(label)
            suggestions.append({
                'label': label,
                'value': label,
                'type': 'city',
                'state': city.state_name,
                'state_id': city.state_id,
                'county': city.county_name,
                'city': city.city,
                'similarity': 1.0 if city.city.lower() == query else 0.95,
                'latitude': float(city.latitude) if city.latitude else None,
                'longitude': float(city.longitude) if city.longitude else None,
                'priority': 'exact' if city.city.lower() == query else 'startswith'
            })
    
    # Process state matches
    for city in exact_state_matches:
        if len(suggestions) >= limit:
            break
        state_label = city.state_name
        if state_label not in seen_labels:
            seen_labels.add(state_label)
            suggestions.append({
                'label': state_label,
                'value': state_label,
                'type': 'state',
                'state': city.state_name,
                'state_id': city.state_id,
                'county': None,
                'city': None,
                'similarity': 1.0 if city.state_name.lower() == query or city.state_id.lower() == query else 0.9,
                'latitude': None,
                'longitude': None,
                'priority': 'exact' if city.state_name.lower() == query else 'state_match'
            })
    
    # Process county matches
    for city in exact_county_matches:
        if len(suggestions) >= limit:
            break
        county_label = f"{city.county_name}, {city.state_id}"
        if county_label not in seen_labels:
            seen_labels.add(county_label)
            suggestions.append({
                'label': county_label,
                'value': county_label,
                'type': 'county',
                'state': city.state_name,
                'state_id': city.state_id,
                'county': city.county_name,
                'city': None,
                'similarity': 1.0 if city.county_name.lower() == query else 0.85,
                'latitude': float(city.latitude) if city.latitude else None,
                'longitude': float(city.longitude) if city.longitude else None,
                'priority': 'county_match'
            })
    
    # Add fuzzy city matches if we have space
    for city in fuzzy_city_matches:
        if len(suggestions) >= limit:
            break
        label = f"{city.city}, {city.state_id}"
        if label not in seen_labels:
            seen_labels.add(label)
            # Calculate similarity for fuzzy matches
            similarity = difflib.SequenceMatcher(None, query, city.city.lower()).ratio()
            if similarity > 0.5:  # Only include reasonable matches
                suggestions.append({
                    'label': label,
                    'value': label,
                    'type': 'city',
                    'state': city.state_name,
                    'state_id': city.state_id,
                    'county': city.county_name,
                    'city': city.city,
                    'similarity': similarity,
                    'latitude': float(city.latitude) if city.latitude else None,
                    'longitude': float(city.longitude) if city.longitude else None,
                    'priority': 'fuzzy'
                })
    
    # Sort by priority and similarity
    priority_order = {'exact': 0, 'startswith': 1, 'state_match': 2, 'county_match': 3, 'fuzzy': 4}
    suggestions.sort(key=lambda x: (priority_order.get(x['priority'], 5), -x['similarity']))
    
    # Limit final results
    suggestions = suggestions[:limit]
    
    # Cache results for 30 minutes
    cache.set(cache_key, suggestions, 1800)
    
    return suggestions


@lru_cache(maxsize=128)
def get_state_counties(state_abbrev: str) -> List[str]:
    """
    Get list of counties for a given state.
    Cached for performance.
    
    Args:
        state_abbrev: State abbreviation (e.g., 'CA', 'NY')
        
    Returns:
        List of county names
    """
    from .models import Venue
    
    # Convert state abbreviation to state name
    state_name = None
    for abbrev, name in Venue.STATE_CHOICES:
        if abbrev == state_abbrev:
            state_name = name
            break
    
    if not state_name:
        return []
    
    return list(
        USCity.objects.filter(state_name__iexact=state_name)
        .values_list('county_name', flat=True)
        .distinct()
        .order_by('county_name')
    )


@lru_cache(maxsize=256)
def get_county_cities(state_abbrev: str, county_name: str) -> List[str]:
    """
    Get list of cities for a given state and county.
    Cached for performance.
    
    Args:
        state_abbrev: State abbreviation
        county_name: County name
        
    Returns:
        List of city names
    """
    from .models import Venue
    
    # Convert state abbreviation to state name
    state_name = None
    for abbrev, name in Venue.STATE_CHOICES:
        if abbrev == state_abbrev:
            state_name = name
            break
    
    if not state_name:
        return []
    
    return list(
        USCity.objects.filter(
            state_name__iexact=state_name,
            county_name__iexact=county_name
        )
        .values_list('city', flat=True)
        .distinct()
        .order_by('city')
    )


def validate_location_combination(state: str, county: str, city: str) -> Tuple[bool, Optional[USCity], str]:
    """
    Enhanced validation that state/county/city combination exists in USCity database.
    Includes better error messages and performance optimizations.
    
    Args:
        state: State abbreviation (e.g., 'CA', 'NY')
        county: County name
        city: City name
        
    Returns:
        Tuple of (is_valid, uscity_instance, error_message)
    """
    if not all([state, county, city]):
        return False, None, _("State, county, and city are all required")
    
    # Clean inputs
    state = state.strip().upper()
    county = county.strip()
    city = city.strip()
    
    # Convert state abbreviation to state name for USCity lookup
    from .models import Venue
    state_name = None
    for abbrev, name in Venue.STATE_CHOICES:
        if abbrev == state:
            state_name = name
            break
    
    if not state_name:
        return False, None, _("Invalid state abbreviation")
    
    # Use optimized query with indexes
    exact_match = USCity.objects.filter(
        state_name__iexact=state_name,
        county_name__iexact=county,
        city__iexact=city
    ).first()
    
    if exact_match:
        return True, exact_match, ""
    
    # Enhanced error reporting with suggestions
    
    # Check if state exists at all (should always be true for valid state codes)
    state_exists = USCity.objects.filter(state_name__iexact=state_name).exists()
    if not state_exists:
        return False, None, _(f"No location data available for {state_name}")
    
    # Check if county exists in the state
    county_exists = USCity.objects.filter(
        state_name__iexact=state_name,
        county_name__iexact=county
    ).exists()
    
    if not county_exists:
        # Get similar county names
        counties_in_state = get_state_counties(state)
        close_counties = difflib.get_close_matches(
            county, counties_in_state, n=3, cutoff=0.6
        )
        if close_counties:
            suggestions = ", ".join(close_counties)
            return False, None, _(f"County '{county}' not found in {state_name}. Did you mean: {suggestions}?")
        else:
            return False, None, _(f"County '{county}' does not exist in {state_name}")
    
    # County exists, check city
    cities_in_county = get_county_cities(state, county)
    if city not in [c.lower() for c in cities_in_county]:
        # Try fuzzy matching for city
        close_cities = difflib.get_close_matches(
            city, cities_in_county, n=3, cutoff=0.6
        )
        if close_cities:
            suggestions = ", ".join(close_cities)
            return False, None, _(f"City '{city}' not found in {county} County. Did you mean: {suggestions}?")
        else:
            return False, None, _(f"City '{city}' does not exist in {county} County, {state_name}")
    
    # This shouldn't happen if our logic is correct, but just in case
    return False, None, _("Location combination not found in database")


def find_matching_uscity(state: str, county: str, city: str) -> Optional[USCity]:
    """
    Find the USCity record that matches the given location information.
    Enhanced with caching for better performance.
    
    Args:
        state: State abbreviation
        county: County name  
        city: City name
        
    Returns:
        USCity instance if found, None otherwise
    """
    # Create cache key
    cache_key = f"uscity_match_{state}_{county}_{city}".lower().replace(' ', '_')
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        return cached_result
    
    is_valid, uscity, _ = validate_location_combination(state, county, city)
    result = uscity if is_valid else None
    
    # Cache for 1 hour
    cache.set(cache_key, result, 3600)
    return result


def auto_link_venue_to_uscity(venue: 'Venue') -> bool:
    """
    Enhanced auto-linking with better error handling and logging.
    
    Args:
        venue: Venue instance to link
        
    Returns:
        True if successfully linked, False otherwise
    """
    if venue.us_city:
        return True  # Already linked
    
    if not all([venue.state, venue.county, venue.city]):
        return False  # Missing required location data
    
    uscity = find_matching_uscity(venue.state, venue.county, venue.city)
    if uscity:
        venue.us_city = uscity
        
        # Also update lat/lng if not set and USCity has coordinates
        if uscity.latitude and uscity.longitude:
            if not venue.latitude or not venue.longitude:
                venue.latitude = uscity.latitude
                venue.longitude = uscity.longitude
        
        # Update only the necessary fields to avoid full model validation
        update_fields = ['us_city']
        if venue.latitude and venue.longitude:
            update_fields.extend(['latitude', 'longitude'])
            
        venue.save(update_fields=update_fields)
        return True
    
    return False


def bulk_link_venues_to_uscity(batch_size: int = 100):
    """
    Bulk link venues to USCity records for better performance.
    Useful for data migration or maintenance tasks.
    
    Args:
        batch_size: Number of venues to process in each batch
        
    Returns:
        Dictionary with statistics about the linking process
    """
    from .models import Venue
    
    stats = {
        'processed': 0,
        'linked': 0,
        'already_linked': 0,
        'failed': 0,
        'missing_data': 0
    }
    
    # Get venues without USCity links
    unlinked_venues = Venue.objects.filter(us_city__isnull=True).iterator(chunk_size=batch_size)
    
    for venue in unlinked_venues:
        stats['processed'] += 1
        
        if not all([venue.state, venue.county, venue.city]):
            stats['missing_data'] += 1
            continue
        
        if auto_link_venue_to_uscity(venue):
            stats['linked'] += 1
        else:
            stats['failed'] += 1
    
    return stats


def get_location_suggestions(query, limit=10):
    """
    Legacy function maintained for backward compatibility.
    Now uses enhanced fuzzy matching with better performance.
    """
    return get_location_suggestions_with_fuzzy_matching(query, limit)


def get_hierarchical_location_data():
    """
    Get hierarchical location data for dropdowns with enhanced organization.
    
    Returns:
        dict: Dictionary containing states, counties, and cities with improved structure
    """
    # Cache this data since it's frequently accessed
    cache_key = 'hierarchical_location_data'
    location_data = cache.get(cache_key)
    
    if location_data is None:
        states = USCity.objects.values_list('state_name', 'state_id').distinct().order_by('state_name')
        
        location_data = {
            'states': [{'name': name, 'id': state_id} for name, state_id in states],
            'counties': {},
            'cities': {},
            'state_mapping': {}  # For quick lookups
        }
        
        # Build state mapping for quick lookups
        for state_name, state_id in states:
            location_data['state_mapping'][state_id] = state_name
            
            # Get counties for this state
            counties = USCity.objects.filter(
                state_name=state_name
            ).values_list('county_name', flat=True).distinct().order_by('county_name')
            location_data['counties'][state_id] = list(counties)
            
            # Get cities for each county in this state
            for county in counties:
                cities = USCity.objects.filter(
                    state_name=state_name,
                    county_name=county
                ).values_list('city', flat=True).distinct().order_by('city')
                location_data['cities'][f"{state_id}_{county}"] = list(cities)
        
        # Cache for 24 hours
        cache.set(cache_key, location_data, 86400)
    
    return location_data


def apply_search_filters(venues, search_params):

    # Keyword search
    if search_params.get('query'):
        query = search_params['query']
        venues = venues.filter(
            Q(venue_name__icontains=query) |
            Q(short_description__icontains=query) |
            Q(tags__icontains=query) |
            Q(services__service_title__icontains=query) |
            Q(services__short_description__icontains=query) |
            Q(categories__category_name__icontains=query) |
            Q(service_provider__business_name__icontains=query)
        ).distinct()
    
    # Location search
    if search_params.get('location'):
        location = search_params['location']
        city_matches = USCity.objects.filter(
            Q(city__icontains=location) |
            Q(state_name__icontains=location) |
            Q(county_name__icontains=location) |
            Q(state_id__iexact=location)
        )
        
        if city_matches.exists():
            venues = venues.filter(
                Q(us_city__in=city_matches) |
                Q(city__icontains=location) |
                Q(state__icontains=location) |
                Q(county__icontains=location)
            ).distinct()
        else:
            venues = venues.filter(
                Q(city__icontains=location) |
                Q(state__icontains=location) |
                Q(county__icontains=location)
            )
    
    # Category filtering
    if search_params.get('category'):
        venues = venues.filter(categories__slug=search_params['category'])
    
    return venues


def apply_advanced_filters(venues, filter_params):

    # Venue type filtering
    if filter_params.get('venue_type'):
        venues = venues.filter(venue_type=filter_params['venue_type'])
    
    # Discount filtering
    if filter_params.get('has_discount'):
        venues = venues.filter(has_discount=True)
    
    # Price range filtering
    if filter_params.get('price_min') is not None:
        venues = venues.filter(min_service_price__gte=filter_params['price_min'])
    
    if filter_params.get('price_max') is not None:
        venues = venues.filter(max_service_price__lte=filter_params['price_max'])
    
    # Hierarchical location filters
    if filter_params.get('state'):
        venues = venues.filter(
            Q(state__iexact=filter_params['state']) | 
            Q(us_city__state_name__iexact=filter_params['state'])
        )
    
    if filter_params.get('county'):
        venues = venues.filter(
            Q(county__iexact=filter_params['county']) | 
            Q(us_city__county_name__iexact=filter_params['county'])
        )
    
    if filter_params.get('city'):
        venues = venues.filter(
            Q(city__iexact=filter_params['city']) | 
            Q(us_city__city__iexact=filter_params['city'])
        )
    
    # Multiple category filtering
    if filter_params.get('categories'):
        venues = venues.filter(categories__in=filter_params['categories']).distinct()
    
    return venues


def apply_sorting(venues, sort_by):

    if sort_by == 'rating_high':
        return venues.order_by('-avg_rating', '-review_count')
    elif sort_by == 'rating_low':
        return venues.order_by('avg_rating', 'review_count')
    elif sort_by == 'price_high':
        return venues.order_by('-min_service_price')
    elif sort_by == 'price_low':
        return venues.order_by('min_service_price')
    elif sort_by == 'newest':
        return venues.order_by('-created_at')
    elif sort_by == 'discount':
        return venues.order_by('-has_discount', '-min_service_price')
    elif sort_by == 'name':
        return venues.order_by('venue_name')
    else:
        # Default sorting: highest rated first, then by review count
        return venues.order_by('-avg_rating', '-review_count', 'venue_name')


def get_search_context_data(venues, search_form, filter_form, applied_filters, total_venues):
    # Get filter options
    categories = Category.objects.filter(is_active=True).order_by('name')
    states = USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')
    
    # Get price range
    price_range = Service.objects.aggregate(
        min_price=Min('price_min'),
        max_price=Max('price_max')
    )
    
    return {
        'venues': venues,
        'search_form': search_form,
        'filter_form': filter_form,
        'categories': categories,
        'states': states,
        'applied_filters': applied_filters,
        'total_venues': total_venues,
        'price_range': price_range,
    }


def get_popular_searches():
    popular_categories = Category.objects.filter(
        is_active=True
    ).annotate(
        venue_count=Count('venues', filter=Q(venues__approval_status=Venue.APPROVED) & ~Q(venues__approval_status=Venue.DRAFT))
    ).order_by('-venue_count')[:6]

    popular_locations = USCity.objects.filter(
        venues__approval_status=Venue.APPROVED
    ).exclude(
        venues__approval_status=Venue.DRAFT
    ).annotate(
        venue_count=Count('venues')
    ).order_by('-venue_count')[:6]
    
    return {
        'popular_categories': popular_categories,
        'popular_locations': popular_locations,
    }


# === Enhanced Business Information Validation ===

def validate_phone_number(phone_number: str) -> str:
    """
    Enhanced phone number validation with format standardization and international support.
    
    Args:
        phone_number: Raw phone number string
        
    Returns:
        str: Cleaned and formatted phone number
        
    Raises:
        ValidationError: If phone number is invalid
    """
    if not phone_number:
        return phone_number
    
    # Clean input - preserve original for international numbers
    original_phone = phone_number.strip()
    
    # Remove all non-digit characters for validation
    digits_only = re.sub(r'[^\d]', '', phone_number)
    
    # Basic length validation
    if len(digits_only) < 10:
        raise ValidationError(_('Phone number must contain at least 10 digits'))
    
    if len(digits_only) > 15:
        raise ValidationError(_('Phone number cannot exceed 15 digits (including country code)'))
    
    # Enhanced US phone number patterns
    us_patterns = [
        # Standard 10-digit US numbers
        r'^(\d{3})(\d{3})(\d{4})$',
        # US numbers with country code 1
        r'^1(\d{3})(\d{3})(\d{4})$',
        # Formatted US numbers
        r'^\+?1?\s*\(?(\d{3})\)?\s*[-.\s]*(\d{3})[-.\s]*(\d{4})$',
    ]
    
    # Check for US phone patterns first
    for pattern in us_patterns:
        match = re.match(pattern, digits_only)
        if match:
            if len(digits_only) == 11 and digits_only.startswith('1'):
                # Remove country code 1 and format
                groups = match.groups()
                if len(groups) == 4:  # Pattern with country code captured
                    area_code, exchange, number = groups[1], groups[2], groups[3]
                else:
                    area_code, exchange, number = groups
                return f"({area_code}) {exchange}-{number}"
            elif len(digits_only) == 10:
                groups = match.groups()
                area_code, exchange, number = groups
                return f"({area_code}) {exchange}-{number}"
    
    # Enhanced validation for US numbers
    if len(digits_only) == 10 or (len(digits_only) == 11 and digits_only.startswith('1')):
        # Extract area code and exchange for US validation
        if len(digits_only) == 11:
            area_code = digits_only[1:4]
            exchange = digits_only[4:7]
        else:
            area_code = digits_only[:3]
            exchange = digits_only[3:6]
        
        # US phone number validation rules
        if area_code.startswith('0') or area_code.startswith('1'):
            raise ValidationError(_('US area code cannot start with 0 or 1'))
        
        if exchange.startswith('0') or exchange.startswith('1'):
            raise ValidationError(_('US exchange code cannot start with 0 or 1'))
        
        if area_code == '555' and exchange[0] == '0':
            raise ValidationError(_('Invalid phone number format'))
    
    # International numbers (basic validation)
    if len(digits_only) >= 10 and len(digits_only) <= 15:
        # For international numbers, perform basic format checks
        if original_phone.startswith('+'):
            # Valid international format starting with +
            return original_phone
        elif len(digits_only) > 11:
            # Likely international number without + prefix
            return f"+{digits_only}"
        else:
            # Return cleaned format for valid length numbers
            return original_phone.strip()
    
    raise ValidationError(_('Please enter a valid phone number (e.g., (************* or ****** 123 4567)'))


def validate_business_email(email: str) -> str:
    """
    Enhanced email validation for business contexts.
    
    Args:
        email: Email address to validate
        
    Returns:
        str: Validated email address
        
    Raises:
        ValidationError: If email is invalid or from non-business domain
    """
    if not email:
        return email
    
    email = email.strip().lower()
    
    # Django's basic email validation is already done by EmailField
    # Add business-specific validation
    
    # Common personal email domains to flag
    personal_domains = {
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'live.com',
        'icloud.com', 'me.com', 'aol.com', 'mail.com', 'ymail.com',
        'rocketmail.com', 'protonmail.com', 'proton.me'
    }
    
    domain = email.split('@')[-1] if '@' in email else ''
    
    if domain in personal_domains:
        # Warning but don't prevent - some small businesses use personal emails
        pass
    
    # Additional validations
    if len(email) > 254:
        raise ValidationError(_('Email address is too long'))
    
    if '..' in email:
        raise ValidationError(_('Email address cannot contain consecutive dots'))
    
    return email


def validate_website_url(url: str, check_accessibility: bool = False) -> str:
    """
    Enhanced website URL validation with optional accessibility checking.
    
    Args:
        url: Website URL to validate
        check_accessibility: Whether to verify URL is accessible (default: False for performance)
        
    Returns:
        str: Validated and formatted URL
        
    Raises:
        ValidationError: If URL is invalid or inaccessible
    """
    if not url:
        return url
    
    url = url.strip()
    
    # Auto-add https if no protocol specified
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Parse URL for validation
    try:
        parsed = urlparse(url)
    except Exception:
        raise ValidationError(_('Please enter a valid website URL'))
    
    # Validate URL components
    if not parsed.netloc:
        raise ValidationError(_('Please enter a valid website URL'))
    
    # Check for valid domain format
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    if not re.match(domain_pattern, parsed.netloc.split(':')[0]):
        raise ValidationError(_('Please enter a valid domain name'))
    
    # Optional accessibility check (disabled by default for performance)
    if check_accessibility:
        try:
            response = requests.head(url, timeout=5, allow_redirects=True)
            if response.status_code >= 400:
                raise ValidationError(_('Website URL appears to be inaccessible (HTTP %d)') % response.status_code)
        except (RequestException, Timeout):
            # Don't fail validation for accessibility issues - just log
            pass
    
    return url


def get_approval_progress(venue: 'Venue') -> Dict[str, any]:
    """
    Calculate venue setup completion progress for approval workflow.
    
    Args:
        venue: Venue instance
        
    Returns:
        Dict with progress information
    """
    progress_items = {
        'basic_info': {
            'name': 'Basic Information',
            'completed': bool(venue.venue_name and venue.short_description),
            'weight': 20
        },
        'location': {
            'name': 'Location Details',
            'completed': bool(venue.state and venue.county and venue.city and 
                           venue.street_number and venue.street_name),
            'weight': 15
        },
        'contact': {
            'name': 'Contact Information',
            'completed': bool(venue.phone or venue.email),
            'weight': 15
        },
        'categories': {
            'name': 'Business Categories',
            'completed': venue.categories.exists(),
            'weight': 10
        },
        'main_image': {
            'name': 'Main Image',
            'completed': bool(venue.main_image),
            'weight': 15
        },
        'gallery_images': {
            'name': 'Gallery Images',
            'completed': venue.images.filter(is_active=True).count() >= 2,
            'weight': 10
        },
        'services': {
            'name': 'Services',
            'completed': venue.services.filter(is_active=True).count() >= 1,
            'weight': 10
        },
        'operating_hours': {
            'name': 'Operating Hours',
            'completed': venue.operating_hours_set.exists() or bool(venue.operating_hours),
            'weight': 5
        }
    }
    
    completed_weight = sum(item['weight'] for item in progress_items.values() if item['completed'])
    total_weight = sum(item['weight'] for item in progress_items.values())
    completion_percentage = int((completed_weight / total_weight) * 100)
    
    return {
        'items': progress_items,
        'completion_percentage': completion_percentage,
        'completed_weight': completed_weight,
        'total_weight': total_weight,
        'is_ready_for_approval': completion_percentage >= 80  # 80% completion required
    }


def check_auto_approval_criteria(venue: 'Venue') -> Tuple[bool, List[str]]:
    """
    Check if venue meets criteria for automatic approval.
    
    Args:
        venue: Venue instance
        
    Returns:
        Tuple of (meets_criteria, list_of_reasons)
    """
    criteria_checks = []
    reasons = []
    
    # Progress completion check
    progress = get_approval_progress(venue)
    meets_completion = progress['completion_percentage'] >= 90
    criteria_checks.append(meets_completion)
    if not meets_completion:
        reasons.append(f"Venue setup only {progress['completion_percentage']}% complete (90% required)")
    
    # Has contact information
    has_contact = bool(venue.phone and venue.email)
    criteria_checks.append(has_contact)
    if not has_contact:
        reasons.append("Both phone and email contact required")
    
    # Has website (preferred but not required for auto-approval)
    # has_website = bool(venue.website_url)
    
    # Has adequate images
    has_images = bool(venue.main_image) and venue.images.filter(is_active=True).count() >= 3
    criteria_checks.append(has_images)
    if not has_images:
        reasons.append("Requires main image and at least 3 gallery images")
    
    # Has services
    has_services = venue.services.filter(is_active=True).count() >= 2
    criteria_checks.append(has_services)
    if not has_services:
        reasons.append("At least 2 active services required")
    
    # Location validation
    has_valid_location = bool(venue.us_city and venue.latitude and venue.longitude)
    criteria_checks.append(has_valid_location)
    if not has_valid_location:
        reasons.append("Valid location with coordinates required")
    
    # No recent flags
    recent_flags = venue.flags.filter(
        status__in=['pending', 'reviewed'],
        created_at__gte=timezone.now() - timedelta(days=30)
    ).exists()
    criteria_checks.append(not recent_flags)
    if recent_flags:
        reasons.append("Has unresolved flags in the last 30 days")
    
    meets_all_criteria = all(criteria_checks)
    
    return meets_all_criteria, reasons


def requires_reapproval(venue: 'Venue', changed_fields: List[str]) -> bool:
    """
    Determine if venue changes require re-approval.
    
    Args:
        venue: Venue instance
        changed_fields: List of field names that changed
        
    Returns:
        bool: True if re-approval is required
    """
    # Critical fields that require re-approval
    critical_fields = {
        'venue_name', 'short_description', 'state', 'county', 'city',
        'street_number', 'street_name', 'phone', 'email'
    }
    
    # Check if any critical field was changed
    return bool(set(changed_fields) & critical_fields)


# === Enhanced Error Handling & User Experience ===

class VenueValidationError(Exception):
    """Custom exception for venue validation errors with user-friendly messages."""
    def __init__(self, message, field=None, code=None, suggestions=None):
        self.message = message
        self.field = field
        self.code = code
        self.suggestions = suggestions or []
        super().__init__(self.message)

class ErrorHandler:
    """Enhanced error handling with retry mechanisms and user-friendly messages."""
    
    @staticmethod
    def get_user_friendly_error(error, field_name=None):
        """Convert technical errors to user-friendly messages with actionable advice."""
        error_messages = {
            # Location validation errors
            'location_not_found': {
                'message': 'We couldn\'t find this location in our database.',
                'suggestions': [
                    'Check the spelling of your city and county',
                    'Try using the full county name (e.g., "Los Angeles County")',
                    'Contact support if your location should be available'
                ]
            },
            'address_incomplete': {
                'message': 'Please provide complete address information.',
                'suggestions': [
                    'Include both street number and street name',
                    'Make sure all location fields are filled',
                    'Double-check for typos in your address'
                ]
            },
            
            # Image upload errors  
            'image_too_large': {
                'message': 'Image file is too large. Please use a smaller image.',
                'suggestions': [
                    'Resize your image to be under 5MB',
                    'Use image compression tools',
                    'Try uploading a different image format (JPG or PNG)'
                ]
            },
            'invalid_image_format': {
                'message': 'Please upload a valid image file.',
                'suggestions': [
                    'Only JPG and PNG images are supported',
                    'Make sure your file isn\'t corrupted',
                    'Try uploading a different image'
                ]
            },
            
            # Business validation errors
            'venue_name_taken': {
                'message': 'This venue name is already in use.',
                'suggestions': [
                    'Try adding your location to make it unique',
                    'Use your business\'s full legal name',
                    'Consider adding a descriptor (e.g., "Downtown Spa")'
                ]
            },
            'invalid_phone': {
                'message': 'Please enter a valid phone number.',
                'suggestions': [
                    'Include area code (e.g., (*************)',
                    'Use only numbers, spaces, parentheses, and dashes',
                    'Make sure it\'s a 10-digit US phone number'
                ]
            },
            'invalid_website': {
                'message': 'Please enter a valid website URL.',
                'suggestions': [
                    'Include http:// or https:// at the beginning',
                    'Check for typos in your domain name',
                    'Make sure your website is accessible'
                ]
            },
            
            # Service-related errors
            'no_services': {
                'message': 'Please add at least one service to your venue.',
                'suggestions': [
                    'Describe the main services you offer',
                    'Include pricing information',
                    'Add service duration and descriptions'
                ]
            },
            'invalid_price': {
                'message': 'Please enter a valid price amount.',
                'suggestions': [
                    'Use numbers only (e.g., 75.00)',
                    'Don\'t include dollar signs or other symbols',
                    'Make sure the price is reasonable for your service'
                ]
            },
            
            # Operating hours errors
            'invalid_hours': {
                'message': 'Please check your operating hours.',
                'suggestions': [
                    'Make sure opening time is before closing time',
                    'Use 24-hour format or AM/PM clearly',
                    'Set hours for at least one day of the week'
                ]
            },
            
            # General errors
            'network_error': {
                'message': 'Connection issue - please try again.',
                'suggestions': [
                    'Check your internet connection',
                    'Wait a moment and try again',
                    'Contact support if the problem persists'
                ]
            },
            'server_error': {
                'message': 'Something went wrong on our end.',
                'suggestions': [
                    'Please try again in a few minutes',
                    'Contact support if this keeps happening',
                    'Your progress has been saved'
                ]
            }
        }
        
        # Get error type from exception or use default
        error_type = getattr(error, 'code', 'server_error')
        error_info = error_messages.get(error_type, error_messages['server_error'])
        
        return {
            'message': error_info['message'],
            'suggestions': error_info['suggestions'],
            'field': field_name,
            'original_error': str(error) if hasattr(error, '__str__') else 'Unknown error'
        }
    
    @staticmethod
    def create_retry_operation(operation, max_retries=3, delay_factor=1):
        """Create a retry wrapper for operations that might fail."""
        def retry_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return operation(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        # Exponential backoff delay
                        delay = delay_factor * (2 ** attempt)
                        import time
                        time.sleep(delay)
                        continue
                    else:
                        # All retries exhausted
                        break
            
            # Return user-friendly error info
            return ErrorHandler.get_user_friendly_error(last_exception)
        
        return retry_wrapper

class ValidationMessages:
    """Centralized, user-friendly validation messages."""
    
    VENUE_CREATION = {
        'venue_name': {
            'required': 'Please enter a name for your venue.',
            'min_length': 'Venue name should be at least 2 characters long.',
            'max_length': 'Venue name cannot exceed 255 characters.',
            'invalid': 'Please use only letters, numbers, and basic punctuation in your venue name.'
        },
        'short_description': {
            'required': 'Please provide a description of your venue.',
            'min_length': 'Description should be at least 10 characters to give customers a good overview.',
            'max_length': 'Description cannot exceed 500 characters. Try to be more concise.',
            'invalid': 'Please avoid using special characters or HTML in your description.'
        },
        'categories': {
            'required': 'Please select at least one category that describes your venue.',
            'max_selections': 'You can select up to 3 categories maximum.',
            'invalid_choice': 'Please select from the available category options.'
        },
        'location': {
            'state_required': 'Please select your state.',
            'county_required': 'Please enter your county name.',
            'city_required': 'Please enter your city name.',
            'street_number_required': 'Please enter your street number.',
            'street_name_required': 'Please enter your street name.',
            'location_not_found': 'We couldn\'t verify this location. Please check your address details.',
            'address_mismatch': 'The address you entered doesn\'t match our location database. Please verify and try again.'
        },
        'contact': {
            'phone_invalid': 'Please enter a valid US phone number (e.g., (*************).',
            'email_invalid': 'Please enter a valid email address.',
            'website_invalid': 'Please enter a valid website URL starting with http:// or https://.'
        },
        'images': {
            'size_too_large': 'Image files must be smaller than 5MB each.',
            'invalid_format': 'Please upload JPG or PNG images only.',
            'upload_failed': 'Image upload failed. Please try again with a different image.',
            'main_image_required': 'Please upload at least one main image for your venue.'
        },
        'services': {
            'min_services': 'Please add at least one service to help customers understand what you offer.',
            'invalid_price': 'Please enter a valid price (numbers only, e.g., 75.00).',
            'price_too_high': 'Price seems unusually high. Please double-check the amount.',
            'price_too_low': 'Price must be at least $1.00.',
            'duration_invalid': 'Service duration must be between 1 minute and 24 hours.'
        },
        'operating_hours': {
            'no_hours_set': 'Please set operating hours for at least one day.',
            'invalid_time_format': 'Please use valid time format (e.g., 9:00 AM or 21:00).',
            'closing_before_opening': 'Closing time must be after opening time.',
            'hours_too_long': 'Daily operating hours cannot exceed 24 hours.'
        }
    }
    
    @classmethod
    def get_message(cls, field, error_type, context=None):
        """Get a user-friendly validation message."""
        try:
            messages = cls.VENUE_CREATION.get(field, {})
            message = messages.get(error_type, f'Please check your {field.replace("_", " ")}.')
            
            # Add context if available
            if context:
                message = message.format(**context)
                
            return message
        except (KeyError, AttributeError):
            return f'Please check your {field.replace("_", " ")} and try again.'

def handle_venue_creation_error(error, field_name=None, user_context=None):
    """
    Comprehensive error handler for venue creation process.
    Returns structured error information for better UX.
    """
    from django.core.exceptions import ValidationError
    from django.db import IntegrityError
    import json
    
    error_response = {
        'success': False,
        'error_type': 'unknown',
        'message': 'An unexpected error occurred.',
        'suggestions': [],
        'field': field_name,
        'retry_allowed': True,
        'context': user_context or {}
    }
    
    # Handle Django ValidationError
    if isinstance(error, ValidationError):
        if hasattr(error, 'error_dict'):
            # Multiple field errors
            errors = {}
            for field, field_errors in error.error_dict.items():
                field_messages = []
                for field_error in field_errors:
                    message = ValidationMessages.get_message(field, field_error.code or 'invalid')
                    field_messages.append(message)
                errors[field] = field_messages
            
            error_response.update({
                'error_type': 'validation',
                'field_errors': errors,
                'message': 'Please correct the errors below and try again.'
            })
        else:
            # Single error
            error_response.update({
                'error_type': 'validation',
                'message': str(error.message) if hasattr(error, 'message') else str(error)
            })
    
    # Handle database integrity errors
    elif isinstance(error, IntegrityError):
        if 'venues_venue.slug' in str(error):
            error_response.update({
                'error_type': 'duplicate_venue',
                'message': 'A venue with similar information already exists.',
                'suggestions': [
                    'Try using a more specific venue name',
                    'Add your location or specialty to make it unique',
                    'Contact support if you believe this is an error'
                ],
                'retry_allowed': False
            })
        else:
            error_response.update({
                'error_type': 'database',
                'message': 'There was a problem saving your venue information.',
                'suggestions': [
                    'Please try again in a moment',
                    'Check that all required fields are filled',
                    'Contact support if this continues'
                ]
            })
    
    # Handle file upload errors
    elif 'image' in str(error).lower() or 'file' in str(error).lower():
        error_response.update({
            'error_type': 'file_upload',
            'message': 'There was a problem uploading your image.',
            'suggestions': [
                'Make sure your image is under 5MB',
                'Use JPG or PNG format only',
                'Try uploading a different image'
            ]
        })
    
    # Handle network/connection errors
    elif 'timeout' in str(error).lower() or 'connection' in str(error).lower():
        error_response.update({
            'error_type': 'network',
            'message': 'Connection issue - please try again.',
            'suggestions': [
                'Check your internet connection',
                'Wait a moment and try again',
                'Your progress has been saved'
            ]
        })
    
    # Generic error fallback
    else:
        error_response.update({
            'message': 'Something went wrong. Please try again.',
            'suggestions': [
                'Refresh the page and try again',
                'Check your internet connection',
                'Contact support if this problem continues'
            ]
        })
    
    # Add debug info in development
    import django.conf
    if django.conf.settings.DEBUG:
        error_response['debug_info'] = {
            'error_class': error.__class__.__name__,
            'error_message': str(error),
            'field': field_name
        }
    
    return error_response

def validate_venue_completion_status(venue):
    """
    Enhanced venue completion validation with detailed feedback.
    Returns completion status with specific improvement suggestions.
    """
    completion_data = {
        'is_complete': False,
        'completion_percentage': 0,
        'missing_items': [],
        'improvement_suggestions': [],
        'next_steps': []
    }
    
    # Check required items
    required_checks = [
        ('venue_name', 'Venue name is required'),
        ('short_description', 'Venue description is required'),
        ('state', 'State location is required'),
        ('county', 'County location is required'),
        ('city', 'City location is required'),
        ('street_number', 'Street number is required'),
        ('street_name', 'Street name is required'),
    ]
    
    completed_items = 0
    total_items = len(required_checks)
    
    for field_name, error_message in required_checks:
        if hasattr(venue, field_name) and getattr(venue, field_name):
            completed_items += 1
        else:
            completion_data['missing_items'].append({
                'field': field_name,
                'message': error_message,
                'priority': 'high'
            })
    
    # Check optional but recommended items
    recommended_checks = [
        ('phone', 'Contact phone number'),
        ('email', 'Contact email address'),
        ('main_image', 'Main venue image'),
        ('website_url', 'Website URL'),
    ]
    
    for field_name, description in recommended_checks:
        total_items += 1
        if hasattr(venue, field_name) and getattr(venue, field_name):
            completed_items += 1
        else:
            completion_data['improvement_suggestions'].append({
                'field': field_name,
                'message': f'Consider adding {description.lower()}',
                'priority': 'medium'
            })
    
    # Check related items
    if venue.services.filter(is_active=True).count() == 0:
        completion_data['missing_items'].append({
            'field': 'services',
            'message': 'At least one service is required',
            'priority': 'high'
        })
    else:
        completed_items += 1
    total_items += 1
    
    if venue.operating_hours_set.count() == 0:
        completion_data['missing_items'].append({
            'field': 'operating_hours',
            'message': 'Operating hours are required',
            'priority': 'high'
        })
    else:
        completed_items += 1
    total_items += 1
    
    # Calculate completion percentage
    completion_percentage = int((completed_items / total_items) * 100)
    completion_data['completion_percentage'] = completion_percentage
    completion_data['is_complete'] = completion_percentage >= 80
    
    # Generate next steps
    high_priority_missing = [item for item in completion_data['missing_items'] if item['priority'] == 'high']
    if high_priority_missing:
        completion_data['next_steps'] = [
            f"Complete {item['message'].lower()}" for item in high_priority_missing[:3]
        ]
    else:
        medium_priority_items = [item for item in completion_data['improvement_suggestions'] if item['priority'] == 'medium']
        completion_data['next_steps'] = [
            f"Add {item['message'].lower()}" for item in medium_priority_items[:2]
        ]
    
    return completion_data

def validate_social_media_url(url: str, platform: str) -> str:
    """
    Enhanced social media URL validation with platform-specific rules.
    
    Args:
        url: Social media URL to validate
        platform: Platform name (instagram, facebook, twitter, linkedin)
        
    Returns:
        str: Validated and formatted URL
        
    Raises:
        ValidationError: If URL is invalid for the platform
    """
    if not url:
        return url
    
    url = url.strip()
    
    # Platform-specific validation patterns
    patterns = {
        'instagram': {
            'pattern': r'^https?://(www\.)?instagram\.com/[a-zA-Z0-9._]+/?$',
            'prefix': 'https://www.instagram.com/',
            'name': 'Instagram'
        },
        'facebook': {
            'pattern': r'^https?://(www\.)?facebook\.com/[a-zA-Z0-9.]+/?$',
            'prefix': 'https://www.facebook.com/',
            'name': 'Facebook'
        },
        'twitter': {
            'pattern': r'^https?://(www\.)?(twitter\.com|x\.com)/[a-zA-Z0-9_]+/?$',
            'prefix': 'https://twitter.com/',
            'name': 'Twitter/X'
        },
        'linkedin': {
            'pattern': r'^https?://(www\.)?linkedin\.com/(company|in)/[a-zA-Z0-9-]+/?$',
            'prefix': 'https://www.linkedin.com/company/',
            'name': 'LinkedIn'
        }
    }
    
    if platform not in patterns:
        raise ValidationError(f'Unsupported social media platform: {platform}')
    
    config = patterns[platform]
    
    # Auto-format URL if needed
    if not url.startswith('http'):
        if url.startswith('@'):
            # Handle @username format
            username = url[1:]
            url = config['prefix'] + username
        elif '/' not in url:
            # Handle plain username
            url = config['prefix'] + url
        else:
            # Add https prefix
            url = 'https://' + url
    
    # Validate against platform pattern
    if not re.match(config['pattern'], url, re.IGNORECASE):
        raise ValidationError(
            _(f'Please enter a valid {config["name"]} URL (e.g., {config["prefix"]}yourbusiness)')
        )
    
    return url

def sync_contact_from_service_provider(venue: 'Venue') -> Dict[str, str]:
    """
    Sync contact information from service provider profile to venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        Dict with synced contact information
    """
    if not venue.service_provider:
        return {}
    
    provider = venue.service_provider
    synced_data = {}
    
    # Sync basic contact info
    if provider.phone and not venue.phone:
        synced_data['phone'] = provider.phone
        venue.phone = provider.phone
    
    if provider.email and not venue.email:
        synced_data['email'] = provider.email
        venue.email = provider.email
    
    if provider.website and not venue.website_url:
        synced_data['website_url'] = provider.website
        venue.website_url = provider.website
    
    # Sync social media links
    if provider.instagram and not venue.instagram_url:
        synced_data['instagram_url'] = provider.instagram
        venue.instagram_url = provider.instagram
    
    if provider.facebook and not venue.facebook_url:
        synced_data['facebook_url'] = provider.facebook
        venue.facebook_url = provider.facebook
    
    return synced_data

def generate_email_verification_token() -> str:
    """Generate a secure token for email verification."""
    import secrets
    return secrets.token_urlsafe(32)

# --- Enhanced Change Detection & Approval ---

class ChangeType:
    """Enumeration for different types of changes."""
    MINOR = 'minor'
    MODERATE = 'moderate'  
    MAJOR = 'major'
    CRITICAL = 'critical'

class ChangeSeverity:
    """Classification of field changes by severity level."""
    
    # Critical changes - Always require admin approval
    CRITICAL_FIELDS = {
        'venue_name', 'short_description', 'phone', 'email', 
        'street_number', 'street_name', 'city', 'county', 'state'
    }
    
    # Major changes - Usually require approval, but may have auto-approval rules
    MAJOR_FIELDS = {
        'website_url', 'venue_type', 'zip_code'
    }
    
    # Moderate changes - May require approval based on context
    MODERATE_FIELDS = {
        'instagram_url', 'facebook_url', 'twitter_url', 'linkedin_url',
        'opening_notes', 'latitude', 'longitude'
    }
    
    # Minor changes - Usually auto-approved
    MINOR_FIELDS = {
        'tags', 'visibility'
    }

    @classmethod
    def get_field_severity(cls, field_name: str) -> str:
        """Get the severity level for a field change."""
        if field_name in cls.CRITICAL_FIELDS:
            return ChangeType.CRITICAL
        elif field_name in cls.MAJOR_FIELDS:
            return ChangeType.MAJOR
        elif field_name in cls.MODERATE_FIELDS:
            return ChangeType.MODERATE
        elif field_name in cls.MINOR_FIELDS:
            return ChangeType.MINOR
        else:
            # Default to moderate for unknown fields
            return ChangeType.MODERATE

def analyze_venue_changes(venue: 'Venue', changed_fields: List[str], original_data: Dict = None) -> Dict:
    """
    Analyze venue changes and categorize them by severity.
    
    Args:
        venue: Venue instance
        changed_fields: List of field names that changed
        original_data: Optional dict of original field values
        
    Returns:
        Dict containing change analysis results
    """
    if not changed_fields:
        return {
            'has_changes': False,
            'requires_approval': False,
            'change_summary': {},
            'severity_breakdown': {},
            'approval_recommendation': 'none'
        }
    
    # Categorize changes by severity
    changes_by_severity = {
        ChangeType.CRITICAL: [],
        ChangeType.MAJOR: [],
        ChangeType.MODERATE: [],
        ChangeType.MINOR: []
    }
    
    # Analyze basic field changes
    for field in changed_fields:
        severity = ChangeSeverity.get_field_severity(field)
        changes_by_severity[severity].append({
            'field': field,
            'type': 'field_change',
            'field_label': field.replace('_', ' ').title()
        })
    
    # Check for category changes (if venue has categories)
    if hasattr(venue, 'categories') and original_data:
        current_categories = set(venue.categories.values_list('id', flat=True))
        original_categories = set(original_data.get('categories', []))
        
        if current_categories != original_categories:
            added_cats = current_categories - original_categories
            removed_cats = original_categories - current_categories
            
            if added_cats or removed_cats:
                changes_by_severity[ChangeType.MAJOR].append({
                    'field': 'categories',
                    'type': 'category_change',
                    'field_label': 'Categories',
                    'added': list(added_cats),
                    'removed': list(removed_cats)
                })
    
    # Check for amenity changes
    if hasattr(venue, 'amenities') and original_data:
        current_amenities = set(venue.amenities.filter(is_active=True).values_list('amenity_type', flat=True))
        original_amenities = set(original_data.get('amenities', []))
        
        if current_amenities != original_amenities:
            added_amenities = current_amenities - original_amenities
            removed_amenities = original_amenities - current_amenities
            
            if added_amenities or removed_amenities:
                changes_by_severity[ChangeType.MODERATE].append({
                    'field': 'amenities',
                    'type': 'amenity_change',
                    'field_label': 'Amenities',
                    'added': list(added_amenities),
                    'removed': list(removed_amenities)
                })
    
    # Determine approval requirement
    has_critical = bool(changes_by_severity[ChangeType.CRITICAL])
    has_major = bool(changes_by_severity[ChangeType.MAJOR])
    has_moderate = bool(changes_by_severity[ChangeType.MODERATE])
    
    if has_critical:
        approval_recommendation = 'required'
        requires_approval = True
    elif has_major:
        # Major changes may be auto-approved under certain conditions
        approval_recommendation = 'recommended'
        requires_approval = True
    elif has_moderate:
        # Moderate changes may be auto-approved for high-quality venues
        approval_recommendation = 'optional'
        requires_approval = can_auto_approve_moderate_changes(venue)
    else:
        # Only minor changes
        approval_recommendation = 'none'
        requires_approval = False
    
    return {
        'has_changes': True,
        'requires_approval': requires_approval,
        'change_summary': changes_by_severity,
        'severity_breakdown': {
            'critical': len(changes_by_severity[ChangeType.CRITICAL]),
            'major': len(changes_by_severity[ChangeType.MAJOR]),
            'moderate': len(changes_by_severity[ChangeType.MODERATE]),
            'minor': len(changes_by_severity[ChangeType.MINOR])
        },
        'approval_recommendation': approval_recommendation,
        'total_changes': len(changed_fields)
    }

def can_auto_approve_moderate_changes(venue: 'Venue') -> bool:
    """
    Determine if moderate changes can be auto-approved for this venue.
    
    Args:
        venue: Venue instance
        
    Returns:
        bool: False if moderate changes require manual approval
    """
    # Auto-approve moderate changes for high-quality, established venues
    criteria = [
        # Venue has been approved for a while (not recently approved)
        venue.approved_at and (timezone.now() - venue.approved_at).days > 30,
        
        # Venue has good review rating
        hasattr(venue, 'avg_rating') and getattr(venue, 'avg_rating', 0) >= 4.0,
        
        # Venue has multiple reviews
        hasattr(venue, 'review_count') and getattr(venue, 'review_count', 0) >= 5,
        
        # No recent flags or complaints
        not venue.flags.filter(
            status__in=['pending', 'reviewed'],
            created_at__gte=timezone.now() - timedelta(days=90)
        ).exists()
    ]
    
    # Require at least 3 of 4 criteria for auto-approval
    return sum(criteria) >= 3

def requires_reapproval(venue: 'Venue', changed_fields: List[str], original_data: Dict = None) -> bool:
    """
    Enhanced function to determine if venue changes require re-approval.
    
    Args:
        venue: Venue instance
        changed_fields: List of field names that changed
        original_data: Optional dict of original field values
        
    Returns:
        bool: True if re-approval is required
    """
    if not changed_fields:
        return False
    
    analysis = analyze_venue_changes(venue, changed_fields, original_data)
    return analysis['requires_approval']

def get_change_comparison_data(venue: 'Venue', original_data: Dict) -> Dict:
    """
    Generate side-by-side comparison data for venue changes.
    
    Args:
        venue: Current venue instance
        original_data: Original venue data before changes
        
    Returns:
        Dict containing comparison data for display
    """
    comparison = {
        'basic_info': {},
        'location': {},
        'contact': {},
        'categories': {},
        'amenities': {},
        'social_media': {}
    }
    
    # Basic info comparisons
    basic_fields = {
        'venue_name': 'Venue Name',
        'short_description': 'Description',
        'venue_type': 'Venue Type'
    }
    
    for field, label in basic_fields.items():
        old_value = original_data.get(field, '')
        new_value = getattr(venue, field, '')
        if old_value != new_value:
            comparison['basic_info'][field] = {
                'label': label,
                'old': old_value,
                'new': new_value,
                'changed': True
            }
    
    # Location comparisons
    location_fields = {
        'street_number': 'Street Number',
        'street_name': 'Street Name',
        'city': 'City',
        'county': 'County',
        'state': 'State',
        'zip_code': 'ZIP Code'
    }
    
    for field, label in location_fields.items():
        old_value = original_data.get(field, '')
        new_value = getattr(venue, field, '')
        if old_value != new_value:
            comparison['location'][field] = {
                'label': label,
                'old': old_value,
                'new': new_value,
                'changed': True
            }
    
    # Contact info comparisons
    contact_fields = {
        'phone': 'Phone',
        'email': 'Email',
        'website_url': 'Website'
    }
    
    for field, label in contact_fields.items():
        old_value = original_data.get(field, '')
        new_value = getattr(venue, field, '')
        if old_value != new_value:
            comparison['contact'][field] = {
                'label': label,
                'old': old_value,
                'new': new_value,
                'changed': True
            }
    
    # Category comparisons
    if 'categories' in original_data:
        try:
            from .models import Category
            old_categories = Category.objects.filter(id__in=original_data['categories'])
            new_categories = venue.categories.all()
            
            old_cat_names = set(cat.category_name for cat in old_categories)
            new_cat_names = set(cat.category_name for cat in new_categories)
            
            if old_cat_names != new_cat_names:
                comparison['categories'] = {
                    'old': list(old_cat_names),
                    'new': list(new_cat_names),
                    'added': list(new_cat_names - old_cat_names),
                    'removed': list(old_cat_names - new_cat_names),
                    'changed': True
                }
        except:
            pass
    
    # Amenity comparisons
    if 'amenities' in original_data:
        try:
            from .models import VenueAmenity
            old_amenities = set(original_data['amenities'])
            new_amenities = set(venue.amenities.filter(is_active=True).values_list('amenity_type', flat=True))
            
            if old_amenities != new_amenities:
                # Get display names for amenities
                amenity_choices = dict(VenueAmenity.AMENITY_CHOICES)
                
                comparison['amenities'] = {
                    'old': [amenity_choices.get(a, a) for a in old_amenities],
                    'new': [amenity_choices.get(a, a) for a in new_amenities],
                    'added': [amenity_choices.get(a, a) for a in new_amenities - old_amenities],
                    'removed': [amenity_choices.get(a, a) for a in old_amenities - new_amenities],
                    'changed': True
                }
        except:
            pass
    
    # Social media comparisons
    social_fields = {
        'instagram_url': 'Instagram',
        'facebook_url': 'Facebook',
        'twitter_url': 'Twitter',
        'linkedin_url': 'LinkedIn'
    }
    
    for field, label in social_fields.items():
        old_value = original_data.get(field, '')
        new_value = getattr(venue, field, '')
        if old_value != new_value:
            comparison['social_media'][field] = {
                'label': label,
                'old': old_value,
                'new': new_value,
                'changed': True
            }
    
    return comparison