"""Forms for venue flagging functionality."""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import FlaggedVenue


class ReasonSelect(forms.Select):
    """Custom select widget that adds tooltips to each option."""

    def __init__(self, *args, help_texts=None, **kwargs):
        self.help_texts = help_texts or {}
        super().__init__(*args, **kwargs)

    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex=subindex, attrs=attrs)
        if value in self.help_texts:
            option["attrs"]["title"] = self.help_texts[value]
            option["attrs"]["data-bs-toggle"] = "tooltip"
        return option


class FlaggedVenueForm(forms.ModelForm):
    """Form for customers to flag inappropriate venues for admin review."""

    REASON_CHOICES = [
        ('inappropriate_content', 'Inappropriate content or images'),
        ('false_information', 'False or misleading information'),
        ('poor_service', 'Poor service quality'),
        ('safety_concerns', 'Safety concerns'),
        ('spam', 'Spam or fake listing'),
        ('other', 'Other (please specify in details)'),
    ]

    REASON_HELP = {
        'inappropriate_content': 'Offensive text or imagery',
        'false_information': 'Misleading business details',
        'poor_service': 'Bad customer experience',
        'safety_concerns': 'Unsafe or unsanitary conditions',
        'spam': 'Duplicate or promotional listing',
        'other': 'Any other issue',
    }

    reason_category = forms.ChoiceField(
        choices=REASON_CHOICES,
        required=True,
        widget=ReasonSelect(
            attrs={'class': 'form-select', 'id': 'reason-category'},
            help_texts=REASON_HELP,
        ),
        label='Reason for flagging'
    )

    class Meta:
        model = FlaggedVenue
        fields = ['reason']
        widgets = {
            'reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Please provide specific details about why you are flagging this venue...',
                'maxlength': 1000,
                'required': True,
            }),
        }
        labels = {
            'reason': 'Additional details',
        }
        help_texts = {
            'reason': 'Please provide specific details to help our team review this venue (max 1000 characters)',
        }

    def __init__(self, *args, **kwargs):
        self.venue = kwargs.pop('venue', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        self.fields['reason'].required = True

    def clean_reason(self):
        reason = self.cleaned_data.get('reason', '').strip()
        if not reason:
            raise ValidationError(_('Please provide details about why you are flagging this venue.'))
        if len(reason) < 10:
            raise ValidationError(_('Please provide more detailed information (at least 10 characters).'))
        return reason

    def save(self, commit=True):
        flagged_venue = super().save(commit=False)
        if self.venue:
            flagged_venue.venue = self.venue
        if self.user:
            flagged_venue.flagged_by = self.user

        reason_category = self.cleaned_data.get('reason_category', '')
        reason_details = self.cleaned_data.get('reason', '')
        if reason_category:
            category_label = dict(self.REASON_CHOICES).get(reason_category, reason_category)
            flagged_venue.reason = f"Category: {category_label}\n\nDetails: {reason_details}"
        else:
            flagged_venue.reason = reason_details

        if commit:
            flagged_venue.save()
        return flagged_venue
