"""Blog management views for creating and editing posts and categories."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Count, Q
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, ListView, UpdateView

# --- Local App Imports ---
from admin_app.constants import CONTENT_SAVED_SUCCESS, CONTENT_DELETED_SUCCESS
from admin_app.forms import BlogCategoryForm, BlogPostForm
from admin_app.logging_utils import log_admin_activity
from admin_app.models import BlogCategory, BlogPost
from .common import ITEMS_PER_PAGE, is_admin_user

logger = logging.getLogger(__name__)

# Blog Management Views
class AdminBlogCategoryListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing blog categories."""

    model = BlogCategory
    template_name = 'admin_app/content/blog/category_list.html'
    context_object_name = 'categories'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        return BlogCategory.objects.order_by('name')


class AdminBlogCategoryCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for blog categories."""

    model = BlogCategory
    form_class = BlogCategoryForm
    template_name = 'admin_app/content/blog/category_create.html'
    success_url = reverse_lazy('admin_app:blog_category_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type='blog_category_created',
            request=self.request,
            details={'category_name': self.object.name, 'category_slug': self.object.slug}
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response


class AdminBlogPostListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing blog posts."""

    model = BlogPost
    template_name = 'admin_app/content/blog/post_list.html'
    context_object_name = 'posts'
    paginate_by = ITEMS_PER_PAGE
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        """Filter posts based on search parameters."""
        queryset = BlogPost.objects.select_related('category', 'author').order_by('-created_at')

        search_query = self.request.GET.get('search', '').strip()
        status = self.request.GET.get('status', '').strip()
        category = self.request.GET.get('category', '').strip()

        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(content__icontains=search_query)
            )

        if status:
            queryset = queryset.filter(status=status)

        if category:
            queryset = queryset.filter(category_id=category)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = BlogCategory.objects.filter(is_active=True)
        return context


class AdminBlogPostCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for blog posts."""

    model = BlogPost
    form_class = BlogPostForm
    template_name = 'admin_app/content/blog/post_create.html'
    success_url = reverse_lazy('admin_app:blog_post_list')
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the author and updated_by fields."""
        form.instance.author = self.request.user
        form.instance.updated_by = self.request.user

        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type='blog_post_created',
            request=self.request,
            details={'post_title': self.object.title, 'post_slug': self.object.slug}
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response


class AdminBlogPostUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """Update view for blog posts."""

    model = BlogPost
    form_class = BlogPostForm
    template_name = 'admin_app/content/blog/post_edit.html'
    success_url = reverse_lazy('admin_app:blog_post_list')
    slug_url_kwarg = 'slug'
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the updated_by field."""
        form.instance.updated_by = self.request.user

        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type='blog_post_updated',
            request=self.request,
            details={'post_title': self.object.title, 'post_slug': self.object.slug}
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response


class AdminBlogPostDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """Delete view for blog posts."""

    model = BlogPost
    template_name = 'admin_app/content/blog/post_delete.html'
    success_url = reverse_lazy('admin_app:blog_post_list')
    slug_url_kwarg = 'slug'
    login_url = 'admin_app:admin_login'

    def test_func(self):
        return is_admin_user(self.request.user)

    def delete(self, request, *args, **kwargs):
        """Log the deletion."""
        post_title = self.get_object().title
        response = super().delete(request, *args, **kwargs)

        log_admin_activity(
            admin_user=request.user,
            activity_type='blog_post_deleted',
            request=request,
            details={'post_title': post_title}
        )
        messages.success(request, CONTENT_DELETED_SUCCESS)

        return response


