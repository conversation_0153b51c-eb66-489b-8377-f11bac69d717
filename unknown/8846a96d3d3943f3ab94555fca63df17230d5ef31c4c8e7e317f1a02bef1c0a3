{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Admin Booking Dashboard - CozyWish{% endblock %}

{% block booking_content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h2>Booking Management Dashboard</h2>
            <p class="text-muted">Overview of all booking activities and statistics</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_bookings }}</h4>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ confirmed_bookings }}</h4>
                            <p class="mb-0">Confirmed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ pending_bookings }}</h4>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${{ total_revenue|floatformat:0 }}</h4>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Recent Bookings</h5>
                </div>
                <div class="card-body">
                    {% if recent_bookings %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Customer</th>
                                    <th>Venue</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in recent_bookings %}
                                <tr>
                                    <td>
                                        <a href="{% url 'booking_cart_app:admin_booking_detail' booking.slug %}">
                                            {{ booking.booking_id|truncatechars:8 }}
                                        </a>
                                    </td>
                                    <td>{{ booking.customer.email }}</td>
                                    <td>{{ booking.venue.venue_name }}</td>
                                    <td>
                                        <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>${{ booking.total_amount }}</td>
                                    <td>{{ booking.created_at|date:"M d, Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">No recent bookings found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'booking_cart_app:admin_booking_list' %}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Bookings
                        </a>
                        <a href="{% url 'booking_cart_app:admin_booking_analytics' %}" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                        <a href="{% url 'booking_cart_app:admin_dispute_list' %}" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> Disputes
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Distribution -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>Booking Status Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small>Confirmed</small>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: {% widthratio confirmed_bookings total_bookings 100 %}%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <small>Pending</small>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-warning" style="width: {% widthratio pending_bookings total_bookings 100 %}%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <small>Cancelled</small>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-danger" style="width: {% widthratio cancelled_bookings total_bookings 100 %}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
