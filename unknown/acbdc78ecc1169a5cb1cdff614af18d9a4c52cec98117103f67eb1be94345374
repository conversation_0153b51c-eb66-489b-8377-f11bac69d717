{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Delete Blog Post - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Delete Blog Post</h1>
    <a href="{% url 'admin_app:blog_post_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Posts
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone. The blog post and all associated data will be permanently deleted.
                </div>

                <h6>Post Details:</h6>
                <dl class="row">
                    <dt class="col-sm-3">Title:</dt>
                    <dd class="col-sm-9">{{ object.title }}</dd>
                    
                    <dt class="col-sm-3">Slug:</dt>
                    <dd class="col-sm-9"><code>{{ object.slug }}</code></dd>
                    
                    <dt class="col-sm-3">Category:</dt>
                    <dd class="col-sm-9">
                        {% if object.category %}
                            {{ object.category.name }}
                        {% else %}
                            <span class="text-muted">No category</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-3">Status:</dt>
                    <dd class="col-sm-9">
                        {% if object.status == 'published' %}
                            <span class="badge bg-success">Published</span>
                        {% elif object.status == 'draft' %}
                            <span class="badge bg-warning">Draft</span>
                        {% else %}
                            <span class="badge bg-secondary">Archived</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-3">Author:</dt>
                    <dd class="col-sm-9">{{ object.author.get_full_name|default:object.author.email }}</dd>
                    
                    <dt class="col-sm-3">Created:</dt>
                    <dd class="col-sm-9">{{ object.created_at|date:"M d, Y H:i" }}</dd>
                    
                    <dt class="col-sm-3">Last Updated:</dt>
                    <dd class="col-sm-9">{{ object.updated_at|date:"M d, Y H:i" }}</dd>
                </dl>

                {% if object.excerpt %}
                <h6>Excerpt:</h6>
                <p class="text-muted">{{ object.excerpt|truncatechars:200 }}</p>
                {% endif %}

                <hr>

                <p class="text-danger">
                    <strong>Are you sure you want to delete this blog post?</strong>
                </p>

                <form method="post" class="d-flex justify-content-end">
                    {% csrf_token %}
                    <a href="{% url 'admin_app:blog_post_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Yes, Delete Post
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation dialog for extra safety
    const deleteForm = document.querySelector('form[method="post"]');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            const confirmed = confirm('Are you absolutely sure you want to delete this blog post? This action cannot be undone.');
            if (!confirmed) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
