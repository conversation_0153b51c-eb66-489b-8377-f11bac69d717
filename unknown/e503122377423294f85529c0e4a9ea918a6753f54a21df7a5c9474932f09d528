from django.test import TestCase, Client, override_settings
from django.urls import reverse

@override_settings(SECURE_SSL_REDIRECT=False)
class ContentSecurityPolicyMiddlewareTest(TestCase):
    def setUp(self):
        self.client = Client()

    def test_csp_header_present(self):
        response = self.client.get(reverse('admin_app:health_check'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('Content-Security-Policy', response)
        self.assertIn("default-src 'self'", response['Content-Security-Policy'])

