"""
Management command to send a test email using SendGrid.
"""
from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings


class Command(BaseCommand):
    """Send a test email using SendGrid."""
    
    help = 'Send a test email to verify SendGrid configuration'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            'recipient_email',
            type=str,
            help='Email address to send test email to'
        )
        parser.add_argument(
            '--subject',
            type=str,
            default='CozyWish - Test Email',
            help='Email subject (default: "CozyWish - Test Email")'
        )

    def handle(self, *args, **options):
        """Execute the command."""
        recipient_email = options['recipient_email']
        subject = options['subject']
        
        self.stdout.write(
            self.style.SUCCESS(f'Sending test email to: {recipient_email}')
        )
        
        try:
            # Check if SendGrid is configured
            if not settings.EMAIL_HOST_PASSWORD:
                self.stdout.write(
                    self.style.ERROR('❌ SendGrid API key not configured')
                )
                return
            
            # Email content
            message = f"""
Hello!

This is a test email from CozyWish to verify that SendGrid email integration is working correctly.

Email Configuration:
- Host: {settings.EMAIL_HOST}
- Port: {settings.EMAIL_PORT}
- TLS: {settings.EMAIL_USE_TLS}

If you receive this email, your SendGrid integration is working perfectly!

Best regards,
CozyWish Development Team

---
This is an automated test email from CozyWish.
            """
            
            from_email = '<EMAIL>'
            
            # Send the email
            send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False,
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ Test email sent successfully!')
            )
            self.stdout.write(f'   📧 To: {recipient_email}')
            self.stdout.write(f'   📧 Subject: {subject}')
            self.stdout.write(f'   📧 From: {from_email}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to send test email: {str(e)}')
            )
            self.stdout.write(
                self.style.WARNING('💡 Possible issues:')
            )
            self.stdout.write('   - Check your SendGrid API key')
            self.stdout.write('   - Verify network connectivity')
            self.stdout.write('   - Check firewall settings')
            self.stdout.write('   - Ensure SendGrid account is active')
