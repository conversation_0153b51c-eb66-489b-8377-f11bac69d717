{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Disputed Payments" %} - Admin - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="disputed-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'payments_app:admin_payment_list' %}">Payment Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Disputed Payments</li>
                    </ol>
                </nav>
                <h1 class="h2">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>{% trans "Disputed Payments" %}
                </h1>
                <p class="text-muted">{% trans "Monitor and resolve payment disputes and failures" %}</p>
            </div>
        </div>

        <!-- Disputed Payment Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ disputed_stats.total_disputed }}</h4>
                        <small>{% trans "Total Disputed" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ disputed_stats.failed_payments }}</h4>
                        <small>{% trans "Failed Payments" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ disputed_stats.requires_action }}</h4>
                        <small>{% trans "Requires Action" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card alert-card">
                    <div class="card-body text-center">
                        <h4>${{ disputed_stats.total_disputed_amount|floatformat:2 }}</h4>
                        <small>{% trans "Disputed Amount" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Section -->
        {% if disputed_stats.requires_action > 0 %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Attention Required" %}
                    </h5>
                    <p class="mb-0">
                        {% blocktrans count counter=disputed_stats.requires_action %}
                            There is {{ counter }} payment that requires immediate attention.
                        {% plural %}
                            There are {{ counter }} payments that require immediate attention.
                        {% endblocktrans %}
                    </p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Search and Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="{{ search_form.search_query.id_for_label }}" class="form-label">
                        {% trans "Search" %}
                    </label>
                    {{ search_form.search_query }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.status.id_for_label }}" class="form-label">
                        {% trans "Status" %}
                    </label>
                    {{ search_form.status }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_from.id_for_label }}" class="form-label">
                        {% trans "From" %}
                    </label>
                    {{ search_form.date_from }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_to.id_for_label }}" class="form-label">
                        {% trans "To" %}
                    </label>
                    {{ search_form.date_to }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> {% trans "Search" %}
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            {% if active_filters %}
            <div class="mt-3">
                <small class="text-muted">{% trans "Active filters:" %}</small>
                {% for filter in active_filters %}
                    <span class="badge bg-secondary me-1">{{ filter }}</span>
                {% endfor %}
                <a href="{% url 'payments_app:admin_disputed_payments' %}" class="btn btn-sm btn-outline-secondary ms-2">
                    {% trans "Clear All" %}
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group">
                    <a href="?status=failed" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-times me-1"></i>{% trans "Failed Only" %}
                    </a>
                    <a href="?status=requires_action" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-exclamation me-1"></i>{% trans "Requires Action" %}
                    </a>
                    <a href="?has_refund_requests=true" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-undo me-1"></i>{% trans "With Refund Requests" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Disputed Payments List -->
        <div class="row">
            {% for payment in disputed_payments %}
            <div class="col-12">
                <div class="card disputed-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Payment ID" %}</small>
                                <div class="fw-bold">{{ payment.payment_id|truncatechars:12 }}</div>
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>{% trans "Disputed" %}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Date" %}</small>
                                <div>{{ payment.payment_date|date:"M d, Y" }}</div>
                                <small class="text-muted">{{ payment.payment_date|time:"g:i A" }}</small>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Amount" %}</small>
                                <div class="fw-bold text-danger">${{ payment.amount_paid }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Status" %}</small>
                                <div>
                                    <span class="badge status-badge bg-{% if payment.payment_status == 'failed' %}danger{% elif payment.payment_status == 'requires_action' %}warning{% else %}secondary{% endif %}">
                                        {{ payment.get_payment_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Customer" %}</small>
                                <div>{{ payment.customer.email|truncatechars:20 }}</div>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group">
                                    <a href="{% url 'payments_app:admin_payment_detail' payment.payment_id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "Investigate" %}
                                    </a>
                                    {% if payment.refund_requests.exists %}
                                    <a href="{% url 'payments_app:admin_refund_list' %}?payment={{ payment.payment_id }}" 
                                       class="btn btn-sm btn-outline-warning">
                                        {% trans "Refunds" %}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Failure Reason -->
                        {% if payment.failure_reason %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-danger alert-sm mb-0">
                                    <strong>{% trans "Failure Reason:" %}</strong> {{ payment.failure_reason }}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Booking Information -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    {% if payment.booking %}
                                        <i class="fas fa-calendar-check me-1"></i>
                                        {% trans "Booking:" %} {{ payment.booking.booking_id|truncatechars:12 }}
                                        | <i class="fas fa-map-marker-alt me-1"></i>{{ payment.booking.venue.venue_name|truncatechars:30 }}
                                    {% endif %}
                                    {% if payment.provider %}
                                        | <i class="fas fa-user-tie me-1"></i>{{ payment.provider.email|truncatechars:25 }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- Refund Requests -->
                        {% if payment.refund_requests.exists %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-warning">
                                    <i class="fas fa-undo me-1"></i>
                                    {% trans "Refund Requests:" %}
                                    {% for refund in payment.refund_requests.all %}
                                        <span class="badge bg-warning text-dark">{{ refund.get_request_status_display }}</span>
                                    {% endfor %}
                                </small>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Required -->
                        {% if payment.payment_status == 'requires_action' %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-warning alert-sm mb-0">
                                    <strong>{% trans "Action Required:" %}</strong> 
                                    {% trans "This payment requires manual intervention. Please review and take appropriate action." %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>{% trans "No Disputed Payments" %}</h5>
                        <p class="text-muted">{% trans "Great! No payments currently require dispute resolution." %}</p>
                        <a href="{% url 'payments_app:admin_payment_list' %}" class="btn btn-primary">
                            {% trans "View All Payments" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Disputed payments pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page=1">{% trans "First" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

        <!-- Help Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-question-circle me-2"></i>{% trans "Dispute Resolution Guide" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>{% trans "Failed Payments" %}</h6>
                                <ul class="list-unstyled small">
                                    <li>• {% trans "Check payment method validity" %}</li>
                                    <li>• {% trans "Verify customer account status" %}</li>
                                    <li>• {% trans "Review failure reason details" %}</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>{% trans "Requires Action" %}</h6>
                                <ul class="list-unstyled small">
                                    <li>• {% trans "Manual verification needed" %}</li>
                                    <li>• {% trans "Contact customer if necessary" %}</li>
                                    <li>• {% trans "Update payment status" %}</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>{% trans "Refund Requests" %}</h6>
                                <ul class="list-unstyled small">
                                    <li>• {% trans "Review refund justification" %}</li>
                                    <li>• {% trans "Check service provider policies" %}</li>
                                    <li>• {% trans "Process or decline accordingly" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Auto-refresh page every 5 minutes for real-time monitoring
    setTimeout(function() {
        location.reload();
    }, 300000); // 5 minutes
</script>
{% endblock %}
