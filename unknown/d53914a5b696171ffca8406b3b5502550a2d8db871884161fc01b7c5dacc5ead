from datetime import <PERSON>elta

import pytest
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone
from model_bakery import baker

from accounts_app.models import (
    CustomUser,
    LoginHistory,
    LoginAlert,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)

# All tests use the database
pytestmark = pytest.mark.django_db


# --- CustomUser & CustomUserManager ---
def test_create_user_success():
    user = CustomUser.objects.create_user("<EMAIL>", "pass123")
    assert user.email == "<EMAIL>"
    assert user.is_active
    assert user.role == CustomUser.CUSTOMER


def test_create_user_without_email_raises():
    with pytest.raises(ValueError):
        CustomUser.objects.create_user("", "pass")


def test_create_superuser_success():
    admin = CustomUser.objects.create_superuser("<EMAIL>", "pass")
    assert admin.is_staff and admin.is_superuser


def test_create_superuser_missing_flags_raises():
    with pytest.raises(ValueError):
        CustomUser.objects.create_superuser(
            "<EMAIL>", "pass", is_staff=False, is_superuser=False
        )


@pytest.mark.parametrize(
    "role, prop",
    [
        (CustomUser.CUSTOMER, "is_customer"),
        (CustomUser.SERVICE_PROVIDER, "is_service_provider"),
        (CustomUser.ADMIN, "is_admin"),
    ],
)
def test_role_helper_properties(role, prop):
    u = baker.make(CustomUser, role=role)
    assert getattr(u, prop) is True
    for other in {"is_customer", "is_service_provider", "is_admin"} - {prop}:
        assert getattr(u, other) is False


def test_user_str_full_short_names():
    u = baker.make(CustomUser, first_name="Jane", last_name="Doe", email="<EMAIL>")
    assert str(u) == "<EMAIL>"
    assert u.full_name == "Jane Doe"
    assert u.short_name == "Jane"


def test_customuser_fullname_when_missing():
    """If first & last name are blank, .full_name returns empty string."""
    u = baker.make(CustomUser, first_name="", last_name="", email="<EMAIL>")
    assert u.full_name == ""
    assert u.short_name == ""


def test_email_uniqueness():
    CustomUser.objects.create_user("<EMAIL>", "pass")
    with pytest.raises(IntegrityError):
        CustomUser.objects.create_user("<EMAIL>", "pass2")



# --- LoginHistory & LoginAlert ---

def test_login_history_str_and_purge():
    user = baker.make(CustomUser)
    # initial create uses auto_now_add; manually update timestamp to 91 days ago
    old = LoginHistory.objects.create(
        user=user,
        ip_address="*********",
        user_agent="pytest",
        is_successful=True,
    )
    LoginHistory.objects.filter(pk=old.pk).update(
        timestamp=timezone.now() - timedelta(days=91)
    )
    old.refresh_from_db()
    # recent entry
    recent = LoginHistory.objects.create(
        user=user,
        ip_address="*********",
        user_agent="pytest",
        is_successful=True
    )
    assert user.email in str(old)
    LoginHistory.purge_old_records(days=90)
    assert not LoginHistory.objects.filter(pk=old.pk).exists()
    assert LoginHistory.objects.filter(pk=recent.pk).exists()



def test_detect_suspicious_activity_creates_alert():
    user, ip = baker.make(CustomUser), "*************"
    for _ in range(5):  # 5 failed attempts → HIGH severity
        LoginHistory.objects.create(user=user, ip_address=ip, is_successful=False)
    assert LoginHistory.detect_suspicious_activity(ip, user)
    alert = LoginAlert.objects.get(ip_address=ip)
    assert alert.severity == LoginAlert.HIGH and alert.attempt_count == 5


def test_detect_suspicious_activity_below_threshold():
    user, ip = baker.make(CustomUser), "***********"
    for _ in range(2):
        LoginHistory.objects.create(user=user, ip_address=ip, is_successful=False)
    assert not LoginHistory.detect_suspicious_activity(ip, user)
    assert not LoginAlert.objects.filter(ip_address=ip).exists()


def test_login_alert_resolve_flow():
    creator, resolver = baker.make(CustomUser), baker.make(CustomUser)
    alert = LoginAlert.objects.create(
        user=creator,
        ip_address="**********",
        alert_type=LoginAlert.BRUTE_FORCE,
        severity=LoginAlert.CRITICAL,
        description="Rapid failures",
        attempt_count=10,
    )
    assert alert.is_critical
    alert.resolve(resolver, "Mitigated")
    alert.refresh_from_db()
    assert alert.is_resolved and alert.resolved_by == resolver and alert.resolved_at


def test_login_alert_counts_and_uniqueness():
    baker.make(LoginAlert, is_resolved=False, severity=LoginAlert.LOW)
    baker.make(LoginAlert, is_resolved=False, severity=LoginAlert.HIGH)
    assert LoginAlert.unresolved_count() == 2
    assert LoginAlert.critical_count() == 1

    # uniqueness (ip, type, is_resolved=False)
    with pytest.raises(IntegrityError):
        baker.make(
            LoginAlert,
            ip_address="*************",
            alert_type=LoginAlert.SUSPICIOUS_IP,
            is_resolved=False,
        )
        baker.make(
            LoginAlert,
            ip_address="*************",
            alert_type=LoginAlert.SUSPICIOUS_IP,
            is_resolved=False,
        )


def test_login_alert_str_representation():
    """__str__ should combine human-readable alert_type & severity."""
    alert = LoginAlert.objects.create(
        ip_address="*******",
        alert_type=LoginAlert.MULTIPLE_FAILURES,
        severity=LoginAlert.MEDIUM,
        attempt_count=3,
        description="3 × wrong password",
    )
    txt = str(alert)
    assert "Multiple Failed Attempts" in txt and "Medium" in txt


# --- CustomerProfile ---
def test_customer_profile_properties():
    p = baker.make(
        CustomerProfile,
        first_name="John",
        last_name="Doe",
        birth_month=CustomerProfile.JANUARY,
    )
    assert p.full_name == "John Doe"
    assert p.birth_month_name == "January"
    assert p.user.email in str(p)


def test_customer_birth_month_invalid_choice():
    """full_clean() should reject an invalid birth_month choice."""
    u = baker.make(CustomUser)
    profile = CustomerProfile(user=u, birth_month=99)  # invalid month
    with pytest.raises(ValidationError):
        profile.full_clean()




# --- ServiceProviderProfile ---

def test_service_provider_profile_helpers():
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    sp = baker.make(
        ServiceProviderProfile,
        user=user,
        legal_name="Legal Spa LLC",
        display_name="Cozy Spa",
        address="123 Main St",
        city="Los Angeles",
        county="Los Angeles County",
        state=ServiceProviderProfile.CALIFORNIA,
        zip_code="90001",
    )
    assert sp.business_name == "Cozy Spa"
    assert str(sp) == f"Legal Spa LLC ({user.email})"
    assert (
        sp.full_address
        == "123 Main St, Los Angeles, Los Angeles County, CA 90001"
    )


def test_service_provider_invalid_state_choice():
    """Invalid 2-letter state abbreviation should raise ValidationError."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    sp = ServiceProviderProfile(
        user=user,
        legal_name="Test LLC",
        display_name="Test LLC",
        address="1 Way",
        city="Nowhere",
        county="Nowhere",
        state="XX",  # not in STATE_CHOICES
        zip_code="00000",
    )
    with pytest.raises(ValidationError):
        sp.full_clean()



# --- TeamMember ---

def test_team_member_max_count():
    assert TeamMember.max_count() == 7
