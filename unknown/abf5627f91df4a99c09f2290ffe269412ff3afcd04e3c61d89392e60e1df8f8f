{% extends 'review_app/base_review.html' %}

{% block title %}Review Summary{% endblock %}

{% block extra_css %}
{% load static %}
<style>
.progress-bar-custom {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}
.stat-card {
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-2px);
}
.skeleton {
    background-color: #eee;
    border-radius: 4px;
    animation: pulse 1.5s infinite;
}
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-chart-bar me-2"></i>Review Summary
                </h2>
                <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>View All Reviews
                </a>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div id="stats-skeleton" class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card skeleton h-100"></div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card skeleton h-100"></div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card skeleton h-100"></div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card skeleton h-100"></div>
        </div>
    </div>

    <div id="stats-content" class="row mb-4" style="display:none;">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-star fa-3x"></i>
                    </div>
                    <h3 class="mb-1">{{ overall_stats.average_rating|floatformat:1 }}</h3>
                    <p class="text-muted mb-0">Average Rating</p>
                    <small class="text-muted">Based on {{ overall_stats.total_reviews }} review{{ overall_stats.total_reviews|pluralize }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-comments fa-3x"></i>
                    </div>
                    <h3 class="mb-1">{{ overall_stats.total_reviews }}</h3>
                    <p class="text-muted mb-0">Total Reviews</p>
                    <small class="text-muted">All time</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-reply fa-3x"></i>
                    </div>
                    <h3 class="mb-1">{{ overall_stats.response_rate|floatformat:0 }}%</h3>
                    <p class="text-muted mb-0">Response Rate</p>
                    <small class="text-muted">{{ overall_stats.responded_reviews }}/{{ overall_stats.total_reviews }} responded</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-calendar fa-3x"></i>
                    </div>
                    <h3 class="mb-1">{{ overall_stats.reviews_this_month }}</h3>
                    <p class="text-muted mb-0">This Month</p>
                    <small class="text-muted">{{ overall_stats.month_change|floatformat:0 }}% vs last month</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Rating Distribution -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Rating Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="ratingChart" height="200" aria-label="Rating distribution chart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Reviews by Venue
                    </h5>
                </div>
                <div class="card-body">
                    {% for venue_stat in venue_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                        <div>
                            <h6 class="mb-1">{{ venue_stat.venue_name }}</h6>
                            <div class="text-warning">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= venue_stat.average_rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                                <span class="ms-2 text-muted">{{ venue_stat.average_rating|floatformat:1 }}</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">{{ venue_stat.total_reviews }}</div>
                            <small class="text-muted">review{{ venue_stat.total_reviews|pluralize }}</small>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">No venue data available</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reviews -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Reviews
                    </h5>
                    <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% for review in recent_reviews %}
                    <div class="d-flex align-items-start mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
                        <div class="me-3">
                            {% if review.customer.customerprofile.profile_picture %}
                                <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                     alt="Customer" class="rounded-circle" style="width: 40px; height: 40px;">
                            {% else %}
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                    <small class="text-muted">{{ review.venue.venue_name }}</small>
                                </div>
                                <div class="text-end">
                                    <div class="text-warning">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                </div>
                            </div>
                            <p class="mb-2">{{ review.written_review|truncatewords:20 }}</p>
                            <div class="d-flex align-items-center">
                                {% if review.response %}
                                    <span class="badge bg-success me-2">
                                        <i class="fas fa-reply me-1"></i>Responded
                                    </span>
                                {% else %}
                                    <a href="{% url 'review_app:provider_respond_to_review' review.id %}" 
                                       class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-reply me-1"></i>Respond
                                    </a>
                                {% endif %}
                                {% if review.is_flagged %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-flag me-1"></i>Flagged
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">No recent reviews</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Action Items -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>Action Items
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% if pending_responses > 0 %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-reply text-warning me-2"></i>
                                Reviews awaiting response
                            </div>
                            <span class="badge bg-warning rounded-pill">{{ pending_responses }}</span>
                        </div>
                        {% endif %}
                        
                        {% if flagged_reviews > 0 %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-flag text-danger me-2"></i>
                                Flagged reviews to review
                            </div>
                            <span class="badge bg-danger rounded-pill">{{ flagged_reviews }}</span>
                        </div>
                        {% endif %}
                        
                        {% if low_rated_reviews > 0 %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-star text-warning me-2"></i>
                                Low-rated reviews (1-2 stars)
                            </div>
                            <span class="badge bg-warning rounded-pill">{{ low_rated_reviews }}</span>
                        </div>
                        {% endif %}
                        
                        {% if pending_responses == 0 and flagged_reviews == 0 and low_rated_reviews == 0 %}
                        <div class="list-group-item text-center text-success">
                            <i class="fas fa-check-circle me-2"></i>
                            All caught up! No pending actions.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Improvement Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        {% if overall_stats.response_rate < 80 %}
                        <li class="mb-2">
                            <i class="fas fa-arrow-up text-info me-2"></i>
                            <small>Respond to more reviews to improve customer engagement</small>
                        </li>
                        {% endif %}
                        
                        {% if overall_stats.average_rating < 4.0 %}
                        <li class="mb-2">
                            <i class="fas fa-star text-warning me-2"></i>
                            <small>Focus on addressing concerns from lower-rated reviews</small>
                        </li>
                        {% endif %}
                        
                        <li class="mb-2">
                            <i class="fas fa-heart text-danger me-2"></i>
                            <small>Encourage satisfied customers to leave reviews</small>
                        </li>
                        
                        <li class="mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            <small>Respond to reviews within 24-48 hours for best results</small>
                        </li>
                        
                        <li class="mb-0">
                            <i class="fas fa-handshake text-success me-2"></i>
                            <small>Thank customers for positive feedback and address concerns professionally</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{{ rating_distribution|json_script:"rating-data" }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('stats-skeleton').style.display = 'none';
    document.getElementById('stats-content').style.display = 'flex';
    const data = JSON.parse(document.getElementById('rating-data').textContent);
    const labels = Object.keys(data).sort((a,b) => b - a).map(r => r + '★');
    const counts = Object.keys(data).sort((a,b) => b - a).map(r => data[r].count);
    new Chart(document.getElementById('ratingChart').getContext('2d'), {
        type: 'bar',
        data: {labels: labels, datasets:[{data: counts, backgroundColor:'#ffc107'}]},
        options: {scales:{y:{beginAtZero:true, ticks:{precision:0}}}}
    });
});
</script>
{% endblock %}
