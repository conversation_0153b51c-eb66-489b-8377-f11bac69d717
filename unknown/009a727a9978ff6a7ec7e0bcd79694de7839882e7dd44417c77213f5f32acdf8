"""
Management command to seed review_app with realistic test data.
Creates reviews, review responses, and review flags.
"""

import random
from datetime import timed<PERSON><PERSON>

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model

from review_app.models import Review, ReviewResponse, ReviewFlag
from venues_app.models import Venue
from booking_cart_app.models import Booking

User = get_user_model()


class Command(BaseCommand):
    """Seed review_app with realistic test data."""
    
    help = 'Seed review_app with reviews, responses, and flags'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing review data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting review_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_reviews()
            self.create_review_responses()
            self.create_review_flags()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Review app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing review data."""
        self.stdout.write('🧹 Clearing existing review data...')

        try:
            ReviewFlag.objects.all().delete()
            ReviewResponse.objects.all().delete()
            Review.objects.all().delete()
            self.stdout.write('   ✅ Existing review data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )

    def create_reviews(self):
        """Create customer reviews for venues."""
        self.stdout.write('⭐ Creating customer reviews...')
        
        # Get completed bookings (customers who actually visited)
        completed_bookings = list(Booking.objects.filter(status=Booking.COMPLETED))
        venues = list(Venue.objects.filter(approval_status='approved'))
        customers = list(User.objects.filter(role=User.CUSTOMER))
        
        if not venues or not customers:
            self.stdout.write('   ⚠️ Missing required data, skipping review creation')
            return
        
        # Review templates by rating
        review_templates = {
            5: [
                "Absolutely amazing experience! The staff was professional and the service was outstanding. Will definitely be back!",
                "Perfect spa day! Everything exceeded my expectations. The atmosphere was so relaxing and peaceful.",
                "Best spa experience I've ever had! The treatment was incredible and the facilities are top-notch.",
                "Outstanding service from start to finish. The staff made me feel so welcome and pampered.",
                "Exceptional quality and attention to detail. This place is a hidden gem!",
            ],
            4: [
                "Really enjoyed my visit! Great service and lovely atmosphere. Just a few minor things could be improved.",
                "Very good experience overall. The treatment was relaxing and the staff was friendly.",
                "Good value for money. The service was professional and I felt refreshed afterwards.",
                "Nice spa with good facilities. Would recommend to friends looking for a relaxing experience.",
                "Solid experience. The massage was great and the environment was clean and peaceful.",
            ],
            3: [
                "Decent experience but nothing special. The service was okay but could be better.",
                "Average spa visit. Some things were good but others could use improvement.",
                "It was fine. The treatment was adequate but I expected more for the price.",
                "Mixed experience. Some staff were great while others seemed rushed.",
                "Okay service but the facilities could use some updating.",
            ],
            2: [
                "Disappointing visit. The service was below expectations and the staff seemed unprepared.",
                "Not great. Had to wait longer than expected and the treatment felt rushed.",
                "Poor value for money. The facilities were not as clean as I hoped.",
                "Service was lacking. The staff didn't seem very knowledgeable about the treatments.",
                "Below average experience. Would not recommend based on this visit.",
            ],
            1: [
                "Terrible experience. Very unprofessional service and poor facilities.",
                "Worst spa visit ever. Nothing went right and the staff was rude.",
                "Completely unsatisfied. The treatment was awful and overpriced.",
                "Horrible service. Would never go back and wouldn't recommend to anyone.",
                "Extremely disappointed. This place needs serious improvements.",
            ],
        }
        
        # Create reviews for customers who had completed bookings
        reviewed_pairs = set()
        
        for booking in completed_bookings:
            # 60% chance customer leaves a review after completed booking
            if random.random() < 0.6:
                customer = booking.customer
                venue = booking.venue
                
                # Ensure one review per customer per venue
                if (customer.id, venue.id) not in reviewed_pairs:
                    reviewed_pairs.add((customer.id, venue.id))
                    
                    # Rating distribution (weighted towards positive)
                    rating_weights = [0.05, 0.10, 0.15, 0.35, 0.35]  # 1-5 stars
                    rating = random.choices(range(1, 6), weights=rating_weights)[0]
                    
                    written_review = random.choice(review_templates[rating])
                    
                    # Review date (1-30 days after booking completion)
                    review_date = booking.booking_date + timedelta(
                        days=random.randint(1, 30)
                    )
                    
                    review = Review.objects.create(
                        customer=customer,
                        venue=venue,
                        rating=rating,
                        written_review=written_review,
                        is_approved=random.choice([True, True, True, False]),  # 75% approved
                        created_at=review_date,
                    )
                    
                    approval_status = "✅ approved" if review.is_approved else "⏳ pending"
                    self.stdout.write(
                        f'   {approval_status} {rating}⭐ review for {venue.venue_name} by {customer.email}'
                    )
        
        # Create additional reviews from customers without bookings (walk-ins, etc.)
        additional_reviews = random.randint(10, 20)
        
        for _ in range(additional_reviews):
            customer = random.choice(customers)
            venue = random.choice(venues)
            
            # Ensure one review per customer per venue
            if (customer.id, venue.id) not in reviewed_pairs:
                reviewed_pairs.add((customer.id, venue.id))
                
                rating = random.choices(range(1, 6), weights=[0.05, 0.10, 0.15, 0.35, 0.35])[0]
                written_review = random.choice(review_templates[rating])
                
                review_date = timezone.now() - timedelta(days=random.randint(1, 90))
                
                review = Review.objects.create(
                    customer=customer,
                    venue=venue,
                    rating=rating,
                    written_review=written_review,
                    is_approved=random.choice([True, True, True, False]),
                    created_at=review_date,
                )
                
                approval_status = "✅ approved" if review.is_approved else "⏳ pending"
                self.stdout.write(
                    f'   {approval_status} {rating}⭐ review for {venue.venue_name} by {customer.email}'
                )

    def create_review_responses(self):
        """Create provider responses to reviews."""
        self.stdout.write('💬 Creating review responses...')
        
        # Get approved reviews (providers can only respond to approved reviews)
        reviews = list(Review.objects.filter(is_approved=True))
        
        if not reviews:
            self.stdout.write('   ⚠️ No approved reviews found, skipping response creation')
            return
        
        response_templates = {
            5: [
                "Thank you so much for your wonderful review! We're thrilled you had such a great experience.",
                "We're delighted to hear you enjoyed your visit! Thank you for choosing us.",
                "Your kind words mean the world to us! We look forward to welcoming you back soon.",
                "Thank you for the amazing feedback! Our team will be so happy to hear this.",
            ],
            4: [
                "Thank you for your positive review! We appreciate your feedback and will work on those improvements.",
                "We're glad you enjoyed your experience! Thank you for the suggestions.",
                "Thanks for the great review! We're always looking for ways to make things even better.",
            ],
            3: [
                "Thank you for your honest feedback. We take all comments seriously and will work to improve.",
                "We appreciate your review and will address the areas you mentioned.",
                "Thank you for taking the time to share your experience. We'll work on making improvements.",
            ],
            2: [
                "We're sorry your experience didn't meet expectations. We'd love to discuss this further.",
                "Thank you for your feedback. We take these concerns seriously and will address them.",
                "We apologize for the issues you experienced. Please contact us so we can make this right.",
            ],
            1: [
                "We sincerely apologize for your poor experience. This is not the standard we strive for.",
                "We're very sorry to hear about your visit. Please contact us directly so we can resolve this.",
                "This is completely unacceptable and we will address these issues immediately.",
            ],
        }
        
        # 40% of reviews get responses from providers
        reviews_to_respond = random.sample(reviews, max(1, len(reviews) * 40 // 100))
        
        for review in reviews_to_respond:
            provider = review.venue.service_provider.user
            response_text = random.choice(response_templates[review.rating])
            
            # Response date (1-14 days after review)
            response_date = review.created_at + timedelta(days=random.randint(1, 14))
            
            ReviewResponse.objects.create(
                review=review,
                provider=provider,
                response_text=response_text,
                created_at=response_date,
            )
            
            self.stdout.write(
                f'   💬 Created response to {review.rating}⭐ review for {review.venue.venue_name}'
            )

    def create_review_flags(self):
        """Create review flags for inappropriate content."""
        self.stdout.write('🚩 Creating review flags...')
        
        reviews = list(Review.objects.filter(is_approved=True))
        customers = list(User.objects.filter(role=User.CUSTOMER))
        
        if not reviews or not customers:
            self.stdout.write('   ⚠️ Missing required data, skipping flag creation')
            return
        
        # Create flags for 5-10% of reviews (simulate inappropriate content reports)
        num_flags = max(1, len(reviews) // 15)
        flagged_reviews = random.sample(reviews, min(num_flags, len(reviews)))
        
        flag_reasons = [
            (ReviewFlag.INAPPROPRIATE_CONTENT, "Contains inappropriate language"),
            (ReviewFlag.FAKE_REVIEW, "Appears to be a fake review"),
            (ReviewFlag.SPAM, "Looks like spam content"),
            (ReviewFlag.OFFENSIVE_LANGUAGE, "Uses offensive language"),
            (ReviewFlag.OTHER, "Violates community guidelines"),
        ]
        
        for review in flagged_reviews:
            # Flag by a different customer (not the review author)
            potential_flaggers = [c for c in customers if c != review.customer]
            if not potential_flaggers:
                continue
                
            flagger = random.choice(potential_flaggers)
            reason, reason_text = random.choice(flag_reasons)
            
            flag_status = random.choice([
                ReviewFlag.PENDING, ReviewFlag.REVIEWED, ReviewFlag.RESOLVED
            ])
            
            flag = ReviewFlag.objects.create(
                review=review,
                flagged_by=flagger,
                reason=reason,
                reason_text=reason_text,
                status=flag_status,
            )
            
            # Handle reviewed/resolved flags
            if flag_status in [ReviewFlag.REVIEWED, ReviewFlag.RESOLVED]:
                admin_user = User.objects.filter(is_superuser=True).first()
                flag.reviewed_by = admin_user
                flag.reviewed_at = timezone.now() - timedelta(days=random.randint(1, 7))
                flag.admin_notes = random.choice([
                    "Reviewed - no violation found",
                    "Content updated by user",
                    "Warning issued to user",
                    "Review removed for violation",
                ])
                flag.save()
            
            status_emoji = {
                ReviewFlag.PENDING: '⏳',
                ReviewFlag.REVIEWED: '👀',
                ReviewFlag.RESOLVED: '✅',
            }
            
            self.stdout.write(
                f'   {status_emoji.get(flag_status, "🚩")} Created {flag_status} flag for review by {review.customer.email}'
            )
