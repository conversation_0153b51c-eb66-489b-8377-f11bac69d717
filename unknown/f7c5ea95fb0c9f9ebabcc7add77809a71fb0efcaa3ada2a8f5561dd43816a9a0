"""Forms used to log and manage system health events."""

# --- Third-Party Imports ---
from django import forms
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from utils.forms import AriaLabelMixin
from ..models import SystemHealthLog

User = get_user_model()


class SystemHealthLogForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating system health log entries."""

    class Meta:
        model = SystemHealthLog
        fields = [
            'event_type', 'severity', 'title', 'description',
            'affected_user', 'ip_address', 'user_agent'
        ]
        widgets = {
            'event_type': forms.Select(attrs={'class': 'form-select'}),
            'severity': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Brief title describing the event'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Detailed description of the event'
            }),
            'affected_user': forms.Select(attrs={'class': 'form-select'}),
            'ip_address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'IP address (optional)'
            }),
            'user_agent': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Browser user agent (optional)'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['affected_user'].queryset = User.objects.filter(is_active=True)
        self.fields['affected_user'].required = False

__all__ = ['SystemHealthLogForm']
