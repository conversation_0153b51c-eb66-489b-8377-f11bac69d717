"""
Signal handlers for the review_app.

This module contains Django signal handlers that respond to model changes
and trigger appropriate actions like logging, notifications, and data updates.
"""

# --- Third-Party Imports ---
from django.db.models import Avg
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

# --- Local App Imports ---
from .models import Review, ReviewFlag, ReviewResponse
from .logging_utils import (
    log_review_creation, log_review_response, log_review_flag,
    log_review_flag_resolution, log_review_moderation, log_review_error,
    send_review_notification_email, send_review_response_notification_email
)


@receiver(post_save, sender=Review)
def handle_review_save(sender, instance, created, **kwargs):
    """
    Handle Review model save events.
    
    This signal handler:
    1. Updates venue average rating
    2. Logs review creation
    3. Sends notifications (handled by notifications_app)
    """
    try:
        # Update venue average rating asynchronously
        from .utils import async_update_venue_average_rating
        async_update_venue_average_rating(instance.venue.id)
        
        # Log review creation if this is a new review
        if created:
            log_review_creation(
                user=instance.customer,
                review=instance,
                additional_details={
                    'venue_provider': instance.venue.service_provider.user.email,
                    'is_approved': instance.is_approved
                }
            )

            # Send email notification to provider
            send_review_notification_email(instance)
            
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review save signal: {str(e)}',
            exception=e,
            review_context={
                'review_id': instance.id,
                'venue_id': instance.venue.id,
                'review_created': created
            }
        )


@receiver(post_delete, sender=Review)
def handle_review_delete(sender, instance, **kwargs):
    """
    Handle Review model delete events.
    
    This signal handler:
    1. Updates venue average rating after review deletion
    2. Logs the deletion event
    """
    try:
        # Update venue average rating asynchronously
        from .utils import async_update_venue_average_rating
        async_update_venue_average_rating(instance.venue.id)
            
        # Log review deletion - use a different logging approach since we don't have admin_user
        from .logging_utils import log_review_error
        log_review_error(
            error_type='review_deletion',
            error_message='Review deleted',
            review_context={
                'review_id': instance.id,
                'venue_id': instance.venue.id,
                'venue_name': instance.venue.venue_name,
                'venue_provider': instance.venue.service_provider.user.email,
                'original_rating': instance.rating,
                'deletion_method': 'programmatic'
            }
        )
        
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review delete signal: {str(e)}',
            exception=e,
            review_context={
                'venue_id': instance.venue.id,
                'original_rating': instance.rating
            }
        )


@receiver(pre_save, sender=Review)
def handle_review_pre_save(sender, instance, **kwargs):
    """
    Handle Review model pre-save events.
    
    This signal handler:
    1. Tracks changes to approval status for logging
    2. Stores old values for comparison
    """
    try:
        # Store old values if this is an update
        if instance.pk:
            try:
                old_instance = Review.objects.get(pk=instance.pk)
                instance._old_is_approved = old_instance.is_approved
                instance._old_is_flagged = old_instance.is_flagged
            except Review.DoesNotExist:
                # Instance was deleted between pre_save and save
                pass
                
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review pre-save signal: {str(e)}',
            exception=e,
            review_context={
                'review_id': instance.id if instance.pk else None
            }
        )


@receiver(post_save, sender=ReviewResponse)
def handle_review_response_save(sender, instance, created, **kwargs):
    """
    Handle ReviewResponse model save events.
    
    This signal handler:
    1. Logs response creation
    2. Sends notifications (handled by notifications_app)
    """
    try:
        if created:
            log_review_response(
                user=instance.provider,
                review=instance.review,
                response=instance,
                additional_details={
                    'customer_email': instance.review.customer.email,
                    'venue_name': instance.review.venue.venue_name
                }
            )

            # Send email notification to customer
            send_review_response_notification_email(instance.review, instance)
            
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review response save signal: {str(e)}',
            exception=e,
            review_context={
                'response_id': instance.id,
                'review_id': instance.review.id,
                'response_created': created
            }
        )


@receiver(post_save, sender=ReviewFlag)
def handle_review_flag_save(sender, instance, created, **kwargs):
    """
    Handle ReviewFlag model save events.
    
    This signal handler:
    1. Updates review flagged status
    2. Logs flag creation and resolution
    3. Sends notifications (handled by notifications_app)
    """
    try:
        if created:
            # Log flag creation
            log_review_flag(
                user=instance.flagged_by,
                review=instance.review,
                flag=instance,
                additional_details={
                    'venue_provider': instance.review.venue.service_provider.user.email,
                    'reason_text': instance.reason_text
                }
            )
        else:
            # Check if flag was resolved
            if (hasattr(instance, '_old_status') and 
                instance._old_status == ReviewFlag.PENDING and
                instance.status in [ReviewFlag.REVIEWED, ReviewFlag.RESOLVED]):
                
                log_review_flag_resolution(
                    admin_user=instance.reviewed_by,
                    flag=instance,
                    action=instance.status,
                    additional_details={
                        'venue_provider': instance.review.venue.service_provider.user.email,
                        'resolution_notes': instance.admin_notes
                    }
                )
        
        # Update review's flagged status
        review = instance.review
        has_pending_flags = review.flags.filter(status=ReviewFlag.PENDING).exists()
        if review.is_flagged != has_pending_flags:
            review.is_flagged = has_pending_flags
            review.save(update_fields=['is_flagged'])
            
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review flag save signal: {str(e)}',
            exception=e,
            review_context={
                'flag_id': instance.id,
                'review_id': instance.review.id,
                'flag_created': created
            }
        )


@receiver(pre_save, sender=ReviewFlag)
def handle_review_flag_pre_save(sender, instance, **kwargs):
    """
    Handle ReviewFlag model pre-save events.
    
    This signal handler:
    1. Stores old status for comparison in post_save
    """
    try:
        # Store old status if this is an update
        if instance.pk:
            try:
                old_instance = ReviewFlag.objects.get(pk=instance.pk)
                instance._old_status = old_instance.status
            except ReviewFlag.DoesNotExist:
                # Instance was deleted between pre_save and save
                pass
                
    except Exception as e:
        log_review_error(
            error_type='signal_error',
            error_message=f'Error in review flag pre-save signal: {str(e)}',
            exception=e,
            review_context={
                'flag_id': instance.id if instance.pk else None
            }
        )
