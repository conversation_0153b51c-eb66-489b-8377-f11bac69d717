"""Admin forms for refund management and payment monitoring."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from accounts_app.forms import AccessibleFormMixin
from ..models import Payment, RefundRequest
from .common import RefundAmountValidationMixin


# --- Admin Refund Forms ---


class AdminRefundSearchForm(AccessibleFormMixin, forms.Form):
    """Form for searching and filtering refund requests in admin interface."""

    STATUS_CHOICES = [('', _('All Statuses'))] + RefundRequest.STATUS_CHOICES
    REASON_CHOICES = [('', _('All Reasons'))] + RefundRequest.REASON_CHOICES

    search_query = forms.CharField(
        label=_('Search'),
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search by refund ID, customer name, payment ID, or reason...'})
    )
    status = forms.ChoiceField(
        label=_('Status'),
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    reason_category = forms.ChoiceField(
        label=_('Reason Category'),
        choices=REASON_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        label=_('From Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        label=_('To Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    amount_min = forms.DecimalField(
        label=_('Minimum Amount'),
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'placeholder': '0.00'})
    )
    amount_max = forms.DecimalField(
        label=_('Maximum Amount'),
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'placeholder': '0.00'})
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        amount_min = cleaned_data.get('amount_min')
        amount_max = cleaned_data.get('amount_max')
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be later than to date.'))
        if amount_min and amount_max and amount_min > amount_max:
            raise ValidationError(_('Minimum amount cannot be greater than maximum amount.'))
        return cleaned_data


class AdminRefundApprovalForm(RefundAmountValidationMixin, AccessibleFormMixin, forms.Form):
    """Form for admin to approve refund requests."""

    processed_amount = forms.DecimalField(
        label=_('Approved Refund Amount'),
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01', 'placeholder': '0.00', 'required': True}),
        help_text=_('Enter the amount to be refunded to the customer')
    )
    admin_notes = forms.CharField(
        label=_('Admin Notes'),
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Add any notes about this refund approval...', 'maxlength': 500}),
        max_length=500,
        required=False,
        help_text=_('Optional notes about the refund approval decision')
    )

    def __init__(self, *args, **kwargs):
        self.refund_request = kwargs.pop('refund_request', None)
        super().__init__(*args, **kwargs)
        if self.refund_request:
            max_amount = self.refund_request.payment.remaining_refundable_amount
            self.fields['processed_amount'].initial = min(self.refund_request.requested_amount, max_amount)
            self.fields['processed_amount'].widget.attrs['max'] = str(max_amount)
            self.fields['processed_amount'].help_text = _(f'Enter the amount to be refunded (maximum: ${max_amount})')

    def clean_processed_amount(self):
        processed_amount = self.cleaned_data.get('processed_amount')
        payment = self.refund_request.payment if self.refund_request else None
        return self.validate_refund_amount(processed_amount, payment)


class AdminRefundDeclineForm(AccessibleFormMixin, forms.Form):
    """Form for admin to decline refund requests."""

    admin_notes = forms.CharField(
        label=_('Reason for Decline'),
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Please provide a reason for declining this refund request...', 'maxlength': 500, 'required': True}),
        max_length=500,
        required=True,
        help_text=_('Explain why this refund request is being declined')
    )

    def clean_admin_notes(self):
        admin_notes = self.cleaned_data.get('admin_notes')
        if not admin_notes or len(admin_notes.strip()) < 10:
            raise ValidationError(_('Please provide a detailed reason of at least 10 characters for declining the refund.'))
        return admin_notes.strip()


class AdminDisputedPaymentSearchForm(AccessibleFormMixin, forms.Form):
    """Form for searching and filtering disputed payments in admin interface."""

    STATUS_CHOICES = [('', _('All Statuses')), ('failed', _('Failed')), ('requires_action', _('Requires Action'))]

    search_query = forms.CharField(
        label=_('Search'),
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search by payment ID, customer name, or failure reason...'})
    )
    status = forms.ChoiceField(
        label=_('Status'),
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        label=_('From Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        label=_('To Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be later than to date.'))
        return cleaned_data
