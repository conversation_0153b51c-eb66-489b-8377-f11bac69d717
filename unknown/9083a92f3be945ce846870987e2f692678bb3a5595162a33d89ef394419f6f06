{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Earnings Overview" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="earnings-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2">{% trans "Earnings Overview" %}</h1>
                <p class="text-muted">{% trans "Track your payment earnings and performance" %}</p>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card earnings-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ total_earnings|floatformat:2 }}</div>
                        <div class="metric-label">Total Earnings</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card earnings-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ total_transactions }}</div>
                        <div class="metric-label">Total Transactions</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card earnings-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ monthly_earnings|floatformat:2 }}</div>
                        <div class="metric-label">This Month</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card earnings-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ average_transaction|floatformat:2 }}</div>
                        <div class="metric-label">Avg Transaction</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Earnings Breakdown -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card earnings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "Earnings Breakdown" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <!-- Placeholder for earnings chart -->
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">{% trans "Earnings chart will be displayed here" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card earnings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "Payment Methods" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">Stripe:</div>
                            <div class="col-6 text-end">{{ stripe_payments }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">PayPal:</div>
                            <div class="col-6 text-end">{{ paypal_payments|default:0 }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Other:</div>
                            <div class="col-6 text-end">{{ other_payments|default:0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card earnings-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{% trans "Recent Transactions" %}</h5>
                        <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Customer" %}</th>
                                        <th>{% trans "Service" %}</th>
                                        <th>{% trans "Amount" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in recent_payments %}
                                    <tr>
                                        <td>{{ payment.payment_date|date:"M d, Y" }}</td>
                                        <td>{{ payment.customer.email|truncatechars:20 }}</td>
                                        <td>{{ payment.booking.service.service_title|default:"N/A"|truncatechars:25 }}</td>
                                        <td class="text-success fw-bold">${{ payment.amount_paid }}</td>
                                        <td>
                                            <span class="badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% else %}warning{% endif %}">
                                                {{ payment.get_payment_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'payments_app:provider_payment_detail' payment.payment_id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                {% trans "View" %}
                                            </a>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            {% trans "No recent transactions found" %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payout Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="card earnings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "Payout Information" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Available Balance:" %}</div>
                            <div class="col-6 text-end text-success fw-bold">${{ available_balance|floatformat:2 }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Pending Balance:" %}</div>
                            <div class="col-6 text-end text-warning fw-bold">${{ pending_balance|floatformat:2 }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Total Paid Out:" %}</div>
                            <div class="col-6 text-end">${{ total_paid_out|floatformat:2 }}</div>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <a href="{% url 'payments_app:provider_payout_history' %}" class="btn btn-primary">
                                {% trans "View Payout History" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card earnings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "Performance Metrics" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Success Rate:" %}</div>
                            <div class="col-6 text-end">{{ success_rate|floatformat:1 }}%</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Refund Rate:" %}</div>
                            <div class="col-6 text-end">{{ refund_rate|floatformat:1 }}%</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">{% trans "Avg Rating:" %}</div>
                            <div class="col-6 text-end">
                                {% for i in "12345" %}
                                    <i class="fas fa-star{% if forloop.counter > average_rating %} text-muted{% else %} text-warning{% endif %}"></i>
                                {% endfor %}
                            </div>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <a href="{% url 'venues_app:provider_dashboard' %}" class="btn btn-outline-primary">
                                {% trans "View Full Dashboard" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Placeholder for earnings charts
    console.log('Earnings overview loaded');
    
    // Future: Add Chart.js or similar for earnings visualization
</script>
{% endblock %}
