# """Role-based access decorators for dashboard views."""

# --- Standard Library Imports ---
from functools import wraps

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import redirect

# --- Local App Imports ---
from .constants import ADMIN_ONLY_ERROR, CUSTOMER_ONLY_ERROR, PROVIDER_ONLY_ERROR


def role_required(role_attr, error_message, dashboard_type=None):
    """Return decorator enforcing the given user role."""
    def decorator(view_func):
        @login_required
        @wraps(view_func)
        def _wrapped(request, *args, **kwargs):
            if not getattr(request.user, role_attr, False):
                # For AJAX requests, return JSON response with 403 status
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': error_message
                    }, status=403)
                # For regular requests, redirect with error message
                messages.error(request, error_message)
                return redirect('venues_app:home')
            return view_func(request, *args, **kwargs)
        return _wrapped
    return decorator


customer_required = role_required('is_customer', CUSTOMER_ONLY_ERROR)
provider_required = role_required('is_service_provider', PROVIDER_ONLY_ERROR)
staff_required = role_required('is_staff', ADMIN_ONLY_ERROR)
