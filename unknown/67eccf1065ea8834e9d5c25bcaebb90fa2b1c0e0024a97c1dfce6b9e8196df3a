# Generated by Django 5.2.3 on 2025-06-16 14:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FavoriteVenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_date', models.DateTimeField(auto_now_add=True, help_text='When the venue was added to favorites')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the favorite was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who favorited this venue', on_delete=django.db.models.deletion.CASCADE, related_name='favorite_venues', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue that was favorited', on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Favorite Venue',
                'verbose_name_plural': 'Favorite Venues',
                'ordering': ['-added_date'],
                'indexes': [models.Index(fields=['customer', '-added_date'], name='dashboard_a_custome_fa9ce1_idx'), models.Index(fields=['venue', '-added_date'], name='dashboard_a_venue_i_830d12_idx')],
                'unique_together': {('customer', 'venue')},
            },
        ),
    ]
