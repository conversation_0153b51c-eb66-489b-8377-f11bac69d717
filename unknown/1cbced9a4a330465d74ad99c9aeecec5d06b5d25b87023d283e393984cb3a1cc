{% extends 'admin_app/base.html' %}

{% block title %}Analytics Dashboard{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">Analytics Dashboard</h1>
<form method="get" class="row g-2 mb-4">
    <div class="col">
        <input type="date" name="start_date" class="form-control" value="{{ start_date|default:'' }}">
    </div>
    <div class="col">
        <input type="date" name="end_date" class="form-control" value="{{ end_date|default:'' }}">
    </div>
    <div class="col-auto">
        <button class="btn btn-primary w-100" type="submit">Update</button>
    </div>
    <div class="col-auto">
        <a href="{% url 'admin_app:export_analytics' %}?start_date={{ start_date }}&end_date={{ end_date }}" class="btn btn-secondary w-100">Export CSV</a>
    </div>
</form>
<div class="row g-3 mb-4 text-center">
    <div class="col-6 col-md-3" >
        <div class="card p-3">
            <div class="fs-4">{{ total_users|default:0 }}</div>
            <div class="text-muted">Total Users</div>
        </div>
    </div>
    <div class="col-6 col-md-3" >
        <div class="card p-3">
            <div class="fs-4">{{ new_users|default:0 }}</div>
            <div class="text-muted">New Users</div>
        </div>
    </div>
    <div class="col-6 col-md-3" >
        <div class="card p-3">
            <div class="fs-4">{{ total_bookings|default:0 }}</div>
            <div class="text-muted">Total Bookings</div>
        </div>
    </div>
    <div class="col-6 col-md-3" >
        <div class="card p-3">
            <div class="fs-4">{{ new_bookings|default:0 }}</div>
            <div class="text-muted">New Bookings</div>
        </div>
    </div>
</div>
<p class="text-muted">Charts would be displayed here.</p>
<div class="mt-4">
    <a href="{% url 'admin_app:admin_dashboard' %}">Back to Dashboard</a>
</div>
{% endblock %}
