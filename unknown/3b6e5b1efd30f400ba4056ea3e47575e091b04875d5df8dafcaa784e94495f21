{% extends 'discount_app/base_discount.html' %}

{% block title %}{{ title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<link rel='stylesheet' href='{% static 'css/discount_app/discount_wireframe.css' %}'>
{% endblock %}

{% block discount_content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="h3 mb-1">{{ title }}</h1>
                    <p class="text-muted mb-0">
                        {% if action == 'Create' %}
                            Create a discount for a specific service in your venue
                        {% else %}
                            Update the service discount details
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <h5 class="mb-3">Basic Info</h5>

                        <!-- Service Selection -->
                        <div class="mb-4">
                            <label class="form-label" for="{{ form.service.id_for_label }}">
                                {{ form.service.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.service|add_class:"form-select" }}
                            {% if form.service.help_text %}
                                <div class="form-text">{{ form.service.help_text }}</div>
                            {% endif %}
                            {% if form.service.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.service.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Discount Name -->
                        <div class="mb-4">
                            <label class="form-label" for="{{ form.name.id_for_label }}">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.name.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="form-label" for="{{ form.description.id_for_label }}">
                                {{ form.description.label }}
                            </label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.description.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Discount Type and Value -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.discount_type.id_for_label }}">
                                    {{ form.discount_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.discount_type|add_class:"form-select" }}
                                {% if form.discount_type.help_text %}
                                    <div class="form-text">{{ form.discount_type.help_text }}</div>
                                {% endif %}
                                {% if form.discount_type.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.discount_type.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.discount_value.id_for_label }}">
                                    {{ form.discount_value.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text" id="discount-prefix">%</span>
                                    {{ form.discount_value|add_class:"form-control" }}
                                </div>
                                {% if form.discount_value.help_text %}
                                    <div class="form-text">{{ form.discount_value.help_text }}</div>
                                {% endif %}
                                <div class="form-text" id="price-preview"></div>
                                {% if form.discount_value.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.discount_value.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <h5 class="mb-3">Validity</h5>
                        <!-- Date Range -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.start_date.id_for_label }}">
                                    {{ form.start_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.start_date|add_class:"form-control" }}
                                {% if form.start_date.help_text %}
                                    <div class="form-text">{{ form.start_date.help_text }}</div>
                                {% endif %}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.start_date.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.end_date.id_for_label }}">
                                    {{ form.end_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.end_date|add_class:"form-control" }}
                                {% if form.end_date.help_text %}
                                    <div class="form-text">{{ form.end_date.help_text }}</div>
                                {% endif %}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.end_date.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form-wide errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger mb-4">
                                {% for error in form.non_field_errors %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Important Notes -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Important Notes
                            </h6>
                            <ul class="mb-0">
                                <li>Maximum discount allowed is 80% for percentage discounts</li>
                                <li>Discounts require admin approval before becoming visible to customers</li>
                                <li>You can edit or delete discounts at any time</li>
                                <li>Fixed amount discounts cannot exceed the service price</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Discount
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountTypeField = document.getElementById('{{ form.discount_type.id_for_label }}');
    const discountPrefix = document.getElementById('discount-prefix');
    const discountValueField = document.getElementById('{{ form.discount_value.id_for_label }}');
    const serviceField = document.getElementById('{{ form.service.id_for_label }}');
    const preview = document.getElementById('price-preview');
    const servicePrices = {{ service_prices_json|safe }};

    function updateDiscountPrefix() {
        if (discountTypeField.value === 'percentage') {
            discountPrefix.textContent = '%';
        } else {
            discountPrefix.textContent = '$';
        }
    }

    function updatePreview() {
        const serviceId = serviceField.value;
        const value = parseFloat(discountValueField.value);
        const price = parseFloat(servicePrices[serviceId] || 0);
        if (!serviceId || !value || !price) {
            preview.textContent = '';
            return;
        }
        let discounted = price;
        if (discountTypeField.value === 'percentage') {
            discounted = Math.max(price - (price * value / 100), 0).toFixed(2);
        } else {
            discounted = Math.max(price - value, 0).toFixed(2);
        }
        preview.textContent = `Discounted price: $${discounted}`;
    }

    // Update prefix on page load
    updateDiscountPrefix();
    updatePreview();

    // Update prefix when discount type changes
    discountTypeField.addEventListener('change', function(){
        updateDiscountPrefix();
        updatePreview();
    });
    discountValueField.addEventListener('input', updatePreview);
    serviceField.addEventListener('change', updatePreview);
});
</script>
{% endblock %}
