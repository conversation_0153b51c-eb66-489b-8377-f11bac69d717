# Standard library imports
import logging
from decimal import Decimal

# Django imports
from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.apps import apps
from django.core.exceptions import ValidationError

# Local imports
from venues_app.models import Venue, Service, Category

# Logger
logger = logging.getLogger(__name__)


class DiscountType(models.TextChoices):
    """Discount type choices for different discount calculation methods."""
    PERCENTAGE = 'percentage', _('Percentage')
    FIXED_AMOUNT = 'fixed_amount', _('Fixed Amount')


class DiscountStatus(models.TextChoices):
    """Discount status choices for tracking discount lifecycle."""
    ACTIVE = 'active', _('Active')
    SCHEDULED = 'scheduled', _('Scheduled')
    EXPIRED = 'expired', _('Expired')
    CANCELLED = 'cancelled', _('Cancelled')


class AbstractManagerDescriptor:
    """Descriptor to prevent manager access on abstract base."""

    def __get__(self, instance, owner):
        raise TypeError('Cannot instantiate abstract base class')


class DiscountBase(models.Model):
    """
    Abstract base model for all discount types.
    Provides common fields and methods for venue, service, and platform discounts.
    """

    def __new__(cls, *args, **kwargs):
        if cls is DiscountBase:
            raise TypeError('Cannot instantiate abstract base class')
        return super().__new__(cls)

    objects = AbstractManagerDescriptor()

    # Basic discount information
    name = models.CharField(
        max_length=255,
        help_text=_('Name/title of the discount')
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        blank=True,
        help_text=_('URL-friendly version of the discount name (auto-generated)')
    )
    description = models.TextField(
        blank=True,
        help_text=_('Optional description of the discount')
    )

    # Discount configuration
    discount_type = models.CharField(
        max_length=20,
        choices=DiscountType.choices,
        default=DiscountType.PERCENTAGE,
        help_text=_('Type of discount calculation')
    )
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Discount value (percentage or fixed amount)')
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_('Maximum number of times this discount can be used')
    )

    # Date range
    start_date = models.DateTimeField(
        help_text=_('When the discount becomes active')
    )
    end_date = models.DateTimeField(
        help_text=_('When the discount expires')
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='%(class)s_created',
        help_text=_('User who created this discount')
    )

    def _generate_unique_slug(self):
        base_slug = slugify(self.name)[:50]
        slug = base_slug
        counter = 1
        while self.__class__.objects.filter(slug=slug).exclude(pk=self.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        return slug

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = self._generate_unique_slug()
        super().save(*args, **kwargs)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name

    def clean(self):
        """Custom validation for discount models."""
        super().clean()

        # Validate date range
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_('Start date must be before end date'))

        # Validate discount value based on type (only if discount_value is not None)
        if self.discount_value is not None:
            if self.discount_type == DiscountType.PERCENTAGE:
                if self.discount_value <= 0 or self.discount_value > 100:
                    raise ValidationError(_('Percentage must be between 0.01 and 80'))
                if self.discount_value > 80:  # Max 80% as per requirements
                    raise ValidationError(_('Percentage discount cannot exceed 80%'))
            elif self.discount_type == DiscountType.FIXED_AMOUNT:
                if self.discount_value <= 0:
                    raise ValidationError(_('Fixed amount must be greater than 0'))

    def is_active(self):
        """Check if the discount is currently active."""
        now = timezone.now()
        return self.start_date <= now <= self.end_date

    def get_status(self):
        """Get the current status of the discount."""
        now = timezone.now()
        if self.start_date > now:
            return DiscountStatus.SCHEDULED
        elif self.end_date < now:
            return DiscountStatus.EXPIRED
        else:
            return DiscountStatus.ACTIVE

    def calculate_discount_amount(self, original_price):
        """Calculate the discount amount based on the discount type and value."""
        if self.discount_type == DiscountType.PERCENTAGE:
            # Ensure discount_value is between 0 and 80 for percentage
            percentage = min(max(self.discount_value, 0), 80)
            return (original_price * percentage) / 100
        else:  # Fixed amount
            # Ensure discount doesn't exceed the original price
            return min(self.discount_value, original_price)

    def get_usage_count(self):
        Usage = apps.get_model('discount_app', 'DiscountUsage')
        return Usage.objects.filter(
            discount_type=self.__class__.__name__,
            discount_id=self.id,
        ).count()

    def remaining_uses(self):
        if self.max_uses is None:
            return None
        return max(self.max_uses - self.get_usage_count(), 0)

    def usage_limit_reached(self):
        remaining = self.remaining_uses()
        return remaining is not None and remaining <= 0

    def calculate_discounted_price(self, original_price):
        """Calculate the final price after applying the discount."""
        discount_amount = self.calculate_discount_amount(original_price)
        return max(original_price - discount_amount, Decimal('0.00'))  # Ensure price doesn't go below 0


class VenueDiscount(DiscountBase):
    """
    Discount applied to an entire venue.
    Created by service providers for their venues.
    """

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='discounts',
        help_text=_('Venue this discount applies to')
    )

    # Minimum booking requirements
    min_booking_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Minimum booking value required to apply this discount')
    )

    # Maximum discount cap for percentage discounts
    max_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Maximum discount amount for percentage discounts (optional)')
    )

    # Admin approval workflow
    is_approved = models.BooleanField(
        default=False,
        help_text=_('Whether this discount has been approved by admin')
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_venue_discounts',
        help_text=_('Admin who approved this discount')
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When this discount was approved')
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Venue Discount')
        verbose_name_plural = _('Venue Discounts')

    def clean(self):
        """Custom validation for venue discounts."""
        super().clean()

        # Ensure the user creating the discount owns the venue
        if self.created_by and hasattr(self.created_by, 'service_provider_profile'):
            if self.venue.service_provider != self.created_by.service_provider_profile:
                raise ValidationError(_('You can only create discounts for your own venue'))

    def save(self, *args, **kwargs):
        """Override save to handle approval timestamp."""
        # Set approved_at when status changes to approved
        if self.is_approved and not self.approved_at:
            self.approved_at = timezone.now()
        elif not self.is_approved:
            self.approved_at = None
            self.approved_by = None

        super().save(*args, **kwargs)

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_approved and self.is_active()


class ServiceDiscount(DiscountBase):
    """
    Discount applied to a specific service.
    Created by service providers for individual services.
    """

    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='discounts',
        help_text=_('Service this discount applies to')
    )

    # Admin approval workflow
    is_approved = models.BooleanField(
        default=False,
        help_text=_('Whether this discount has been approved by admin')
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_service_discounts',
        help_text=_('Admin who approved this discount')
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When this discount was approved')
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Service Discount')
        verbose_name_plural = _('Service Discounts')

    def clean(self):
        """Custom validation for service discounts."""
        super().clean()

        # Ensure the user creating the discount owns the service's venue
        if self.created_by and hasattr(self.created_by, 'service_provider_profile'):
            if self.service.venue.service_provider != self.created_by.service_provider_profile:
                raise ValidationError(_('You can only create discounts for services in your own venue'))

    def save(self, *args, **kwargs):
        """Override save to handle approval timestamp."""
        # Set approved_at when status changes to approved
        if self.is_approved and not self.approved_at:
            self.approved_at = timezone.now()
        elif not self.is_approved:
            self.approved_at = None
            self.approved_by = None

        super().save(*args, **kwargs)

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_approved and self.is_active()

    def get_discounted_service_price(self):
        """Calculate the discounted price for the service."""
        return self.calculate_discounted_price(self.service.price_min)


class PlatformDiscount(DiscountBase):
    """
    Platform-wide discount created by administrators.
    Can be applied across multiple venues and categories.
    """

    # Category filtering (optional)
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='platform_discounts',
        help_text=_('Category this discount applies to (optional - leave blank for all categories)')
    )

    # Minimum booking requirements
    min_booking_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Minimum booking value required to apply this discount')
    )

    # Maximum discount cap for percentage discounts
    max_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Maximum discount amount for percentage discounts (optional)')
    )

    # Featured status for homepage display
    is_featured = models.BooleanField(
        default=False,
        help_text=_('Whether this discount should be featured on the homepage')
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Platform Discount')
        verbose_name_plural = _('Platform Discounts')

    def clean(self):
        """Custom validation for platform discounts."""
        # Call parent clean but skip the discount_value validation
        models.Model.clean(self)

        # Validate date range
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_('Start date must be before end date'))

        # Validate discount value based on type (platform allows up to 100%)
        if self.discount_value is not None:
            if self.discount_type == DiscountType.PERCENTAGE:
                if self.discount_value <= 0 or self.discount_value > 100:
                    raise ValidationError(_('Percentage must be between 0.01 and 100'))
            elif self.discount_type == DiscountType.FIXED_AMOUNT:
                if self.discount_value <= 0:
                    raise ValidationError(_('Fixed amount must be greater than 0'))

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_active()


class DiscountUsage(models.Model):
    """
    Track discount usage by customers for analytics and preventing abuse.
    Uses generic foreign key pattern to track usage across different discount types.
    """

    DISCOUNT_MODEL_CHOICES = [
        ('VenueDiscount', _('Venue Discount')),
        ('ServiceDiscount', _('Service Discount')),
        ('PlatformDiscount', _('Platform Discount')),
    ]

    # User who used the discount
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='discount_usages',
        help_text=_('Customer who used this discount')
    )

    # Generic reference to the discount used
    discount_type = models.CharField(
        max_length=20,
        choices=DISCOUNT_MODEL_CHOICES,
        help_text=_('Type of discount that was used')
    )
    discount_id = models.PositiveIntegerField(
        help_text=_('ID of the specific discount that was used')
    )

    # Booking reference (will be linked when booking_cart_app is implemented)
    booking_reference = models.CharField(
        max_length=100,
        help_text=_('Reference to the booking where this discount was applied')
    )

    # Financial details
    original_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Original price before discount')
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Amount of discount applied')
    )
    final_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Final price after discount')
    )

    # Metadata
    used_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the discount was used')
    )

    class Meta:
        ordering = ['-used_at']
        verbose_name = _('Discount Usage')
        verbose_name_plural = _('Discount Usages')
        indexes = [
            models.Index(fields=['discount_type', 'discount_id']),
            models.Index(fields=['user', '-used_at']),
            models.Index(fields=['-used_at']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'discount_type', 'discount_id', 'booking_reference'],
                name='unique_user_discount_booking'
            )
        ]

    def __str__(self):
        return f"{self.user.email} - {self.discount_type} - {self.used_at.strftime('%Y-%m-%d')}"

    def get_discount_object(self):
        """Get the actual discount object that was used."""
        if self.discount_type == 'VenueDiscount':
            return VenueDiscount.objects.filter(id=self.discount_id).first()
        elif self.discount_type == 'ServiceDiscount':
            return ServiceDiscount.objects.filter(id=self.discount_id).first()
        elif self.discount_type == 'PlatformDiscount':
            return PlatformDiscount.objects.filter(id=self.discount_id).first()
        return None

    def get_savings_amount(self):
        """Calculate the amount saved by using this discount."""
        return self.original_price - self.final_price

    def get_savings_percentage(self):
        """Calculate the percentage saved by using this discount."""
        if self.original_price > 0:
            return round((self.get_savings_amount() / self.original_price) * 100, 2)
        return 0
