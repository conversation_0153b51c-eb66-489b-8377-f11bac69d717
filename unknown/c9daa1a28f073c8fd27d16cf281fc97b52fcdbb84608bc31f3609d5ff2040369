"""Customer and provider views for :mod:`notifications_app`."""

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db.models import Q
from django.http import Http404, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods, require_POST


# --- Local Imports ---
from ..forms import (
    AdminAnnouncementForm,
    BulkNotificationForm,
    NotificationFilterForm,
    NotificationForm,
)
from ..models import AdminAnnouncement, Notification, NotificationPreference
from .common import (
    User,
    log_error,
    log_notification_created,
    log_notification_deleted,
    log_notification_delivery_status,
    log_notification_preferences_updated,
    log_notification_read,
    log_notification_validation_error,
    log_notifications_marked_read,
    log_security_event,
    log_system_announcement_created,
    log_user_activity,
    performance_monitor,
)


# --- Customer and Provider Notification Views ---


@login_required
@require_http_methods(["GET"])
@performance_monitor('notification_list_view')
def notification_list(request):
    """Display paginated list of notifications for the authenticated user."""
    try:
        log_user_activity(
            app_name='notifications_app',
            activity_type='notification_list_access',
            user=request.user,
            request=request,
            details={'user_role': request.user.role},
        )

        notifications = Notification.objects.select_related('user').filter(user=request.user)

        filter_form = NotificationFilterForm(request.GET)
        if filter_form.is_valid():
            notification_type = filter_form.cleaned_data.get('notification_type')
            read_status = filter_form.cleaned_data.get('read_status')
            date_from = filter_form.cleaned_data.get('date_from')
            date_to = filter_form.cleaned_data.get('date_to')

            if notification_type:
                notifications = notifications.filter(notification_type=notification_type)

            if read_status == 'unread':
                notifications = notifications.filter(read_status=Notification.UNREAD)
            elif read_status == 'read':
                notifications = notifications.filter(read_status=Notification.READ)

            if date_from:
                notifications = notifications.filter(created_at__date__gte=date_from)

            if date_to:
                notifications = notifications.filter(created_at__date__lte=date_to)

        paginator = Paginator(notifications, 20)
        page = request.GET.get('page')

        try:
            notifications_page = paginator.page(page)
        except PageNotAnInteger:
            notifications_page = paginator.page(1)
        except EmptyPage:
            notifications_page = paginator.page(paginator.num_pages)

        unread_count = Notification.get_unread_count_for_user(request.user)

        context = {
            'notifications': notifications_page,
            'filter_form': filter_form,
            'unread_count': unread_count,
            'total_notifications': notifications.count(),
        }

        return render(request, 'notifications_app/notification_list.html', context)

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='notification_list_error',
            error_message="Failed to load notification list",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, 'Unable to load notifications. Please try again.')
        if request.user.role == 'customer':
            return redirect('dashboard_app:customer_dashboard')
        elif request.user.role == 'service_provider':
            return redirect('dashboard_app:provider_dashboard')
        else:
            return redirect('dashboard_app:admin_dashboard')


@login_required
@require_http_methods(["GET"])
def notification_detail(request, notification_id):
    """Display detailed view of a specific notification."""
    try:
        notification = get_object_or_404(Notification, id=notification_id, user=request.user)

        if notification.read_status == Notification.UNREAD:
            notification.mark_as_read()
            cache.delete(f"notif_unread_count_{request.user.id}")
            log_notification_read(
                user=request.user,
                notification_id=notification.id,
                notification_title=notification.title,
                request=request,
                details={
                    'notification_type': notification.notification_type,
                    'auto_marked': True,
                },
            )

        log_user_activity(
            app_name='notifications_app',
            activity_type='notification_detail_access',
            user=request.user,
            request=request,
            details={
                'notification_id': notification.id,
                'notification_type': notification.notification_type,
            },
        )

        context = {
            'notification': notification,
        }

        return render(request, 'notifications_app/notification_detail.html', context)

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='unauthorized_notification_access',
            user_email=request.user.email,
            request=request,
            details={'attempted_notification_id': notification_id, 'access_type': 'detail_view'},
        )
        messages.error(request, 'Notification not found.')
        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='notification_detail_error',
            error_message="Failed to load notification detail",
            user=request.user,
            request=request,
            exception=e,
            details={'notification_id': notification_id},
        )
        messages.error(request, 'Unable to load notification details. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """Mark a specific notification as read."""
    try:
        notification = get_object_or_404(Notification, id=notification_id, user=request.user)

        if notification.read_status == Notification.UNREAD:
            notification.mark_as_read()
            log_notification_read(
                user=request.user,
                notification_id=notification.id,
                notification_title=notification.title,
                request=request,
                details={
                    'notification_type': notification.notification_type,
                    'manual_action': True,
                },
            )
            messages.success(request, 'Notification marked as read.')
        else:
            messages.info(request, 'Notification was already read.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': 'Notification marked as read.',
                'unread_count': Notification.get_unread_count_for_user(request.user),
            })

        return redirect('notifications_app:notification_detail', notification_id=notification.id)

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='unauthorized_notification_access',
            user_email=request.user.email,
            request=request,
            details={'attempted_notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Notification not found.'})

        messages.error(request, 'Notification not found.')
        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='mark_notification_read_error',
            error_message="Failed to mark notification as read",
            user=request.user,
            request=request,
            exception=e,
            details={'notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Unable to mark notification as read.'})

        messages.error(request, 'Unable to mark notification as read. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def mark_notification_unread(request, notification_id):
    """Mark a specific notification as unread."""
    try:
        notification = get_object_or_404(Notification, id=notification_id, user=request.user)

        if notification.read_status == Notification.READ:
            notification.mark_as_unread()
            cache.delete(f"notif_unread_count_{request.user.id}")
            log_user_activity(
                app_name='notifications_app',
                activity_type='notification_marked_unread',
                user=request.user,
                request=request,
                details={
                    'notification_id': notification.id,
                    'notification_type': notification.notification_type,
                },
            )
            messages.success(request, 'Notification marked as unread.')
        else:
            messages.info(request, 'Notification was already unread.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': 'Notification marked as unread.',
                'unread_count': Notification.get_unread_count_for_user(request.user),
            })

        return redirect('notifications_app:notification_detail', notification_id=notification.id)

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='unauthorized_notification_access',
            user_email=request.user.email,
            request=request,
            details={'attempted_notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Notification not found.'})

        messages.error(request, 'Notification not found.')
        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='mark_notification_unread_error',
            error_message="Failed to mark notification as unread",
            user=request.user,
            request=request,
            exception=e,
            details={'notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Unable to mark notification as unread.'})

        messages.error(request, 'Unable to mark notification as unread. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def delete_notification(request, notification_id):
    """Delete a specific notification."""
    try:
        notification = get_object_or_404(Notification, id=notification_id, user=request.user)

        notification_type = notification.notification_type
        notification_title = notification.title
        notification.delete()
        cache.delete(f"notif_unread_count_{request.user.id}")

        log_notification_deleted(
            user=request.user,
            notification_id=notification_id,
            notification_title=notification_title,
            request=request,
            details={'notification_type': notification_type},
        )

        messages.success(request, 'Notification deleted successfully.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': 'Notification deleted successfully.',
                'unread_count': Notification.get_unread_count_for_user(request.user),
            })

        return redirect('notifications_app:notification_list')

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='unauthorized_notification_access',
            user_email=request.user.email,
            request=request,
            details={'attempted_notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Notification not found.'})

        messages.error(request, 'Notification not found.')
        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='delete_notification_error',
            error_message="Failed to delete notification",
            user=request.user,
            request=request,
            exception=e,
            details={'notification_id': notification_id},
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Unable to delete notification.'})

        messages.error(request, 'Unable to delete notification. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def mark_all_notifications_read(request):
    """Mark all notifications as read for the authenticated user."""
    try:
        count = Notification.mark_all_as_read_for_user(request.user)
        cache.delete(f"notif_unread_count_{request.user.id}")

        log_notifications_marked_read(
            user=request.user,
            count=count,
            request=request,
            details={'action_type': 'bulk_mark_all'},
        )

        if count > 0:
            messages.success(request, f'{count} notifications marked as read.')
        else:
            messages.info(request, 'No unread notifications to mark.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'{count} notifications marked as read.' if count > 0 else 'No unread notifications to mark.',
                'unread_count': 0,
                'marked_count': count,
            })

        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='mark_all_notifications_read_error',
            error_message="Failed to mark all notifications as read",
            user=request.user,
            request=request,
            exception=e,
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Unable to mark all notifications as read.'})

        messages.error(request, 'Unable to mark all notifications as read. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def mark_all_notifications_unread(request):
    """Mark all notifications as unread for the authenticated user."""
    try:
        count = Notification.mark_all_as_unread_for_user(request.user)
        cache.delete(f"notif_unread_count_{request.user.id}")

        log_notifications_marked_read(
            user=request.user,
            count=count,
            request=request,
            details={'action_type': 'bulk_mark_all_unread'},
        )

        if count > 0:
            messages.success(request, f'{count} notifications marked as unread.')
        else:
            messages.info(request, 'No read notifications to mark.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'{count} notifications marked as unread.' if count > 0 else 'No read notifications to mark.',
                'unread_count': Notification.get_unread_count_for_user(request.user),
                'marked_count': count,
            })

        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='mark_all_notifications_unread_error',
            error_message="Failed to mark all notifications as unread",
            user=request.user,
            request=request,
            exception=e,
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Unable to mark all notifications as unread.'})

        messages.error(request, 'Unable to mark all notifications as unread. Please try again.')
        return redirect('notifications_app:notification_list')


@login_required
@require_POST
def bulk_mark_notifications_read(request):
    """Mark selected notifications as read."""
    ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
    notifications = Notification.objects.filter(
        user=request.user, id__in=ids, read_status=Notification.UNREAD
    )
    count = notifications.update(read_status=Notification.READ, read_at=timezone.now())
    cache.delete(f"notif_unread_count_{request.user.id}")

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': f'{count} marked as read',
            'unread_count': Notification.get_unread_count_for_user(request.user),
        })
    messages.success(request, f'{count} notifications marked as read.')
    return redirect('notifications_app:notification_list')


@login_required
@require_POST
def bulk_mark_notifications_unread(request):
    """Mark selected notifications as unread."""
    ids = request.POST.getlist('ids[]') or request.POST.getlist('ids')
    notifications = Notification.objects.filter(
        user=request.user, id__in=ids, read_status=Notification.READ
    )
    count = notifications.update(read_status=Notification.UNREAD, read_at=None)
    cache.delete(f"notif_unread_count_{request.user.id}")

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': f'{count} marked as unread',
            'unread_count': Notification.get_unread_count_for_user(request.user),
        })
    messages.success(request, f'{count} notifications marked as unread.')
    return redirect('notifications_app:notification_list')


@login_required
@require_http_methods(["GET"])
def get_unread_notifications(request):
    """AJAX endpoint to get unread notifications count and recent notifications."""
    try:
        unread_count = Notification.get_unread_count_for_user(request.user)

        recent_notifications = Notification.objects.filter(
            user=request.user,
            read_status=Notification.UNREAD,
        ).order_by('-created_at')[:5]

        notifications_data = []
        for notification in recent_notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message[:100] + '...' if len(notification.message) > 100 else notification.message,
                'type': notification.notification_type,
                'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M'),
                'is_recent': notification.is_recent,
                'action_url': notification.action_url,
            })

        log_user_activity(
            app_name='notifications_app',
            activity_type='unread_notifications_check',
            user=request.user,
            request=request,
            details={'unread_count': unread_count},
        )

        return JsonResponse({
            'success': True,
            'unread_count': unread_count,
            'notifications': notifications_data,
        })

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='get_unread_notifications_error',
            error_message="Failed to get unread notifications",
            user=request.user,
            request=request,
            exception=e,
        )

        return JsonResponse({
            'success': False,
            'message': 'Unable to load notifications.',
            'unread_count': 0,
            'notifications': [],
        })


@login_required
@require_http_methods(["GET", "POST"])
def notification_preferences_view(request):
    """Allow users to configure notification channels."""
    notification_types = Notification.TYPE_CHOICES

    if request.method == "POST":
        changes = {}
        for key, _ in notification_types:
            for channel in [NotificationPreference.EMAIL, NotificationPreference.DASHBOARD]:
                field = f'preference_{key}_{channel}'
                enabled = field in request.POST
                pref, created = NotificationPreference.objects.get_or_create(
                    user=request.user,
                    notification_type=key,
                    channel=channel,
                    defaults={'is_enabled': enabled},
                )
                if not created and pref.is_enabled != enabled:
                    pref.is_enabled = enabled
                    pref.save(update_fields=['is_enabled'])
                changes[field] = enabled

        log_notification_preferences_updated(
            user=request.user,
            preferences_changed=changes,
            request=request,
        )

        messages.success(request, 'Notification preferences updated.')
        return redirect('notifications_app:notification_preferences')

    # Prepare notification types with preference data
    notification_types_with_prefs = []
    for key, label in notification_types:
        email_enabled = NotificationPreference.objects.filter(
            user=request.user,
            notification_type=key,
            channel=NotificationPreference.EMAIL,
            is_enabled=True,
        ).exists()

        dashboard_enabled = NotificationPreference.objects.filter(
            user=request.user,
            notification_type=key,
            channel=NotificationPreference.DASHBOARD,
            is_enabled=True,
        ).exists()

        notification_types_with_prefs.append({
            'key': key,
            'label': label,
            'email_enabled': email_enabled,
            'dashboard_enabled': dashboard_enabled,
        })

    context = {
        'notification_types': notification_types_with_prefs,
    }
    return render(request, 'notifications_app/notification_preferences.html', context)


@login_required
@require_http_methods(["GET"])
def announcement_detail(request, slug):
    """Display an admin announcement based on slug."""
    try:
        announcement = get_object_or_404(AdminAnnouncement, slug=slug)

        context = {'announcement': announcement}

        return render(request, 'notifications_app/announcement_detail.html', context)

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='announcement_not_found',
            user_email=request.user.email,
            request=request,
            details={'slug': slug},
        )
        messages.error(request, 'Announcement not found.')
        return redirect('notifications_app:notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='announcement_detail_error',
            error_message="Failed to load announcement detail",
            user=request.user,
            request=request,
            exception=e,
            details={'slug': slug},
        )
        messages.error(request, 'Unable to load announcement details.')
        return redirect('notifications_app:notification_list')


@login_required
@require_http_methods(["GET", "POST"])
def test_view(request):
    """Test view for creating sample notifications."""
    from django.conf import settings

    if not getattr(settings, 'ENABLE_TEST_VIEW', False):
        messages.error(request, 'Test view is only available in development mode.')
        return redirect('notifications_app:notification_list')

    try:
        if request.method == 'POST':
            test_notification = Notification.objects.create(
                user=request.user,
                notification_type=Notification.SYSTEM,
                title='Test Notification',
                message=f'This is a test notification created at {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}.',
                action_url=request.build_absolute_uri(reverse('notifications_app:notification_list')),
            )

            log_user_activity(
                app_name='notifications_app',
                activity_type='test_notification_created',
                user=request.user,
                request=request,
                details={'notification_id': test_notification.id},
            )

            messages.success(request, 'Test notification created successfully!')
            return redirect('notifications_app:notification_list')

        context = {
            'user': request.user,
            'notification_count': Notification.objects.filter(user=request.user).count(),
            'unread_count': Notification.get_unread_count_for_user(request.user),
        }

        return render(request, 'notifications_app/test.html', context)

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='test_view_error',
            error_message="Failed to create test notification",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, 'Unable to create test notification. Please try again.')
        return redirect('notifications_app:notification_list')

