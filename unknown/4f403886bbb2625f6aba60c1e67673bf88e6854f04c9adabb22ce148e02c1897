"""Utility helpers for payment calculations."""

# --- Standard Library Imports ---
from decimal import Decimal, ROUND_HALF_UP


TWOPLACES = Decimal('0.01')

def quantize_money(amount: Decimal) -> Decimal:
    """Return the amount rounded to two decimal places."""
    if amount is None:
        return amount
    if not isinstance(amount, Decimal):
        amount = Decimal(amount)
    return amount.quantize(TWOPLACES, rounding=ROUND_HALF_UP)

