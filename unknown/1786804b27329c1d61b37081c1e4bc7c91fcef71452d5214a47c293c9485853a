# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import CustomUser



# --- Form Mixins ---

class AccessibleFormMixin:
    """
    Mixin to add ARIA labels for better accessibility on form fields.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for name, field in self.fields.items():
            field.widget.attrs.setdefault('aria-label', field.label)




# --- Account Deactivation Form ---

class AccountDeactivationForm(AccessibleFormMixin, forms.Form):
    """
    Form for service provider account deactivation with email confirmation.

    Fields:
    - confirm_email: Email field to confirm the user's email address before deactivation.
    """

    confirm_email = forms.EmailField(
        label=_('Confirm your email address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address to confirm'),
                'autocomplete': 'email',
            }
        ),
        help_text=_(
            'Please enter your email address to confirm account deactivation.'
        )
    )

    def __init__(self, user: CustomUser, *args, **kwargs):
        """
        Initialize form with the current user for validation context.

        Args:
            user (CustomUser): The user requesting account deactivation.
        """
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_confirm_email(self) -> str:
        """
        Validate that the provided email matches the authenticated user's email.

        Raises:
            ValidationError: If the emails do not match.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('confirm_email')
        if email and email != self.user.email:
            raise ValidationError(
                _('Email address does not match your account email.'),
                code='invalid_email_match'
            )
        return email









