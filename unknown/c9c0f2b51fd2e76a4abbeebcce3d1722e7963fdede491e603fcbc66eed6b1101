"""
Logging utilities specifically for the payments_app.

This module provides specialized logging functions for payment-related operations,
building on top of the centralized logging utilities in utils/logging_utils.py.
Follows the same patterns as accounts_app, venues_app, discount_app, and booking_cart_app.

Usage:
    from payments_app.logging_utils import log_payment_event, log_refund_event

    log_payment_event(user, payment, 'payment_initiated', request)
    log_refund_event(user, refund_request, 'refund_requested', request)
"""

# --- Standard Library Imports ---
from decimal import Decimal
from typing import Any, Dict, Optional

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

# --- Local App Imports ---
from utils.logging_utils import (
    get_app_logger,
    get_client_info,
    log_audit_event,
    log_error,
    log_performance,
    log_security_event,
    log_user_activity,
)


User = get_user_model()

# Initialize application logger for tests and internal use
logger = get_app_logger('payments_app')



# --- Core Payment Logging ---


def log_payment_event(
    user: User,
    payment: Any,
    event_type: str,
    request: Optional[HttpRequest] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log payment-related events using centralized logging utilities.

    Args:
        user: User involved in the payment
        payment: Payment object
        event_type: Type of payment event
        request: Django HttpRequest object
        additional_data: Additional data to log
    """
    details = {
        'payment_id': str(payment.payment_id) if payment else None,
        'booking_id': str(payment.booking.booking_id) if payment and payment.booking else None,
        'amount': str(payment.amount_paid) if payment else None,
        'payment_status': payment.payment_status if payment else None,
        'payment_method': payment.payment_method if payment else None,
        'provider_id': payment.provider.id if payment and payment.provider else None,
        'event_category': 'payment_operation'
    }

    if additional_data:
        details.update(additional_data)

    log_user_activity(
        app_name='payments_app',
        activity_type=event_type,
        user=user,
        request=request,
        details=details
    )


def log_refund_event(
    user: User,
    refund_request: Any,
    event_type: str,
    request: Optional[HttpRequest] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log refund-related events using centralized logging utilities.

    Args:
        user: User involved in the refund
        refund_request: RefundRequest object
        event_type: Type of refund event
        request: Django HttpRequest object
        additional_data: Additional data to log
    """
    details = {
        'refund_request_id': str(refund_request.refund_request_id) if refund_request else None,
        'payment_id': str(refund_request.payment.payment_id) if refund_request and refund_request.payment else None,
        'requested_amount': str(refund_request.requested_amount) if refund_request else None,
        'processed_amount': str(refund_request.processed_amount) if refund_request else None,
        'refund_status': refund_request.request_status if refund_request else None,
        'reason_category': refund_request.reason_category if refund_request else None,
        'customer_id': refund_request.customer.id if refund_request and refund_request.customer else None,
        'event_category': 'refund_operation'
    }

    if additional_data:
        details.update(additional_data)

    log_user_activity(
        app_name='payments_app',
        activity_type=event_type,
        user=user,
        request=request,
        details=details
    )


def log_stripe_event(
    event_type: str,
    user: Optional[User] = None,
    stripe_event_id: Optional[str] = None,
    payment: Optional[Any] = None,
    request: Optional[HttpRequest] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log Stripe-related events (placeholder for future implementation).

    Args:
        event_type: Type of Stripe event
        user: User involved (if applicable)
        stripe_event_id: Stripe event ID
        payment: Related payment object
        request: Django HttpRequest object
        additional_data: Additional data to log
    """
    details = {
        'stripe_event_id': stripe_event_id,
        'payment_id': str(payment.payment_id) if payment else None,
        'event_category': 'stripe_integration',
        'integration_status': 'placeholder'
    }

    if additional_data:
        details.update(additional_data)

    log_user_activity(
        app_name='payments_app',
        activity_type=event_type,
        user=user,
        request=request,
        details=details
    )


def log_payment_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    payment: Optional[Any] = None,
    exception: Optional[Exception] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log payment-related errors using centralized logging utilities.

    Args:
        error_type: Type of error
        error_message: Error message
        user: User involved (if applicable)
        request: Django HttpRequest object
        payment: Related payment object
        exception: Exception object (if applicable)
        additional_data: Additional error data
    """
    details = {
        'payment_id': str(payment.payment_id) if payment else None,
        'booking_id': str(payment.booking.booking_id) if payment and payment.booking else None,
        'error_category': 'payment_operation'
    }

    if additional_data:
        details.update(additional_data)

    log_error(
        app_name='payments_app',
        error_type=error_type,
        error_message=error_message,
        user=user,
        request=request,
        exception=exception,
        details=details
    )


def performance_monitor(operation_name: str):
    """
    Decorator to monitor performance of payment operations using centralized logging.

    Args:
        operation_name: Name of the operation being monitored
    """
    def decorator(func):
        from functools import wraps
        import time

        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Try to extract user and request from args/kwargs
                user = None
                request = None

                # Look for user in self (for class-based views)
                if args and hasattr(args[0], 'request'):
                    request = args[0].request
                    user = getattr(request, 'user', None) if request else None

                # Look for request in kwargs
                if 'request' in kwargs:
                    request = kwargs['request']
                    user = getattr(request, 'user', None) if request else None

                log_performance(
                    app_name='payments_app',
                    operation=operation_name,
                    duration=duration,
                    user=user,
                    request=request,
                    details={'status': 'success'}
                )

                return result

            except Exception as e:
                duration = time.time() - start_time

                # Try to extract user and request from args/kwargs
                user = None
                request = None

                if args and hasattr(args[0], 'request'):
                    request = args[0].request
                    user = getattr(request, 'user', None) if request else None

                if 'request' in kwargs:
                    request = kwargs['request']
                    user = getattr(request, 'user', None) if request else None

                log_performance(
                    app_name='payments_app',
                    operation=operation_name,
                    duration=duration,
                    user=user,
                    request=request,
                    details={'status': 'error', 'error': str(e)}
                )

                raise

        return wrapper
    return decorator


# ===== SPECIFIC PAYMENT EVENT LOGGING FUNCTIONS =====

def log_payment_initiated(
    user: User,
    payment: Any,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a payment is initiated."""
    log_payment_event(
        user=user,
        payment=payment,
        event_type='payment_initiated',
        request=request,
        additional_data={
            'payment_method': payment.payment_method,
            'provider_id': payment.provider.id if payment.provider else None,
            'booking_total': str(payment.booking.total_price) if payment.booking else None
        }
    )


def log_payment_completed(
    user: User,
    payment: Any,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a payment is completed successfully."""
    log_payment_event(
        user=user,
        payment=payment,
        event_type='payment_completed',
        request=request,
        additional_data={
            'payment_method': payment.payment_method,
            'completed_date': payment.completed_date.isoformat() if payment.completed_date else None,
            'booking_status': payment.booking.status if payment.booking else None
        }
    )


def log_payment_failed(
    user: User,
    payment: Any,
    failure_reason: Optional[str] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a payment fails."""
    log_payment_event(
        user=user,
        payment=payment,
        event_type='payment_failed',
        request=request,
        additional_data={
            'payment_method': payment.payment_method,
            'failure_reason': failure_reason or payment.failure_reason,
            'attempted_amount': str(payment.amount_paid)
        }
    )


# ===== SPECIFIC REFUND EVENT LOGGING FUNCTIONS =====

def log_refund_requested(
    user: User,
    refund_request: Any,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a refund is requested."""
    log_refund_event(
        user=user,
        refund_request=refund_request,
        event_type='refund_requested',
        request=request,
        additional_data={
            'reason_category': refund_request.reason_category,
            'reason_description': refund_request.reason_description[:100],  # Truncate for logging
            'original_payment_amount': str(refund_request.payment.amount_paid) if refund_request.payment else None
        }
    )


def log_refund_approved(
    refund_request: Any,
    user: User,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a refund is approved by admin."""
    log_refund_event(
        user=user,
        refund_request=refund_request,
        event_type='refund_approved',
        request=request,
        additional_data={
            'admin_notes': refund_request.admin_notes[:100] if refund_request.admin_notes else None,
            'approval_date': refund_request.reviewed_at.isoformat() if refund_request.reviewed_at else None
        }
    )


def log_refund_declined(
    refund_request: Any,
    user: User,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a refund is declined by admin."""
    log_refund_event(
        user=user,
        refund_request=refund_request,
        event_type='refund_declined',
        request=request,
        additional_data={
            'admin_notes': refund_request.admin_notes[:100] if refund_request.admin_notes else None,
            'decline_date': refund_request.reviewed_at.isoformat() if refund_request.reviewed_at else None
        }
    )


def log_refund_processed(
    admin_user: User,
    refund_request: Any,
    processed_amount: Decimal,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a refund is processed."""
    log_refund_event(
        user=admin_user,
        refund_request=refund_request,
        event_type='refund_processed',
        request=request,
        additional_data={
            'requested_amount': str(refund_request.requested_amount),
            'processed_amount': str(processed_amount),
            'processing_date': timezone.now().isoformat()
        }
    )


# ===== PROVIDER-SPECIFIC LOGGING FUNCTIONS =====

def log_provider_earnings_viewed(
    user: User,
    request: Optional[HttpRequest] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """Log when a provider views their earnings overview."""
    details = {
        'view_type': 'earnings_overview',
        'provider_role': 'service_provider'
    }

    if additional_data:
        details.update(additional_data)

    log_user_activity(
        app_name='payments_app',
        activity_type='provider_earnings_viewed',
        user=user,
        request=request,
        details=details
    )


def log_provider_payment_history_viewed(
    user: User,
    request: Optional[HttpRequest] = None,
    filters: Optional[Dict[str, Any]] = None
) -> None:
    """Log when a provider views their payment history."""
    details = {
        'view_type': 'payment_history',
        'filters_applied': filters or {},
        'provider_role': 'service_provider'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='provider_payment_history_viewed',
        user=user,
        request=request,
        details=details
    )


def log_provider_payment_detail_viewed(
    provider_user: User,
    payment: Any,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a provider views a specific payment detail."""
    details = {
        'view_type': 'payment_detail',
        'payment_id': str(payment.payment_id),
        'customer_id': payment.customer.id,
        'booking_id': str(payment.booking.booking_id) if payment.booking else None,
        'payment_amount': str(payment.amount_paid),
        'payment_status': payment.payment_status,
        'provider_role': 'service_provider'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='provider_payment_detail_viewed',
        user=provider_user,
        request=request,
        details=details
    )


def log_provider_payout_history_viewed(
    provider_user: User,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a provider views their payout history."""
    details = {
        'view_type': 'payout_history',
        'payout_source': 'stripe_placeholder',
        'provider_role': 'service_provider'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='provider_payout_history_viewed',
        user=provider_user,
        request=request,
        details=details
    )


def log_payout_processed(
    provider_user: User,
    payout_amount: Decimal,
    payout_id: Optional[str] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a payout is processed for a provider (placeholder for Stripe integration)."""
    details = {
        'payout_id': payout_id,
        'payout_amount': str(payout_amount),
        'payout_method': 'stripe',
        'payout_status': 'processed',
        'integration_status': 'placeholder',
        'provider_role': 'service_provider'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='payout_processed',
        user=provider_user,
        request=request,
        details=details
    )


def log_payout_failed(
    provider_user: User,
    payout_amount: Decimal,
    failure_reason: Optional[str] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when a payout fails for a provider."""
    details = {
        'payout_amount': str(payout_amount),
        'payout_method': 'stripe',
        'payout_status': 'failed',
        'failure_reason': failure_reason,
        'integration_status': 'placeholder',
        'provider_role': 'service_provider'
    }

    log_payment_error(
        error_type='payout_failed',
        error_message=f'Payout failed for provider {provider_user.email}: {failure_reason}',
        user=provider_user,
        request=request,
        additional_data=details
    )


# ===== ADMIN-SPECIFIC LOGGING FUNCTIONS =====

def log_admin_refund_management_viewed(
    admin_user: User,
    request: Optional[HttpRequest] = None,
    filters: Optional[Dict[str, Any]] = None
) -> None:
    """Log when admin views refund management page."""
    details = {
        'view_type': 'refund_management',
        'filters_applied': filters or {},
        'admin_action': 'view_refund_management',
        'user_role': 'admin'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='admin_refund_management_viewed',
        user=admin_user,
        request=request,
        details=details
    )


def log_admin_refund_detail_viewed(
    admin_user: User,
    refund_request: Any,
    request: Optional[HttpRequest] = None
) -> None:
    """Log when admin views refund request details."""
    details = {
        'view_type': 'refund_detail',
        'refund_request_id': str(refund_request.refund_request_id),
        'customer_id': refund_request.customer.id,
        'refund_status': refund_request.request_status,
        'requested_amount': str(refund_request.requested_amount),
        'admin_action': 'view_refund_detail',
        'user_role': 'admin'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='admin_refund_detail_viewed',
        user=admin_user,
        request=request,
        details=details
    )


def log_admin_disputed_payments_viewed(
    admin_user: User,
    request: Optional[HttpRequest] = None,
    filters: Optional[Dict[str, Any]] = None
) -> None:
    """Log when admin views disputed payments page."""
    details = {
        'view_type': 'disputed_payments',
        'filters_applied': filters or {},
        'admin_action': 'view_disputed_payments',
        'user_role': 'admin'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='admin_disputed_payments_viewed',
        user=admin_user,
        request=request,
        details=details
    )


def log_admin_payment_analytics_viewed(
    admin_user: User,
    request: Optional[HttpRequest] = None,
    analytics_data: Optional[Dict[str, Any]] = None
) -> None:
    """Log when admin views payment analytics dashboard."""
    details = {
        'view_type': 'payment_analytics',
        'analytics_data': analytics_data or {},
        'admin_action': 'view_payment_analytics',
        'user_role': 'admin'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='admin_payment_analytics_viewed',
        user=admin_user,
        request=request,
        details=details
    )


def log_admin_refund_action(
    admin_user: User,
    refund_request: Any,
    action_type: str,
    request: Optional[HttpRequest] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """Log admin actions on refund requests (approve/decline)."""
    details = {
        'refund_request_id': str(refund_request.refund_request_id),
        'customer_id': refund_request.customer.id,
        'action_type': action_type,
        'admin_action': f'refund_{action_type}',
        'requested_amount': str(refund_request.requested_amount),
        'user_role': 'admin'
    }

    if additional_data:
        details.update(additional_data)

    log_audit_event(
        app_name='payments_app',
        event_type=f'admin_refund_{action_type}',
        user=admin_user,
        request=request,
        details=details
    )


# ===== SECURITY AND UNAUTHORIZED ACCESS LOGGING =====

def log_unauthorized_payment_access(
    user_email: str,
    attempted_action: str,
    payment_id: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """Log unauthorized attempts to access or modify payments."""
    details = {
        'payment_id': payment_id,
        'attempted_action': attempted_action,
        'security_violation': 'unauthorized_payment_access',
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_security_event(
        app_name='payments_app',
        event_type='unauthorized_payment_access',
        user_email=user_email,
        request=request,
        details=details
    )


def log_unauthorized_refund_access(
    user_email: str,
    attempted_action: str,
    refund_request_id: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """Log unauthorized attempts to access or modify refund requests."""
    details = {
        'refund_request_id': refund_request_id,
        'attempted_action': attempted_action,
        'security_violation': 'unauthorized_refund_access',
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_security_event(
        app_name='payments_app',
        event_type='unauthorized_refund_access',
        user_email=user_email,
        request=request,
        details=details
    )


def log_payment_data_breach_attempt(
    user_email: str,
    attempted_data_access: str,
    request: Optional[HttpRequest] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """Log potential payment data breach attempts."""
    details = {
        'attempted_data_access': attempted_data_access,
        'security_violation': 'payment_data_breach_attempt',
        'severity': 'high',
        'timestamp': timezone.now().isoformat()
    }

    if additional_details:
        details.update(additional_details)

    log_security_event(
        app_name='payments_app',
        event_type='payment_data_breach_attempt',
        user_email=user_email,
        request=request,
        details=details
    )


# ===== CUSTOMER-SPECIFIC LOGGING FUNCTIONS =====

def log_customer_payment_history_viewed(
    customer_user: User,
    request: Optional[HttpRequest] = None,
    filters: Optional[Dict[str, Any]] = None
) -> None:
    """Log when a customer views their payment history."""
    details = {
        'view_type': 'customer_payment_history',
        'filters_applied': filters or {},
        'user_role': 'customer'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='customer_payment_history_viewed',
        user=customer_user,
        request=request,
        details=details
    )


def log_customer_refund_history_viewed(
    customer_user: User,
    request: Optional[HttpRequest] = None,
    filters: Optional[Dict[str, Any]] = None
) -> None:
    """Log when a customer views their refund history."""
    details = {
        'view_type': 'customer_refund_history',
        'filters_applied': filters or {},
        'user_role': 'customer'
    }

    log_user_activity(
        app_name='payments_app',
        activity_type='customer_refund_history_viewed',
        user=customer_user,
        request=request,
        details=details
    )
