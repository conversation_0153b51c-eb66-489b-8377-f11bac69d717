# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import ReviewFlag


class ReviewFlagForm(forms.ModelForm):
    """Form for customers to flag inappropriate or fake reviews."""

    class Meta:
        model = ReviewFlag
        fields = ['reason', 'reason_text']
        widgets = {
            'reason': forms.Select(
                choices=ReviewFlag.REASON_CHOICES,
                attrs={'class': 'form-select', 'required': True},
            ),
            'reason_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Please provide additional details about why you are flagging this review...'),
                'maxlength': 500,
            }),
        }
        labels = {
            'reason': _('Reason for Flagging'),
            'reason_text': _('Additional Details (Optional)'),
        }
        help_texts = {
            'reason': _('Select the primary reason for flagging this review'),
            'reason_text': _('Provide additional context to help our moderators understand the issue'),
        }

    def __init__(self, *args, **kwargs):
        self.flagged_by = kwargs.pop('flagged_by', None)
        self.review = kwargs.pop('review', None)
        super().__init__(*args, **kwargs)

    def clean_reason_text(self):
        reason_text = self.cleaned_data.get('reason_text')
        if reason_text and len(reason_text.strip()) < 5:
            raise ValidationError(_('If providing additional details, please write at least 5 characters.'))
        return reason_text.strip() if reason_text else ''

    def save(self, commit=True):
        flag = super().save(commit=False)
        if self.flagged_by:
            flag.flagged_by = self.flagged_by
        if self.review:
            flag.review = self.review
        if commit:
            flag.save()
        return flag
