# --- Standard Library Imports ---
import csv
from datetime import timedelta

# --- Django Imports ---
from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.tokens import default_token_generator
from django.conf import settings
from django.db import models
from django.http import HttpResponse
from django.utils import timezone
from django.utils.encoding import force_bytes
from django.utils.html import format_html
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _
from django.urls import reverse

# --- Local Imports ---
from .models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
    LoginHistory,
    LoginAlert
)


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    Admin interface for CustomUser with bulk actions and status indicators.
    """
    list_display = (
        'email', 'role', 'get_full_name', 'is_active', 'is_staff',
        'date_joined', 'last_login'
    )
    list_filter = ('role', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)
    readonly_fields = ('last_login', 'date_joined')

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'groups', 'user_permissions'
            )
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )

    actions = [
        'activate_users',
        'deactivate_users',
        'clear_failed_login_attempts',
        'reset_passwords',
    ]

    def activate_users(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} user(s) activated.', messages.SUCCESS)
    activate_users.short_description = 'Activate selected users'

    def deactivate_users(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} user(s) deactivated.', messages.SUCCESS)
    deactivate_users.short_description = 'Deactivate selected users'

    def clear_failed_login_attempts(self, request, queryset):
        total_cleared = 0
        for user in queryset:
            cleared, _ = LoginHistory.objects.filter(
                user=user, is_successful=False
            ).delete()
            total_cleared += cleared
        self.message_user(
            request,
            f'Cleared failed attempts for {total_cleared} user(s).',
            messages.SUCCESS
        )
    clear_failed_login_attempts.short_description = 'Clear failed login attempts'

    def reset_passwords(self, request, queryset):
        for user in queryset:
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            url = reverse(
                'password_reset_confirm',
                kwargs={'uidb64': uid, 'token': token}
            )
            full_url = request.build_absolute_uri(url)
            # TODO: send reset link (full_url) to user.email
        self.message_user(request, 'Password reset emails sent.', messages.SUCCESS)
    reset_passwords.short_description = 'Send password reset email'


@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    """Admin for CustomerProfile."""
    list_display = ('user', 'get_full_name', 'phone_number', 'city', 'created_at')
    list_filter = ('gender', 'city', 'created_at')
    search_fields = ('user__email', 'first_name', 'last_name', 'phone_number')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Personal Information'), {
            'fields': (
                'first_name', 'last_name', 'profile_picture',
                'gender', 'birth_month', 'birth_year'
            )
        }),
        (_('Contact Information'), {
            'fields': (
                'phone_number', 'address', 'city', 'zip_code'
            )
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )

    def get_full_name(self, obj):
        return obj.get_full_name()
    get_full_name.short_description = 'Full Name'


@admin.register(ServiceProviderProfile)
class ServiceProviderProfileAdmin(admin.ModelAdmin):
    """Manage service provider profiles."""
    list_display = (
        'business_name', 'user', 'city', 'state',
        'is_public', 'created'
    )
    list_filter = ('state', 'is_public', 'created')
    search_fields = (
        'legal_name', 'display_name', 'user__email',
        'city', 'phone'
    )
    readonly_fields = ('created', 'updated')
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Business'), {'fields': (
            'legal_name', 'display_name', 'description',
            'logo', 'ein'
        )}),
        (_('Contact'), {'fields': (
            'phone', 'contact_name'
        )}),
        (_('Address'), {'fields': (
            'address', 'city', 'state',
            'county', 'zip_code'
        )}),
        (_('Web'), {'fields': (
            'website', 'instagram', 'facebook'
        )}),
        (_('Settings'), {'fields': ('is_public',)}),
        (_('Metadata'), {'fields': ('created', 'updated')}),
    )


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    """Manage service provider team members."""
    list_display = (
        'name', 'position', 'service_provider',
        'is_active', 'created'
    )
    list_filter = ('is_active', 'position', 'created')
    search_fields = (
        'name', 'position',
        'service_provider__legal_name'
    )
    readonly_fields = ('created', 'updated')
    fieldsets = (
        (_('Service Provider'), {'fields': ('service_provider',)}),
        (_('Staff Info'), {'fields': (
            'name', 'position', 'photo', 'is_active'
        )}),
        (_('Metadata'), {'fields': ('created', 'updated')}),
    )


@admin.register(LoginHistory)
class LoginHistoryAdmin(admin.ModelAdmin):
    """Monitor login history with risk indicators."""
    list_display = (
        'user', 'timestamp', 'ip_address', 'is_successful',
        'user_role', 'get_user_agent_short', 'is_suspicious'
    )
    list_filter = (
        'is_successful', 'timestamp', 'user__role',
        ('timestamp', admin.DateFieldListFilter)
    )
    search_fields = ('user__email', 'ip_address')
    readonly_fields = (
        'timestamp', 'user', 'ip_address',
        'user_agent', 'is_successful'
    )
    date_hierarchy = 'timestamp'
    actions = ['cleanup_old_records', 'export_security_report']

    def user_role(self, obj):
        return getattr(obj.user, 'get_role_display', lambda: obj.user.role)()
    user_role.short_description = 'Role'

    def get_user_agent_short(self, obj):
        ua = obj.user_agent or ''
        browser = next((b for b in ['Chrome', 'Firefox', 'Safari', 'Edge'] if b in ua), 'Other')
        os_ = next((o for o in ['Windows', 'Mac', 'Linux', 'Android', 'iOS'] if o in ua), 'Unknown')
        return f"{browser} on {os_}"
    get_user_agent_short.short_description = 'Browser/OS'

    def is_suspicious(self, obj):
        since = timezone.now() - timedelta(hours=1)
        fails = LoginHistory.objects.filter(
            ip_address=obj.ip_address,
            is_successful=False,
            timestamp__gte=since
        ).count()
        if fails >= 5:
            return format_html('<span style="color:red;">High Risk</span>')
        if fails >= 3:
            return format_html('<span style="color:orange;">Medium Risk</span>')
        return 'Failed' if not obj.is_successful else 'Normal'
    is_suspicious.short_description = 'Risk'

    def cleanup_old_records(self, request, queryset):
        cutoff = timezone.now() - timedelta(days=90)
        deleted, _ = LoginHistory.objects.filter(
            timestamp__lt=cutoff
        ).delete()
        self.message_user(request, f'Cleaned {deleted} records.', messages.SUCCESS)
    cleanup_old_records.short_description = 'Clean old records'

    def export_security_report(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = (
            f'attachment; filename="security_{timezone.now():%Y%m%d_%H%M%S}.csv"'
        )
        writer = csv.writer(response)
        writer.writerow(['Email', 'Role', 'Time', 'IP', 'Success', 'Risk'])
        for obj in queryset.order_by('-timestamp'):
            writer.writerow([
                obj.user.email,
                self.user_role(obj),
                obj.timestamp,
                obj.ip_address,
                obj.is_successful,
                self.is_suspicious(obj),
            ])
        return response
    export_security_report.short_description = 'Export report'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(LoginAlert)
class LoginAlertAdmin(admin.ModelAdmin):
    """Manage login alerts and export stats."""
    list_display = (
        'alert_type', 'ip_address', 'user', 'severity',
        'attempt_count', 'is_resolved', 'created',
        'get_time_since_created'
    )
    list_filter = ('alert_type', 'severity', 'is_resolved', 'created')
    search_fields = ('ip_address', 'user__email')
    readonly_fields = ('created', 'updated')
    actions = ['mark_as_resolved', 'mark_as_unresolved', 'export_alert_report']

    def get_time_since_created(self, obj):
        delta = timezone.now() - obj.created
        if delta.days:
            return f"{delta.days}d ago"
        return f"{delta.seconds // 3600}h ago"
    get_time_since_created.short_description = 'Age'

    def mark_as_resolved(self, request, queryset):
        count = queryset.filter(is_resolved=False).update(is_resolved=True)
        self.message_user(request, f'{count} resolved.', messages.SUCCESS)
    mark_as_resolved.short_description = 'Resolve'

    def mark_as_unresolved(self, request, queryset):
        count = queryset.update(is_resolved=False)
        self.message_user(request, f'{count} unresolved.', messages.SUCCESS)
    mark_as_unresolved.short_description = 'Unresolve'

    def export_alert_report(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = (
            f'attachment; filename="alerts_{timezone.now():%Y%m%d_%H%M%S}.csv"'
        )
        writer = csv.writer(response)
        writer.writerow(['Type', 'IP', 'Email', 'Severity', 'Count', 'Resolved'])
        for obj in queryset.order_by('-created'):
            writer.writerow([
                obj.alert_type,
                obj.ip_address,
                obj.user.email,
                obj.severity,
                obj.attempt_count,
                obj.is_resolved,
            ])
        return response
    export_alert_report.short_description = 'Export alerts'
