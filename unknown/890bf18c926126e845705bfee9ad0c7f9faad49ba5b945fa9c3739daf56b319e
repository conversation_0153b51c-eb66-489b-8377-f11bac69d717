# """Helper utilities for dashboard forms and views."""

# --- Standard Library Imports ---
from datetime import date, timedelta

# --- Third-Party Imports ---
from django.utils import timezone


def get_date_range(form, default_start=None, default_end=None, default_days=30):
    """Return (start_date, end_date) based on DateRangeForm."""
    end_date = default_end or timezone.now().date()
    if default_start:
        start_date = default_start
    else:
        start_date = end_date - timedelta(days=default_days)

    if form is not None and form.is_valid():
        period = form.cleaned_data.get('period')
        custom_start = form.cleaned_data.get('start_date')
        custom_end = form.cleaned_data.get('end_date')

        if period == 'custom' and custom_start and custom_end:
            start_date = custom_start
            end_date = custom_end
        elif period == 'today':
            start_date = end_date
        elif period == 'yesterday':
            start_date = end_date - timedelta(days=1)
            end_date = start_date
        elif period == 'this_week':
            start_date = end_date - timedelta(days=end_date.weekday())
        elif period == 'last_week':
            start_date = end_date - timedelta(days=end_date.weekday() + 7)
            end_date = end_date - timedelta(days=end_date.weekday() + 1)
        elif period == 'this_month':
            start_date = end_date.replace(day=1)
        elif period == 'last_month':
            if end_date.month == 1:
                start_date = date(end_date.year - 1, 12, 1)
                end_date = date(end_date.year - 1, 12, 31)
            else:
                start_date = date(end_date.year, end_date.month - 1, 1)
                end_date = date(end_date.year, end_date.month, 1) - timedelta(days=1)
        elif period == 'this_year':
            start_date = date(end_date.year, 1, 1)
        elif period == 'last_year':
            start_date = date(end_date.year - 1, 1, 1)
            end_date = date(end_date.year - 1, 12, 31)

    return start_date, end_date


def get_valid_param(request, name, allowed_values, default):
    """Return sanitized GET parameter within allowed values."""
    value = request.GET.get(name, default)
    if value not in allowed_values:
        return default
    return value


def get_int_param(request, name, default=1, minimum=None, maximum=None):
    """Return integer GET parameter or default if invalid.

    Parameters
    ----------
    request : HttpRequest
        The current request object.
    name : str
        Name of the GET parameter.
    default : int, optional
        Fallback value if the parameter is missing or invalid.
    minimum : int, optional
        Minimum allowed value (inclusive). If provided and the parameter is
        below this value it will be clamped.
    maximum : int, optional
        Maximum allowed value (inclusive). If provided and the parameter is
        above this value it will be clamped.
    """

    try:
        value = int(request.GET.get(name, default))
    except (TypeError, ValueError):
        value = default

    if minimum is not None and value < minimum:
        value = minimum
    if maximum is not None and value > maximum:
        value = maximum

    return value
