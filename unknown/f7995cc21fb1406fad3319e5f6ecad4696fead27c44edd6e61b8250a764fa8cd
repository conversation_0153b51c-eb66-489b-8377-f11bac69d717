{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Admin Booking List - CozyWish{% endblock %}

{% block booking_content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>All Bookings</h4>
                        <div>
                            <a href="{% url 'booking_cart_app:admin_booking_dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending</option>
                                    <option value="confirmed" {% if request.GET.status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                    <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>Completed</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}" placeholder="From Date">
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}" placeholder="To Date">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{% url 'booking_cart_app:admin_booking_list' %}" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    {% if bookings %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Customer</th>
                                    <th>Provider</th>
                                    <th>Venue</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in bookings %}
                                <tr>
                                    <td>
                                        <strong>{{ booking.booking_id|truncatechars:8 }}</strong>
                                        <br>
                                        <small class="text-muted">{{ booking.created_at|date:"M d, Y H:i" }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ booking.customer.email }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {% if booking.customer.customerprofile %}
                                                {{ booking.customer.customerprofile.first_name }} {{ booking.customer.customerprofile.last_name }}
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <strong>{{ booking.venue.service_provider.user.email }}</strong>
                                        <br>
                                        <small class="text-muted">{{ booking.venue.service_provider.business_name }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ booking.venue.venue_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ booking.venue.city }}, {{ booking.venue.state }}</small>
                                    </td>
                                    <td>
                                        {% if booking.items.all %}
                                            {% for item in booking.items.all|slice:":1" %}
                                                {{ item.scheduled_date|date:"M d, Y" }}
                                                <br>
                                                <small class="text-muted">{{ item.scheduled_time|time:"g:i A" }}</small>
                                            {% endfor %}
                                            {% if booking.items.count > 1 %}
                                                <br><small class="badge badge-info">+{{ booking.items.count|add:"-1" }} more</small>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>${{ booking.total_amount }}</strong>
                                    </td>
                                    <td>
                                        <a href="{% url 'booking_cart_app:admin_booking_detail' booking.slug %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            View Details
                                        </a>
                                        {% if booking.status == 'pending' %}
                                        <a href="{% url 'booking_cart_app:admin_update_booking_status' booking.slug %}" 
                                           class="btn btn-sm btn-outline-warning">
                                            Update Status
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Bookings pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Previous</a>
                            </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Last</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <h5>No bookings found</h5>
                        <p class="text-muted">No bookings match your current filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
