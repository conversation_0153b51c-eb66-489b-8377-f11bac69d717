"""Template filters used across admin templates."""

# --- Third-Party Imports ---
from django import template

register = template.Library()

@register.filter
def initials(user):
    """Return initials from user's name or email."""
    if not user:
        return ''
    first = (getattr(user, 'first_name', '') or '').strip()
    last = (getattr(user, 'last_name', '') or '').strip()
    if first or last:
        return (first[:1] + last[:1]).upper()
    email = getattr(user, 'email', '')
    return email[:1].upper() if email else ''
