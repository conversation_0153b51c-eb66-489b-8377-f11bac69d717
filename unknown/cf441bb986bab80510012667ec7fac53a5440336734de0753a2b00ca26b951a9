"""
Unit tests for dashboard_app forms.

This module contains comprehensive unit tests for all form classes in the dashboard_app,
including DateRangeForm validation and functionality.
"""

# Standard library imports
from datetime import date, timedelta

# Django imports
from django.test import TestCase
from django.forms import ValidationError
from django import forms

# Local imports
from dashboard_app.forms import DateRangeForm


class DateRangeFormTest(TestCase):
    """Test the DateRangeForm functionality."""

    def test_form_with_default_values(self):
        """Test form with default values."""
        form = DateRangeForm(data={})

        # Check that form is valid with empty data (all fields are optional)
        self.assertTrue(form.is_valid())

        # Check default period initial value
        self.assertEqual(form.fields['period'].initial, 'this_month')

    def test_form_with_valid_period_choices(self):
        """Test form with all valid period choices."""
        valid_periods = [
            'today', 'yesterday', 'this_week', 'last_week',
            'this_month', 'last_month', 'this_year', 'last_year'
        ]

        for period in valid_periods:
            form_data = {'period': period}
            form = DateRangeForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for period: {period}")

        # Test custom period separately with required dates
        form_data = {
            'period': 'custom',
            'start_date': date.today() - timedelta(days=7),
            'end_date': date.today()
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid(), "Form should be valid for custom period with dates")

    def test_form_with_invalid_period(self):
        """Test form with invalid period choice."""
        form_data = {'period': 'invalid_period'}
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('period', form.errors)

    def test_custom_period_with_valid_dates(self):
        """Test custom period with valid start and end dates."""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        
        form_data = {
            'period': 'custom',
            'start_date': start_date,
            'end_date': end_date
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_custom_period_missing_start_date(self):
        """Test custom period with missing start date."""
        form_data = {
            'period': 'custom',
            'end_date': date.today()
        }
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Both start date and end date are required for custom range.', form.non_field_errors())

    def test_custom_period_missing_end_date(self):
        """Test custom period with missing end date."""
        form_data = {
            'period': 'custom',
            'start_date': date.today() - timedelta(days=7)
        }
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Both start date and end date are required for custom range.', form.non_field_errors())

    def test_custom_period_missing_both_dates(self):
        """Test custom period with both dates missing."""
        form_data = {'period': 'custom'}
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Both start date and end date are required for custom range.', form.non_field_errors())

    def test_start_date_after_end_date(self):
        """Test validation when start date is after end date."""
        start_date = date.today()
        end_date = date.today() - timedelta(days=7)
        
        form_data = {
            'period': 'custom',
            'start_date': start_date,
            'end_date': end_date
        }
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Start date cannot be after end date.', form.non_field_errors())

    def test_same_start_and_end_date(self):
        """Test validation when start and end dates are the same."""
        same_date = date.today()
        
        form_data = {
            'period': 'custom',
            'start_date': same_date,
            'end_date': same_date
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_non_custom_period_with_dates(self):
        """Test that non-custom periods work even with date fields provided."""
        form_data = {
            'period': 'this_month',
            'start_date': date.today() - timedelta(days=7),
            'end_date': date.today()
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_field_widgets(self):
        """Test that form fields have correct widgets and attributes."""
        form = DateRangeForm()

        # Test period field widget
        period_widget = form.fields['period'].widget
        self.assertEqual(period_widget.attrs.get('class'), 'form-select')

        # Test start_date field widget
        start_date_widget = form.fields['start_date'].widget
        self.assertEqual(start_date_widget.attrs.get('class'), 'form-control')
        # Check that the widget is a DateInput widget with correct input_type
        self.assertIsInstance(start_date_widget, forms.DateInput)
        self.assertEqual(start_date_widget.input_type, 'date')

        # Test end_date field widget
        end_date_widget = form.fields['end_date'].widget
        self.assertEqual(end_date_widget.attrs.get('class'), 'form-control')
        # Check that the widget is a DateInput widget with correct input_type
        self.assertIsInstance(end_date_widget, forms.DateInput)
        self.assertEqual(end_date_widget.input_type, 'date')

    def test_form_field_requirements(self):
        """Test form field requirements."""
        form = DateRangeForm()
        
        # All fields should be optional by default
        self.assertFalse(form.fields['period'].required)
        self.assertFalse(form.fields['start_date'].required)
        self.assertFalse(form.fields['end_date'].required)

    def test_period_choices_content(self):
        """Test that period choices contain expected values."""
        form = DateRangeForm()
        period_choices = form.fields['period'].choices
        
        expected_choices = [
            ('today', 'Today'),
            ('yesterday', 'Yesterday'),
            ('this_week', 'This Week'),
            ('last_week', 'Last Week'),
            ('this_month', 'This Month'),
            ('last_month', 'Last Month'),
            ('this_year', 'This Year'),
            ('last_year', 'Last Year'),
            ('custom', 'Custom Range'),
        ]
        
        self.assertEqual(list(period_choices), expected_choices)

    def test_form_clean_method_with_valid_data(self):
        """Test the clean method with valid data."""
        form_data = {
            'period': 'this_week',
            'start_date': date.today() - timedelta(days=7),
            'end_date': date.today()
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        cleaned_data = form.cleaned_data
        self.assertEqual(cleaned_data['period'], 'this_week')
        self.assertIsInstance(cleaned_data['start_date'], date)
        self.assertIsInstance(cleaned_data['end_date'], date)

    def test_form_clean_method_with_invalid_custom_range(self):
        """Test the clean method with invalid custom range."""
        form_data = {
            'period': 'custom',
            'start_date': date.today(),
            'end_date': date.today() - timedelta(days=1)
        }
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())

    def test_empty_form_submission(self):
        """Test form submission with no data."""
        form = DateRangeForm(data={})
        self.assertTrue(form.is_valid())  # All fields are optional

    def test_form_with_future_dates(self):
        """Test form with future dates."""
        future_start = date.today() + timedelta(days=1)
        future_end = date.today() + timedelta(days=7)
        
        form_data = {
            'period': 'custom',
            'start_date': future_start,
            'end_date': future_end
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_with_very_old_dates(self):
        """Test form with very old dates."""
        old_start = date(2020, 1, 1)
        old_end = date(2020, 12, 31)
        
        form_data = {
            'period': 'custom',
            'start_date': old_start,
            'end_date': old_end
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_rendering(self):
        """Test that form can be rendered without errors."""
        form = DateRangeForm()
        
        # Test that form can be converted to string (rendered)
        form_html = str(form)
        self.assertIsInstance(form_html, str)
        self.assertIn('period', form_html)
        self.assertIn('start_date', form_html)
        self.assertIn('end_date', form_html)
