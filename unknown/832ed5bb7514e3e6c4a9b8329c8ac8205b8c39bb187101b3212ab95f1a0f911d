{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}{{ page.title }} - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">{{ page.title }}</h1>
    <div>
        <a href="{% url 'admin_app:static_page_list' %}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to Pages
        </a>
        <a href="{% url 'admin_app:static_page_edit' slug=page.slug %}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>Edit Page
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Page Content</h5>
            </div>
            <div class="card-body">
                {% if page.featured_image %}
                    <img src="{{ page.featured_image.url }}" alt="{{ page.title }}" class="img-fluid mb-3 rounded">
                {% endif %}
                
                <div class="content-preview">
                    {{ page.content|safe }}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Page Information</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Status:</dt>
                    <dd class="col-sm-8">
                        {% if page.status == 'published' %}
                            <span class="badge bg-success">Published</span>
                        {% elif page.status == 'draft' %}
                            <span class="badge bg-warning">Draft</span>
                        {% else %}
                            <span class="badge bg-secondary">Archived</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-4">Featured:</dt>
                    <dd class="col-sm-8">
                        {% if page.is_featured %}
                            <i class="fas fa-star text-warning"></i> Yes
                        {% else %}
                            <i class="far fa-star text-muted"></i> No
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-4">Slug:</dt>
                    <dd class="col-sm-8"><code>{{ page.slug }}</code></dd>
                    
                    <dt class="col-sm-4">Created:</dt>
                    <dd class="col-sm-8">
                        <small>{{ page.created_at|date:"M d, Y H:i" }}</small>
                        {% if page.created_by %}
                            <br><small class="text-muted">by {{ page.created_by.get_full_name|default:page.created_by.email }}</small>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-4">Updated:</dt>
                    <dd class="col-sm-8">
                        <small>{{ page.updated_at|date:"M d, Y H:i" }}</small>
                        {% if page.updated_by %}
                            <br><small class="text-muted">by {{ page.updated_by.get_full_name|default:page.updated_by.email }}</small>
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>
        
        {% if page.meta_title or page.meta_description or page.meta_keywords %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">SEO Information</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    {% if page.meta_title %}
                    <dt class="col-sm-4">Meta Title:</dt>
                    <dd class="col-sm-8"><small>{{ page.meta_title }}</small></dd>
                    {% endif %}
                    
                    {% if page.meta_description %}
                    <dt class="col-sm-4">Meta Description:</dt>
                    <dd class="col-sm-8"><small>{{ page.meta_description }}</small></dd>
                    {% endif %}
                    
                    {% if page.meta_keywords %}
                    <dt class="col-sm-4">Meta Keywords:</dt>
                    <dd class="col-sm-8"><small>{{ page.meta_keywords }}</small></dd>
                    {% endif %}
                </dl>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block admin_css %}
<style>
.content-preview {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}
</style>
{% endblock %}
