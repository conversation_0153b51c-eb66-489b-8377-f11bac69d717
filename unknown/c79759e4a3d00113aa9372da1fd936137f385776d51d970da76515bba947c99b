{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Request Refund - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:payment_history' %}">Payment History</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:payment_detail' payment_id=payment.payment_id %}">Payment Details</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Request Refund</li>
                </ol>
            </nav>
            
            <div class="page-header mb-4">
                <h1 class="h2">Request Refund</h1>
                <p class="text-muted">Submit a refund request for your payment</p>
            </div>
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Refund Policy Notice -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Refund Policy</h6>
                                <p class="mb-0">Refunds are subject to our refund policy and will be reviewed by our team. Processing may take 5-7 business days depending on your payment method.</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Refund Request Form -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-undo me-2"></i>Refund Request Form
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="mb-4">
                                    {{ form.reason_category|as_crispy_field }}
                                </div>
                                
                                <div class="mb-4">
                                    {{ form.reason_description|as_crispy_field }}
                                </div>
                                
                                <div class="mb-4">
                                    {{ form.requested_amount|as_crispy_field }}
                                </div>
                                
                                <div class="alert alert-warning mb-4">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div>
                                            <h6 class="alert-heading">Important Notice</h6>
                                            <ul class="mb-0">
                                                <li>Refund requests are reviewed manually by our team</li>
                                                <li>You will receive an email notification once your request is reviewed</li>
                                                <li>Approved refunds will be processed to your original payment method</li>
                                                <li>Please provide detailed information to help us process your request quickly</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'payments_app:payment_detail' payment_id=payment.payment_id %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Payment Details
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-undo me-2"></i>Submit Refund Request
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Payment Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>Payment Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6>Payment ID</h6>
                                <p class="mb-1"><code>{{ payment.payment_id|truncatechars:20 }}</code></p>
                                
                                <h6>Payment Date</h6>
                                <p class="mb-1">{{ payment.payment_date|date:"M d, Y H:i" }}</p>
                                
                                <h6>Amount Paid</h6>
                                <p class="mb-1 fw-bold">${{ payment.amount_paid }}</p>
                                
                                <h6>Payment Method</h6>
                                <p class="mb-1">{{ payment.get_payment_method_display }}</p>
                                
                                {% if payment.refunded_amount > 0 %}
                                <h6>Already Refunded</h6>
                                <p class="mb-1 text-danger">${{ payment.refunded_amount }}</p>
                                {% endif %}
                                
                                <h6>Maximum Refundable</h6>
                                <p class="mb-0 fw-bold text-success">${{ payment.remaining_refundable_amount }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Booking Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-check me-2"></i>Booking Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6>Booking ID</h6>
                                <p class="mb-1">
                                    <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="text-decoration-none">
                                        <code>{{ payment.booking.booking_id|truncatechars:20 }}</code>
                                    </a>
                                </p>
                                
                                <h6>Venue</h6>
                                <p class="mb-1">{{ payment.booking.venue.name }}</p>
                                
                                <h6>Booking Date</h6>
                                <p class="mb-1">{{ payment.booking.booking_date|date:"M d, Y H:i" }}</p>
                                
                                <h6>Status</h6>
                                <p class="mb-0">
                                    <span class="badge bg-info">{{ payment.booking.get_status_display }}</span>
                                </p>
                            </div>
                            
                            <div class="d-grid">
                                <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-calendar-check me-2"></i>View Booking Details
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Support -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-headset me-2"></i>Need Help?
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">If you have questions about the refund process, our support team is here to help.</p>
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="fas fa-headset me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
