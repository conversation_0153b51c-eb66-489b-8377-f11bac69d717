"""Shared helpers and decorators used across admin views."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required, user_passes_test
from django.shortcuts import render
from django.db.models import Avg, Count, Q
from django.utils import timezone
from datetime import timedelta

# Shared constants
ITEMS_PER_PAGE = 20

# Get the custom user model
User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


def is_admin_user(user):
    """Check if user is an admin/staff member."""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def admin_required(view_func):
    """Decorator ensuring the user is logged in and is an admin."""
    decorated_view = login_required(
        user_passes_test(is_admin_user, login_url='admin_app:admin_login')(view_func)
    )
    return decorated_view


def home_view(request):
    """
    Display the home page with Popular Categories, Service Highlights, Top Picks, Trending, and Hot Deals sections.
    """
    try:
        # Import models here to avoid circular imports
        from venues_app.models import Venue, Category
        from discount_app.models import VenueDiscount, ServiceDiscount
        from review_app.models import Review
        from venues_app.forms.search import VenueSearchForm
    except ImportError as e:
        logger.error(f"Import error in home_view: {e}")
        # Return a basic home page if imports fail
        return render(request, 'admin_app/home.html', {
            'hero_section': True,
            'categories': [],
            'popular_categories': [],
            'popular_service_types': [],
            'search_form': None,
            'top_venues': [],
            'trending_venues': [],
            'discounted_venues': [],
        })

    try:
        # Get current time for filtering
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        # Get popular categories (categories with most approved and active venues)
        popular_categories = Category.objects.filter(
            is_active=True,
            venues__approval_status=Venue.APPROVED,
            venues__visibility=Venue.ACTIVE
        ).annotate(
            venue_count=Count('venues', distinct=True)
        ).filter(venue_count__gt=0).order_by('-venue_count')[:6]
    except Exception as e:
        logger.error(f"Database error in home_view popular_categories: {e}")
        popular_categories = []

    # Get popular services for service highlights
    popular_service_types = [
        {
            'name': 'Massage Therapy',
            'description': 'Relaxing full-body massages',
            'icon': 'fas fa-hand-paper',
            'search_query': 'massage'
        },
        {
            'name': 'Facial Treatments',
            'description': 'Rejuvenating skincare services',
            'icon': 'fas fa-user-circle',
            'search_query': 'facial'
        },
        {
            'name': 'Manicure & Pedicure',
            'description': 'Professional nail care',
            'icon': 'fas fa-hand-sparkles',
            'search_query': 'manicure'
        },
        {
            'name': 'Hair Styling',
            'description': 'Expert hair cuts and styling',
            'icon': 'fas fa-cut',
            'search_query': 'hair'
        },
        {
            'name': 'Body Treatments',
            'description': 'Luxurious body wraps and scrubs',
            'icon': 'fas fa-spa',
            'search_query': 'body treatment'
        },
        {
            'name': 'Wellness Therapy',
            'description': 'Holistic wellness services',
            'icon': 'fas fa-leaf',
            'search_query': 'wellness'
        }
    ]

    try:
        # Top Picks: Venues with highest ratings (4.5+ stars) and good review count
        # If not enough venues meet criteria, fall back to general approved venues
        top_picks = Venue.objects.filter(
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        ).annotate(
            avg_rating=Avg('reviews__rating'),
            review_count=Count('reviews')
        ).filter(
            avg_rating__gte=4.5,
            review_count__gte=5
        ).order_by('-avg_rating', '-review_count')[:8]

        # If we don't have enough top picks, get general venues
        if len(top_picks) < 4:
            top_picks = Venue.objects.filter(
                approval_status=Venue.APPROVED,
                visibility=Venue.ACTIVE,
            ).annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).order_by('-avg_rating', '-review_count', 'venue_name')[:8]
    except Exception as e:
        logger.error(f"Database error in home_view top_picks: {e}")
        top_picks = []

    try:
        # Trending: Venues with recent activity (reviews or high ratings in last 30 days)
        # If not enough venues meet criteria, fall back to general approved venues
        trending_venues = Venue.objects.filter(
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
            reviews__created_at__gte=thirty_days_ago
        ).annotate(
            recent_reviews=Count('reviews', filter=Q(reviews__created_at__gte=thirty_days_ago)),
            avg_rating=Avg('reviews__rating')
        ).filter(
            recent_reviews__gte=2
        ).order_by('-recent_reviews', '-avg_rating').distinct()[:8]

        # If we don't have enough trending venues, get general venues
        if len(trending_venues) < 4:
            trending_venues = Venue.objects.filter(
                approval_status=Venue.APPROVED,
                visibility=Venue.ACTIVE,
            ).exclude(
                id__in=[v.id for v in top_picks]
            ).annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).order_by('-review_count', '-avg_rating', 'venue_name')[:8]
    except Exception as e:
        logger.error(f"Database error in home_view trending_venues: {e}")
        trending_venues = []

    try:
        # Hot Deals: Venues with active discounts
        # Get venues with venue-level discounts
        venues_with_venue_discounts = VenueDiscount.objects.filter(
            start_date__lte=now,
            end_date__gte=now,
            is_approved=True,
            venue__approval_status=Venue.APPROVED,
            venue__visibility=Venue.ACTIVE
        ).values_list('venue_id', flat=True)

        # Get venues with service-level discounts
        venues_with_service_discounts = ServiceDiscount.objects.filter(
            start_date__lte=now,
            end_date__gte=now,
            is_approved=True,
            service__venue__approval_status=Venue.APPROVED,
            service__venue__visibility=Venue.ACTIVE,
            service__is_active=True
        ).values_list('service__venue_id', flat=True)

        # Combine both types of discounted venues
        discounted_venue_ids = list(venues_with_venue_discounts) + list(venues_with_service_discounts)

        hot_deals = Venue.objects.filter(
            id__in=discounted_venue_ids,
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        ).annotate(
            avg_rating=Avg('reviews__rating'),
            review_count=Count('reviews')
        ).distinct()[:8]

        # If we don't have enough hot deals, get general venues
        if len(hot_deals) < 4:
            used_venue_ids = [v.id for v in top_picks] + [v.id for v in trending_venues]
            hot_deals = Venue.objects.filter(
                approval_status=Venue.APPROVED,
                visibility=Venue.ACTIVE,
            ).exclude(
                id__in=used_venue_ids
            ).annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).order_by('-avg_rating', 'venue_name')[:8]
    except Exception as e:
        logger.error(f"Database error in home_view hot_deals: {e}")
        hot_deals = []



    try:
        # Get active categories for the search dropdown
        categories = Category.objects.filter(is_active=True).order_by('category_name')
    except Exception as e:
        logger.error(f"Database error in home_view categories: {e}")
        categories = []

    try:
        # Initialize search form
        search_form = VenueSearchForm()
    except Exception as e:
        logger.error(f"Form error in home_view search_form: {e}")
        search_form = None

    context = {
        'hero_section': True,  # Enable hero section with radial gradient background
        'categories': categories,
        'popular_categories': popular_categories,
        'popular_service_types': popular_service_types,
        'search_form': search_form,
        'top_venues': top_picks,  # Renamed to match template expectations
        'trending_venues': trending_venues,
        'discounted_venues': hot_deals,  # Renamed to match template expectations
    }

    return render(request, 'admin_app/home.html', context)

