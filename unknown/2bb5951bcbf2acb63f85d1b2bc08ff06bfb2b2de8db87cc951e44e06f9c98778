"""
Unit tests for notifications_app forms.

This module contains comprehensive unit tests for all form classes in the notifications_app,
including NotificationPreferenceForm, SystemAnnouncementForm, NotificationCategoryForm, 
NotificationForm, and BulkNotificationForm.
"""

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

# Local imports
from notifications_app.forms import (
    NotificationForm, AdminAnnouncementForm, NotificationFilterForm, BulkNotificationForm
)
from notifications_app.models import Notification, AdminAnnouncement

User = get_user_model()


class NotificationFormTest(TestCase):
    """Test the NotificationForm."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_valid_notification_form(self):
        """Test form with valid data."""
        form_data = {
            'user': self.user.id,
            'notification_type': Notification.BOOKING,
            'title': 'Booking Confirmation',
            'message': 'Your booking has been confirmed successfully.',
            'action_url': 'https://example.com/booking/123'
        }

        form = NotificationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_notification_form_type_choices(self):
        """Test that form accepts valid notification type choices."""
        valid_types = [
            Notification.BOOKING,
            Notification.PAYMENT,
            Notification.REVIEW,
            Notification.ANNOUNCEMENT,
            Notification.SYSTEM
        ]

        for notification_type in valid_types:
            form_data = {
                'user': self.user.id,
                'notification_type': notification_type,
                'title': 'Test Notification',
                'message': 'This is a test message for validation.'
            }
            form = NotificationForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for type: {notification_type}")

    def test_notification_form_title_validation(self):
        """Test title field validation."""
        # Test minimum length
        form_data = {
            'user': self.user.id,
            'notification_type': Notification.BOOKING,
            'title': 'AB',  # Too short
            'message': 'This is a test message for validation.'
        }

        form = NotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)

    def test_notification_form_message_validation(self):
        """Test message field validation."""
        # Test minimum length
        form_data = {
            'user': self.user.id,
            'notification_type': Notification.BOOKING,
            'title': 'Test Notification',
            'message': 'Short'  # Too short
        }

        form = NotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('message', form.errors)

    def test_notification_form_optional_action_url(self):
        """Test that action_url is optional."""
        form_data = {
            'user': self.user.id,
            'notification_type': Notification.BOOKING,
            'title': 'Test Notification',
            'message': 'This is a test message for validation.'
            # action_url is omitted
        }

        form = NotificationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_notification_form_invalid_action_url(self):
        """Test form with invalid action URL."""
        form_data = {
            'user': self.user.id,
            'notification_type': Notification.BOOKING,
            'title': 'Test Notification',
            'message': 'This is a test message for validation.',
            'action_url': 'invalid_url'
        }

        form = NotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('action_url', form.errors)

    def test_notification_form_missing_required_fields(self):
        """Test form with missing required fields."""
        form_data = {}

        form = NotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('user', form.errors)
        self.assertIn('notification_type', form.errors)
        self.assertIn('title', form.errors)
        self.assertIn('message', form.errors)


class AdminAnnouncementFormTest(TestCase):
    """Test the AdminAnnouncementForm."""

    def test_valid_admin_announcement_form(self):
        """Test form with valid data."""
        form_data = {
            'title': 'System Maintenance Announcement',
            'announcement_text': 'The system will be under maintenance from 2-4 AM tomorrow.',
            'target_audience': AdminAnnouncement.ALL_USERS
        }

        form = AdminAnnouncementForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_admin_announcement_form_target_audience_choices(self):
        """Test that form accepts valid target audience choices."""
        valid_audiences = [
            AdminAnnouncement.ALL_USERS,
            AdminAnnouncement.CUSTOMERS,
            AdminAnnouncement.PROVIDERS,
            AdminAnnouncement.ADMINS
        ]

        for audience in valid_audiences:
            form_data = {
                'title': 'Test Announcement',
                'announcement_text': 'This is a test announcement message for validation.',
                'target_audience': audience
            }
            form = AdminAnnouncementForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for audience: {audience}")

    def test_admin_announcement_form_title_validation(self):
        """Test title field validation."""
        # Test minimum length
        form_data = {
            'title': 'Test',  # Too short (less than 5 characters)
            'announcement_text': 'This is a test announcement message for validation.',
            'target_audience': AdminAnnouncement.ALL_USERS
        }

        form = AdminAnnouncementForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)

    def test_admin_announcement_form_text_validation(self):
        """Test announcement_text field validation."""
        # Test minimum length
        form_data = {
            'title': 'Test Announcement',
            'announcement_text': 'Short',  # Too short (less than 20 characters)
            'target_audience': AdminAnnouncement.ALL_USERS
        }

        form = AdminAnnouncementForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('announcement_text', form.errors)

    def test_admin_announcement_form_missing_required_fields(self):
        """Test form with missing required fields."""
        form_data = {}

        form = AdminAnnouncementForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)
        self.assertIn('announcement_text', form.errors)
        self.assertIn('target_audience', form.errors)

    def test_admin_announcement_form_widgets(self):
        """Test that form widgets have correct CSS classes."""
        form = AdminAnnouncementForm()

        self.assertEqual(form.fields['title'].widget.attrs['class'], 'form-control')
        self.assertEqual(form.fields['announcement_text'].widget.attrs['class'], 'form-control')
        self.assertEqual(form.fields['target_audience'].widget.attrs['class'], 'form-select')


class NotificationFilterFormTest(TestCase):
    """Test the NotificationFilterForm."""

    def test_valid_notification_filter_form(self):
        """Test form with valid data."""
        form_data = {
            'notification_type': Notification.BOOKING,
            'read_status': 'unread',
            'date_from': '2024-01-01',
            'date_to': '2024-01-31'
        }

        form = NotificationFilterForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_notification_filter_form_empty_data(self):
        """Test form with empty data (all fields optional)."""
        form_data = {}

        form = NotificationFilterForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_notification_filter_form_type_choices(self):
        """Test notification type choices."""
        valid_types = [
            '',  # All types
            Notification.BOOKING,
            Notification.PAYMENT,
            Notification.REVIEW,
            Notification.ANNOUNCEMENT,
            Notification.SYSTEM
        ]

        for notification_type in valid_types:
            form_data = {
                'notification_type': notification_type,
                'read_status': 'unread'
            }
            form = NotificationFilterForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for type: {notification_type}")

    def test_notification_filter_form_read_status_choices(self):
        """Test read status choices."""
        valid_statuses = ['', 'unread', 'read']

        for status in valid_statuses:
            form_data = {
                'notification_type': Notification.BOOKING,
                'read_status': status
            }
            form = NotificationFilterForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for status: {status}")

    def test_notification_filter_form_date_validation(self):
        """Test date range validation."""
        # Test invalid date range (from > to)
        form_data = {
            'date_from': '2024-01-31',
            'date_to': '2024-01-01'
        }

        form = NotificationFilterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_notification_filter_form_valid_date_range(self):
        """Test valid date range."""
        form_data = {
            'date_from': '2024-01-01',
            'date_to': '2024-01-31'
        }

        form = NotificationFilterForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_notification_filter_form_widgets(self):
        """Test that form widgets have correct CSS classes."""
        form = NotificationFilterForm()

        self.assertEqual(form.fields['notification_type'].widget.attrs['class'], 'form-select')
        self.assertEqual(form.fields['read_status'].widget.attrs['class'], 'form-select')
        self.assertEqual(form.fields['date_from'].widget.attrs['class'], 'form-control')
        self.assertEqual(form.fields['date_to'].widget.attrs['class'], 'form-control')


# BulkNotificationFormTest continues here


class BulkNotificationFormTest(TestCase):
    """Test the ``BulkNotificationForm`` for sending notifications to multiple users."""

    def setUp(self):
        """Create two active users for form tests."""
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

    def test_valid_bulk_notification_form(self):
        """Form should be valid with required fields and recipients."""
        form_data = {
            'target_users': [self.user1.id, self.user2.id],
            'notification_type': Notification.ANNOUNCEMENT,
            'title': 'Important System Update',
            'message': 'We have updated our system with new features and improvements.'
        }

        form = BulkNotificationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_bulk_notification_form_requires_users(self):
        """Form should require at least one user recipient."""
        form_data = {
            'notification_type': Notification.ANNOUNCEMENT,
            'title': 'Test Bulk Notification',
            'message': 'This is a test bulk notification message for validation.'
        }

        form = BulkNotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_users', form.errors)

    def test_bulk_notification_form_notification_type_choices(self):
        """Form should accept all valid notification types."""
        valid_types = [
            Notification.ANNOUNCEMENT,
            Notification.SYSTEM,
            Notification.BOOKING,
            Notification.PAYMENT,
            Notification.REVIEW
        ]

        for notification_type in valid_types:
            form_data = {
                'target_users': [self.user1.id],
                'notification_type': notification_type,
                'title': 'Test Bulk Notification',
                'message': 'This is a test bulk notification message for validation.'
            }
            form = BulkNotificationForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Form should be valid for type: {notification_type}")

    def test_bulk_notification_form_title_validation(self):
        """Test title field validation."""
        # Test minimum length
        form_data = {
            'target_users': [self.user1.id],
            'notification_type': Notification.ANNOUNCEMENT,
            'title': 'Te',  # Too short (less than 3 characters)
            'message': 'This is a test bulk notification message for validation.'
        }

        form = BulkNotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)

    def test_bulk_notification_form_message_validation(self):
        """Test message field validation."""
        # Test minimum length
        form_data = {
            'target_users': [self.user1.id],
            'notification_type': Notification.ANNOUNCEMENT,
            'title': 'Test Bulk Notification',
            'message': 'Short'  # Too short (less than 20 characters)
        }

        form = BulkNotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('message', form.errors)

    def test_bulk_notification_form_missing_required_fields(self):
        """Test form with missing required fields."""
        form_data = {}

        form = BulkNotificationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('target_users', form.errors)
        self.assertIn('notification_type', form.errors)
        self.assertIn('title', form.errors)
        self.assertIn('message', form.errors)

    def test_bulk_notification_form_widgets(self):
        """Test that form widgets have correct CSS classes."""
        form = BulkNotificationForm()

        self.assertEqual(form.fields['target_users'].widget.attrs['class'], 'form-check-input')
        self.assertEqual(form.fields['notification_type'].widget.attrs['class'], 'form-select')
        self.assertEqual(form.fields['title'].widget.attrs['class'], 'form-control')
        self.assertEqual(form.fields['message'].widget.attrs['class'], 'form-control')
