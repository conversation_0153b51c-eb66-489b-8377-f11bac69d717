{% extends 'discount_app/base_discount.html' %}

{% block title %}Search Discounts - CozyWish{% endblock %}

{% block discount_extra_css %}
{% load static i18n %}
<style>
    /* Search discounts specific styles - black & white theme */
    .search-filters {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .savings-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: black;
        font-family: var(--font-heading);
    }

    .filter-badge {
        background: white;
        color: black;
        border: 2px solid black;
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        display: inline-block;
        font-weight: 500;
        font-family: var(--font-primary);
    }

    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: none;
        z-index: 1000;
        background-color: black;
        color: white;
        border: 2px solid black;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .back-to-top.show {
        display: flex;
    }

    .back-to-top:hover {
        background-color: white;
        color: black;
        border-color: black;
    }

    /* Section title styling */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block discount_content %}
<!-- Page Header -->
<div class="row">
    <div class="col-12">
        <div class="text-center mb-4">
            <h1 class="section-title">{% trans "Search Discounts" %}</h1>
            <p class="section-subtitle">{% trans "Find the best deals on spa and wellness services" %}</p>
        </div>
    </div>
</div>

        <!-- Search and Filter Form -->
        <button class="btn btn-outline-secondary d-md-none mb-3" type="button" data-bs-toggle="offcanvas" data-bs-target="#searchFilters" aria-controls="searchFilters">
            {% trans "Filter Search" %}
        </button>
        <div id="searchFilters" class="offcanvas offcanvas-start d-md-none" tabindex="-1" aria-labelledby="searchFiltersLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="searchFiltersLabel">{% trans "Search Filters" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                {% include 'discount_app/includes/search_filter_form.html' %}
            </div>
        </div>
        <div class="search-filters sticky-top d-none d-md-block">
            {% include 'discount_app/includes/search_filter_form.html' %}
        </div>
        <!-- Active Filters Display -->
        {% if search_query or discount_type_filter != 'all' or category_filter or location_filter or min_discount %}
        <div class="mb-3">
            <h6 class="text-muted">Active Filters:</h6>
            {% if search_query %}
                <span class="filter-badge">Search: "{{ search_query }}"</span>
            {% endif %}
            {% if discount_type_filter != 'all' %}
                <span class="filter-badge">Type: {{ discount_type_filter|title }}</span>
            {% endif %}
            {% if category_filter %}
                {% for cat in categories %}
                    {% if cat.id|stringformat:'s' == category_filter %}
                        <span class="filter-badge">Category: {{ cat.category_name }}</span>
                    {% endif %}
                {% endfor %}
            {% endif %}
            {% if location_filter %}
                <span class="filter-badge">Location: {{ location_filter }}</span>
            {% endif %}
            {% if min_discount %}
                <span class="filter-badge">Min Discount: {{ min_discount }}%</span>
            {% endif %}
        </div>
        {% endif %}

<!-- Results Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        {{ total_results }} Discount{{ total_results|pluralize }} Found
                    </h5>
                    <form method="get" class="d-flex align-items-center ms-auto">
                        <input type="hidden" name="search" value="{{ search_query }}">
                        <input type="hidden" name="type" value="{{ discount_type_filter }}">
                        <input type="hidden" name="category" value="{{ category_filter }}">
                        <input type="hidden" name="location" value="{{ location_filter }}">
                        <input type="hidden" name="min_discount" value="{{ min_discount }}">
                        <input type="hidden" name="start_date" value="{{ start_date }}">
                        <input type="hidden" name="end_date" value="{{ end_date }}">
                        <label for="sort" class="visually-hidden">Sort By</label>
                        <select id="sort" name="sort" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="savings" {% if sort == 'savings' %}selected{% endif %}>Highest Savings</option>
                            <option value="popular" {% if sort == 'popular' %}selected{% endif %}>Popularity</option>
                            <option value="expiry" {% if sort == 'expiry' %}selected{% endif %}>Soonest Expiry</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
{% include 'discount_app/includes/discount_skeleton.html' %}
{% if page_obj %}
<div id="discount-results" class="row g-4" style="display:none">
    {% for item in page_obj %}
    <div class="col-lg-4 col-md-6">
        <div class="card discount-card h-100">
            <div class="position-relative">
                {% if item.type == 'service' %}
                    <img src="{{ item.venue.get_primary_image|default:'https://via.placeholder.com/400x250' }}"
                         class="card-img-top" alt="{{ item.service.title }}" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Service</span>
                {% elif item.type == 'venue' %}
                    <img src="{{ item.venue.get_primary_image|default:'https://via.placeholder.com/400x250' }}"
                         class="card-img-top" alt="{{ item.venue.name }}" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Venue</span>
                {% else %}
                    <img src="https://via.placeholder.com/400x250"
                         class="card-img-top" alt="Platform Discount" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Platform</span>
                {% endif %}
            </div>

            <div class="card-body d-flex flex-column">
                <h5 class="card-title">
                    <a href="{% url 'discount_app:discount_detail' item.discount.slug %}" class="text-decoration-none text-dark">{{ item.discount.name }}</a>
                    {% if item.badge %}
                        <span class="badge bg-success ms-2">{{ item.badge }}</span>
                    {% endif %}
                </h5>
                {% if item.rating %}
                <div class="small mb-1" style="color: black;">
                    <i class="fas fa-star me-1"></i>{{ item.rating|floatformat:1 }}★
                </div>
                {% endif %}

                {% if item.type == 'service' %}
                    <p class="text-muted mb-1">
                        <i class="fas fa-spa me-1"></i>{{ item.service.title }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ item.venue.name }}
                    </p>
                {% elif item.type == 'venue' %}
                    <p class="text-muted mb-1">
                        <i class="fas fa-building me-1"></i>{{ item.venue.name }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ item.venue.city }}, {{ item.venue.state }}
                    </p>
                {% endif %}

                <div class="mb-3">
                    <div class="savings-text">{{ item.savings_text }}</div>
                    {% if item.original_price and item.discounted_price %}
                        <div class="mt-1">
                            <span class="original-price">${{ item.original_price }}</span>
                            <span class="discounted-price ms-2">${{ item.discounted_price }}</span>
                        </div>
                    {% endif %}
                </div>

                {% if item.discount.description %}
                    <p class="card-text text-muted small">{{ item.discount.description|truncatewords:12 }}</p>
                {% endif %}

                <div class="mt-auto">
                    {% if item.type == 'service' %}
                        <a href="{% url 'venues_app:venue_detail' venue_slug=item.venue.slug %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-calendar-plus me-2"></i>Book Service
                        </a>
                    {% elif item.type == 'venue' %}
                        <a href="{% url 'venues_app:venue_detail' slug=item.venue.slug %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-eye me-2"></i>View Venue
                        </a>
                    {% else %}
                        <a href="{% url 'venues_app:venue_list' %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Browse Venues
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Search results pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if location_filter %}&location={{ location_filter }}{% endif %}{% if min_discount %}&min_discount={{ min_discount }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if location_filter %}&location={{ location_filter }}{% endif %}{% if min_discount %}&min_discount={{ min_discount }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if location_filter %}&location={{ location_filter }}{% endif %}{% if min_discount %}&min_discount={{ min_discount }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if location_filter %}&location={{ location_filter }}{% endif %}{% if min_discount %}&min_discount={{ min_discount }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if location_filter %}&location={{ location_filter }}{% endif %}{% if min_discount %}&min_discount={{ min_discount }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

{% else %}
<div class="row">
    <div class="col-12 text-center py-5">
        <div class="card">
            <div class="card-body py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>No Discounts Found</h3>
                <p class="text-muted">Try adjusting your search criteria or browse all available discounts.</p>
                <div class="mt-4">
                    <a href="{% url 'discount_app:featured_discounts' %}" class="btn btn-primary me-2">
                        <i class="fas fa-tags me-2"></i>Featured Discounts
                    </a>
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>Browse All Venues
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
<a href="#" id="back-to-top" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</a>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('location');
    const list = document.getElementById('locationList');
    if (!input) return;
    input.addEventListener('input', function() {
        const q = this.value;
        if (q.length < 2) return;
        fetch('{% url "venues_app:location_autocomplete" %}?q=' + encodeURIComponent(q))
            .then(resp => resp.json())
            .then(data => {
                list.innerHTML = '';
                data.suggestions.forEach(s => {
                    const opt = document.createElement('option');
                    opt.value = s.label;
                    list.appendChild(opt);
                });
            });
    });

    const backBtn = document.getElementById('back-to-top');
    if (backBtn) {
        window.addEventListener('scroll', function(){
            if(window.scrollY > 200){
                backBtn.classList.add('show');
            } else {
                backBtn.classList.remove('show');
            }
        });
        backBtn.addEventListener('click', function(e){
            e.preventDefault();
            window.scrollTo({top:0, behavior:'smooth'});
        });
    }
});
</script>
{% endblock %}
