"""Admin configuration for notifications_app models."""

# --- Third-Party Imports ---
from django.contrib import admin, messages
from django.db import models
from django.utils import timezone
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from .models import AdminAnnouncement, Notification, NotificationPreference


# --- Notification Admin ---
@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Enhanced admin configuration for Notification model."""

    # Fields to display in the notification list
    list_display = (
        'user', 'title', 'notification_type', 'read_status',
        'created_at', 'is_recent_notification', 'get_message_preview'
    )
    list_filter = (
        'notification_type', 'read_status', 'created_at',
        ('created_at', admin.DateFieldListFilter)
    )
    search_fields = ('user__email', 'title', 'message', 'user__first_name', 'user__last_name')
    readonly_fields = ('created_at', 'read_at')
    date_hierarchy = 'created_at'
    list_per_page = 50
    actions = ['mark_as_read', 'mark_as_unread', 'delete_old_notifications']

    fieldsets = (
        (_('Notification Details'), {
            'fields': ('user', 'notification_type', 'title', 'message')
        }),
        (_('Status'), {
            'fields': ('read_status', 'read_at')
        }),
        (_('Related Object'), {
            'fields': ('related_object_id', 'related_object_type', 'action_url'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at',)
        }),
    )

    def get_message_preview(self, obj):
        """Return a preview of the notification message."""
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message
    get_message_preview.short_description = 'Message Preview'

    def is_recent_notification(self, obj):
        """Check if notification is recent (last 24 hours)."""
        if obj.is_recent:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Recent</span>'
            )
        return format_html(
            '<span style="color: gray;">Older</span>'
        )
    is_recent_notification.short_description = 'Recency'

    def mark_as_read(self, request, queryset):
        """Mark selected notifications as read."""
        updated = 0
        for notification in queryset:
            if notification.read_status == Notification.UNREAD:
                notification.mark_as_read()
                updated += 1

        self.message_user(
            request,
            f'{updated} notification(s) marked as read.',
            messages.SUCCESS
        )
    mark_as_read.short_description = 'Mark selected notifications as read'

    def mark_as_unread(self, request, queryset):
        """Mark selected notifications as unread."""
        updated = 0
        for notification in queryset:
            if notification.read_status == Notification.READ:
                notification.mark_as_unread()
                updated += 1

        self.message_user(
            request,
            f'{updated} notification(s) marked as unread.',
            messages.SUCCESS
        )
    mark_as_unread.short_description = 'Mark selected notifications as unread'

    def delete_old_notifications(self, request, queryset):
        """Delete notifications older than 90 days."""
        from datetime import timedelta

        cutoff_date = timezone.now() - timedelta(days=90)
        old_notifications = Notification.objects.filter(created_at__lt=cutoff_date)
        deleted_count = old_notifications.delete()[0]

        self.message_user(
            request,
            f'Deleted {deleted_count} notification(s) older than 90 days.',
            messages.SUCCESS
        )
    delete_old_notifications.short_description = 'Delete notifications older than 90 days'

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('user')

    def changelist_view(self, request, extra_context=None):
        """Add notification statistics to the changelist view."""
        from datetime import timedelta

        # Get notification statistics
        one_day_ago = timezone.now() - timedelta(days=1)
        one_week_ago = timezone.now() - timedelta(days=7)

        total_notifications = Notification.objects.count()
        unread_notifications = Notification.objects.filter(read_status=Notification.UNREAD).count()
        recent_notifications = Notification.objects.filter(created_at__gte=one_day_ago).count()
        weekly_notifications = Notification.objects.filter(created_at__gte=one_week_ago).count()

        # Get notification type breakdown
        type_breakdown = Notification.objects.values('notification_type').annotate(
            count=models.Count('id')
        ).order_by('-count')

        extra_context = extra_context or {}
        extra_context.update({
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
            'recent_notifications': recent_notifications,
            'weekly_notifications': weekly_notifications,
            'type_breakdown': type_breakdown,
            'show_notification_stats': True
        })

        return super().changelist_view(request, extra_context=extra_context)


# --- AdminAnnouncement Admin ---
@admin.register(AdminAnnouncement)
class AdminAnnouncementAdmin(admin.ModelAdmin):
    """Enhanced admin configuration for AdminAnnouncement model."""

    # Fields to display in the announcement list
    list_display = (
        'title', 'slug', 'target_audience', 'status', 'created_by',
        'created_at', 'sent_at', 'total_recipients', 'get_status_display_colored'
    )
    list_filter = (
        'target_audience', 'status', 'created_at',
        ('created_at', admin.DateFieldListFilter),
        ('sent_at', admin.DateFieldListFilter)
    )
    search_fields = ('title', 'slug', 'announcement_text', 'created_by__email')
    readonly_fields = ('created_at', 'sent_at', 'total_recipients')
    date_hierarchy = 'created_at'
    list_per_page = 25
    actions = ['send_announcements', 'cancel_announcements']

    fieldsets = (
        (_('Announcement Details'), {
            'fields': ('title', 'slug', 'announcement_text', 'target_audience')
        }),
        (_('Status & Metadata'), {
            'fields': ('status', 'created_by', 'created_at', 'sent_at', 'total_recipients')
        }),
    )

    def get_status_display_colored(self, obj):
        """Return colored status display."""
        if obj.status == AdminAnnouncement.PENDING:
            return format_html(
                '<span style="color: orange; font-weight: bold;">⏳ Pending</span>'
            )
        elif obj.status == AdminAnnouncement.SENT:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Sent</span>'
            )
        elif obj.status == AdminAnnouncement.CANCELLED:
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ Cancelled</span>'
            )
        return obj.get_status_display()
    get_status_display_colored.short_description = 'Status'

    def send_announcements(self, request, queryset):
        """Send selected pending announcements."""
        sent_count = 0
        failed_count = 0

        for announcement in queryset:
            if announcement.can_be_sent:
                try:
                    if announcement.send_announcement():
                        sent_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    failed_count += 1
                    self.message_user(
                        request,
                        f'Failed to send announcement "{announcement.title}": {str(e)}',
                        messages.ERROR
                    )
            else:
                failed_count += 1

        if sent_count > 0:
            self.message_user(
                request,
                f'Successfully sent {sent_count} announcement(s).',
                messages.SUCCESS
            )

        if failed_count > 0:
            self.message_user(
                request,
                f'{failed_count} announcement(s) could not be sent (already sent or cancelled).',
                messages.WARNING
            )
    send_announcements.short_description = 'Send selected pending announcements'

    def cancel_announcements(self, request, queryset):
        """Cancel selected pending announcements."""
        cancelled_count = 0
        failed_count = 0

        for announcement in queryset:
            if announcement.cancel_announcement():
                cancelled_count += 1
            else:
                failed_count += 1

        if cancelled_count > 0:
            self.message_user(
                request,
                f'Successfully cancelled {cancelled_count} announcement(s).',
                messages.SUCCESS
            )

        if failed_count > 0:
            self.message_user(
                request,
                f'{failed_count} announcement(s) could not be cancelled (already sent or cancelled).',
                messages.WARNING
            )
    cancel_announcements.short_description = 'Cancel selected pending announcements'

    def save_model(self, request, obj, form, change):
        """Set the created_by field to the current user when creating."""
        if not change:  # Only for new objects
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('created_by')

    def changelist_view(self, request, extra_context=None):
        """Add announcement statistics to the changelist view."""
        # Get announcement statistics
        total_announcements = AdminAnnouncement.objects.count()
        pending_announcements = AdminAnnouncement.objects.filter(status=AdminAnnouncement.PENDING).count()
        sent_announcements = AdminAnnouncement.objects.filter(status=AdminAnnouncement.SENT).count()
        cancelled_announcements = AdminAnnouncement.objects.filter(status=AdminAnnouncement.CANCELLED).count()

        # Get target audience breakdown
        audience_breakdown = AdminAnnouncement.objects.values('target_audience').annotate(
            count=models.Count('id')
        ).order_by('-count')

        extra_context = extra_context or {}
        extra_context.update({
            'total_announcements': total_announcements,
            'pending_announcements': pending_announcements,
            'sent_announcements': sent_announcements,
            'cancelled_announcements': cancelled_announcements,
            'audience_breakdown': audience_breakdown,
            'show_announcement_stats': True
        })

        return super().changelist_view(request, extra_context=extra_context)


# --- NotificationPreference Admin ---
@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    """Admin configuration for NotificationPreference."""

    list_display = ('user', 'notification_type', 'channel', 'is_enabled')
    list_filter = ('notification_type', 'channel', 'is_enabled')
    search_fields = ('user__email',)
