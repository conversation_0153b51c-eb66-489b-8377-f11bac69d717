{% extends 'discount_app/base_discount.html' %}

{% block title %}Featured Discounts - CozyWish{% endblock %}

{% block discount_extra_css %}
<style>
    /* Featured discounts specific styles - black & white theme */
    .savings-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: black;
        font-family: var(--font-heading);
    }

    .original-price {
        text-decoration: line-through;
        color: rgba(0, 0, 0, 0.5);
        font-size: 0.9rem;
        font-weight: 400;
    }

    .discounted-price {
        color: black;
        font-weight: 600;
        font-size: 1.1rem;
        font-family: var(--font-heading);
    }

    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: none;
        z-index: 1000;
        background-color: white;
        color: black;
        border: 2px solid black;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .back-to-top.show {
        display: flex;
    }

    .back-to-top:hover {
        background-color: white;
        color: black;
        border-color: black;
    }

    /* Discount badge styles */
    .discount-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: black !important;
        color: white;
        border: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    /* Card image styling */
    .discount-wrapper .card-img-top {
        border-bottom: 2px solid black;
    }

    /* Section title styling */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block discount_content %}
<!-- Page Header -->
<div class="row">
    <div class="col-12">
        <div class="text-center mb-5">
            <h1 class="section-title">Featured Discounts</h1>
            <p class="section-subtitle">Discover amazing deals on spa and wellness services</p>
        </div>
    </div>
</div>

<!-- Search and Filter Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            {{ total_discounts }} Active Discount{{ total_discounts|pluralize }}
                        </h5>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="{% url 'discount_app:search_discounts' %}" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Advanced Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Discounts Grid -->
{% include 'discount_app/includes/discount_skeleton.html' %}
{% if page_obj %}
<div id="discount-results" class="row g-4" style="display:none">
    {% for item in page_obj %}
    <div class="col-lg-4 col-md-6">
        <div class="card discount-card h-100">
            <div class="position-relative">
                {% if item.type == 'service' %}
                    <img src="{{ item.venue.get_primary_image|default:'https://via.placeholder.com/400x250' }}"
                         class="card-img-top" alt="{{ item.service.title }}" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Service</span>
                {% elif item.type == 'venue' %}
                    <img src="{{ item.venue.get_primary_image|default:'https://via.placeholder.com/400x250' }}"
                         class="card-img-top" alt="{{ item.venue.name }}" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Venue</span>
                {% else %}
                    <img src="https://via.placeholder.com/400x250"
                         class="card-img-top" alt="Platform Discount" style="height: 200px; object-fit: cover;">
                    <span class="badge discount-badge">Platform</span>
                {% endif %}
            </div>

            <div class="card-body d-flex flex-column">
                <h5 class="card-title">
                    <a href="{% url 'discount_app:discount_detail' item.discount.slug %}" class="text-decoration-none text-dark">{{ item.discount.name }}</a>
                    {% if item.badge %}
                        <span class="badge bg-success ms-2">{{ item.badge }}</span>
                    {% endif %}
                </h5>
                {% if item.rating %}
                <div class="small mb-1" style="color: black;">
                    <i class="fas fa-star me-1"></i>{{ item.rating|floatformat:1 }}★
                </div>
                {% endif %}

                {% if item.type == 'service' %}
                    <p class="text-muted mb-2">
                        <i class="fas fa-spa me-1"></i>{{ item.service.title }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ item.venue.name }}
                    </p>
                {% elif item.type == 'venue' %}
                    <p class="text-muted mb-2">
                        <i class="fas fa-building me-1"></i>{{ item.venue.name }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ item.venue.city }}, {{ item.venue.state }}
                    </p>
                {% endif %}

                <div class="mb-3">
                    <div class="savings-text">{{ item.savings_text }}</div>
                    {% if item.original_price and item.discounted_price %}
                        <div class="mt-1">
                            <span class="original-price">${{ item.original_price }}</span>
                            <span class="discounted-price ms-2">${{ item.discounted_price }}</span>
                        </div>
                    {% endif %}
                </div>

                {% if item.discount.description %}
                    <p class="card-text text-muted small">{{ item.discount.description|truncatewords:15 }}</p>
                {% endif %}

                <div class="mt-auto">
                    {% if item.type == 'service' %}
                        <a href="{% url 'venues_app:service_detail' venue_slug=item.venue.slug service_slug=item.service.slug %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-eye me-2"></i>View Service
                        </a>
                    {% elif item.type == 'venue' %}
                        <a href="{% url 'venues_app:venue_detail' slug=item.venue.slug %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-eye me-2"></i>View Venue
                        </a>
                    {% else %}
                        <a href="{% url 'venues_app:venue_list' %}"
                           class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Browse Venues
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="row mt-5">
    <div class="col-12">
        <nav aria-label="Discounts pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

{% else %}
<div class="row">
    <div class="col-12 text-center py-5">
        <div class="card">
            <div class="card-body py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h3>No Discounts Available</h3>
                <p class="text-muted">Check back later for amazing deals on spa and wellness services!</p>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Browse All Venues
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
<a href="#" id="back-to-top" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</a>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const backBtn = document.getElementById('back-to-top');
    if (backBtn) {
        window.addEventListener('scroll', function(){
            if(window.scrollY > 200){
                backBtn.classList.add('show');
            } else {
                backBtn.classList.remove('show');
            }
        });
        backBtn.addEventListener('click', function(e){
            e.preventDefault();
            window.scrollTo({top:0, behavior:'smooth'});
        });
    }
});
</script>
{% endblock %}
