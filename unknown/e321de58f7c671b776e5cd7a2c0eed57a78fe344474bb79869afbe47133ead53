"""Form classes for bulk user actions and search."""

# --- Third-Party Imports ---
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from utils.forms import AriaLabelMixin

User = get_user_model()


class BulkUserActionForm(AriaLabelMixin, forms.Form):
    """Form for performing bulk actions on users."""

    ACTION_CHOICES = [
        ('activate', _('Activate Users')),
        ('deactivate', _('Deactivate Users')),
        ('approve_providers', _('Approve Service Providers')),
        ('reject_providers', _('Reject Service Providers')),
    ]

    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    user_ids = forms.CharField(
        widget=forms.HiddenInput()
    )
    reason = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Optional reason for this action'
        })
    )

    def clean_user_ids(self):
        """Validate and parse user IDs."""
        user_ids_str = self.cleaned_data.get('user_ids')
        if not user_ids_str:
            raise ValidationError(_('No users selected.'))
        try:
            user_ids = [int(id.strip()) for id in user_ids_str.split(',') if id.strip()]
            if not user_ids:
                raise ValidationError(_('No valid user IDs provided.'))
            existing_count = User.objects.filter(id__in=user_ids).count()
            if existing_count != len(user_ids):
                raise ValidationError(_('Some selected users do not exist.'))
            return user_ids
        except ValueError:
            raise ValidationError(_('Invalid user ID format.'))


class UserSearchForm(AriaLabelMixin, forms.Form):
    """Form for searching and filtering users in admin interface."""

    ROLE_CHOICES = [
        ('', _('All Roles')),
        ('customer', _('Customers')),
        ('service_provider', _('Service Providers')),
        ('admin', _('Admins')),
    ]

    STATUS_CHOICES = [
        ('', _('All Statuses')),
        ('active', _('Active')),
        ('inactive', _('Inactive')),
    ]

    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by email, name, or business name'
        })
    )
    role = forms.ChoiceField(
        choices=ROLE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_joined_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    date_joined_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

__all__ = ['BulkUserActionForm', 'UserSearchForm']
