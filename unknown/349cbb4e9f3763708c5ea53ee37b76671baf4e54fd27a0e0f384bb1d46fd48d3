"""Management command to send daily notification digests."""

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

# --- Local Imports ---
from notifications_app.tasks import send_daily_digest_task


User = get_user_model()


class Command(BaseCommand):
    """Send daily notification digests to all active users."""

    help = 'Send daily notification digest emails to users'

    def handle(self, *args, **options):
        users = User.objects.filter(is_active=True)
        for user in users:
            send_daily_digest_task.delay(user.id)
        self.stdout.write(self.style.SUCCESS(f'Scheduled digests for {users.count()} users.'))
