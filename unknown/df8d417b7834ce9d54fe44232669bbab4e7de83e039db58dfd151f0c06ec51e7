{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Respond to Review - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>Respond to Review</h2>
                    <p class="mb-0">Respond to a customer review for {{ venue.venue_name }}</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Customer Review:</strong>
                        <div class="mt-2 p-3 bg-light">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ review.customer.first_name|default:"Anonymous" }}</strong>
                                    <span class="rating">{{ review.rating }}/5 stars</span>
                                </div>
                                <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mt-2">{{ review.written_review }}</p>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.response_text.id_for_label }}" class="form-label">Your Response *</label>
                            {{ form.response_text }}
                            {% if form.response_text.errors %}
                                <div class="text-danger">
                                    {% for error in form.response_text.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Respond professionally and courteously. Your response will be visible to all customers.
                            </small>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="alert alert-warning">
                            <strong>Response Guidelines:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Thank the customer for their feedback</li>
                                <li>Address any specific concerns mentioned</li>
                                <li>Keep responses professional and courteous</li>
                                <li>Avoid sharing personal information</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Submit Response</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
