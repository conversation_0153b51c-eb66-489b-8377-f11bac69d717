#!/bin/bash

# CozyWish Pytest Runner Script
# This script provides convenient commands for running pytest with common options

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "CozyWish Pytest Runner"
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  all                    Run all tests"
    echo "  app <app_name>         Run tests for specific app (e.g., accounts_app)"
    echo "  models <app_name>      Run model tests for specific app"
    echo "  views <app_name>       Run view tests for specific app"
    echo "  forms <app_name>       Run form tests for specific app"
    echo "  integration <app_name> Run integration tests for specific app"
    echo "  coverage <app_name>    Run tests with coverage for specific app"
    echo "  fast                   Run tests with minimal output"
    echo "  verbose                Run tests with verbose output"
    echo "  help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 app accounts_app"
    echo "  $0 models accounts_app"
    echo "  $0 coverage accounts_app"
    echo "  $0 all"
    echo "  $0 fast"
}

# Main script logic
case "${1:-help}" in
    "all")
        print_status "Running all tests..."
        pytest
        print_success "All tests completed!"
        ;;
    
    "app")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 app <app_name>"
            exit 1
        fi
        print_status "Running tests for $2..."
        pytest "$2/tests/" -v
        print_success "Tests for $2 completed!"
        ;;
    
    "models")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 models <app_name>"
            exit 1
        fi
        print_status "Running model tests for $2..."
        pytest "$2/tests/test_models.py" -v
        print_success "Model tests for $2 completed!"
        ;;
    
    "views")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 views <app_name>"
            exit 1
        fi
        print_status "Running view tests for $2..."
        pytest "$2/tests/test_views.py" -v
        print_success "View tests for $2 completed!"
        ;;
    
    "forms")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 forms <app_name>"
            exit 1
        fi
        print_status "Running form tests for $2..."
        pytest "$2/tests/test_forms.py" -v
        print_success "Form tests for $2 completed!"
        ;;
    
    "integration")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 integration <app_name>"
            exit 1
        fi
        print_status "Running integration tests for $2..."
        pytest "$2/tests/test_integration.py" -v
        print_success "Integration tests for $2 completed!"
        ;;
    
    "coverage")
        if [ -z "$2" ]; then
            print_error "App name required. Usage: $0 coverage <app_name>"
            exit 1
        fi
        print_status "Running tests with coverage for $2..."
        pytest "$2/tests/" --cov="$2" --cov-report=term-missing --cov-report=html
        print_success "Coverage report generated for $2!"
        print_status "HTML coverage report available at htmlcov/index.html"
        ;;
    
    "fast")
        print_status "Running tests with minimal output..."
        pytest -q --tb=no
        print_success "Fast test run completed!"
        ;;
    
    "verbose")
        print_status "Running tests with verbose output..."
        pytest -vvv --tb=long
        print_success "Verbose test run completed!"
        ;;
    
    "help"|*)
        show_usage
        ;;
esac
