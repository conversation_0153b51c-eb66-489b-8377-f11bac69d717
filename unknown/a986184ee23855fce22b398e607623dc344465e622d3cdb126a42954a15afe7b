{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Review Details - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:customer_review_history' %}">My Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">Review Details</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <!-- Review Details Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Your Review of {{ venue.venue_name }}</h5>
                </div>
                <div class="card-body">
                    <!-- Venue Info -->
                    <div class="mb-3">
                        <h6 class="text-muted">Venue</h6>
                        <p class="mb-1"><strong>{{ venue.venue_name }}</strong></p>
                        <p class="text-muted small">{{ venue.city }}, {{ venue.state }}</p>
                    </div>

                    <!-- Review Content -->
                    <div class="mb-3">
                        <h6 class="text-muted">Your Rating</h6>
                        <div class="mb-2">
                            {% for i in "12345" %}
                                {% if forloop.counter <= review.rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="ms-2">{{ review.rating }}/5</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Your Review</h6>
                        <p>{{ review.written_review }}</p>
                    </div>

                    <!-- Review Metadata -->
                    <div class="mb-3">
                        <small class="text-muted">
                            Posted on {{ review.created_at|date:"F j, Y" }}
                            {% if review.updated_at != review.created_at %}
                                • Updated on {{ review.updated_at|date:"F j, Y" }}
                            {% endif %}
                        </small>
                    </div>

                    <!-- Provider Response -->
                    {% if review.response %}
                        <div class="border-top pt-3">
                            <h6 class="text-muted">Provider Response</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-2">{{ review.response.response_text }}</p>
                                <small class="text-muted">
                                    Response from {{ venue.service_provider.business_name }} 
                                    on {{ review.response.created_at|date:"F j, Y" }}
                                </small>
                            </div>
                        </div>
                    {% else %}
                        <div class="border-top pt-3">
                            <p class="text-muted"><em>No response from the provider yet.</em></p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2">
                <a href="{% url 'review_app:edit_review' review_slug=review.slug %}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Review
                </a>
                <a href="{% url 'venues_app:venue_detail' pk=venue.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-eye"></i> View Venue
                </a>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Venue Summary Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Venue Information</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>{{ venue.venue_name }}</strong></p>
                    <p class="text-muted small mb-2">{{ venue.short_description|truncatechars:100 }}</p>
                    <p class="text-muted small">
                        <i class="fas fa-map-marker-alt"></i> 
                        {{ venue.city }}, {{ venue.state }}
                    </p>
                    <a href="{% url 'venues_app:venue_detail' pk=venue.id %}" class="btn btn-sm btn-outline-primary">
                        View Full Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
