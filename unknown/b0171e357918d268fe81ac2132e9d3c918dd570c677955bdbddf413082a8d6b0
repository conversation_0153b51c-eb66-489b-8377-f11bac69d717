{% extends 'review_app/base_review.html' %}

{% block title %}
    Edit Response - {{ review.venue.venue_name }}
{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:provider_venue_reviews' %}">Venue Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Response</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Edit Your Response
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Original Review -->
                    <div class="card bg-light mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Customer Review</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="me-3">
                                    {% if review.customer.customerprofile.profile_picture %}
                                        <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                             alt="Customer" class="rounded-circle" style="width: 50px; height: 50px;">
                                    {% else %}
                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="mb-0">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <p class="mb-2">{{ review.written_review }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ review.created_at|date:"F d, Y g:i A" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Response -->
                    <div class="card bg-info bg-opacity-10 mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Current Response
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">{{ response.response_text }}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Originally responded on {{ response.created_at|date:"F d, Y g:i A" }}
                                {% if response.updated_at != response.created_at %}
                                    <span class="ms-2">
                                        <i class="fas fa-edit me-1"></i>
                                        Last updated {{ response.updated_at|date:"F d, Y g:i A" }}
                                    </span>
                                {% endif %}
                            </small>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Response Text -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.response_text.id_for_label }}">
                                {{ form.response_text.label }}
                            </label>
                            {{ form.response_text|add_class:"form-control" }}
                            {% if form.response_text.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.response_text.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.response_text.help_text %}
                                <small class="form-text text-muted">{{ form.response_text.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:provider_venue_reviews' %}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Response
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
