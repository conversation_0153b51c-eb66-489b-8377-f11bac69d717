{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Static Pages - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Static Pages</h1>
    <a href="{% url 'admin_app:static_page_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Create New Page
    </a>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" name="search" class="form-control" placeholder="Search pages..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-4">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="published" {% if request.GET.status == 'published' %}selected{% endif %}>Published</option>
                    <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>Archived</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Pages List -->
<div class="card">
    <div class="card-body">
        {% if pages %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Featured</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for page in pages %}
                        <tr>
                            <td>
                                <strong>{{ page.title }}</strong><br>
                                <small class="text-muted">{{ page.slug }}</small>
                            </td>
                            <td>
                                {% if page.status == 'published' %}
                                    <span class="badge bg-success">Published</span>
                                {% elif page.status == 'draft' %}
                                    <span class="badge bg-warning">Draft</span>
                                {% else %}
                                    <span class="badge bg-secondary">Archived</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if page.is_featured %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ page.updated_at|date:"M d, Y H:i" }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_app:static_page_edit' slug=page.slug %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'admin_app:static_page_delete' slug=page.slug %}" 
                                       class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No static pages found</h5>
                <p class="text-muted">Create your first static page to get started.</p>
                <a href="{% url 'admin_app:static_page_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Page
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
