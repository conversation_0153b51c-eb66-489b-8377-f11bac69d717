"""Views for customer payment features."""

# --- Standard Library Imports ---
from decimal import Decimal
import hashlib
import json

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import etag
from django.views.generic import CreateView, DetailView, FormView, ListView, TemplateView


# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..forms import PaymentSearchForm, RefundRequestForm, RefundSearchForm, StripeCheckoutForm
from ..logging_utils import (
    log_admin_disputed_payments_viewed,
    log_admin_payment_analytics_viewed,
    log_admin_refund_detail_viewed,
    log_admin_refund_management_viewed,
    log_customer_payment_history_viewed,
    log_customer_refund_history_viewed,
    log_payment_completed,
    log_payment_error,
    log_payment_failed,
    log_payment_initiated,
    log_provider_earnings_viewed,
    log_provider_payment_detail_viewed,
    log_provider_payment_history_viewed,
    log_provider_payout_history_viewed,
    log_refund_approved,
    log_refund_declined,
    log_refund_processed,
    log_refund_requested,
    log_stripe_event,
    performance_monitor,
)
from ..models import Payment, RefundRequest
from ..utils import quantize_money


# Try to import email notification utilities
try:
    from notifications_app.utils import (
        send_payment_receipt_email_async, send_payout_email_async
    )
    EMAIL_NOTIFICATIONS_ENABLED = True
except Exception:
    EMAIL_NOTIFICATIONS_ENABLED = False

    def send_payment_receipt_email_async(*args, **kwargs):
        pass

    def send_payout_email_async(*args, **kwargs):
        pass


# Success and error messages
PAYMENT_INITIATED_SUCCESS = _('Payment process initiated. You will be redirected to complete payment.')
PAYMENT_COMPLETED_SUCCESS = _('Payment completed successfully!')
PAYMENT_FAILED_ERROR = _('Payment failed. Please try again or contact support.')
REFUND_REQUESTED_SUCCESS = _('Refund request submitted successfully. We will review it within 2-3 business days.')
REFUND_REQUEST_ERROR = _('Unable to submit refund request. Please try again.')
INVALID_PAYMENT_ERROR = _('Invalid payment or you do not have permission to access it.')
INVALID_BOOKING_ERROR = _('Invalid booking or you do not have permission to access it.')


# --- Utility Views ---


def payment_receipt_etag(request, payment_id):
    if not request.user.is_authenticated:
        return None
    updated = Payment.objects.filter(
        payment_id=payment_id,
        customer=request.user
    ).values_list('updated_at', flat=True).first()
    return f"{payment_id}-{updated.timestamp()}" if updated else None


@etag(payment_receipt_etag)
@login_required
def payment_receipt_view(request, payment_id):
    """View to display/download payment receipt."""
    payment = get_object_or_404(
        Payment,
        payment_id=payment_id,
        customer=request.user
    )

    if payment.payment_status != Payment.SUCCEEDED:
        messages.error(request, _('Receipt is only available for successful payments.'))
        return redirect('payments_app:payment_detail', payment_id=payment_id)

    context = {
        'payment': payment,
        'booking': payment.booking,
        'customer': payment.customer,
    }

    if request.GET.get('format') == 'pdf':
        from io import BytesIO
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfgen import canvas

        buffer = BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        p.setFont("Helvetica", 12)
        p.drawString(100, 750, str(_('Payment Receipt')))
        p.drawString(100, 730, f"{_('Payment ID')}: {payment.payment_id}")
        p.drawString(100, 715, f"{_('Booking ID')}: {payment.booking.booking_id}")
        p.drawString(100, 700, f"{_('Amount Paid')}: ${payment.amount_paid}")
        p.drawString(100, 685, f"{_('Date')}: {payment.completed_date.strftime('%Y-%m-%d')}")
        p.showPage()
        p.save()
        pdf = buffer.getvalue()
        buffer.close()
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename=receipt_{payment.payment_id}.pdf'
        return response

    return render(request, 'payments_app/customer/payment_receipt.html', context)


@csrf_exempt
def stripe_webhook_view(request):
    """Minimal Stripe webhook handler for testing."""
    try:
        event = json.loads(request.body.decode())
    except json.JSONDecodeError:
        return HttpResponse(status=400)

    event_type = event.get('type')
    data = event.get('data', {}).get('object', {})
    intent_id = data.get('id') or data.get('payment_intent')
    if not intent_id:
        return HttpResponse(status=400)

    try:
        payment = Payment.objects.get(stripe_payment_intent_id=intent_id)
    except Payment.DoesNotExist:
        return HttpResponse(status=404)

    if event_type == 'payment_intent.succeeded':
        payment.payment_status = Payment.SUCCEEDED
        payment.completed_date = timezone.now()
    elif event_type == 'payment_intent.payment_failed':
        payment.payment_status = Payment.FAILED
        payment.failure_reason = data.get('last_payment_error', {}).get('message', '')
    elif event_type == 'charge.refunded':
        payment.payment_status = Payment.REFUNDED
        payment.refunded_amount = payment.amount_paid
    else:
        return HttpResponse(status=400)

    payment.save()
    log_stripe_event(
        event_type=event_type,
        user=payment.customer,
        stripe_event_id=event.get('id'),
        payment=payment,
        request=request
    )
    return HttpResponse(status=200)
