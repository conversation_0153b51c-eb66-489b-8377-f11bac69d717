"""
Comprehensive logging utilities for admin_app.

This module provides structured logging functions for admin-related events
including user management, content management, system configuration, security events,
and audit trails. Built on top of the centralized logging utilities in utils/logging_utils.py.

Usage:
    from admin_app.logging_utils import log_admin_activity, log_user_management_event
    
    log_admin_activity(admin_user, 'dashboard_access', request)
    log_user_management_event('user_created', admin_user, target_user, request)
"""

from typing import Any, Dict, Optional

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

# --- Local App Imports ---
# Note: utils.logging_utils may not exist yet, so we'll provide fallback implementations
try:
    from utils.logging_utils import (
        get_app_logger, log_user_activity, log_error, log_security_event,
        log_audit_event, log_performance
    )
except ImportError:
    import logging

    def get_app_logger(app_name, logger_type='main'):
        """Fallback logger implementation."""
        return logging.getLogger(f'{app_name}.{logger_type}')

    def log_user_activity(*args, **kwargs):
        """Fallback user activity logging."""
        pass

    def log_error(*args, **kwargs):
        """Fallback error logging."""
        pass

    def log_security_event(*args, **kwargs):
        """Fallback security event logging."""
        pass

    def log_audit_event(*args, **kwargs):
        """Fallback audit event logging."""
        pass

    def log_performance(*args, **kwargs):
        """Fallback performance logging."""
        pass

User = get_user_model()

# Get app-specific loggers
logger = get_app_logger('admin_app')
activity_logger = get_app_logger('admin_app', 'activity')
security_logger = get_app_logger('admin_app', 'security')
audit_logger = get_app_logger('admin_app', 'audit')
performance_logger = get_app_logger('admin_app', 'performance')


# ===== ADMIN ACTIVITY LOGGING =====

def log_admin_activity(
    admin_user: User,
    activity_type: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    target_object: Optional[str] = None
) -> None:
    """
    Log general admin activity events.
    
    Args:
        admin_user: Admin user performing the activity
        activity_type: Type of activity (dashboard_access, settings_view, etc.)
        request: Django HttpRequest object
        details: Additional details about the activity
        target_object: Object being acted upon (if applicable)
    """
    log_user_activity(
        app_name='admin_app',
        activity_type=activity_type,
        user=admin_user,
        request=request,
        details={
            'admin_role': 'superuser' if admin_user.is_superuser else 'staff',
            'is_superuser': admin_user.is_superuser,
            **(details or {})
        },
        target_object=target_object
    )


def log_admin_dashboard_access(
    admin_user: User,
    dashboard_section: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log admin dashboard access events.
    
    Args:
        admin_user: Admin user accessing the dashboard
        dashboard_section: Section being accessed (overview, users, content, etc.)
        request: Django HttpRequest object
        details: Additional context about the access
    """
    log_admin_activity(
        admin_user=admin_user,
        activity_type=f'dashboard_{dashboard_section}_access',
        request=request,
        details={
            'dashboard_section': dashboard_section,
            'access_type': 'dashboard_view',
            **(details or {})
        },
        target_object=f'admin_dashboard_{dashboard_section}'
    )


# ===== USER MANAGEMENT LOGGING =====

def log_user_management_event(
    action_type: str,
    admin_user: User,
    target_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    changes: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log user management events (create, update, delete, activate, deactivate).
    
    Args:
        action_type: Type of action (user_created, user_updated, user_deleted, etc.)
        admin_user: Admin user performing the action
        target_user: User being acted upon
        request: Django HttpRequest object
        details: Additional details about the action
        changes: Before/after values for changes
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        target_user=target_user,
        request=request,
        details={
            'action_category': 'user_management',
            'target_user_role': target_user.role if target_user else None,
            'target_user_active': target_user.is_active if target_user else None,
            **(details or {})
        },
        changes=changes
    )


def log_bulk_user_action(
    action_type: str,
    admin_user: User,
    affected_users_count: int,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    user_ids: Optional[list] = None
) -> None:
    """
    Log bulk user management actions.
    
    Args:
        action_type: Type of bulk action (bulk_activate, bulk_deactivate, bulk_delete)
        admin_user: Admin user performing the action
        affected_users_count: Number of users affected
        request: Django HttpRequest object
        details: Additional details about the bulk action
        user_ids: List of user IDs affected (optional, for audit trail)
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'bulk_user_management',
            'affected_users_count': affected_users_count,
            'user_ids': user_ids[:10] if user_ids else None,  # Limit to first 10 for logging
            'total_user_ids': len(user_ids) if user_ids else 0,
            **(details or {})
        }
    )


# ===== PROVIDER MANAGEMENT LOGGING =====

def log_provider_approval_event(
    action_type: str,
    admin_user: User,
    provider_user: User,
    is_approved: bool,
    request: Optional[HttpRequest] = None,
    rejection_reason: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log provider approval or rejection events.
    
    Args:
        action_type: Type of action (provider_approved, provider_rejected)
        admin_user: Admin user performing the approval/rejection
        provider_user: Provider user being approved/rejected
        is_approved: Whether the provider was approved or rejected
        request: Django HttpRequest object
        rejection_reason: Reason for rejection (if applicable)
        details: Additional details about the approval process
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        target_user=provider_user,
        request=request,
        details={
            'action_category': 'provider_management',
            'approval_status': 'approved' if is_approved else 'rejected',
            'rejection_reason': rejection_reason,
            'provider_business_name': getattr(provider_user, 'business_name', None),
            **(details or {})
        }
    )


# ===== CONTENT MANAGEMENT LOGGING =====

def log_content_management_event(
    action_type: str,
    admin_user: User,
    content_type: str,
    content_id: Optional[int] = None,
    content_title: Optional[str] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    changes: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log content management events (static pages, blog posts, media files).
    
    Args:
        action_type: Type of action (content_created, content_updated, content_deleted, etc.)
        admin_user: Admin user performing the action
        content_type: Type of content (static_page, blog_post, media_file, etc.)
        content_id: ID of the content object
        content_title: Title of the content
        request: Django HttpRequest object
        details: Additional details about the content action
        changes: Before/after values for changes
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'content_management',
            'content_type': content_type,
            'content_id': content_id,
            'content_title': content_title,
            **(details or {})
        },
        changes=changes
    )


def log_media_management_event(
    action_type: str,
    admin_user: User,
    media_file_name: str,
    media_file_size: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log media file management events (upload, delete, organize).

    Args:
        action_type: Type of action (media_uploaded, media_deleted, media_organized)
        admin_user: Admin user performing the action
        media_file_name: Name of the media file
        media_file_size: Size of the media file in bytes
        request: Django HttpRequest object
        details: Additional details about the media action
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'media_management',
            'media_file_name': media_file_name,
            'media_file_size': media_file_size,
            'media_file_size_mb': round(media_file_size / (1024 * 1024), 2) if media_file_size else None,
            **(details or {})
        }
    )


# ===== SYSTEM CONFIGURATION LOGGING =====

def log_system_configuration_event(
    action_type: str,
    admin_user: User,
    config_section: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    changes: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log system configuration changes.

    Args:
        action_type: Type of action (config_updated, settings_changed, etc.)
        admin_user: Admin user performing the action
        config_section: Section of configuration (site_settings, security, maintenance, etc.)
        request: Django HttpRequest object
        details: Additional details about the configuration change
        changes: Before/after values for changes
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'system_configuration',
            'config_section': config_section,
            'critical_change': config_section in ['security', 'maintenance', 'authentication'],
            **(details or {})
        },
        changes=changes
    )


def log_maintenance_mode_event(
    action_type: str,
    admin_user: User,
    maintenance_enabled: bool,
    maintenance_message: Optional[str] = None,
    request: Optional[HttpRequest] = None
) -> None:
    """
    Log maintenance mode changes.

    Args:
        action_type: Type of action (maintenance_enabled, maintenance_disabled)
        admin_user: Admin user performing the action
        maintenance_enabled: Whether maintenance mode is enabled
        maintenance_message: Maintenance message displayed to users
        request: Django HttpRequest object
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'system_maintenance',
            'maintenance_enabled': maintenance_enabled,
            'maintenance_message': maintenance_message,
            'critical_system_change': True
        }
    )


# ===== SECURITY EVENT LOGGING =====

def log_admin_security_event(
    event_type: str,
    admin_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    severity: str = 'WARNING',
    details: Optional[Dict[str, Any]] = None,
    target_user: Optional[User] = None
) -> None:
    """
    Log admin-specific security events.

    Args:
        event_type: Type of security event (unauthorized_access, privilege_escalation, etc.)
        admin_user: Admin user involved in the event
        request: Django HttpRequest object
        severity: Severity level (INFO, WARNING, ERROR, CRITICAL)
        details: Additional security event details
        target_user: Target user (if applicable)
    """
    log_security_event(
        app_name='admin_app',
        event_type=event_type,
        user_email=admin_user.email if admin_user else None,
        user_id=admin_user.id if admin_user else None,
        request=request,
        severity=severity,
        details={
            'admin_context': True,
            'admin_role': 'superuser' if admin_user and admin_user.is_superuser else 'staff',
            'target_user_email': target_user.email if target_user else None,
            'target_user_id': target_user.id if target_user else None,
            **(details or {})
        }
    )


def log_unauthorized_admin_access(
    attempted_action: str,
    user: User,
    request: Optional[HttpRequest] = None,
    required_permission: Optional[str] = None
) -> None:
    """
    Log unauthorized admin access attempts.

    Args:
        attempted_action: Action that was attempted
        user: User who attempted the action
        request: Django HttpRequest object
        required_permission: Permission that was required
    """
    log_admin_security_event(
        event_type='unauthorized_admin_access',
        admin_user=user,
        request=request,
        severity='WARNING',
        details={
            'attempted_action': attempted_action,
            'required_permission': required_permission,
            'user_is_staff': user.is_staff,
            'user_is_superuser': user.is_superuser,
            'access_denied': True
        }
    )


def log_suspicious_admin_activity(
    activity_type: str,
    admin_user: User,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log suspicious admin activities that may indicate security issues.

    Args:
        activity_type: Type of suspicious activity
        admin_user: Admin user performing the activity
        request: Django HttpRequest object
        details: Additional details about the suspicious activity
    """
    log_admin_security_event(
        event_type='suspicious_admin_activity',
        admin_user=admin_user,
        request=request,
        severity='ERROR',
        details={
            'suspicious_activity_type': activity_type,
            'requires_investigation': True,
            **(details or {})
        }
    )


# ===== VENUE MANAGEMENT LOGGING =====

def log_venue_management_event(
    action_type: str,
    admin_user: User,
    venue_id: Optional[int] = None,
    venue_name: Optional[str] = None,
    provider_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log venue management events (approval, rejection, flagging).

    Args:
        action_type: Type of action (venue_approved, venue_rejected, venue_flagged, etc.)
        admin_user: Admin user performing the action
        venue_id: ID of the venue
        venue_name: Name of the venue
        provider_user: Provider who owns the venue
        request: Django HttpRequest object
        details: Additional details about the venue action
    """
    log_audit_event(
        app_name='admin_app',
        action=action_type,
        admin_user=admin_user,
        target_user=provider_user,
        request=request,
        details={
            'action_category': 'venue_management',
            'venue_id': venue_id,
            'venue_name': venue_name,
            'provider_email': provider_user.email if provider_user else None,
            **(details or {})
        }
    )


# ===== PERFORMANCE LOGGING =====

def log_admin_performance(
    operation: str,
    duration: float,
    admin_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log performance metrics for admin operations.

    Args:
        operation: Name of the operation (dashboard_load, bulk_action, report_generation, etc.)
        duration: Duration in seconds
        admin_user: Admin user performing the operation
        request: Django HttpRequest object
        details: Additional performance details
    """
    log_performance(
        app_name='admin_app',
        operation=operation,
        duration=duration,
        user=admin_user,
        request=request,
        details={
            'admin_context': True,
            'admin_role': 'superuser' if admin_user and admin_user.is_superuser else 'staff',
            **(details or {})
        }
    )


def log_bulk_operation_performance(
    operation_type: str,
    items_processed: int,
    duration: float,
    admin_user: User,
    request: Optional[HttpRequest] = None,
    success_count: Optional[int] = None,
    error_count: Optional[int] = None
) -> None:
    """
    Log performance metrics for bulk operations.

    Args:
        operation_type: Type of bulk operation (bulk_user_update, bulk_venue_approval, etc.)
        items_processed: Number of items processed
        duration: Duration in seconds
        admin_user: Admin user performing the operation
        request: Django HttpRequest object
        success_count: Number of successful operations
        error_count: Number of failed operations
    """
    items_per_second = round(items_processed / duration, 2) if duration > 0 else 0

    log_admin_performance(
        operation=f'bulk_{operation_type}',
        duration=duration,
        admin_user=admin_user,
        request=request,
        details={
            'operation_type': 'bulk_operation',
            'items_processed': items_processed,
            'items_per_second': items_per_second,
            'success_count': success_count,
            'error_count': error_count,
            'success_rate': round((success_count / items_processed) * 100, 2) if success_count and items_processed > 0 else None
        }
    )


# ===== ERROR LOGGING =====

def log_admin_error(
    error_type: str,
    error_message: str,
    admin_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log admin-specific error events.

    Args:
        error_type: Type of error (validation, permission, database, etc.)
        error_message: Human-readable error message
        admin_user: Admin user involved in the error
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        details: Additional error details
    """
    log_error(
        app_name='admin_app',
        error_type=error_type,
        error_message=error_message,
        user=admin_user,
        request=request,
        exception=exception,
        details={
            'admin_context': True,
            'admin_role': 'superuser' if admin_user and admin_user.is_superuser else 'staff',
            **(details or {})
        }
    )


def log_bulk_operation_error(
    operation_type: str,
    error_message: str,
    admin_user: User,
    failed_items: Optional[list] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None
) -> None:
    """
    Log errors that occur during bulk operations.

    Args:
        operation_type: Type of bulk operation that failed
        error_message: Error message
        admin_user: Admin user performing the operation
        failed_items: List of items that failed (limited for logging)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
    """
    log_admin_error(
        error_type='bulk_operation_error',
        error_message=error_message,
        admin_user=admin_user,
        request=request,
        exception=exception,
        details={
            'operation_type': operation_type,
            'failed_items_count': len(failed_items) if failed_items else 0,
            'failed_items_sample': failed_items[:5] if failed_items else None,  # First 5 for debugging
            'bulk_operation_failure': True
        }
    )


# ===== ANALYTICS AND REPORTING LOGGING =====

def log_analytics_access(
    admin_user: User,
    report_type: str,
    date_range: Optional[str] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log analytics and reporting access events.

    Args:
        admin_user: Admin user accessing analytics
        report_type: Type of report (user_analytics, venue_analytics, financial_reports, etc.)
        date_range: Date range for the report
        request: Django HttpRequest object
        details: Additional details about the analytics access
    """
    log_admin_activity(
        admin_user=admin_user,
        activity_type=f'analytics_{report_type}_access',
        request=request,
        details={
            'report_type': report_type,
            'date_range': date_range,
            'analytics_access': True,
            **(details or {})
        },
        target_object=f'analytics_{report_type}'
    )


def log_data_export_event(
    admin_user: User,
    export_type: str,
    export_format: str,
    record_count: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log data export events.

    Args:
        admin_user: Admin user performing the export
        export_type: Type of data being exported (users, venues, bookings, etc.)
        export_format: Format of export (CSV, Excel, PDF, etc.)
        record_count: Number of records exported
        request: Django HttpRequest object
        details: Additional details about the export
    """
    log_audit_event(
        app_name='admin_app',
        action='data_export',
        admin_user=admin_user,
        request=request,
        details={
            'action_category': 'data_export',
            'export_type': export_type,
            'export_format': export_format,
            'record_count': record_count,
            'sensitive_data_export': export_type in ['users', 'financial_data', 'personal_info'],
            **(details or {})
        }
    )


# ===== UTILITY FUNCTIONS =====

def performance_monitor(operation_name: str):
    """
    Decorator to monitor performance of admin functions.

    Args:
        operation_name: Name of the operation being monitored

    Usage:
        @performance_monitor('bulk_user_update')
        def bulk_update_users(request, user_ids):
            # function implementation
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Try to extract admin user from args/kwargs
                admin_user = None
                request = None

                # Look for request object in args/kwargs
                for arg in args:
                    if hasattr(arg, 'user') and hasattr(arg.user, 'is_staff'):
                        request = arg
                        admin_user = arg.user
                        break

                if 'request' in kwargs:
                    request = kwargs['request']
                    admin_user = request.user if hasattr(request, 'user') else None

                log_admin_performance(
                    operation=operation_name,
                    duration=duration,
                    admin_user=admin_user,
                    request=request,
                    details={
                        'function_name': func.__name__,
                        'success': True
                    }
                )

                return result

            except Exception as e:
                duration = time.time() - start_time

                # Try to extract admin user for error logging
                admin_user = None
                request = None

                for arg in args:
                    if hasattr(arg, 'user') and hasattr(arg.user, 'is_staff'):
                        request = arg
                        admin_user = arg.user
                        break

                if 'request' in kwargs:
                    request = kwargs['request']
                    admin_user = request.user if hasattr(request, 'user') else None

                log_admin_error(
                    error_type='function_execution_error',
                    error_message=f'Error in {operation_name}: {str(e)}',
                    admin_user=admin_user,
                    request=request,
                    exception=e,
                    details={
                        'function_name': func.__name__,
                        'operation_name': operation_name,
                        'duration': duration
                    }
                )

                raise

        return wrapper
    return decorator


def get_client_info(request: Optional[HttpRequest]) -> Dict[str, Any]:
    """
    Extract client information from request for logging.

    Args:
        request: Django HttpRequest object

    Returns:
        Dictionary containing client information
    """
    if not request:
        return {}

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip_address = x_forwarded_for.split(',')[0].strip()
    else:
        ip_address = request.META.get('REMOTE_ADDR')

    return {
        'ip_address': ip_address,
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'referer': request.META.get('HTTP_REFERER', ''),
        'request_method': request.method,
        'request_path': request.path,
        'is_ajax': request.headers.get('X-Requested-With') == 'XMLHttpRequest',
        'is_secure': request.is_secure()
    }
