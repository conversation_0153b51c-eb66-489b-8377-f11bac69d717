/**
 * Customer Profile Page Styles
 * Professional black-and-white design for CozyWish customer profile page
 */

/* CSS Custom Properties */
:root {
    --profile-primary: black;
    --profile-secondary: white;
    --profile-border: 2px solid black;
    --profile-border-radius: 0.75rem;
    --profile-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --profile-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.15);
    --profile-transition: all 0.3s ease;
    --profile-spacing: 1.5rem;
}

/* Profile Wrapper */
.profile-wrapper {
    background-color: var(--profile-secondary);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Profile Header Section */
.profile-header {
    background: var(--profile-secondary);
    border: var(--profile-border);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    padding: 3rem 2rem;
    margin-bottom: 3rem;
    transition: var(--profile-transition);
}

.profile-header:hover {
    box-shadow: var(--profile-shadow-hover);
}

/* Profile Picture Styling */
.profile-picture-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.profile-picture {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--profile-primary);
    box-shadow: var(--profile-shadow);
    transition: var(--profile-transition);
}

.profile-picture:hover {
    transform: scale(1.05);
}

.profile-picture-edit-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--profile-primary);
    border: 2px solid var(--profile-secondary);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    color: var(--profile-secondary);
    cursor: pointer;
    box-shadow: var(--profile-shadow);
    transition: var(--profile-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-picture-edit-btn:hover {
    background: var(--profile-secondary);
    color: var(--profile-primary);
    border-color: var(--profile-primary);
    transform: scale(1.1);
}

/* Profile Header Content */
.profile-name {
    font-family: var(--font-heading);
    font-size: 2.75rem;
    font-weight: 700;
    color: var(--profile-primary);
    margin-bottom: 0.75rem;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.profile-email {
    color: var(--profile-primary);
    font-size: 1.25rem;
    margin-bottom: 0;
    opacity: 0.8;
    font-weight: 500;
}

/* Information Display Cards */
.profile-info-section {
    margin-bottom: 3rem;
}

.section-title {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 600;
    color: var(--profile-primary);
    margin-bottom: 2rem;
    text-align: center;
    letter-spacing: -0.02em;
}

.profile-info-card {
    background: var(--profile-secondary);
    border: var(--profile-border);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    transition: var(--profile-transition);
    height: 100%;
    overflow: hidden;
}

.profile-info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--profile-shadow-hover);
}

.profile-card-header {
    padding: 2rem 2rem 1rem;
    border-bottom: var(--profile-border);
    background: var(--profile-secondary);
}

.profile-card-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--profile-primary);
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.02em;
}

.profile-card-title i {
    color: var(--profile-primary);
    font-size: 1.25rem;
    width: 1.5rem;
    text-align: center;
}

.profile-card-body {
    padding: 2rem;
}

/* Information Rows */
.profile-info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    gap: 1rem;
}

.profile-info-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.profile-info-label {
    font-weight: 600;
    color: var(--profile-primary);
    flex: 0 0 45%;
    font-size: 1rem;
    line-height: 1.5;
}

.profile-info-value {
    color: var(--profile-primary);
    text-align: right;
    flex: 1;
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.5;
    word-break: break-word;
}

.profile-info-value.empty {
    color: var(--profile-primary);
    opacity: 0.6;
    font-style: italic;
}

/* Badge Styling */
.badge {
    font-weight: 600;
    letter-spacing: 0.025em;
    background-color: var(--profile-primary) !important;
    color: var(--profile-secondary) !important;
    border: var(--profile-border) !important;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Action Buttons Section */
.profile-actions-section {
    background: var(--profile-secondary);
    border: var(--profile-border);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    padding: 2.5rem 2rem;
    margin-top: 3rem;
}

.actions-title {
    font-family: var(--font-heading);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--profile-primary);
    margin-bottom: 2rem;
    text-align: center;
    letter-spacing: -0.02em;
}

.profile-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.profile-btn {
    background: var(--profile-primary);
    color: var(--profile-secondary);
    border: var(--profile-border);
    border-radius: 0.5rem;
    padding: 1rem 2rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--profile-transition);
    display: inline-flex;
    align-items: center;
    letter-spacing: 0.025em;
    min-width: 180px;
    justify-content: center;
    cursor: pointer;
}

.profile-btn:hover {
    background: var(--profile-secondary);
    color: var(--profile-primary);
    border-color: var(--profile-primary);
    transform: translateY(-2px);
    box-shadow: var(--profile-shadow);
    text-decoration: none;
}

.profile-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.profile-btn-danger {
    background: #dc3545;
    border-color: #dc3545;
    color: var(--profile-secondary);
}

.profile-btn-danger:hover {
    background: var(--profile-secondary);
    color: #dc3545;
    border-color: #dc3545;
}

/* Animation Classes */
.profile-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.profile-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 992px) {
    .profile-header {
        padding: 2.5rem 2rem;
    }

    .profile-name {
        font-size: 2.25rem;
    }

    .section-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 768px) {
    .profile-wrapper {
        padding: 1rem 0;
    }

    .profile-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .profile-name {
        font-size: 2rem;
        text-align: center;
    }

    .profile-email {
        text-align: center;
        font-size: 1.125rem;
    }

    .profile-picture-container {
        text-align: center;
        width: 100%;
        margin-bottom: 1.5rem;
    }

    .profile-actions {
        flex-direction: column;
        align-items: center;
    }

    .profile-btn {
        width: 100%;
        max-width: 280px;
    }

    .profile-card-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .profile-card-body {
        padding: 1.5rem;
    }

    .profile-info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .profile-info-label {
        flex: none;
    }

    .profile-info-value {
        text-align: left;
        flex: none;
        width: 100%;
    }

    .section-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .profile-actions-section {
        padding: 2rem 1.5rem;
        margin-top: 2rem;
    }

    .actions-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .profile-header {
        padding: 1.5rem 1rem;
    }

    .profile-name {
        font-size: 1.75rem;
    }

    .profile-email {
        font-size: 1rem;
    }

    .profile-picture {
        width: 120px;
        height: 120px;
    }

    .profile-picture-edit-btn {
        width: 40px;
        height: 40px;
        bottom: 0;
        right: 0;
    }

    .profile-card-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .profile-card-body {
        padding: 1.25rem;
    }

    .profile-card-title {
        font-size: 1.25rem;
    }

    .profile-actions-section {
        padding: 1.5rem 1rem;
    }

    .profile-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Loading State */
.profile-loading {
    position: relative;
}

.profile-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .profile-fade-in,
    .profile-btn,
    .profile-info-card,
    .profile-picture {
        transition: none !important;
        animation: none !important;
    }
}

/* Focus Styles */
.profile-btn:focus,
.profile-picture-edit-btn:focus {
    outline: 2px solid var(--profile-primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .profile-actions-section,
    .profile-picture-edit-btn {
        display: none !important;
    }

    .profile-wrapper {
        background: white !important;
        padding: 0 !important;
    }

    .profile-info-card,
    .profile-header {
        box-shadow: none !important;
        border: 1px solid black !important;
    }
}
