# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import ReviewResponse


class ReviewResponseForm(forms.ModelForm):
    """Form for service providers to respond to customer reviews."""

    class Meta:
        model = ReviewResponse
        fields = ['response_text']
        widgets = {
            'response_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('Thank you for your feedback. We appreciate...'),
                'maxlength': 500,
                'required': True,
            })
        }
        labels = {
            'response_text': _('Your Response')
        }
        help_texts = {
            'response_text': _('Respond professionally to address the customer\'s feedback (maximum 500 characters)')
        }

    def __init__(self, *args, **kwargs):
        self.provider = kwargs.pop('provider', None)
        self.review = kwargs.pop('review', None)
        super().__init__(*args, **kwargs)

    def clean_response_text(self):
        response_text = self.cleaned_data.get('response_text')
        if not response_text or len(response_text.strip()) < 10:
            raise ValidationError(_('Response must be at least 10 characters long.'))
        return response_text.strip()

    def save(self, commit=True):
        response = super().save(commit=False)
        if self.provider:
            response.provider = self.provider
        if self.review:
            response.review = self.review
        if commit:
            response.save()
        return response
