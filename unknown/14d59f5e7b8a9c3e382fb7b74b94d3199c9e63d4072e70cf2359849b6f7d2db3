"""
Tests package for notifications_app.

This package contains all unit and integration tests for the notifications_app.
All test modules are imported here to ensure proper test discovery.
"""

# Import all test modules to ensure they are discovered by <PERSON><PERSON><PERSON>'s test runner
from .test_models import (
    NotificationModelTest, NotificationMethodsTest, AdminAnnouncementModelTest
)
from .test_forms import (
    NotificationFormTest, AdminAnnouncementFormTest,
    NotificationFilterFormTest, BulkNotificationFormTest
)
from .test_utils import (
    NotificationUtilsTest
)
from .simple_test import (
    SimpleTestCase
)
from .test_integration import (
    NotificationWorkflowIntegrationTest, CrossAppIntegrationTest,
    AdminNotificationIntegrationTest, NotificationAjaxIntegrationTest,
    NotificationSecurityIntegrationTest
)
from .test_management_commands import TestNotificationManagementCommands

__all__ = [
    # Model tests
    'NotificationModelTest', 'NotificationMethodsTest', 'AdminAnnouncementModelTest',

    # Form tests
    'NotificationFormTest', 'AdminAnnouncementFormTest',
    'NotificationFilterFormTest', 'BulkNotificationFormTest',

    # Utility tests
    'NotificationUtilsTest',

    # Simple tests
    'SimpleTestCase',

    # Integration tests
    'NotificationWorkflowIntegrationTest', 'CrossAppIntegrationTest',
    'AdminNotificationIntegrationTest', 'NotificationAjaxIntegrationTest',
    'NotificationSecurityIntegrationTest',
    'TestNotificationManagementCommands',
]
