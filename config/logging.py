import os

LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()


# --- Common Formatters ---
COMMON_FORMATTERS = {
    'verbose': {
        'format': '[{asctime}] {levelname} {name} | PID:{process} TID:{thread} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    },
    'simple': {
        'format': '[{asctime}] {levelname} {name} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    },
    'security': {
        'format': '[{asctime}] SECURITY {levelname} {name} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    },
    'audit': {
        'format': '[{asctime}] AUDIT {name} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    },
    'performance': {
        'format': '[{asctime}] PERF {levelname} {name} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    },
    'error': {
        'format': '[{asctime}] ERROR {name} | PID:{process} | {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S'
    }
}



# --- Common Handlers ---
COMMON_HANDLERS = {
    'console': {
        'level': LOG_LEVEL,
        'class': 'logging.StreamHandler',
        'formatter': 'simple',
        'stream': 'ext://sys.stdout'
    },
    'console_verbose': {
        'level': LOG_LEVEL,
        'class': 'logging.StreamHandler',
        'formatter': 'verbose',
        'stream': 'ext://sys.stdout'
    },
    'security_console': {
        'level': 'INFO',
        'class': 'logging.StreamHandler',
        'formatter': 'security',
        'stream': 'ext://sys.stdout'
    },
    'error_console': {
        'level': 'ERROR',
        'class': 'logging.StreamHandler',
        'formatter': 'error',
        'stream': 'ext://sys.stderr'
    },
    'performance_console': {
        'level': 'DEBUG',
        'class': 'logging.StreamHandler',
        'formatter': 'performance',
        'stream': 'ext://sys.stdout'
    },
    'audit_console': {
        'level': 'INFO',
        'class': 'logging.StreamHandler',
        'formatter': 'audit',
        'stream': 'ext://sys.stdout'
    },
}



# --- App-Specific Loggers ---
def app_loggers(app):
    """Return standard loggers for an app with common patterns."""
    return {
        app: {
            'handlers': ['console_verbose'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        f'{app}.security': {
            'handlers': ['security_console'],
            'level': 'INFO',
            'propagate': False,
        },
        f'{app}.activity': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        f'{app}.errors': {
            'handlers': ['error_console'],
            'level': 'ERROR',
            'propagate': False,
        },
        f'{app}.performance': {
            'handlers': ['performance_console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        f'{app}.audit': {
            'handlers': ['audit_console'],
            'level': 'INFO',
            'propagate': False,
        },
    }


LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': COMMON_FORMATTERS,
    'handlers': COMMON_HANDLERS,
    'loggers': {
        # Django's own loggers
        'django': {
            'handlers': ['console_verbose'],
            'level': LOG_LEVEL,
            'propagate': False
        },
        'django.request': {
            'handlers': ['error_console', 'console'],
            'level': 'WARNING',
            'propagate': False
        },
        'django.security': {
            'handlers': ['security_console'],
            'level': 'INFO',
            'propagate': False
        },
        'django.db.backends': {
            'handlers': ['console_verbose'],
            'level': 'WARNING',
            'propagate': False
        },
        # Third-party - Enable more detailed logging for S3 debugging
        'storages': {'handlers': ['console'], 'level': 'INFO', 'propagate': False},
        'boto3': {'handlers': ['console'], 'level': 'INFO', 'propagate': False},
        'botocore': {'handlers': ['console'], 'level': 'INFO', 'propagate': False},
        's3transfer': {'handlers': ['console'], 'level': 'INFO', 'propagate': False},
        # Root logger
        '': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False
        },
    }
}


# Add your apps here to auto-generate their logger configs
for app in [
    'accounts_app', 'venues_app', 'utility_app', 'utils', 'booking_cart_app',
    'payments_app', 'dashboard_app', 'cms_app', 'review_app', 'admin_app',
    'discount_app', 'notifications_app'
]:
    LOGGING['loggers'].update(app_loggers(app))
