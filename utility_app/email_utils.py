"""
Email utility functions for CozyWish with enhanced HTML templates and delivery tracking.
"""
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


def send_welcome_email(user_email, user_name, user_type='customer'):
    """
    Send a welcome email to new users using the enhanced notification system.

    Args:
        user_email (str): User's email address
        user_name (str): User's name
        user_type (str): 'customer' or 'provider'

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from django.contrib.auth import get_user_model
        from notifications_app.utils import send_welcome_email as send_welcome_notification
        
        User = get_user_model()
        
        # Try to get the user object
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            # Create a temporary user object for the email
            user = User(email=user_email, first_name=user_name)
        
        # Use the enhanced notification system
        result = send_welcome_notification(user, user_type)
        
        if result['success']:
            logger.info(
                f"Welcome email sent successfully to {user_email}",
                extra={
                    'email_type': 'welcome',
                    'recipient': user_email,
                    'user_name': user_name,
                    'user_type': user_type,
                    'status': 'success',
                    'email_id': result.get('email_id')
                }
            )
            return True
        else:
            logger.error(
                f"Failed to send welcome email to {user_email}: {result.get('error')}",
                extra={
                    'email_type': 'welcome',
                    'recipient': user_email,
                    'user_name': user_name,
                    'user_type': user_type,
                    'error': result.get('error'),
                    'status': 'failed'
                }
            )
            return False

    except Exception as e:
        logger.error(
            f"Exception while sending welcome email to {user_email}: {str(e)}",
            extra={
                'email_type': 'welcome',
                'recipient': user_email,
                'user_name': user_name,
                'error': str(e),
                'status': 'failed'
            }
        )
        return False


def send_booking_confirmation_email(user_email, user_name, booking_details):
    """
    Send booking confirmation email using enhanced templates.
    
    Args:
        user_email (str): User's email address
        user_name (str): User's name
        booking_details (dict): Booking information
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from notifications_app.utils import send_notification_email
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to get the user object
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            # Create a temporary user object for the email
            user = User(email=user_email, first_name=user_name)
        
        # Use the enhanced booking confirmation template
        title = f'Booking Confirmed - {booking_details.get("reference", "CozyWish Service")}'
        message = f"""Your booking has been confirmed! Here are the details:
        
Service: {booking_details.get('service_name', 'N/A')}
Date: {booking_details.get('date', 'N/A')}
Time: {booking_details.get('time', 'N/A')}
Location: {booking_details.get('location', 'N/A')}
Booking Reference: {booking_details.get('reference', 'N/A')}

Please arrive 15 minutes before your appointment time."""
        
        # Create a mock booking object for the template
        class MockBooking:
            def __init__(self, details):
                self.friendly_id = details.get('reference', 'N/A')
                self.booking_date = details.get('date', 'N/A')
                self.total_price = details.get('total_price', '0.00')
                self.special_requests = details.get('special_requests', '')
                self.customer = user
                
                class MockVenue:
                    venue_name = details.get('location', 'CozyWish Venue')
                
                self.venue = MockVenue()
                
                # Mock booking items
                class MockItem:
                    def __init__(self):
                        self.service_title = details.get('service_name', 'Wellness Service')
                        self.scheduled_date = details.get('date', 'N/A')
                        self.scheduled_time = details.get('time', 'N/A')
                        self.service_price = details.get('price', '0.00')
                
                class MockItems:
                    def all(self):
                        return [MockItem()]
                    
                    def order_by(self, *args):
                        return self.all()
                
                self.items = MockItems()
        
        mock_booking = MockBooking(booking_details)
        
        result = send_notification_email(
            user=user,
            title=title,
            message=message,
            template='emails/booking_confirmation.html',
            context={'booking': mock_booking},
            email_type='booking_confirmation'
        )
        
        if result['success']:
            logger.info(f"Booking confirmation email sent successfully to {user_email}")
            return True
        else:
            logger.error(f"Failed to send booking confirmation email to {user_email}: {result.get('error')}")
            return False
        
    except Exception as e:
        logger.error(f"Exception while sending booking confirmation email to {user_email}: {str(e)}")
        return False


def send_email_verification(user_email, verification_link):
    """
    Send email verification link to service providers using enhanced templates.

    Args:
        user_email (str): User's email address
        verification_link (str): Email verification link

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from notifications_app.utils import send_email_verification as send_verification_notification
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to get the user object
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            # Create a temporary user object for the email
            user = User(email=user_email)
        
        # Use the enhanced notification system
        result = send_verification_notification(user, verification_link)
        
        if result['success']:
            logger.info(f"Email verification sent successfully to {user_email}")
            return True
        else:
            logger.error(f"Failed to send email verification to {user_email}: {result.get('error')}")
            return False

    except Exception as e:
        logger.error(f"Exception while sending email verification to {user_email}: {str(e)}")
        return False


def send_password_reset_email(user_email, reset_link):
    """
    Send password reset email - leveraging existing professional templates.
    
    Args:
        user_email (str): User's email address
        reset_link (str): Password reset link
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # The password reset emails already have excellent HTML templates
        # This function is kept for backward compatibility
        
        # Import here to avoid circular imports
        from notifications_app.utils import send_notification_email
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to get the user object
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            # Create a temporary user object for the email
            user = User(email=user_email)
        
        title = 'Password Reset Request'
        message = f"""You have requested to reset your password for your CozyWish account.

Please click the link below to reset your password:
{reset_link}

This link will expire in 24 hours for security reasons."""
        
        result = send_notification_email(
            user=user,
            title=title,
            message=message,
            action_url=reset_link,
            email_type='password_reset'
        )
        
        if result['success']:
            logger.info(f"Password reset email sent successfully to {user_email}")
            return True
        else:
            logger.error(f"Failed to send password reset email to {user_email}: {result.get('error')}")
            return False
        
    except Exception as e:
        logger.error(f"Exception while sending password reset email to {user_email}: {str(e)}")
        return False


def send_promotional_email(user_email, user_name, promotion_details):
    """
    Send promotional email about special offers using enhanced templates.
    
    Args:
        user_email (str): User's email address
        user_name (str): User's name
        promotion_details (dict): Promotion information
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from notifications_app.utils import send_notification_email
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to get the user object
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            # Create a temporary user object for the email
            user = User(email=user_email, first_name=user_name)
        
        title = f'Special Offer: {promotion_details.get("title", "Amazing Spa Deals")}'
        message = f"""We have an exciting special offer just for you!

{promotion_details.get('title', 'Special Promotion')}

{promotion_details.get('description', 'Check out our latest deals and discounts.')}

Discount: {promotion_details.get('discount', 'Up to 50% off')}
Valid Until: {promotion_details.get('valid_until', 'Limited time')}

Don't miss out on this amazing opportunity to treat yourself!"""
        
        result = send_notification_email(
            user=user,
            title=title,
            message=message,
            context={
                'promotion': promotion_details,
                'user_name': user_name,
            },
            email_type='promotional'
        )
        
        if result['success']:
            logger.info(f"Promotional email sent successfully to {user_email}")
            return True
        else:
            logger.error(f"Failed to send promotional email to {user_email}: {result.get('error')}")
            return False
        
    except Exception as e:
        logger.error(f"Exception while sending promotional email to {user_email}: {str(e)}")
        return False


def get_email_delivery_statistics(email_type=None, days=30):
    """
    Get email delivery statistics using the enhanced tracking system.
    
    Args:
        email_type (str, optional): Filter by email type
        days (int): Number of days to look back
    
    Returns:
        dict: Delivery statistics
    """
    try:
        from notifications_app.utils import get_email_delivery_stats
        return get_email_delivery_stats(email_type, days)
    except Exception as e:
        logger.error(f"Failed to get email delivery statistics: {str(e)}")
        return {}


def handle_email_bounce_webhook(email_address, bounce_type, bounce_reason):
    """
    Handle email bounce from webhook (e.g., SendGrid, AWS SES).
    
    Args:
        email_address (str): Email that bounced
        bounce_type (str): Type of bounce
        bounce_reason (str): Reason for bounce
    
    Returns:
        bool: True if handled successfully
    """
    try:
        from notifications_app.utils import handle_email_bounce
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to find the user
        user = None
        try:
            user = User.objects.get(email=email_address)
        except User.DoesNotExist:
            pass
        
        bounce_record = handle_email_bounce(email_address, bounce_type, bounce_reason, user)
        
        if bounce_record:
            logger.info(f"Email bounce handled successfully for {email_address}")
            return True
        else:
            logger.error(f"Failed to handle email bounce for {email_address}")
            return False
            
    except Exception as e:
        logger.error(f"Exception while handling email bounce for {email_address}: {str(e)}")
        return False
