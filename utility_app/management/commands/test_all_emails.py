"""
Management command to test all email functionalities in CozyWish.
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from utility_app.email_utils import (
    send_welcome_email, 
    send_email_verification, 
    send_password_reset_email,
    send_promotional_email
)
from notifications_app.utils import send_welcome_email as send_welcome_notification

User = get_user_model()


class Command(BaseCommand):
    """Test all email functionalities."""
    
    help = 'Test all email functionalities in CozyWish'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            'test_email',
            type=str,
            help='Email address to send test emails to'
        )
        parser.add_argument(
            '--name',
            type=str,
            default='Test User',
            help='Name to use in test emails'
        )

    def handle(self, *args, **options):
        """Execute the command."""
        test_email = options['test_email']
        test_name = options['name']
        
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing All CozyWish Email Functionalities')
        )
        self.stdout.write('=' * 60)
        self.stdout.write(f'📧 Test Email: {test_email}')
        self.stdout.write(f'👤 Test Name: {test_name}')
        
        # Show current email configuration
        self.show_email_config()
        
        # Test all email functionalities
        self.test_welcome_emails(test_email, test_name)
        self.test_password_reset_emails(test_email, test_name)
        self.test_email_verification(test_email, test_name)
        self.test_promotional_emails(test_email, test_name)
        
        self.stdout.write(
            self.style.SUCCESS('\n✅ All email functionality tests completed!')
        )

    def show_email_config(self):
        """Show current email configuration."""
        self.stdout.write('\n📋 Current Email Configuration:')
        self.stdout.write(f'   Backend: {settings.EMAIL_BACKEND}')
        self.stdout.write(f'   Host: {settings.EMAIL_HOST}')
        self.stdout.write(f'   Port: {settings.EMAIL_PORT}')
        self.stdout.write(f'   From Email: {settings.DEFAULT_FROM_EMAIL}')
        
        if hasattr(settings, 'EMAIL_HOST_PASSWORD') and settings.EMAIL_HOST_PASSWORD:
            self.stdout.write('   SendGrid API Key: ✅ Configured')
        else:
            self.stdout.write('   SendGrid API Key: ❌ Not configured')
        
        if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
            self.stdout.write('   📺 Emails will be printed to console')

    def test_welcome_emails(self, test_email, test_name):
        """Test welcome email functionality."""
        self.stdout.write('\n🎉 Testing Welcome Emails:')
        self.stdout.write('-' * 30)
        
        # Test customer welcome email
        self.stdout.write('   Testing customer welcome email...')
        try:
            success = send_welcome_email(test_email, test_name, 'customer')
            if success:
                self.stdout.write('   ✅ Customer welcome email: PASSED')
            else:
                self.stdout.write('   ❌ Customer welcome email: FAILED')
        except Exception as e:
            self.stdout.write(f'   ❌ Customer welcome email: ERROR - {str(e)}')
        
        # Test provider welcome email
        self.stdout.write('   Testing provider welcome email...')
        try:
            success = send_welcome_email(test_email, test_name, 'provider')
            if success:
                self.stdout.write('   ✅ Provider welcome email: PASSED')
            else:
                self.stdout.write('   ❌ Provider welcome email: FAILED')
        except Exception as e:
            self.stdout.write(f'   ❌ Provider welcome email: ERROR - {str(e)}')

    def test_password_reset_emails(self, test_email, test_name):
        """Test password reset email functionality."""
        self.stdout.write('\n🔐 Testing Password Reset Emails:')
        self.stdout.write('-' * 35)
        
        # Create a test user for password reset
        try:
            test_user, created = User.objects.get_or_create(
                email=test_email,
                defaults={
                    'first_name': test_name.split()[0],
                    'last_name': test_name.split()[-1] if len(test_name.split()) > 1 else '',
                    'role': User.CUSTOMER
                }
            )
            
            # Generate password reset link
            token = default_token_generator.make_token(test_user)
            uid = urlsafe_base64_encode(force_bytes(test_user.pk))
            reset_link = f"https://cozywish.com/accounts/customer/password-reset/confirm/{uid}/{token}/"
            
            self.stdout.write('   Testing password reset email...')
            success = send_password_reset_email(test_email, reset_link)
            if success:
                self.stdout.write('   ✅ Password reset email: PASSED')
            else:
                self.stdout.write('   ❌ Password reset email: FAILED')
                
        except Exception as e:
            self.stdout.write(f'   ❌ Password reset email: ERROR - {str(e)}')

    def test_email_verification(self, test_email, test_name):
        """Test email verification functionality."""
        self.stdout.write('\n📧 Testing Email Verification:')
        self.stdout.write('-' * 30)
        
        try:
            # Generate verification link
            verification_link = f"https://cozywish.com/accounts/provider/verify/test-token/"
            
            self.stdout.write('   Testing email verification...')
            success = send_email_verification(test_email, verification_link)
            if success:
                self.stdout.write('   ✅ Email verification: PASSED')
            else:
                self.stdout.write('   ❌ Email verification: FAILED')
                
        except Exception as e:
            self.stdout.write(f'   ❌ Email verification: ERROR - {str(e)}')

    def test_promotional_emails(self, test_email, test_name):
        """Test promotional email functionality."""
        self.stdout.write('\n🎁 Testing Promotional Emails:')
        self.stdout.write('-' * 30)
        
        try:
            promotion_details = {
                'title': 'Special Summer Spa Deals',
                'description': 'Enjoy up to 50% off on all massage and wellness services!',
                'discount': '50% OFF',
                'valid_until': 'End of Summer'
            }
            
            self.stdout.write('   Testing promotional email...')
            success = send_promotional_email(test_email, test_name, promotion_details)
            if success:
                self.stdout.write('   ✅ Promotional email: PASSED')
            else:
                self.stdout.write('   ❌ Promotional email: FAILED')
                
        except Exception as e:
            self.stdout.write(f'   ❌ Promotional email: ERROR - {str(e)}')

    def test_notification_system(self, test_email, test_name):
        """Test the notification system directly."""
        self.stdout.write('\n🔔 Testing Notification System:')
        self.stdout.write('-' * 32)
        
        try:
            # Create temporary user for notification testing
            temp_user = User(email=test_email, first_name=test_name)
            
            self.stdout.write('   Testing notification welcome email...')
            result = send_welcome_notification(temp_user, 'customer')
            if result.get('success'):
                self.stdout.write('   ✅ Notification welcome email: PASSED')
            else:
                self.stdout.write(f'   ❌ Notification welcome email: FAILED - {result.get("error")}')
                
        except Exception as e:
            self.stdout.write(f'   ❌ Notification welcome email: ERROR - {str(e)}') 