{"name": "CozyWish Design System", "version": "2.0.0", "description": "Professional design system for spa and wellness marketplace applications", "colors": {"brand": {"primary": {"value": "#2F160F", "description": "Primary brand color - dark brown for main CTAs and primary actions"}, "light": {"value": "#4a2a1f", "description": "Lighter brand color for hover states and secondary brand elements"}, "accent": {"value": "#fae1d7", "description": "Brand accent color - light cream/beige for backgrounds and highlights"}, "accent_light": {"value": "#fef7f0", "description": "Lightest accent color for subtle backgrounds and light sections"}, "accent_dark": {"value": "#f1d4c4", "description": "Darker accent color for card sections and secondary elements"}}, "secondary": {"50": "#f9f7f4", "200": "#e3d5c4", "500": "#ad7f5a", "900": "#583d30", "950": "#2f1f18"}, "neutral": {"50": "#fafafa", "100": "#f5f5f5", "200": "#e5e5e5", "300": "#d4d4d4", "400": "#a3a3a3", "500": "#737373", "600": "#525252", "700": "#404040", "800": "#262626", "900": "#171717", "950": "#0a0a0a"}, "semantic": {"success": "#059669", "warning": "#d97706", "error": "#dc2626", "info": "#0284c7"}}, "typography": {"fonts": {"primary": {"family": "Inter", "fallback": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "usage": "Body text, forms, and general content"}, "heading": {"family": "<PERSON><PERSON><PERSON>", "fallback": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "usage": "Headings, navigation, and UI elements"}, "display": {"family": "Playfair Display", "fallback": "Georgia, 'Times New Roman', serif", "usage": "Hero headings and decorative text"}}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "base": "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"}, "gradients": {"hero": "radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%)", "card": "linear-gradient(135deg, #ffffff 0%, #fae1d7 100%)", "card_subtle": "linear-gradient(135deg, #ffffff 0%, #fef7f0 100%)", "brand_button": "linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%)", "accent": "linear-gradient(135deg, #fae1d7 0%, #f1d4c4 100%)"}, "components": {"buttons": {"primary": {"background": "var(--cw-gradient-brand-button)", "color": "white", "border": "none", "usage": "Primary actions, main CTAs"}, "secondary": {"background": "white", "color": "var(--cw-brand-primary)", "border": "2px solid var(--cw-brand-primary)", "usage": "Secondary actions, alternative CTAs"}, "accent": {"background": "var(--cw-gradient-accent)", "color": "var(--cw-brand-primary)", "border": "none", "usage": "Accent actions, special highlights"}, "ghost": {"background": "transparent", "color": "var(--cw-brand-primary)", "border": "none", "usage": "Subtle actions, links"}}, "cards": {"default": {"background": "white", "border": "1px solid var(--cw-neutral-200)", "shadow": "var(--cw-shadow-md)"}, "brand": {"background": "var(--cw-gradient-card)", "border": "2px solid var(--cw-brand-primary)", "shadow": "0 10px 15px -3px rgba(47, 22, 15, 0.1)"}, "accent": {"background": "var(--cw-brand-accent)", "border": "1px solid var(--cw-brand-accent)", "shadow": "var(--cw-shadow-sm)"}, "featured": {"background": "var(--cw-gradient-card-subtle)", "border": "2px solid var(--cw-brand-primary)", "shadow": "var(--cw-shadow-md)"}}}, "usage_guidelines": {"primary_actions": "Use Brand Primary (#2F160F) for all primary buttons and main CTAs", "secondary_actions": "Use Accent Dark (#f1d4c4) for secondary buttons and card sections", "backgrounds": "Use Brand Accent (#fae1d7) for highlighted backgrounds and special sections", "text_hierarchy": "Brand Primary for headings, Neutral colors for body text", "consistency": "Always use the defined color variables instead of hardcoded hex values"}}