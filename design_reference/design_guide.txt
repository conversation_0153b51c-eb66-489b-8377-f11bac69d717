COZYWISH DESIGN SYSTEM - IMPLEMENTATION GUIDE
==============================================

VERSION: 2.0.0
LAST UPDATED: 2025-01-23

OVERVIEW
--------
The CozyWish Design System is a comprehensive design framework for spa and wellness marketplace applications. This guide provides implementation guidelines for developers to ensure consistent brand application across all digital touchpoints.

BRAND COLORS (FINAL PALETTE)
-----------------------------
The design system uses ONLY the following approved brand colors:

PRIMARY BRAND COLORS:
• Brand Primary: #2F160F (Dark brown - primary actions, CTAs)
• Brand Light: #4a2a1f (Lighter brown - hover states, secondary brand elements)
• Brand Accent: #fae1d7 (Light cream/beige - backgrounds, highlights)
• Accent Light: #fef7f0 (Lightest cream - subtle backgrounds)
• Accent Dark: #f1d4c4 (Darker cream - card sections, secondary elements)

SUPPORTING COLORS:
• Secondary colors: #f9f7f4, #e3d5c4, #ad7f5a, #583d30, #2f1f18
• Neutral colors: #fafafa to #0a0a0a (grayscale palette)
• Semantic colors: Success #059669, Warning #d97706, Error #dc2626, Info #0284c7

REMOVED COLORS:
All orange colors (#ed7544, #de5a2c, etc.) have been completely removed from the design system.

COLOR USAGE GUIDELINES
-----------------------
1. PRIMARY ACTIONS: Always use Brand Primary (#2F160F) for:
   - Primary buttons and main CTAs
   - Navigation active states
   - Important headings
   - Form focus states

2. SECONDARY ACTIONS: Use Accent Dark (#f1d4c4) for:
   - Secondary buttons
   - Card section backgrounds
   - Subtle highlights
   - Alternative CTAs

3. BACKGROUNDS: Use Brand Accent (#fae1d7) for:
   - Hero section backgrounds
   - Special offer cards
   - Highlighted content areas
   - Feature sections

4. SUBTLE ELEMENTS: Use Accent Light (#fef7f0) for:
   - Page backgrounds
   - Light card backgrounds
   - Subtle section dividers
   - Form field backgrounds

CSS IMPLEMENTATION
------------------
Use the following CSS custom properties in your stylesheets:

:root {
  /* Brand Colors */
  --cw-brand-primary: #2F160F;
  --cw-brand-light: #4a2a1f;
  --cw-brand-accent: #fae1d7;
  --cw-accent-light: #fef7f0;
  --cw-accent-dark: #f1d4c4;
  
  /* Gradients */
  --cw-gradient-hero: radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%);
  --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, #fae1d7 100%);
  --cw-gradient-brand-button: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
  --cw-gradient-accent: linear-gradient(135deg, #fae1d7 0%, #f1d4c4 100%);
}

COMPONENT CLASSES
-----------------
Button Classes:
• .btn-cw-primary - Primary brand button (dark brown gradient)
• .btn-cw-secondary - Secondary button (white with brand border)
• .btn-cw-accent - Accent button (cream gradient with brand text)
• .btn-cw-accent-outline - Outlined accent button
• .btn-cw-ghost - Transparent button with brand text

Card Classes:
• .card-cw - Default white card with shadow
• .card-cw-brand - Brand card with gradient background
• .card-cw-accent - Accent card with cream background
• .card-cw-featured - Featured card with brand border

Text Classes:
• .text-brand-cw - Brand primary color text
• .text-brand-light-cw - Brand light color text
• .text-accent-cw - Brand accent color text
• .text-neutral-cw - Neutral gray text

Background Classes:
• .bg-brand-cw - Brand primary background
• .bg-brand-accent-cw - Brand accent background
• .bg-accent-dark-cw - Accent dark background
• .bg-light-cw - Light accent background

TYPOGRAPHY
----------
Font Families:
• Primary: Inter (body text, forms, general content)
• Heading: Poppins (headings, navigation, UI elements)
• Display: Playfair Display (hero headings, decorative text)

Font Loading:
Include these Google Fonts in your HTML head:
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

DEVELOPMENT BEST PRACTICES
---------------------------
1. CONSISTENCY: Always use CSS custom properties instead of hardcoded hex values
2. ACCESSIBILITY: Ensure sufficient color contrast for text readability
3. RESPONSIVE: Test color combinations across different screen sizes
4. PERFORMANCE: Use gradients sparingly to maintain good performance
5. MAINTENANCE: Update colors through CSS variables for easy theme changes

BOOTSTRAP 5 INTEGRATION
------------------------
The design system is built on Bootstrap 5. Use these approaches:

1. Override Bootstrap variables with brand colors
2. Use utility classes with custom modifiers (.text-brand-cw, .bg-brand-cw)
3. Extend Bootstrap components with custom classes
4. Maintain Bootstrap's responsive grid system

QUALITY ASSURANCE
------------------
Before deployment, verify:
• No orange colors (#ed7544, #de5a2c) remain in the codebase
• All buttons use approved brand color classes
• Text hierarchy follows brand color guidelines
• Gradients render correctly across browsers
• Color contrast meets WCAG accessibility standards

MIGRATION FROM PREVIOUS VERSION
--------------------------------
If updating from a previous version:
1. Replace all .text-primary-cw with .text-brand-cw
2. Replace all .btn-cw-orange with .btn-cw-accent
3. Replace all .btn-cw-orange-outline with .btn-cw-accent-outline
4. Update any hardcoded orange hex values to brand colors
5. Test all components for visual consistency

SUPPORT AND MAINTENANCE
------------------------
For questions or updates to this design system:
• Reference the brand.html file for visual examples
• Use cozywish_design_system.json for programmatic access to design tokens
• Follow the color usage guidelines strictly for brand consistency

This design system ensures a cohesive, professional appearance across all CozyWish applications while maintaining the warm, inviting aesthetic appropriate for the spa and wellness industry.
