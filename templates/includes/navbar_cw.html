{% comment %}
CozyWish Professional Navbar Component
Three navbar types: without login, customer after login, service provider after login
Based on design_reference and brand styling
{% endcomment %}

{% load static %}

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-cw {% if hero_section %}navbar-hero{% endif %}">
    <div class="container px-4">
        <!-- Brand -->
        <a class="navbar-brand-cw" href="{% url 'home' %}">
            CozyWish
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler navbar-toggler-cw d-lg-none" 
                type="button" 
                data-bs-toggle="collapse" 
                data-bs-target="#navbarNav" 
                aria-controls="navbarNav" 
                aria-expanded="false" 
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon-cw"></span>
        </button>

        <!-- Navigation Content -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <div class="nav-buttons ms-auto">
                


                <!-- Shopping Cart Button (Customer Only) -->
                {% if user.is_authenticated and user.is_customer %}
                    <a href="{% url 'booking_cart_app:cart_view' %}"
                       class="btn-cw-nav-icon position-relative"
                       title="View Cart">
                        <i class="fas fa-shopping-cart"></i>
                        {% if cart_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge-cw-notification">
                                {{ cart_count }}
                                <span class="visually-hidden">items in cart</span>
                            </span>
                        {% endif %}
                    </a>
                {% endif %}

                <!-- Notifications (Authenticated Users) -->
                {% if user.is_authenticated %}
                    <a href="{% url 'notifications_app:notification_list' %}"
                       class="btn-cw-nav-icon position-relative"
                       title="Notifications">
                        <i class="fas fa-bell"></i>
                        {% if unread_notifications_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge-cw-notification">
                                {{ unread_notifications_count }}
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        {% endif %}
                    </a>
                {% endif %}

                <!-- Provider Dashboard or Business Link -->
                {% if user.is_service_provider %}
                    <a href="{% url 'dashboard_app:provider_dashboard' %}"
                       class="btn-cw-nav-icon {% if request.resolver_match.url_name == 'provider_dashboard' %}active{% endif %}"
                       title="Provider Dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                    </a>
                {% elif not user.is_authenticated or not user.is_customer %}
                    <a href="{% url 'accounts_app:for_business' %}"
                       class="btn-cw-nav-secondary {% if request.resolver_match.url_name == 'for_business' %}active{% endif %}"
                       title="Business Registration">
                        <i class="fas fa-store me-2"></i>For Business
                    </a>
                {% endif %}

                <!-- Main Menu Dropdown -->
                <div class="dropdown">
                    <button class="btn-cw-nav-primary dropdown-toggle" 
                            type="button" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false"
                            title="Main Menu">
                        {% if user.is_authenticated %}
                            <div class="user-avatar-cw">
                                {{ user.first_name|first|default:"U" }}
                            </div>
                            {{ user.first_name|default:"Account" }}
                        {% else %}
                            <i class="fas fa-user me-2"></i>Menu
                        {% endif %}
                    </button>
                    
                    <!-- Dropdown Menu Content -->
                    <ul class="dropdown-menu dropdown-menu-end dropdown-menu-cw">
                        {% if user.is_authenticated %}
                            <!-- User Profile Section -->
                            <li class="dropdown-header-cw">
                                {% if user.is_service_provider %}
                                    <i class="fas fa-store me-2"></i>Business Account
                                {% else %}
                                    <i class="fas fa-user me-2"></i>Customer Account
                                {% endif %}
                            </li>
                            
                            <li>
                                {% if user.is_service_provider %}
                                    <a class="dropdown-item-cw" href="{% url 'accounts_app:service_provider_profile' %}">
                                        <i class="fas fa-user"></i>My Profile
                                    </a>
                                {% else %}
                                    <a class="dropdown-item-cw" href="{% url 'accounts_app:customer_profile' %}">
                                        <i class="fas fa-user"></i>My Profile
                                    </a>
                                {% endif %}
                            </li>

                            <!-- Customer-Specific Menu Items -->
                            {% if user.is_customer %}
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'dashboard_app:customer_dashboard' %}">
                                        <i class="fas fa-tachometer-alt"></i>Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'dashboard_app:customer_booking_status' %}">
                                        <i class="fas fa-calendar-check"></i>My Bookings
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'booking_cart_app:cart_view' %}">
                                        <i class="fas fa-shopping-cart"></i>My Cart
                                        {% if cart_count > 0 %}
                                            <span class="menu-badge-cw">{{ cart_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'review_app:customer_review_history' %}">
                                        <i class="fas fa-star"></i>My Reviews
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'notifications_app:notification_list' %}">
                                        <i class="fas fa-bell"></i>Notifications
                                        {% if unread_notifications_count > 0 %}
                                            <span class="menu-badge-cw">{{ unread_notifications_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'payments_app:payment_history' %}">
                                        <i class="fas fa-credit-card"></i>Payment History
                                    </a>
                                </li>
                            {% endif %}

                            <!-- Service Provider-Specific Menu Items -->
                            {% if user.is_service_provider %}
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'booking_cart_app:provider_booking_list' %}">
                                        <i class="fas fa-calendar-check"></i>Bookings
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'notifications_app:notification_list' %}">
                                        <i class="fas fa-bell"></i>Notifications
                                        {% if unread_notifications_count > 0 %}
                                            <span class="menu-badge-cw">{{ unread_notifications_count }}</span>
                                        {% endif %}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'payments_app:provider_payment_history' %}">
                                        <i class="fas fa-credit-card"></i>Payment History
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'venues_app:provider_venues' %}">
                                        <i class="fas fa-building"></i>My Venue
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'venues_app:manage_services' %}">
                                        <i class="fas fa-spa"></i>My Services
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'review_app:provider_venue_reviews' %}">
                                        <i class="fas fa-star"></i>Reviews
                                    </a>
                                </li>
                            {% endif %}

                            <!-- Admin Menu Items -->
                            {% if user.is_staff %}
                                <li><hr class="dropdown-divider-cw"></li>
                                <li class="dropdown-header-cw">
                                    <i class="fas fa-cog me-2"></i>Administration
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'admin_app:admin_dashboard' %}">
                                        <i class="fas fa-chart-bar"></i>Admin Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item-cw" href="{% url 'admin:index' %}">
                                        <i class="fas fa-cog"></i>Django Admin
                                    </a>
                                </li>
                            {% endif %}

                            <li><hr class="dropdown-divider-cw"></li>

                            <!-- Logout -->
                            <li>
                                <a class="dropdown-item-cw" href="{% url 'accounts_app:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i>Log out
                                </a>
                            </li>
                        {% else %}
                            <!-- Guest User Menu Items -->
                            <li>
                                <a class="dropdown-item-cw" href="{% url 'accounts_app:customer_login' %}">
                                    <i class="fas fa-sign-in-alt"></i>Log in
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item-cw" href="{% url 'accounts_app:customer_signup' %}">
                                    <i class="fas fa-user-plus"></i>Sign Up
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item-cw" href="{% url 'accounts_app:service_provider_login' %}">
                                    <i class="fas fa-sign-in-alt"></i>Business Login
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>
