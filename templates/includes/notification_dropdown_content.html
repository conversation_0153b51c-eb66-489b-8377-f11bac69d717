{% comment %}
Notification Dropdown Content for CozyWish Navbar
Simplified version for navbar integration
{% endcomment %}

<div class="dropdown-menu dropdown-menu-end dropdown-menu-cw" aria-labelledby="notificationDropdown">
    <div class="dropdown-header-cw">
        <i class="fas fa-bell me-2"></i>Notifications
    </div>
    
    {% if recent_notifications %}
        {% for notification in recent_notifications|slice:":3" %}
            <a class="dropdown-item-cw" href="{% url 'notifications_app:notification_detail' notification.id %}">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        {% if notification.notification_type == 'booking' %}
                            <i class="fas fa-calendar-check text-primary"></i>
                        {% elif notification.notification_type == 'payment' %}
                            <i class="fas fa-credit-card text-success"></i>
                        {% elif notification.notification_type == 'review' %}
                            <i class="fas fa-star text-warning"></i>
                        {% else %}
                            <i class="fas fa-info-circle text-info"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-semibold">{{ notification.title|truncatechars:30 }}</div>
                        <small class="text-muted">{{ notification.created_at|timesince }} ago</small>
                    </div>
                </div>
            </a>
        {% endfor %}
        <hr class="dropdown-divider-cw">
        <a class="dropdown-item-cw text-center" href="{% url 'notifications_app:notification_list' %}">
            <i class="fas fa-list me-2"></i>View All Notifications
        </a>
    {% else %}
        <div class="dropdown-item-cw text-center text-muted">
            <i class="fas fa-bell-slash me-2"></i>No new notifications
        </div>
    {% endif %}
</div>
