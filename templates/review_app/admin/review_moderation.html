{% extends 'review_app/base_review.html' %}

{% block title %}Review Moderation{% endblock %}

{% block extra_css %}
{% load static %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-shield-alt me-2"></i>Review Moderation
                </h2>
                <div>
                    <a href="{% url 'review_app:admin_manage_flags' %}" class="btn btn-warning me-2">
                        <i class="fas fa-flag me-2"></i>Flagged Reviews
                        {% if flagged_count > 0 %}
                            <span class="badge ms-1" style="background-color: white; color: black; border: 2px solid black;">{{ flagged_count }}</span>
                        {% endif %}
                    </a>
                    <span class="badge fs-6" style="background-color: black; color: white;">{{ reviews.paginator.count }} Total Review{{ reviews.paginator.count|pluralize }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Reviews</option>
                                <option value="approved" {% if request.GET.status == "approved" %}selected{% endif %}>Approved</option>
                                <option value="not_approved" {% if request.GET.status == "not_approved" %}selected{% endif %}>Not Approved</option>
                                <option value="flagged" {% if request.GET.status == "flagged" %}selected{% endif %}>Flagged</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="rating" class="form-label">Rating</label>
                            <select name="rating" id="rating" class="form-select">
                                <option value="">All Ratings</option>
                                <option value="5" {% if request.GET.rating == "5" %}selected{% endif %}>5 Stars</option>
                                <option value="4" {% if request.GET.rating == "4" %}selected{% endif %}>4 Stars</option>
                                <option value="3" {% if request.GET.rating == "3" %}selected{% endif %}>3 Stars</option>
                                <option value="2" {% if request.GET.rating == "2" %}selected{% endif %}>2 Stars</option>
                                <option value="1" {% if request.GET.rating == "1" %}selected{% endif %}>1 Star</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="venue" class="form-label">Venue</label>
                            <select name="venue" id="venue" class="form-select">
                                <option value="">All Venues</option>
                                {% for venue in venues %}
                                    <option value="{{ venue.id }}" {% if request.GET.venue == venue.id|stringformat:"s" %}selected{% endif %}>
                                        {{ venue.venue_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search reviews..." value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="{{ request.GET.date_from }}">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if reviews %}
        <!-- Reviews List -->
        <div class="row">
            {% for review in reviews %}
            <div class="col-12 mb-4">
                <div class="card shadow-sm {% if review.is_flagged %}border-warning{% elif not review.is_approved %}border-danger{% endif %}">
                    <div class="card-body">
                        <div class="row">
                            <!-- Customer & Venue Info -->
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    {% if review.customer.customerprofile.profile_picture %}
                                        <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                             alt="Customer" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                                    {% else %}
                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 40px; height: 40px; background-color: white; border: 2px solid black;">
                                            <i class="fas fa-user" style="color: black; opacity: 0.6;"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <h6 class="mb-0">{{ review.customer.customerprofile.get_full_name|default:"Anonymous" }}</h6>
                                        <small class="text-muted">{{ review.customer.email }}</small>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <h6 class="mb-1">{{ review.venue.venue_name }}</h6>
                                    <small class="text-muted">{{ review.venue.service_provider.business_name }}</small>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ review.created_at|date:"M d, Y g:i A" }}
                                </small>
                            </div>
                            
                            <!-- Review Content -->
                            <div class="col-md-6">
                                <!-- Rating -->
                                <div class="d-flex align-items-center mb-2">
                                    <div class="text-warning me-2">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <span class="text-muted">{{ review.rating }} out of 5 stars</span>
                                </div>
                                
                                <!-- Review Text -->
                                <p class="mb-2">{{ review.written_review }}</p>
                                
                                <!-- Status Indicators -->
                                <div class="mb-2">
                                    {% if review.is_approved %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Approved
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Not Approved
                                        </span>
                                    {% endif %}
                                    
                                    {% if review.is_flagged %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-flag me-1"></i>Flagged ({{ review.pending_count }})
                                        </span>
                                    {% endif %}
                                    
                                    {% if review.response %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-reply me-1"></i>Provider Responded
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <!-- Provider Response (if any) -->
                                {% if review.response %}
                                <div class="card bg-light mt-2">
                                    <div class="card-body py-2">
                                        <h6 class="mb-1 small">Provider Response:</h6>
                                        <p class="mb-1 small">{{ review.response.response_text|truncatewords:20 }}</p>
                                        <small class="text-muted">{{ review.response.created_at|date:"M d, Y" }}</small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- Actions -->
                            <div class="col-md-3">
                                <div class="d-grid gap-2">
                                    <a href="{% url 'review_app:admin_moderate_review' review.slug %}"
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>Moderate
                                    </a>

                                    {% if review.is_flagged %}
                                        <a href="{% url 'review_app:admin_manage_flags' %}?review={{ review.id }}"
                                           class="btn btn-warning btn-sm">
                                            <i class="fas fa-flag me-1"></i>View Flags
                                        </a>
                                    {% endif %}

                                    {% if review.is_approved %}
                                        <form method="post" action="{% url 'review_app:admin_moderate_review' review.slug %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="disapprove">
                                            <button type="submit" class="btn btn-outline-danger btn-sm w-100"
                                                    onclick="return confirm('Are you sure you want to disapprove this review?')">
                                                <i class="fas fa-times me-1"></i>Disapprove
                                            </button>
                                        </form>
                                    {% else %}
                                        <form method="post" action="{% url 'review_app:admin_moderate_review' review.slug %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="approve">
                                            <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                                <i class="fas fa-check me-1"></i>Approve
                                            </button>
                                        </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if reviews.has_other_pages %}
        <nav aria-label="Reviews pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reviews.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-shield-alt fa-4x text-muted mb-3"></i>
                        <h4>No Reviews Found</h4>
                        <p class="text-muted mb-4">
                            {% if request.GET.status or request.GET.rating or request.GET.venue or request.GET.search or request.GET.date_from %}
                                No reviews match your current filters. Try adjusting your search criteria.
                            {% else %}
                                No reviews have been submitted yet.
                            {% endif %}
                        </p>
                        {% if request.GET.status or request.GET.rating or request.GET.venue or request.GET.search or request.GET.date_from %}
                            <a href="{% url 'review_app:admin_review_moderation' %}" class="btn btn-primary">
                                <i class="fas fa-refresh me-2"></i>Clear Filters
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
