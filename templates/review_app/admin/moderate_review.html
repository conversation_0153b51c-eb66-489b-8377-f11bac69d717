{% extends 'review_app/base_review.html' %}

{% block title %}Moderate Review - {{ review.venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:admin_review_moderation' %}">Review Moderation</a></li>
            <li class="breadcrumb-item active" aria-current="page">Moderate Review</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Review Details -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Review Moderation
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Customer & Venue Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-2">Customer Information</h6>
                            <div class="d-flex align-items-center mb-2">
                                {% if review.customer.customerprofile.profile_picture %}
                                    <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                         alt="Customer" class="rounded-circle me-3" style="width: 50px; height: 50px;">
                                {% else %}
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                    <small class="text-muted">{{ review.customer.email }}</small>
                                    <br>
                                    <small class="text-muted">Member since {{ review.customer.date_joined|date:"M Y" }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-2">Venue Information</h6>
                            <div class="d-flex align-items-center">
                                {% if review.venue.main_image %}
                                    <img src="{{ review.venue.main_image.url }}" alt="{{ review.venue.venue_name }}" 
                                         class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ review.venue.venue_name }}</h6>
                                    <small class="text-muted">{{ review.venue.service_provider.business_name }}</small>
                                    <br>
                                    <small class="text-muted">{{ review.venue.service_provider.user.email }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Review Content -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Review Content</h6>
                                <div class="text-warning">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="ms-2 text-muted">{{ review.rating }} out of 5 stars</span>
                                </div>
                            </div>
                            <p class="mb-3">{{ review.written_review }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Submitted on {{ review.created_at|date:"F d, Y g:i A" }}
                                    {% if review.updated_at != review.created_at %}
                                        <span class="ms-2">
                                            <i class="fas fa-edit me-1"></i>
                                            Updated {{ review.updated_at|date:"F d, Y g:i A" }}
                                        </span>
                                    {% endif %}
                                </small>
                                <div>
                                    {% if review.is_approved %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Approved
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Not Approved
                                        </span>
                                    {% endif %}
                                    
                                    {% if review.is_flagged %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-flag me-1"></i>Flagged
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Provider Response (if any) -->
                    {% if review.response %}
                    <div class="card bg-info bg-opacity-10 mb-4">
                        <div class="card-body">
                            <h6 class="mb-2">
                                <i class="fas fa-reply me-2"></i>Provider Response
                            </h6>
                            <p class="mb-2">{{ review.response.response_text }}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Responded on {{ review.response.created_at|date:"F d, Y g:i A" }}
                                {% if review.response.updated_at != review.response.created_at %}
                                    <span class="ms-2">
                                        <i class="fas fa-edit me-1"></i>
                                        Updated {{ review.response.updated_at|date:"F d, Y g:i A" }}
                                    </span>
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Moderation Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Review Status -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Review Status</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="is_approved" value="true" 
                                               id="approve" {% if review.is_approved %}checked{% endif %}>
                                        <label class="form-check-label text-success" for="approve">
                                            <i class="fas fa-check me-1"></i>Approve Review
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="is_approved" value="false" 
                                               id="disapprove" {% if not review.is_approved %}checked{% endif %}>
                                        <label class="form-check-label text-danger" for="disapprove">
                                            <i class="fas fa-times me-1"></i>Disapprove Review
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Notes -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="admin_notes">Admin Notes (Internal)</label>
                            <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                                      placeholder="Add internal notes about this moderation decision...">{{ review.admin_notes|default:"" }}</textarea>
                            <small class="form-text text-muted">These notes are for internal use only and will not be visible to customers or providers.</small>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:admin_review_moderation' %}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                            <div>
                                <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                                {% if review.is_approved %}
                                    <button type="submit" name="action" value="delete" class="btn btn-danger" 
                                            onclick="return confirm('Are you sure you want to delete this review? This action cannot be undone.')">
                                        <i class="fas fa-trash me-2"></i>Delete Review
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Flags (if any) -->
            {% if review.flags.all %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning bg-opacity-10">
                    <h6 class="mb-0">
                        <i class="fas fa-flag me-2"></i>Review Flags
                    </h6>
                </div>
                <div class="card-body">
                    {% for flag in review.flags.all %}
                    <div class="border-bottom pb-3 mb-3 {% if forloop.last %}border-0 pb-0 mb-0{% endif %}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">{{ flag.get_reason_display }}</h6>
                            <span class="badge bg-{% if flag.status == 'pending' %}warning{% elif flag.status == 'reviewed' %}info{% else %}success{% endif %}">
                                {{ flag.get_status_display }}
                            </span>
                        </div>
                        {% if flag.reason_text %}
                            <p class="mb-2 small">{{ flag.reason_text }}</p>
                        {% endif %}
                        <small class="text-muted">
                            Flagged by {{ flag.flagged_by.customerprofile.get_full_name|default:"Anonymous" }}
                            on {{ flag.created_at|date:"M d, Y" }}
                        </small>
                        {% if flag.reviewed_by %}
                            <br>
                            <small class="text-muted">
                                Reviewed by {{ flag.reviewed_by.get_full_name|default:flag.reviewed_by.email }}
                                on {{ flag.reviewed_at|date:"M d, Y" }}
                            </small>
                        {% endif %}
                        
                        {% if flag.status == 'pending' %}
                        <div class="mt-2">
                            <form method="post" action="{% url 'review_app:admin_resolve_flag' flag.id %}" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="approve">
                                <button type="submit" class="btn btn-sm btn-outline-success me-1">
                                    <i class="fas fa-check me-1"></i>Approve Flag
                                </button>
                            </form>
                            <form method="post" action="{% url 'review_app:admin_resolve_flag' flag.id %}" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="reject">
                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-times me-1"></i>Reject Flag
                                </button>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Review Statistics -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Review Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="fw-bold text-primary">{{ customer_review_count }}</div>
                            <small class="text-muted">Customer's Reviews</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-success">{{ venue_review_count }}</div>
                            <small class="text-muted">Venue's Reviews</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="fw-bold text-warning">{{ customer_avg_rating|floatformat:1 }}</div>
                            <small class="text-muted">Customer's Avg Rating</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-info">{{ venue_avg_rating|floatformat:1 }}</div>
                            <small class="text-muted">Venue's Avg Rating</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Moderation Guidelines -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Moderation Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="small fw-bold text-success">Approve if:</h6>
                    <ul class="small text-muted mb-3">
                        <li>Review is honest and factual</li>
                        <li>Language is appropriate</li>
                        <li>Content is relevant to the venue</li>
                        <li>No personal attacks or harassment</li>
                    </ul>
                    
                    <h6 class="small fw-bold text-danger">Disapprove if:</h6>
                    <ul class="small text-muted mb-0">
                        <li>Contains offensive language</li>
                        <li>Appears to be fake or spam</li>
                        <li>Includes personal information</li>
                        <li>Violates community guidelines</li>
                        <li>Is completely irrelevant</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
