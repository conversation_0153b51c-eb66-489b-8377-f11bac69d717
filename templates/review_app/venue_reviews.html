{% extends 'review_app/base_review.html' %}
{% load i18n %}

{% block title %}Reviews - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
/* CozyWish Review App - Black & White Professional Design */

/* CSS Variables for fonts */
:root {
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Base styling */
body {
    font-family: var(--font-primary);
    background-color: white;
    color: black;
}

/* Typography improvements */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    color: black;
    line-height: 1.3;
}

h1 { font-size: 2.5rem; margin-bottom: 1.5rem; }
h2 { font-size: 2rem; margin-bottom: 1.25rem; }
h3 { font-size: 1.75rem; margin-bottom: 1rem; }
h4 { font-size: 1.5rem; margin-bottom: 1rem; }
h5 { font-size: 1.25rem; margin-bottom: 0.75rem; }
h6 { font-size: 1.1rem; margin-bottom: 0.5rem; }

p, span, div {
    font-family: var(--font-primary);
    line-height: 1.6;
}

/* Card styling */
.card {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Review card specific styles */
.review-card {
    margin-bottom: 2rem;
}

.review-card .card-body {
    padding: 2rem;
}

/* Button styling */
.btn {
    font-family: var(--font-primary);
    font-weight: 500;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid black;
}

.btn-primary {
    background: black;
    color: white;
    border-color: black;
}

.btn-primary:hover {
    background: white;
    color: black;
    border-color: black;
}

.btn-outline-warning {
    background: white;
    color: #f39c12;
    border-color: #f39c12;
}

.btn-outline-warning:hover {
    background: #f39c12;
    color: white;
    border-color: #f39c12;
}

.btn-outline-secondary {
    background: white;
    color: black;
    border-color: black;
}

.btn-outline-secondary:hover {
    background: black;
    color: white;
    border-color: black;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.bg-info {
    background-color: #17a2b8 !important;
    color: white;
}

.bg-primary {
    background-color: black !important;
    color: white;
}

/* Rating stars */
.text-warning {
    color: #f39c12 !important;
}

/* Professional spacing */
.mb-4 { margin-bottom: 2rem !important; }
.mb-3 { margin-bottom: 1.5rem !important; }
.mb-2 { margin-bottom: 1rem !important; }
.mb-1 { margin-bottom: 0.5rem !important; }

/* Skeleton loader */
.skeleton-loader {
    background-color: #eee;
    border-radius: 1rem;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 50;
    background: black;
    color: white;
    border: 2px solid black;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top:hover {
    background: white;
    color: black;
    border-color: black;
}

/* Breadcrumb styling */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: black;
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: black;
    opacity: 0.7;
}

.breadcrumb-item.active {
    color: black;
    opacity: 0.6;
}

/* Form styling */
.form-select, .form-control {
    border: 2px solid black;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-family: var(--font-primary);
    background: white;
    color: black;
}

.form-select:focus, .form-control:focus {
    border-color: black;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    background: white;
    color: black;
}

/* Pagination styling */
.pagination .page-link {
    color: black;
    border: 2px solid black;
    background: white;
    margin: 0 0.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
}

.pagination .page-link:hover {
    background: black;
    color: white;
    border-color: black;
}

.pagination .page-item.active .page-link {
    background: black;
    color: white;
    border-color: black;
}

/* Accordion styling */
.accordion-button {
    background: white;
    color: black;
    border: 2px solid black;
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background: black;
    color: white;
    border-color: black;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
}

/* Professional review content styling */
.review-content {
    font-size: 1rem;
    line-height: 1.7;
    color: black;
    margin-bottom: 1.5rem;
}

.review-meta {
    font-size: 0.9rem;
    color: black;
    opacity: 0.8;
}

.customer-name {
    font-weight: 600;
    color: black;
    font-size: 1.1rem;
}

.review-date {
    font-size: 0.9rem;
    color: black;
    opacity: 0.7;
}

/* Provider response styling */
.provider-response {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.provider-response .card-body {
    padding: 0;
}

/* Rating display improvements */
.rating-display {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.rating-score {
    font-weight: 700;
    color: black;
    font-size: 2rem;
}

.review-count {
    color: black;
    opacity: 0.7;
    font-weight: 500;
}

/* Enhanced Status Indicators */
.review-status-indicators .badge {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.review-status-indicators .badge.bg-warning {
    color: #000 !important;
    background-color: #ffc107 !important;
}

.review-status-indicators .badge.bg-success {
    background-color: #28a745 !important;
}

.review-status-indicators .badge.bg-info {
    background-color: #17a2b8 !important;
}

.review-status-indicators .badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Review Metadata */
.review-metadata {
    padding: 0.75rem 0;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem !important;
}

.review-metadata small {
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Enhanced Review Cards */
.review-card {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.review-card:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
}

/* Helpful votes styling */
.review-helpfulness {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.helpfulness-btn {
    transition: all 0.2s ease;
}

.helpfulness-btn:hover {
    transform: translateY(-1px);
}

.helpfulness-btn.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.helpfulness-btn.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue.slug %}">{{ venue.venue_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Reviews</li>
        </ol>
    </nav>

    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if venue.main_image %}
                                <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" 
                                     class="img-fluid rounded">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 100px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-7">
                            <h2 class="mb-2">{{ venue.venue_name }}</h2>
                            <p class="text-muted mb-2">{{ venue.service_provider.business_name }}</p>
                            <p class="mb-0">{{ venue.short_description|truncatewords:25 }}</p>
                        </div>
                        <div class="col-md-3 text-center">
                            {% if can_review and not has_reviewed %}
                                <a href="{% url 'review_app:submit_review' venue.id %}" 
                                   class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-star me-2"></i>Write a Review
                                </a>
                            {% elif has_reviewed %}
                                <div class="alert alert-info mb-2">
                                    <i class="fas fa-check-circle me-2"></i>You've already reviewed this venue
                                </div>
                            {% endif %}
                            <a href="{% url 'venues_app:venue_detail' venue.slug %}" 
                               class="btn btn-outline-secondary w-100">
                                <i class="fas fa-arrow-left me-2"></i>Back to Venue
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Summary -->
    <div class="row mb-5">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center py-4">
                    <div class="rating-score text-warning mb-3">{{ average_rating|floatformat:1 }}</div>
                    <div class="text-warning mb-3" style="font-size: 1.5rem;">
                        {% for i in "12345" %}
                            {% if forloop.counter <= average_rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <p class="review-meta mb-0">Based on {{ total_reviews }} review{{ total_reviews|pluralize }}</p>
                    {% if total_reviews < 5 %}
                        <div class="mt-3">
                            <span class="badge bg-info">
                                <i class="fas fa-sparkles me-1"></i>New on CozyWish
                            </span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="mb-4">Rating Breakdown</h5>
                    <canvas id="ratingChart" height="160" aria-label="Rating distribution chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div id="reviews-skeleton">
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
    </div>

    {% if reviews %}
        <!-- Reviews List -->
        <div class="row align-items-end mb-3">
            <div class="col-md-8">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Customer Reviews
                    <span class="badge bg-primary ms-2">{{ total_reviews }}</span>
                </h4>
            </div>
            <div class="col-md-4 text-end">
                <form method="get" id="sortForm">
                    <div class="input-group">
                        <label class="input-group-text" for="sortSelect">Sort by</label>
                        <select name="sort" id="sortSelect" class="form-select" onchange="document.getElementById('sortForm').submit();">
                            <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest</option>
                            <option value="oldest" {% if current_sort == 'oldest' %}selected{% endif %}>Oldest</option>
                            <option value="rating_desc" {% if current_sort == 'rating_desc' %}selected{% endif %}>Highest Rating</option>
                            <option value="rating_asc" {% if current_sort == 'rating_asc' %}selected{% endif %}>Lowest Rating</option>
                        </select>
                    </div>
                    {% if reviews.number > 1 %}
                        <input type="hidden" name="page" value="{{ reviews.number }}">
                    {% endif %}
                </form>
            </div>
        </div>

        <div class="row">
            {% for review in reviews %}
            <div class="col-12 mb-4">
                <div class="card review-card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <!-- Customer Avatar -->
                            <div class="me-3">
                                {% if review.customer.customerprofile.profile_picture %}
                                    <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                         alt="Customer" class="rounded-circle" style="width: 60px; height: 60px;">
                                {% else %}
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Review Content -->
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h6 class="customer-name mb-2">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                        <div class="rating-display text-warning mb-1">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <span class="ms-2 text-muted small">{{ review.rating }} out of 5 stars</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="review-date mb-2">{{ review.created_at|date:"F d, Y" }}</div>
                                        
                                        <!-- Enhanced Status Indicators -->
                                        {% include 'review_app/components/review_status_badges.html' with review=review show_verified=True %}
                                        
                                        {% if user.is_authenticated and user != review.customer %}
                                            <a href="{% url 'review_app:flag_review' review.slug %}"
                                               class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-flag me-1"></i>Flag Review
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="review-content">{{ review.written_review }}</div>

                                <!-- Enhanced Review Metadata -->
                                <div class="review-metadata mt-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- Edit History -->
                                            {% if review.updated_at != review.created_at %}
                                                <small class="text-muted d-block mb-1">
                                                    <i class="fas fa-history me-1"></i>
                                                    Last updated: {{ review.updated_at|date:"M d, Y g:i A" }}
                                                </small>
                                            {% endif %}
                                            
                                            <!-- Review Age -->
                                            <small class="text-muted d-block">
                                                <i class="fas fa-calendar me-1"></i>
                                                Posted {{ review.created_at|timesince }} ago
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-6 text-end">
                                            <!-- Helpful Votes Summary -->
                                            {% with stats=review.get_helpfulness_stats %}
                                                {% if stats.total_votes > 0 %}
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-thumbs-up me-1"></i>
                                                        {{ stats.helpful_votes }} of {{ stats.total_votes }} found helpful
                                                        {% if stats.helpfulness_ratio > 0 %}
                                                            ({{ stats.helpfulness_ratio|floatformat:0 }}%)
                                                        {% endif %}
                                                    </small>
                                                {% endif %}
                                            {% endwith %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Review Helpfulness -->
                                {% if user.is_authenticated and user != review.customer and user.role == 'customer' %}
                                <div class="review-helpfulness mt-3">
                                    <div class="d-flex align-items-center">
                                        <span class="me-3 small text-muted">Was this review helpful?</span>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-success helpfulness-btn"
                                                    data-review-slug="{{ review.slug }}"
                                                    data-helpful="true">
                                                <i class="fas fa-thumbs-up me-1"></i>
                                                Yes (<span class="helpful-count">{{ review.get_helpfulness_stats.helpful_votes }}</span>)
                                            </button>
                                            <button type="button" class="btn btn-outline-danger helpfulness-btn"
                                                    data-review-slug="{{ review.slug }}"
                                                    data-helpful="false">
                                                <i class="fas fa-thumbs-down me-1"></i>
                                                No (<span class="not-helpful-count">{{ review.get_helpfulness_stats.not_helpful_votes }}</span>)
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% elif review.get_helpfulness_stats.total_votes > 0 %}
                                <div class="review-helpfulness mt-3">
                                    <div class="small text-muted">
                                        <i class="fas fa-thumbs-up me-1"></i>
                                        {{ review.get_helpfulness_stats.helpful_votes }} of {{ review.get_helpfulness_stats.total_votes }} found this helpful
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Provider Response -->
                                {% if review.response %}
                                <div class="provider-response mt-4">
                                    <div class="d-flex align-items-start">
                                        <div class="me-3">
                                            {% if venue.service_provider.business_logo %}
                                                <img src="{{ venue.service_provider.business_logo.url }}"
                                                     alt="Business" class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                                <div class="rounded d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: black; color: white;">
                                                    <i class="fas fa-store"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0 fw-bold">
                                                    <i class="fas fa-reply me-2"></i>Response from {{ venue.service_provider.business_name }}
                                                </h6>
                                                <small class="review-date">{{ review.response.created_at|date:"F d, Y" }}</small>
                                            </div>
                                            <div class="review-content">{{ review.response.response_text }}</div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if reviews.has_other_pages %}
        <nav aria-label="Reviews pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reviews.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.previous_page_number }}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.next_page_number }}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.paginator.num_pages }}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card text-center p-4">
                    <h5 class="mb-2">No Reviews Yet</h5>
                    <p class="text-muted mb-3">Be the first to share your experience!</p>
                    {% if can_review %}
                        <a href="{% url 'review_app:submit_review' venue.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-star me-1"></i>Write Review
                        </a>
                    {% else %}
                        <p class="text-muted small">You need to complete a booking to leave a review.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Review Guidelines -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="accordion" id="guidelinesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingGuide">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGuide" aria-expanded="false" aria-controls="collapseGuide">
                            <i class="fas fa-info-circle me-2"></i>Review Guidelines
                        </button>
                    </h2>
                    <div id="collapseGuide" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="small fw-bold text-success">What makes a great review:</h6>
                                    <ul class="small text-muted">
                                        <li>Share your honest experience</li>
                                        <li>Be specific about services received</li>
                                        <li>Mention staff and atmosphere</li>
                                        <li>Include helpful details for other customers</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="small fw-bold text-warning">Please avoid:</h6>
                                    <ul class="small text-muted">
                                        <li>Offensive or inappropriate language</li>
                                        <li>Personal information about staff</li>
                                        <li>Unrelated or promotional content</li>
                                        <li>Reviews about other venues</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<button id="backToTop" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</button>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{{ rating_distribution|json_script:"rating-data" }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('reviews-skeleton').style.display = 'none';
    var btn = document.getElementById('backToTop');
    window.addEventListener('scroll', function(){
        if (window.pageYOffset > 300) { btn.style.display = 'block'; } else { btn.style.display = 'none'; }
    });
    btn.addEventListener('click', function(){
        window.scrollTo({top:0, behavior:'smooth'});
    });
    const data = JSON.parse(document.getElementById('rating-data').textContent);
    const labels = Object.keys(data).map(r => r + '★').reverse();
    const counts = Object.keys(data).map(r => data[r]).reverse();
    new Chart(document.getElementById('ratingChart').getContext('2d'), {
        type: 'bar',
        data: {labels: labels, datasets:[{data: counts, backgroundColor:'#ffc107'}]},
        options: {scales:{y:{beginAtZero:true, ticks:{precision:0}}}}
    });

    // Review helpfulness voting
    document.querySelectorAll('.helpfulness-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reviewSlug = this.dataset.reviewSlug;
            const isHelpful = this.dataset.helpful === 'true';
            const buttonGroup = this.closest('.btn-group');

            // Disable buttons during request
            buttonGroup.querySelectorAll('.helpfulness-btn').forEach(btn => btn.disabled = true);

            fetch(`/reviews/review/${reviewSlug}/vote-helpfulness/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: `is_helpful=${isHelpful}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update vote counts
                    buttonGroup.querySelector('.helpful-count').textContent = data.helpful_votes;
                    buttonGroup.querySelector('.not-helpful-count').textContent = data.not_helpful_votes;

                    // Update button states
                    buttonGroup.querySelectorAll('.helpfulness-btn').forEach(btn => {
                        btn.classList.remove('btn-success', 'btn-danger');
                        if (btn.dataset.helpful === 'true') {
                            btn.classList.add('btn-outline-success');
                        } else {
                            btn.classList.add('btn-outline-danger');
                        }
                    });

                    // Highlight the selected button
                    if (isHelpful) {
                        this.classList.remove('btn-outline-success');
                        this.classList.add('btn-success');
                    } else {
                        this.classList.remove('btn-outline-danger');
                        this.classList.add('btn-danger');
                    }

                    showToast('Thank you for your feedback!', 'success');
                } else {
                    showToast('Unable to record your vote. Please try again.', 'error');
                }
            })
            .catch(error => {
                showToast('Unable to record your vote. Please try again.', 'error');
            })
            .finally(() => {
                // Re-enable buttons
                buttonGroup.querySelectorAll('.helpfulness-btn').forEach(btn => btn.disabled = false);
            });
        });
    });

    // Toast notification function
    function showToast(message, type) {
        const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
        const toastHtml = `
            <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // Add to toast container or create one
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = toastContainer.lastElementChild;
        const bsToast = new bootstrap.Toast(toastElement);
        bsToast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    }
});
</script>
{% endblock %}
