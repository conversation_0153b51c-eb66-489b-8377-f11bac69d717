{% extends 'review_app/base_review.html' %}

{% block title %}Edit Review - {{ review.venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<style>
.star-rating {
    font-size: 2rem;
    color: #ddd;
    cursor: pointer;
}
.star-rating.active {
    color: #ffc107;
}
.star-rating:hover {
    color: #ffc107;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    const stars = document.querySelectorAll('.star-rating');
    const ratingInput = document.getElementById('id_rating');
    
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingInput.value = rating;
            updateStars(rating);
        });

        star.addEventListener('mouseover', function() {
            const rating = index + 1;
            updateStars(rating);
        });

        star.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const rating = index + 1;
                ratingInput.value = rating;
                updateStars(rating);
            }
        });
    });
    
    // Reset stars on mouse leave
    document.querySelector('.star-container').addEventListener('mouseleave', function() {
        const currentRating = ratingInput.value || 0;
        updateStars(currentRating);
    });
    
    function updateStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
                star.setAttribute('aria-checked', 'true');
            } else {
                star.classList.remove('active');
                star.setAttribute('aria-checked', 'false');
            }
        });
    }
    
    // Get current rating and set stars
    const currentRating = {{ review.rating|default:0 }};
    updateStars(currentRating);
    updateRatingText(currentRating);
    
    // Character counter functionality
    const reviewTextarea = document.getElementById('id_written_review');
    const charCount = document.getElementById('char-count');
    
    if (reviewTextarea && charCount) {
        // Update character count on page load
        charCount.textContent = reviewTextarea.value.length;
        
        // Update character count as user types
        reviewTextarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            charCount.textContent = currentLength;
            
            // Change color based on character limit
            if (currentLength > 1000) {
                charCount.style.color = '#dc3545'; // Red
                charCount.parentElement.style.color = '#dc3545';
            } else if (currentLength > 900) {
                charCount.style.color = '#fd7e14'; // Orange
                charCount.parentElement.style.color = '#fd7e14';
            } else {
                charCount.style.color = '#6c757d'; // Default gray
                charCount.parentElement.style.color = '#6c757d';
            }
        });
    }
});
</script>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:customer_review_history' %}">My Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Review</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Your Review
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Venue Information -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div id="venueCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner rounded overflow-hidden ratio ratio-4x3">
                                    {% if review.venue.main_image %}
                                    <div class="carousel-item active">
                                        <img src="{{ review.venue.main_image.url }}" alt="{{ review.venue.venue_name }}" class="d-block w-100 h-100">
                                    </div>
                                    {% else %}
                                    <div class="carousel-item active">
                                        <div class="bg-light d-flex align-items-center justify-content-center h-100">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                    </div>
                                    {% endif %}
                                    {% for image in review.venue.images.all %}
                                    <div class="carousel-item{% if not review.venue.main_image and forloop.first %} active{% endif %}">
                                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:review.venue.venue_name }}" class="d-block w-100 h-100">
                                    </div>
                                    {% endfor %}
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Next</span>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4>{{ review.venue.venue_name }}</h4>
                            <p class="text-muted mb-2">{{ review.venue.service_provider.business_name }}</p>
                            <p class="mb-2">{{ review.venue.short_description|truncatewords:20 }}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Originally reviewed on {{ review.created_at|date:"F d, Y" }}
                            </small>
                        </div>
                    </div>

                    <!-- Review Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Time Remaining Alert -->
                        {% if time_remaining %}
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock me-2"></i>
                                <div>
                                    <strong>Time Remaining to Edit:</strong>
                                    {% if time_remaining.hours > 0 %}
                                        {{ time_remaining.hours }} hour{{ time_remaining.hours|pluralize }} and 
                                    {% endif %}
                                    {{ time_remaining.minutes }} minute{{ time_remaining.minutes|pluralize }}
                                    <br>
                                    <small class="text-muted">Edit deadline: {{ edit_deadline|date:"F d, Y g:i A" }}</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Star Rating -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">{{ form.rating.label }}</label>
                            <div class="star-container d-flex align-items-center mb-2" role="radiogroup" aria-label="Star rating">
                                {% for i in "12345" %}
                                    <i class="fas fa-star star-rating me-1" data-rating="{{ forloop.counter }}" role="radio" aria-label="{{ forloop.counter }} star{% if forloop.counter != 1 %}s{% endif %}" aria-checked="false" tabindex="0"></i>
                                {% endfor %}
                                <span class="ms-3 text-muted" id="rating-text">Click to rate</span>
                            </div>
                            {{ form.rating|add_class:"d-none" }}
                            {% if form.rating.errors %}
                                <div class="text-danger small">
                                    {% for error in form.rating.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.rating.help_text %}
                                <small class="form-text text-muted">{{ form.rating.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Written Review -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.written_review.id_for_label }}">
                                {{ form.written_review.label }}
                            </label>
                            {{ form.written_review|add_class:"form-control" }}
                            
                            <!-- Character Counter -->
                            <div class="d-flex justify-content-between mt-1">
                                <div>
                                    {% if form.written_review.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.written_review.errors %}
                                                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <span id="char-count">0</span>/1000 characters
                                </small>
                            </div>
                            
                            {% if form.written_review.help_text %}
                                <small class="form-text text-muted">{{ form.written_review.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:customer_review_history' %}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Review
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Response (if any) -->
            {% if review.response %}
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-reply me-2"></i>Provider Response
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">{{ review.response.response_text }}</p>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Responded on {{ review.response.created_at|date:"F d, Y" }}
                    </small>
                </div>
            </div>
            {% endif %}

            <!-- Review Guidelines -->
            <div class="accordion mt-4" id="guidelinesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingGuide">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGuide" aria-expanded="false" aria-controls="collapseGuide">
                            <i class="fas fa-info-circle me-2"></i>Review Guidelines
                        </button>
                    </h2>
                    <div id="collapseGuide" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <ul class="mb-0 small text-muted">
                                <li>Be honest and fair in your review</li>
                                <li>Focus on your personal experience</li>
                                <li>Keep your language professional and respectful</li>
                                <li>Avoid sharing personal information</li>
                                <li>Reviews help other customers make informed decisions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
