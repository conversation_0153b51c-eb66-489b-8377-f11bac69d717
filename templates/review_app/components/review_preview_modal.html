<!-- Review Preview Modal -->
<div class="modal fade" id="reviewPreviewModal" tabindex="-1" aria-labelledby="reviewPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewPreviewModalLabel">
                    <i class="fas fa-eye me-2"></i>Review Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Venue Info -->
                <div class="venue-preview-info mb-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            {% if venue.main_image %}
                                <img src="{{ venue.main_image.url }}" 
                                     alt="{{ venue.venue_name }}"
                                     class="venue-preview-image">
                            {% else %}
                                <div class="venue-preview-placeholder">
                                    <i class="fas fa-image"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col">
                            <h6 class="mb-1 venue-preview-name">{{ venue.venue_name }}</h6>
                            <p class="text-muted mb-0 venue-preview-provider">{{ venue.service_provider.business_name }}</p>
                        </div>
                    </div>
                </div>

                <!-- Preview Content -->
                <div class="review-preview-content">
                    <!-- Rating Preview -->
                    <div class="rating-preview mb-3">
                        <label class="fw-bold mb-2">Your Rating:</label>
                        <div class="d-flex align-items-center gap-2">
                            <div class="preview-stars text-warning" id="previewStars">
                                <!-- Stars will be populated by JavaScript -->
                            </div>
                            <span class="preview-rating-text" id="previewRatingText"></span>
                        </div>
                    </div>

                    <!-- Written Review Preview -->
                    <div class="written-review-preview mb-3">
                        <label class="fw-bold mb-2">Your Review:</label>
                        <div class="preview-review-text" id="previewReviewText">
                            <!-- Review text will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Review Meta Info -->
                    <div class="review-meta-preview">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Will be published on <span id="previewDate"></span>
                        </small>
                    </div>
                </div>

                <!-- Preview Guidelines -->
                <div class="preview-guidelines mt-4">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-1"></i>Before You Publish
                        </h6>
                        <ul class="mb-0 small">
                            <li>Make sure your rating reflects your experience</li>
                            <li>Keep your review honest and helpful for other customers</li>
                            <li>You can edit your review within 24 hours after publishing</li>
                            <li>Reviews are moderated and may take time to appear publicly</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-edit me-1"></i>Continue Editing
                </button>
                <button type="button" class="btn btn-primary" id="confirmPublishBtn">
                    <i class="fas fa-paper-plane me-1"></i>Publish Review
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.venue-preview-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 0.5rem;
    border: 2px solid #dee2e6;
}

.venue-preview-placeholder {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.preview-stars {
    font-size: 1.25rem;
}

.preview-review-text {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    min-height: 60px;
    font-style: italic;
    color: #495057;
}

.preview-review-text.empty {
    color: #6c757d;
    font-style: normal;
}

.preview-guidelines .alert {
    margin-bottom: 0;
    border: none;
    background: #f8f9fa;
    border-left: 4px solid #17a2b8;
}
</style>

<script>
// Review Preview Modal Functions
function showReviewPreview() {
    const rating = document.querySelector('input[name="rating"]:checked')?.value || 
                  document.getElementById('id_rating')?.value;
    const reviewText = document.getElementById('id_written_review')?.value || '';
    
    // Update preview stars
    updatePreviewStars(rating);
    
    // Update preview text
    updatePreviewText(reviewText);
    
    // Update date
    document.getElementById('previewDate').textContent = new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('reviewPreviewModal'));
    modal.show();
}

function updatePreviewStars(rating) {
    const starsContainer = document.getElementById('previewStars');
    const ratingText = document.getElementById('previewRatingText');
    
    if (!rating || rating === '0') {
        starsContainer.innerHTML = '<span class="text-muted">No rating selected</span>';
        ratingText.textContent = '';
        return;
    }
    
    const ratingLabels = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
    let starsHtml = '';
    
    for (let i = 1; i <= 5; i++) {
        if (i <= parseInt(rating)) {
            starsHtml += '<i class="fas fa-star"></i>';
        } else {
            starsHtml += '<i class="far fa-star"></i>';
        }
    }
    
    starsContainer.innerHTML = starsHtml;
    ratingText.textContent = `${rating}/5 - ${ratingLabels[parseInt(rating)]}`;
}

function updatePreviewText(text) {
    const textContainer = document.getElementById('previewReviewText');
    
    if (!text || text.trim() === '') {
        textContainer.innerHTML = 'No written review provided';
        textContainer.classList.add('empty');
    } else {
        textContainer.innerHTML = `"${text}"`;
        textContainer.classList.remove('empty');
    }
}

// Confirm publish button handler
document.getElementById('confirmPublishBtn')?.addEventListener('click', function() {
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('reviewPreviewModal'));
    modal.hide();
    
    // Submit the actual form
    document.getElementById('reviewForm')?.submit();
});
</script> 