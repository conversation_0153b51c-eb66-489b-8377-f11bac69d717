<!-- Review Status Badges Component -->
<!-- Usage: {% include 'review_app/components/review_status_badges.html' with review=review show_verified=True %} -->

<div class="review-status-badges d-flex flex-wrap gap-1">
    <!-- Flagged Status -->
    {% if review.is_flagged %}
        <span class="badge badge-status bg-warning text-dark flagged" title="This review has been flagged and is under moderation">
            <i class="fas fa-flag me-1"></i>Under Review
        </span>
    {% endif %}
    
    <!-- Approval Status -->
    {% if not review.is_approved %}
        <span class="badge badge-status bg-secondary" title="This review is pending approval from moderators">
            <i class="fas fa-clock me-1"></i>Pending Approval
        </span>
    {% elif show_verified|default:True %}
        <span class="badge badge-status bg-success" title="This review has been verified and approved">
            <i class="fas fa-check me-1"></i>Verified
        </span>
    {% endif %}
    
    <!-- Edit Status -->
    {% if review.updated_at != review.created_at %}
        <span class="badge badge-status bg-info" title="Last edited {{ review.updated_at|date:'F d, Y g:i A' }}">
            <i class="fas fa-edit me-1"></i>Edited
        </span>
    {% endif %}
    
    <!-- New Review Badge (within 7 days) -->
    {% if review.is_recent %}
        <span class="badge badge-status bg-primary" title="This is a recent review">
            <i class="fas fa-sparkles me-1"></i>New
        </span>
    {% endif %}
    
    <!-- High Rating Badge -->
    {% if review.rating >= 4 %}
        <span class="badge badge-status bg-warning text-dark" title="High rating review">
            <i class="fas fa-star me-1"></i>{{ review.rating }}/5
        </span>
    {% endif %}
    
    <!-- Helpful Review Badge (if many helpful votes) -->
    {% with stats=review.get_helpfulness_stats %}
        {% if stats.total_votes >= 5 and stats.helpfulness_ratio >= 80 %}
            <span class="badge badge-status bg-info" title="{{ stats.helpful_votes }} of {{ stats.total_votes }} found this helpful">
                <i class="fas fa-thumbs-up me-1"></i>Helpful
            </span>
        {% endif %}
    {% endwith %}
    
    <!-- Edit time remaining indicator (for review owner) -->
    {% if review.can_be_edited and review.edit_time_remaining_hours > 0 %}
        <span class="badge badge-status bg-light text-dark" title="You can edit this review for {{ review.edit_time_remaining_hours }} more hours">
            <i class="fas fa-clock me-1"></i>{{ review.edit_time_remaining_hours }}h left to edit
        </span>
    {% endif %}
</div>

<style>
.review-status-badges {
    margin-bottom: 0.5rem;
}

.badge-status {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
}

.badge-status:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.badge-status.bg-warning {
    color: #000 !important;
    background-color: #ffc107 !important;
    border: 1px solid #ffca2c;
}

.badge-status.bg-success {
    background-color: #28a745 !important;
    border: 1px solid #34ce57;
}

.badge-status.bg-info {
    background-color: #17a2b8 !important;
    border: 1px solid #20c997;
}

.badge-status.bg-secondary {
    background-color: #6c757d !important;
    border: 1px solid #7a838b;
}

.badge-status.bg-primary {
    background-color: #007bff !important;
    border: 1px solid #0056b3;
}

.badge-status.bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6;
    color: #495057 !important;
}

/* Pulse animation for important statuses */
.badge-status.flagged {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Shimmer effect for new reviews */
.badge-status.bg-primary {
    position: relative;
    overflow: hidden;
}

.badge-status.bg-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
</style> 