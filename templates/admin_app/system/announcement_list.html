{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Announcements - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Announcements</h1>
    <a href="{% url 'admin_app:announcement_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Create New Announcement
    </a>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" name="search" class="form-control" placeholder="Search announcements..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <select name="type" class="form-select">
                    <option value="">All Types</option>
                    <option value="info" {% if request.GET.type == 'info' %}selected{% endif %}>Information</option>
                    <option value="success" {% if request.GET.type == 'success' %}selected{% endif %}>Success</option>
                    <option value="warning" {% if request.GET.type == 'warning' %}selected{% endif %}>Warning</option>
                    <option value="danger" {% if request.GET.type == 'danger' %}selected{% endif %}>Danger</option>
                    <option value="promotion" {% if request.GET.type == 'promotion' %}selected{% endif %}>Promotion</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="active" class="form-select">
                    <option value="">All Status</option>
                    <option value="true" {% if request.GET.active == 'true' %}selected{% endif %}>Active</option>
                    <option value="false" {% if request.GET.active == 'false' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-12">
                <button type="submit" class="btn btn-outline-primary">Filter</button>
                <a href="{% url 'admin_app:announcement_list' %}" class="btn btn-outline-secondary">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Announcements List -->
<div class="card">
    <div class="card-body">
        {% if announcements %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for announcement in announcements %}
                        <tr>
                            <td>
                                <strong>{{ announcement.title }}</strong><br>
                                <small class="text-muted">{{ announcement.content|truncatechars:60 }}</small>
                            </td>
                            <td>
                                {% if announcement.announcement_type == 'info' %}
                                    <span class="badge bg-info">Information</span>
                                {% elif announcement.announcement_type == 'success' %}
                                    <span class="badge bg-success">Success</span>
                                {% elif announcement.announcement_type == 'warning' %}
                                    <span class="badge bg-warning">Warning</span>
                                {% elif announcement.announcement_type == 'danger' %}
                                    <span class="badge bg-danger">Danger</span>
                                {% elif announcement.announcement_type == 'promotion' %}
                                    <span class="badge bg-primary">Promotion</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ announcement.get_display_location_display }}</small>
                            </td>
                            <td>
                                {% if announcement.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ announcement.priority }}</span>
                            </td>
                            <td>
                                <small>{{ announcement.created_at|date:"M d, Y H:i" }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="editAnnouncement({{ announcement.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteAnnouncement({{ announcement.id }}, '{{ announcement.title }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No announcements found</h5>
                <p class="text-muted">Create your first announcement to get started.</p>
                <a href="{% url 'admin_app:announcement_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Announcement
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
function editAnnouncement(id) {
    // Implement edit functionality
    window.location.href = `/admin-panel/system/announcements/${id}/edit/`;
}

function deleteAnnouncement(id, title) {
    if (confirm(`Are you sure you want to delete the announcement "${title}"?`)) {
        // Implement delete functionality
        window.location.href = `/admin-panel/system/announcements/${id}/delete/`;
    }
}
</script>
{% endblock %}
