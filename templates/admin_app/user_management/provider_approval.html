{% extends 'admin_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Provider Approval - {{ provider.business_name }} - CozyWish Admin{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin_app:pending_providers' %}">Pending Providers</a></li>
        <li class="breadcrumb-item active">{{ provider.business_name }}</li>
    </ol>
</nav>
{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Provider Approval Review</h1>
    <a href="{% url 'admin_app:pending_providers' %}" class="btn btn-outline-secondary" title="Back" data-bs-toggle="tooltip">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Pending
    </a>
</div>

<!-- Provider Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow border-left-warning">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        {% if provider.business_logo %}
                            <img src="{{ provider.business_logo.url }}" 
                                 alt="Business Logo" class="rounded-circle" 
                                 style="width: 120px; height: 120px; object-fit: cover;">
                        {% else %}
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 120px; height: 120px;">
                                <i class="fas fa-building fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <h2 class="mb-2">{{ provider.business_name }}</h2>
                        <p class="text-muted mb-2">{{ provider.user.email }}</p>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Contact:</strong></div>
                            <div class="col-sm-9">{{ provider.contact_first_name }} {{ provider.contact_last_name }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Applied:</strong></div>
                            <div class="col-sm-9">{{ provider.user.date_joined|date:"F d, Y g:i A" }} ({{ provider.user.date_joined|timesince }} ago)</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-3"><strong>Status:</strong></div>
                            <div class="col-sm-9">
                                {% if provider.is_approved %}
                                    <span class="badge bg-success fs-6">Approved</span>
                                {% else %}
                                    <span class="badge bg-warning fs-6">Pending Approval</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        {% if not provider.is_approved %}
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                                <i class="fas fa-check me-1"></i>
                                Approve
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times me-1"></i>
                                Reject
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Information -->
<div class="row">
    <!-- Business Information -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Business Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Business Name:</strong></div>
                    <div class="col-sm-8">{{ provider.business_name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Business Type:</strong></div>
                    <div class="col-sm-8">{{ provider.get_business_type_display|default:"Not specified" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Phone:</strong></div>
                    <div class="col-sm-8">{{ provider.business_phone|default:"Not provided" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">{{ provider.business_email|default:provider.user.email }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Website:</strong></div>
                    <div class="col-sm-8">
                        {% if provider.business_website %}
                            <a href="{{ provider.business_website }}" target="_blank">{{ provider.business_website }}</a>
                        {% else %}
                            Not provided
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Address:</strong></div>
                    <div class="col-sm-8">{{ provider.business_address|default:"Not provided" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>City:</strong></div>
                    <div class="col-sm-8">{{ provider.business_city|default:"Not provided" }}</div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>ZIP Code:</strong></div>
                    <div class="col-sm-8">{{ provider.business_zip_code|default:"Not provided" }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Contact Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>First Name:</strong></div>
                    <div class="col-sm-8">{{ provider.contact_first_name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Last Name:</strong></div>
                    <div class="col-sm-8">{{ provider.contact_last_name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Position:</strong></div>
                    <div class="col-sm-8">{{ provider.contact_position|default:"Not provided" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Phone:</strong></div>
                    <div class="col-sm-8">{{ provider.contact_phone|default:"Not provided" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">{{ provider.user.email }}</div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>Account Created:</strong></div>
                    <div class="col-sm-8">{{ provider.user.date_joined|date:"F d, Y g:i A" }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Description -->
{% if provider.business_description %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Business Description</h5>
            </div>
            <div class="card-body">
                <p>{{ provider.business_description|linebreaks }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Documents and Verification -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-check me-2"></i>Documents & Verification</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Business License</h6>
                        {% if provider.business_license %}
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                Business license uploaded
                                <br>
                                <a href="{{ provider.business_license.url }}" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-download me-1"></i>
                                    View Document
                                </a>
                            </div>
                        {% else %}
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No business license uploaded
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>Insurance Certificate</h6>
                        {% if provider.insurance_certificate %}
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                Insurance certificate uploaded
                                <br>
                                <a href="{{ provider.insurance_certificate.url }}" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-download me-1"></i>
                                    View Document
                                </a>
                            </div>
                        {% else %}
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No insurance certificate uploaded
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Notes -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Admin Notes</h5>
            </div>
            <div class="card-body">
                <form method="post" action="#">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="add_note">
                    <div class="mb-3">
                        <textarea class="form-control" name="admin_note" rows="3" 
                                  placeholder="Add internal notes about this provider application..."></textarea>
                    </div>
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        Add Note
                    </button>
                </form>
                
                {% if admin_notes %}
                <hr>
                <div class="mt-3">
                    {% for note in admin_notes %}
                    <div class="border-start border-primary ps-3 mb-3">
                        <p class="mb-1">{{ note.content }}</p>
                        <small class="text-muted">
                            by {{ note.created_by.email }} - {{ note.created_at|timesince }} ago
                        </small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Approval/Rejection Modals -->
<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">Approve Provider Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="approve">
                <div class="modal-body">
                    <p>Are you sure you want to approve <strong>{{ provider.business_name }}</strong> as a service provider?</p>
                    
                    <div class="mb-3">
                        <label for="approval_notes" class="form-label">Approval Notes (Optional)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3" 
                                  placeholder="Add any notes about the approval..."></textarea>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        The provider will be notified via email and can start creating venues and services.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success spinner-button" title="Approve" data-bs-toggle="tooltip">
                        <i class="fas fa-check me-1"></i>
                        Approve Provider
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">Reject Provider Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="reject">
                <div class="modal-body">
                    <p>Are you sure you want to reject <strong>{{ provider.business_name }}</strong>'s provider application?</p>
                    
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">
                            Reason for Rejection <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4" 
                                  placeholder="Please provide a detailed reason for rejection..." required></textarea>
                        <small class="form-text text-muted">
                            This reason will be sent to the provider via email.
                        </small>
                    </div>
                    
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        The provider will be notified via email with the rejection reason. They can reapply after addressing the issues.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger spinner-button" title="Reject" data-bs-toggle="tooltip">
                        <i class="fas fa-times me-1"></i>
                        Reject Application
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation for rejection
    const rejectForm = document.querySelector('#rejectModal form');
    const rejectionReason = document.getElementById('rejection_reason');
    
    rejectForm.addEventListener('submit', function(e) {
        if (!rejectionReason.value.trim()) {
            e.preventDefault();
            rejectionReason.classList.add('is-invalid');
            rejectionReason.focus();
            return false;
        }
        
        rejectionReason.classList.remove('is-invalid');
        return true;
    });
    
    rejectionReason.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('is-invalid');
        }
    });
});
</script>
{% endblock %}
