{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Admin Dashboard - CozyWish{% endblock %}

{% block admin_extra_css %}
<style>
    /* Admin dashboard specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    /* Statistics cards */
    .stats-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card h5 {
        font-family: var(--font-heading);
        font-weight: 700;
        font-size: 2rem;
        color: black;
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 0;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.5px;
    }

    .stats-card i {
        color: black;
        margin-bottom: 1rem;
    }

    /* Chart containers */
    .chart-container {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1rem;
    }

    /* List group styling */
    .admin-wrapper .list-group-item {
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: white;
        color: black;
    }

    .admin-wrapper .list-group-item:first-child {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .admin-wrapper .list-group-flush .list-group-item {
        border-left: 0;
        border-right: 0;
    }

    /* Card header styling */
    .admin-wrapper .card-header {
        background-color: white !important;
        color: black;
        border-bottom: 2px solid black;
        font-family: var(--font-heading);
        font-weight: 600;
    }

    .admin-wrapper .card-header h6 {
        color: black;
        margin-bottom: 0;
    }

    /* Quick actions grid */
    .quick-actions .btn {
        margin-bottom: 0.5rem;
        text-align: left;
        justify-content: flex-start;
    }

    /* System health indicators */
    .system-health-good {
        text-align: center;
        padding: 2rem;
    }

    .system-health-good i {
        color: black;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block admin_content %}
{% include 'payments_app/includes/dashboard_skeleton.html' %}
<div id="dashboard-content" style="display:none;">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="section-title">Admin Dashboard</h1>
    <div class="text-muted">
        <i class="fas fa-calendar me-1"></i>
        {{ current_date|date:"F d, Y" }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-users fa-2x"></i>
            <h5>{{ total_users|default:0 }}</h5>
            <p>Total Users</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-building fa-2x"></i>
            <h5>{{ total_providers|default:0 }}</h5>
            <p>Total Providers</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-calendar-check fa-2x"></i>
            <h5>{{ total_bookings|default:0 }}</h5>
            <p>Total Bookings</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-clock fa-2x"></i>
            <h5>{{ pending_providers|default:0 }}</h5>
            <p>Pending Approvals</p>
        </div>
    </div>
</div>

<!-- Recent Activity and Quick Actions -->
<div class="row">
    <!-- Recent Activity -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    <div class="list-group list-group-flush">
                        {% for activity in recent_activities %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ activity.description }}</div>
                                <small class="text-muted">
                                    by {{ activity.user.email }} - {{ activity.timestamp|timesince }} ago
                                </small>
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ activity.action_type }}</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>No recent activity to display</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header py-3">
                <h6>Quick Actions</h6>
            </div>
            <div class="card-body quick-actions">
                <div class="d-grid gap-2">
                    <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        Manage Users
                    </a>
                    <a href="{% url 'admin_app:pending_providers' %}" class="btn btn-outline-primary">
                        <i class="fas fa-clock me-2"></i>
                        Review Providers
                    </a>
                    <a href="{% url 'admin_app:static_page_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create Page
                    </a>
                    <a href="{% url 'admin_app:blog_post_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-blog me-2"></i>
                        New Blog Post
                    </a>
                    <a href="{% url 'admin_app:announcement_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-bullhorn me-2"></i>
                        Add Announcement
                    </a>
                    <a href="{% url 'admin_app:analytics_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        View Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="card mt-4">
            <div class="card-header py-3">
                <h6>System Health</h6>
            </div>
            <div class="card-body">
                {% if system_health_issues %}
                    {% for issue in system_health_issues %}
                    <div class="alert alert-warning" role="alert">
                        <strong>{{ issue.get_severity_display }}:</strong> {{ issue.title }}
                        <br><small>{{ issue.recorded_at|timesince }} ago</small>
                    </div>
                    {% endfor %}
                    <a href="{% url 'admin_app:system_health_logs' %}" class="btn btn-sm btn-danger">
                        View All Issues
                    </a>
                {% else %}
                    <div class="system-health-good">
                        <i class="fas fa-check-circle fa-2x"></i>
                        <p class="mb-0">All systems operational</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Growth Charts -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header py-3">
                <h6>User Growth (Last 30 Days)</h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header py-3">
                <h6>Booking Trends (Last 30 Days)</h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="bookingTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block admin_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: {{ user_growth_labels|safe }},
            datasets: [{
                label: 'New Users',
                data: {{ user_growth_data|safe }},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Booking Trends Chart
    const bookingTrendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
    new Chart(bookingTrendsCtx, {
        type: 'bar',
        data: {
            labels: {{ booking_trends_labels|safe }},
            datasets: [{
                label: 'Bookings',
                data: {{ booking_trends_data|safe }},
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
<script src="{% static 'js/dashboard.js' %}"></script>
{% endblock %}
