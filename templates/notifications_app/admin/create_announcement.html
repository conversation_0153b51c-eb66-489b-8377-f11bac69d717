{% extends 'base.html' %}

{% block title %}Create Announcement - CozyWish Admin{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Admin Create Announcement */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    body {
        font-family: var(--cw-font-primary);
        background: var(--cw-accent-light);
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    /* Custom Breadcrumb */
    .breadcrumb {
        background: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
        border: 1px solid #e9ecef;
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid #e9ecef;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
    }

    .card-cw-header {
        background: var(--cw-brand-primary);
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }

    .card-cw-body {
        padding: 2rem;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Custom Forms */
    .form-control-cw {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-label-cw {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    /* Custom Alerts */
    .alert-cw-success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #166534;
        border-radius: 0.5rem;
    }

    .alert-cw-warning {
        background: #fffbeb;
        border: 1px solid #fed7aa;
        color: #92400e;
        border-radius: 0.5rem;
    }

    .alert-cw-error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
    }

    /* Preview Section */
    .preview-card {
        background: var(--cw-accent-light);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
    }

    .preview-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    /* Help Section */
    .help-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .help-header {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 1rem 1.5rem;
        border-bottom: none;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .help-body {
        padding: 1.5rem;
    }

    /* Page Header */
    .page-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid #e9ecef;
    }

    .page-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Create Announcement</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header text-center">
                <h1 class="page-title">
                    <i class="fas fa-bullhorn me-3"></i>Create New Announcement
                </h1>
                <p class="page-subtitle">Send important updates and announcements to your users</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card-cw">
                <div class="card-cw-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Announcement Details
                    </h5>
                </div>
                <div class="card-cw-body">
                    <!-- Display messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert-cw-{{ message.tags }} alert mb-4" role="alert">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" id="announcementForm">
                        {% csrf_token %}

                        <!-- Title Field -->
                        <div class="mb-4">
                            <label for="{{ form.title.id_for_label }}" class="form-label-cw">
                                <i class="fas fa-heading me-2"></i>Announcement Title
                            </label>
                            <input type="text" name="{{ form.title.name }}" id="{{ form.title.id_for_label }}"
                                   class="form-control form-control-cw"
                                   placeholder="Enter a clear and descriptive title..."
                                   value="{{ form.title.value|default:'' }}" required>
                            {% if form.title.errors %}
                                <div class="text-danger mt-2">
                                    {% for error in form.title.errors %}
                                        <small><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text mt-2" style="color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle me-1"></i>Enter a clear and descriptive title for your announcement.
                            </div>
                        </div>

                        <!-- Message Field -->
                        <div class="mb-4">
                            <label for="{{ form.announcement_text.id_for_label }}" class="form-label-cw">
                                <i class="fas fa-align-left me-2"></i>Announcement Message
                            </label>
                            <textarea name="{{ form.announcement_text.name }}" id="{{ form.announcement_text.id_for_label }}"
                                      class="form-control form-control-cw" rows="6"
                                      placeholder="Write the full announcement message that users will receive..."
                                      required>{{ form.announcement_text.value|default:'' }}</textarea>
                            {% if form.announcement_text.errors %}
                                <div class="text-danger mt-2">
                                    {% for error in form.announcement_text.errors %}
                                        <small><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text mt-2" style="color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle me-1"></i>Write the full announcement message that users will receive.
                            </div>
                        </div>

                        <!-- Target Audience Field -->
                        <div class="mb-4">
                            <label for="{{ form.target_audience.id_for_label }}" class="form-label-cw">
                                <i class="fas fa-users me-2"></i>Target Audience
                            </label>
                            <select name="{{ form.target_audience.name }}" id="{{ form.target_audience.id_for_label }}"
                                    class="form-select form-control-cw" required>
                                <option value="">Select target audience...</option>
                                {% for choice in form.target_audience.field.choices %}
                                    <option value="{{ choice.0 }}" {% if form.target_audience.value == choice.0 %}selected{% endif %}>
                                        {{ choice.1 }}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if form.target_audience.errors %}
                                <div class="text-danger mt-2">
                                    {% for error in form.target_audience.errors %}
                                        <small><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text mt-2" style="color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle me-1"></i>Select who should receive this announcement.
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="preview-card mb-4">
                            <div class="preview-title">
                                <i class="fas fa-eye me-2"></i>Live Preview
                            </div>
                            <div id="preview-content">
                                <h6 id="preview-title" style="color: var(--cw-brand-primary); margin-bottom: 0.75rem;">
                                    Title will appear here...
                                </h6>
                                <p id="preview-message" style="color: var(--cw-neutral-700); margin-bottom: 0.75rem;">
                                    Message will appear here...
                                </p>
                                <small id="preview-audience" style="color: var(--cw-neutral-600);">
                                    <i class="fas fa-users me-1"></i>Target audience: Not selected
                                </small>
                            </div>
                        </div>

                        <!-- Warning Alert -->
                        <div class="alert-cw-warning alert mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Once sent, announcements cannot be edited or recalled.
                            Please review your message carefully before sending.
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-between">
                            <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-cw-primary" id="sendButton">
                                <i class="fas fa-paper-plane me-2"></i>Send Announcement
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="help-card mt-4">
                <div class="help-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Announcement Guidelines
                    </h6>
                </div>
                <div class="help-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: var(--cw-brand-primary); margin-bottom: 1rem;">
                                <i class="fas fa-lightbulb me-2"></i>Best Practices
                            </h6>
                            <ul style="color: var(--cw-neutral-700); line-height: 1.6;">
                                <li>Keep titles concise and descriptive</li>
                                <li>Use clear, professional language</li>
                                <li>Include relevant dates and deadlines</li>
                                <li>Test with a small group first if possible</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: var(--cw-brand-primary); margin-bottom: 1rem;">
                                <i class="fas fa-users me-2"></i>Target Audiences
                            </h6>
                            <ul style="color: var(--cw-neutral-700); line-height: 1.6;">
                                <li><strong>All Users:</strong> Everyone on the platform</li>
                                <li><strong>Customers:</strong> Only customer accounts</li>
                                <li><strong>Service Providers:</strong> Only business accounts</li>
                                <li><strong>Admins:</strong> Only admin accounts</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
    const messageInput = document.getElementById('{{ form.announcement_text.id_for_label }}');
    const audienceSelect = document.getElementById('{{ form.target_audience.id_for_label }}');

    const previewTitle = document.getElementById('preview-title');
    const previewMessage = document.getElementById('preview-message');
    const previewAudience = document.getElementById('preview-audience');

    // Update preview in real-time
    function updatePreview() {
        const titleValue = titleInput.value.trim();
        const messageValue = messageInput.value.trim();

        previewTitle.textContent = titleValue || 'Title will appear here...';
        previewMessage.textContent = messageValue || 'Message will appear here...';

        const selectedOption = audienceSelect.options[audienceSelect.selectedIndex];
        const audienceText = selectedOption && selectedOption.value ? selectedOption.text : 'Not selected';
        previewAudience.innerHTML = '<i class="fas fa-users me-1"></i>Target audience: ' + audienceText;

        // Update preview styling based on content
        if (titleValue) {
            previewTitle.style.color = 'var(--cw-brand-primary)';
            previewTitle.style.fontWeight = '600';
        } else {
            previewTitle.style.color = 'var(--cw-neutral-600)';
            previewTitle.style.fontWeight = '400';
        }

        if (messageValue) {
            previewMessage.style.color = 'var(--cw-neutral-700)';
        } else {
            previewMessage.style.color = 'var(--cw-neutral-600)';
        }
    }

    // Add event listeners
    if (titleInput) titleInput.addEventListener('input', updatePreview);
    if (messageInput) messageInput.addEventListener('input', updatePreview);
    if (audienceSelect) audienceSelect.addEventListener('change', updatePreview);

    // Form validation with professional styling
    const form = document.getElementById('announcementForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const titleValue = titleInput.value.trim();
            const messageValue = messageInput.value.trim();
            const audienceValue = audienceSelect.value;

            if (!titleValue || !messageValue || !audienceValue) {
                e.preventDefault();

                // Create professional alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert-cw-error alert mb-4';
                alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Error:</strong> Please fill in all required fields before sending the announcement.';

                // Insert alert at the top of the form
                const firstChild = form.firstElementChild;
                form.insertBefore(alertDiv, firstChild);

                // Auto-remove alert after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);

                return false;
            }

            // Professional confirmation dialog
            const audience = audienceSelect.options[audienceSelect.selectedIndex].text;
            const confirmMessage = `Are you sure you want to send this announcement to ${audience}?\n\nThis action cannot be undone and the announcement will be delivered immediately.`;

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
    }

    // Auto-hide success messages after 4 seconds
    const successAlerts = document.querySelectorAll('.alert-cw-success');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.transition = 'opacity 0.3s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 4000);
    });

    // Initialize preview on page load
    updatePreview();
});
</script>
{% endblock %}
