{% extends 'base.html' %}

{% block title %}All Notifications - CozyWish Admin{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Admin Notification List */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    body {
        font-family: var(--cw-font-primary);
        background: var(--cw-accent-light);
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    /* Custom Breadcrumb */
    .breadcrumb {
        background: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
        border: 1px solid #e9ecef;
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    /* Page Header */
    .page-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid #e9ecef;
    }

    .page-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 2rem;
        margin-bottom: 0;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid #e9ecef;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
    }

    .card-cw-header {
        background: var(--cw-brand-primary);
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }

    .card-cw-body {
        padding: 2rem;
    }

    /* Custom Forms */
    .form-control-cw {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    /* Custom Table */
    .table-cw {
        background: white;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: var(--cw-shadow-sm);
    }

    .table-cw thead {
        background: var(--cw-brand-primary);
        color: white;
    }

    .table-cw thead th {
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem;
    }

    .table-cw tbody tr {
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.2s ease;
    }

    .table-cw tbody tr:hover {
        background-color: var(--cw-accent-light);
    }

    .table-cw tbody td {
        padding: 1rem;
        vertical-align: middle;
        border: none;
    }

    /* Custom Badges */
    .badge-cw {
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
    }

    .badge-cw-success {
        background: #059669;
        color: white;
    }

    .badge-cw-warning {
        background: #d97706;
        color: white;
    }

    .badge-cw-outline {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    /* Custom Alerts */
    .alert-cw-success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #166534;
        border-radius: 0.5rem;
    }

    /* Pagination */
    .pagination-cw .page-link {
        color: var(--cw-brand-primary);
        border: 1px solid #e9ecef;
        padding: 0.75rem 1rem;
        margin: 0 0.125rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .pagination-cw .page-link:hover {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .pagination-cw .page-item.active .page-link {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--cw-neutral-600);
    }

    .empty-state i {
        color: var(--cw-brand-accent);
        margin-bottom: 1.5rem;
    }

    .empty-state h4 {
        color: var(--cw-neutral-700);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">All Notifications</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start align-items-lg-center gap-3">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-list me-3"></i>All Notifications
                        </h1>
                        <p class="text-muted mb-0">Manage and monitor all system notifications</p>
                    </div>
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn btn-cw-primary">
                            <i class="fas fa-bullhorn me-2"></i>Create Announcement
                        </a>
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-cw-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Display messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-cw-{{ message.tags }} alert mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-cw-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Filter & Search Notifications
                    </h5>
                </div>
                <div class="card-cw-body">
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-search me-1"></i>Search
                                </label>
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control form-control-cw"
                                           placeholder="Search notifications..."
                                           value="{{ search_query|default:'' }}">
                                    <button class="btn btn-cw-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-tag me-1"></i>Type
                                </label>
                                <select name="notification_type" class="form-select form-control-cw">
                                    <option value="">All Types</option>
                                    <option value="booking" {% if request.GET.notification_type == 'booking' %}selected{% endif %}>Booking</option>
                                    <option value="payment" {% if request.GET.notification_type == 'payment' %}selected{% endif %}>Payment</option>
                                    <option value="review" {% if request.GET.notification_type == 'review' %}selected{% endif %}>Review</option>
                                    <option value="announcement" {% if request.GET.notification_type == 'announcement' %}selected{% endif %}>Announcement</option>
                                    <option value="system" {% if request.GET.notification_type == 'system' %}selected{% endif %}>System</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-eye me-1"></i>Status
                                </label>
                                <select name="read_status" class="form-select form-control-cw">
                                    <option value="">All Status</option>
                                    <option value="unread" {% if request.GET.read_status == 'unread' %}selected{% endif %}>Unread Only</option>
                                    <option value="read" {% if request.GET.read_status == 'read' %}selected{% endif %}>Read Only</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-calendar me-1"></i>From Date
                                </label>
                                <input type="date" name="date_from" class="form-control form-control-cw"
                                       value="{{ request.GET.date_from|default:'' }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-calendar me-1"></i>To Date
                                </label>
                                <input type="date" name="date_to" class="form-control form-control-cw"
                                       value="{{ request.GET.date_to|default:'' }}">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label fw-bold" style="color: var(--cw-brand-primary);">
                                    <i class="fas fa-redo me-1"></i>Reset
                                </label>
                                <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-cw-secondary w-100">
                                    <i class="fas fa-redo"></i>
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if notifications %}
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <span style="color: var(--cw-neutral-600); font-weight: 500;">
                                <i class="fas fa-info-circle me-1"></i>
                                Showing {{ notifications.start_index }}-{{ notifications.end_index }} of {{ total_notifications }} notifications
                            </span>
                        </div>

                        <!-- Notifications Table -->
                        <div class="table-responsive">
                            <table class="table table-cw">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-user me-1"></i>User</th>
                                        <th><i class="fas fa-tag me-1"></i>Type</th>
                                        <th><i class="fas fa-envelope me-1"></i>Title</th>
                                        <th><i class="fas fa-eye me-1"></i>Status</th>
                                        <th><i class="fas fa-calendar me-1"></i>Created</th>
                                        <th><i class="fas fa-cog me-1"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for notification in notifications %}
                                        <tr {% if notification.read_status == 'unread' %}style="border-left: 4px solid var(--cw-brand-primary); background-color: var(--cw-accent-light);"{% endif %}>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                             style="width: 40px; height: 40px; background: var(--cw-brand-accent); color: var(--cw-brand-primary);">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold" style="color: var(--cw-brand-primary);">
                                                            {{ notification.user.get_full_name|default:notification.user.email }}
                                                        </div>
                                                        <small style="color: var(--cw-neutral-600);">{{ notification.user.email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge-cw badge-cw-outline">{{ notification.get_notification_type_display }}</span>
                                            </td>
                                            <td>
                                                <div>
                                                    {% if notification.read_status == 'unread' %}
                                                        <span class="badge-cw badge-cw-primary me-2">NEW</span>
                                                    {% endif %}
                                                    <span style="color: var(--cw-brand-primary); font-weight: 600;">
                                                        {{ notification.title|truncatechars:50 }}
                                                    </span>
                                                </div>
                                                <small style="color: var(--cw-neutral-600);">{{ notification.message|truncatechars:80 }}</small>
                                            </td>
                                            <td>
                                                {% if notification.read_status == 'read' %}
                                                    <span class="badge-cw badge-cw-success">
                                                        <i class="fas fa-check me-1"></i>Read
                                                    </span>
                                                    {% if notification.read_at %}
                                                        <br><small style="color: var(--cw-neutral-600);">{{ notification.read_at|date:"M d, g:i a" }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge-cw badge-cw-warning">
                                                        <i class="fas fa-exclamation-circle me-1"></i>Unread
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div style="color: var(--cw-brand-primary); font-weight: 600;">{{ notification.created_at|date:"M d, Y" }}</div>
                                                <small style="color: var(--cw-neutral-600);">{{ notification.created_at|date:"g:i a" }}</small>
                                            </td>
                                            <td>
                                                <a href="{% url 'notifications_app:admin_notification_detail' notification.id %}"
                                                   class="btn btn-cw-primary btn-sm" title="View Details">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if notifications.has_other_pages %}
                            <nav aria-label="Notification pagination" class="mt-4">
                                <ul class="pagination pagination-cw justify-content-center">
                                    {% if notifications.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" title="First Page">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" title="Previous Page">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for i in notifications.paginator.page_range %}
                                        {% if notifications.number == i %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ i }}</span>
                                            </li>
                                        {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if notifications.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" title="Next Page">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" title="Last Page">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}

                    {% else %}
                        <!-- Empty State -->
                        <div class="empty-state">
                            <div class="mb-4">
                                <i class="fas fa-bell-slash fa-4x"></i>
                            </div>
                            <h4>No Notifications Found</h4>
                            <p>No notifications match your current filters. Try adjusting your search criteria.</p>
                            <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-cw-primary">
                                <i class="fas fa-redo me-2"></i>Clear All Filters
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Auto-submit filter form when select changes
    const filterSelects = document.querySelectorAll('select[name="notification_type"], select[name="read_status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Add loading state
            const submitBtn = this.form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                submitBtn.disabled = true;
            }
            this.form.submit();
        });
    });

    // Auto-submit form when date inputs change
    const dateInputs = document.querySelectorAll('input[name="date_from"], input[name="date_to"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add small delay to allow user to select both dates
            setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    });

    // Auto-hide success messages after 4 seconds
    const successAlerts = document.querySelectorAll('.alert-cw-success');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.transition = 'opacity 0.3s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 4000);
    });

    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('.table-cw tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(2px)';
            this.style.transition = 'transform 0.2s ease';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Add loading state to view buttons
    const viewButtons = document.querySelectorAll('a[href*="notification_detail"]');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
        });
    });

    // Search form enhancement
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const value = this.value.trim();

            // Auto-submit after user stops typing for 1 second
            if (value.length >= 3) {
                searchTimeout = setTimeout(() => {
                    this.form.submit();
                }, 1000);
            }
        });
    }
});
</script>
{% endblock %}
