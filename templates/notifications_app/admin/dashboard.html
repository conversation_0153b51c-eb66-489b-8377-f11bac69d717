{% extends 'base.html' %}

{% block title %}Notification Management Dashboard - CozyWish{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Admin Dashboard - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-accent-light);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Dashboard Container */
    .cw-dashboard-container {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* Breadcrumb Styling */
    .breadcrumb {
        background: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
        margin-bottom: 1.5rem;
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-item.active {
        color: var(--cw-neutral-600);
    }

    /* Header Section */
    .cw-dashboard-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 2rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .cw-dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Statistics Cards */
    .cw-stat-card {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        height: 100%;
    }

    .cw-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .cw-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .cw-stat-icon.primary { background: var(--cw-gradient-brand-button); color: white; }
    .cw-stat-icon.warning { background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); color: white; }
    .cw-stat-icon.info { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; }
    .cw-stat-icon.success { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }

    .cw-stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .cw-stat-label {
        color: var(--cw-neutral-600);
        font-weight: 500;
        margin: 0;
    }

    /* Additional Dashboard Styles */
    .cw-notification-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .cw-notification-item-admin {
        padding: 1rem;
        border-bottom: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .cw-notification-item-admin:hover {
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
    }

    .cw-notification-item-admin:last-child {
        border-bottom: none;
    }

    .cw-notification-type-badge {
        background: var(--cw-brand-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .cw-announcement-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .cw-announcement-item {
        padding: 1rem;
        border-bottom: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .cw-announcement-item:hover {
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
    }

    .cw-announcement-item:last-child {
        border-bottom: none;
    }

    .cw-status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .cw-status-badge.success {
        background: #dcfce7;
        color: #166534;
    }

    .cw-status-badge.warning {
        background: #fef3c7;
        color: #92400e;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .cw-dashboard-container {
            padding: 1rem 0;
        }

        .cw-dashboard-header {
            padding: 1.5rem;
        }

        .cw-dashboard-title {
            font-size: 1.5rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .cw-stat-card {
            padding: 1rem;
        }

        .cw-stat-number {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="cw-dashboard-container">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_app:admin_dashboard' %}">Admin Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Notification Dashboard</li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="cw-dashboard-header">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h1 class="cw-dashboard-title">Notification Management</h1>
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn-cw-primary">
                        <i class="fas fa-bullhorn"></i>
                        <span>Create Announcement</span>
                    </a>
                    <a href="{% url 'dashboard_app:admin_dashboard' %}" class="btn-cw-secondary">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Admin Dashboard</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert"
                     style="background: var(--cw-accent-light); border: 1px solid var(--cw-brand-accent); color: var(--cw-brand-primary); border-radius: 0.75rem;">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- CozyWish Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="cw-stat-card">
                    <div class="text-center">
                        <div class="cw-stat-icon primary">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h5 class="cw-stat-number">{{ total_notifications }}</h5>
                        <p class="cw-stat-label">Total Notifications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="cw-stat-card">
                    <div class="text-center">
                        <div class="cw-stat-icon warning">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5 class="cw-stat-number">{{ unread_notifications }}</h5>
                        <p class="cw-stat-label">Unread Notifications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="cw-stat-card">
                    <div class="text-center">
                        <div class="cw-stat-icon info">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h5 class="cw-stat-number">{{ pending_announcements }}</h5>
                        <p class="cw-stat-label">Pending Announcements</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="cw-stat-card">
                    <div class="text-center">
                        <div class="cw-stat-icon success">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="cw-stat-number">{{ notification_types|length }}</h5>
                        <p class="cw-stat-label">Notification Types</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- CozyWish Recent Notifications -->
            <div class="col-lg-8 mb-4">
                <div class="cw-stat-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">
                            <i class="fas fa-bell me-2"></i>Recent Notifications
                        </h5>
                        <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn-cw-primary btn-sm">
                            View All
                        </a>
                    </div>
                    {% if recent_notifications %}
                        <div class="cw-notification-list">
                            {% for notification in recent_notifications %}
                                <div class="cw-notification-item-admin">
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading);">
                                                <span class="cw-notification-type-badge">{{ notification.get_notification_type_display }}</span>
                                                {{ notification.title }}
                                            </h6>
                                            <p class="mb-1" style="color: var(--cw-neutral-600);">{{ notification.message|truncatechars:100 }}</p>
                                            <small style="color: var(--cw-neutral-600);">
                                                To: {{ notification.user.email }} •
                                                {{ notification.created_at|date:"M d, Y, g:i a" }}
                                            </small>
                                        </div>
                                        <div class="ms-3">
                                            <a href="{% url 'notifications_app:admin_notification_detail' notification.id %}"
                                               class="btn-cw-secondary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-3x mb-3" style="color: var(--cw-brand-primary); opacity: 0.5;"></i>
                            <p style="color: var(--cw-neutral-600);">No recent notifications</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- CozyWish Notification Types & Recent Announcements -->
            <div class="col-lg-4">
                <!-- Notification Types Distribution -->
                <div class="cw-stat-card mb-4">
                    <h5 class="mb-3" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">
                        <i class="fas fa-chart-pie me-2"></i>Notification Types
                    </h5>
                    {% if notification_types %}
                        {% for type_name, count in notification_types.items %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2"
                                 style="background: var(--cw-accent-light); border-radius: 0.5rem; border: 1px solid var(--cw-brand-accent);">
                                <span style="color: var(--cw-brand-primary); font-weight: 500;">{{ type_name }}</span>
                                <span class="cw-notification-type-badge">{{ count }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p style="color: var(--cw-neutral-600); text-align: center;">No notification data available</p>
                    {% endif %}
                </div>

                <!-- Recent Announcements -->
                <div class="cw-stat-card">
                    <h5 class="mb-3" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">
                        <i class="fas fa-bullhorn me-2"></i>Recent Announcements
                    </h5>
                    {% if recent_announcements %}
                        <div class="cw-announcement-list">
                            {% for announcement in recent_announcements %}
                                <div class="cw-announcement-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <a href="{{ announcement.get_absolute_url }}"
                                               style="color: var(--cw-brand-primary); text-decoration: none; font-weight: 600;">
                                                {{ announcement.title }}
                                            </a>
                                        </h6>
                                        <small style="color: var(--cw-neutral-600);">{{ announcement.created_at|date:"M d" }}</small>
                                    </div>
                                    <p class="mb-1 small" style="color: var(--cw-neutral-600);">{{ announcement.announcement_text|truncatechars:80 }}</p>
                                    <small style="color: var(--cw-neutral-600);">
                                        <span class="cw-status-badge {{ announcement.status|yesno:'success,warning' }}">
                                            {{ announcement.get_status_display }}
                                        </span>
                                        • {{ announcement.get_target_audience_display }}
                                        {% if announcement.total_recipients %}
                                            • {{ announcement.total_recipients }} recipients
                                        {% endif %}
                                    </small>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-bullhorn fa-2x mb-2" style="color: var(--cw-brand-primary); opacity: 0.5;"></i>
                            <p style="color: var(--cw-neutral-600); font-size: 0.9rem;">No recent announcements</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- CozyWish Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="cw-stat-card">
                    <h5 class="mb-3" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn-cw-primary w-100">
                                <i class="fas fa-bullhorn"></i>Create Announcement
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn-cw-secondary w-100">
                                <i class="fas fa-list"></i>View All Notifications
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_notification_list' %}?read_status=unread" class="btn-cw-secondary w-100">
                                <i class="fas fa-envelope"></i>View Unread
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'dashboard_app:admin_dashboard' %}" class="btn-cw-secondary w-100">
                                <i class="fas fa-tachometer-alt"></i>Admin Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
