{% load static %}

<!-- CozyWish Professional Notification Dropdown for Navigation Bar -->
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationDropdown" role="button"
       data-bs-toggle="dropdown" aria-expanded="false" title="Notifications">
        <i class="fas fa-bell"></i>
        {% if unread_notifications_count > 0 %}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill cw-notification-badge"
                  id="navNotificationBadge">
                {{ unread_notifications_count }}
                <span class="visually-hidden">unread notifications</span>
            </span>
        {% endif %}
    </a>

    <div class="dropdown-menu dropdown-menu-end cw-notification-dropdown"
         aria-labelledby="notificationDropdown">

        <!-- CozyWish Professional Header -->
        <div class="cw-notification-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="cw-notification-icon-header">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h6 class="mb-0 cw-header-title">Notifications</h6>
                        <small class="cw-header-subtitle">Stay updated with your activity</small>
                    </div>
                </div>
                {% if unread_notifications_count > 0 %}
                    <span class="cw-unread-badge" id="dropdownUnreadBadge">
                        {{ unread_notifications_count }}
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- CozyWish Quick Actions -->
        {% if unread_notifications_count > 0 %}
        <div class="cw-notification-actions">
            <form method="post" action="{% url 'notifications_app:mark_all_notifications_read' %}"
                  class="d-inline cw-mark-all-form">
                {% csrf_token %}
                <button type="submit" class="cw-quick-action-btn">
                    <i class="fas fa-check-double"></i>
                    Mark all read
                </button>
            </form>
        </div>
        {% endif %}

        <!-- CozyWish Notification List -->
        <div class="cw-notification-body">
            {% if recent_notifications %}
                {% for notification in recent_notifications %}
                    <a href="{% if notification.action_url %}{{ notification.action_url }}{% else %}{% url 'notifications_app:notification_detail' notification.id %}{% endif %}"
                       class="cw-notification-item {{ notification.read_status }}">
                        <div class="cw-notification-icon">
                            {% if notification.notification_type == 'review' %}
                                <div class="cw-icon cw-review-icon">
                                    <i class="fas fa-star"></i>
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        <div class="cw-response-indicator">
                                            <i class="fas fa-reply"></i>
                                        </div>
                                    {% endif %}
                                </div>
                            {% elif notification.notification_type == 'booking' %}
                                <div class="cw-icon cw-booking-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            {% elif notification.notification_type == 'payment' %}
                                <div class="cw-icon cw-payment-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                            {% else %}
                                <div class="cw-icon cw-system-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="cw-notification-details">
                            <div class="cw-notification-title">
                                {% if notification.read_status == 'unread' %}
                                    <span class="cw-unread-indicator"></span>
                                {% endif %}

                                {% if notification.notification_type == 'review' %}
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        💬 Provider Response
                                    {% else %}
                                        ⭐ New Review
                                    {% endif %}
                                {% else %}
                                    {{ notification.title|truncatechars:35 }}
                                {% endif %}
                            </div>
                            <div class="cw-notification-message">
                                {% if notification.notification_type == 'review' %}
                                    {% if notification.metadata.venue_name %}
                                        <span class="cw-venue-highlight">{{ notification.metadata.venue_name }}</span>
                                    {% endif %}
                                    {% if notification.metadata.review_rating %}
                                        <span class="cw-rating-preview">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= notification.metadata.review_rating %}★{% else %}☆{% endif %}
                                            {% endfor %}
                                        </span>
                                    {% endif %}
                                    <div class="cw-message-preview">{{ notification.message|truncatechars:60 }}</div>
                                {% else %}
                                    {{ notification.message|truncatechars:70 }}
                                {% endif %}
                            </div>
                            <div class="cw-notification-meta">
                                <span class="cw-notification-badge {{ notification.notification_type }}">
                                    {% if notification.notification_type == 'review' %}
                                        {% if notification.related_object_type == 'ReviewResponse' %}
                                            Response
                                        {% else %}
                                            Review
                                        {% endif %}
                                    {% else %}
                                        {{ notification.get_notification_type_display }}
                                    {% endif %}
                                </span>
                                <span class="cw-notification-time">
                                    {{ notification.created_at|timesince }} ago
                                </span>
                            </div>
                        </div>
                    </a>
                {% endfor %}
            {% else %}
                <div class="cw-empty-notifications">
                    <div class="cw-empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <div class="cw-empty-text">
                        <h6>No notifications</h6>
                        <p>You're all caught up!</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- CozyWish Professional Footer -->
        <div class="cw-notification-footer">
            <a href="{% url 'notifications_app:notification_list' %}" class="cw-view-all-btn">
                <i class="fas fa-list me-1"></i>View All Notifications
            </a>
        </div>
    </div>
</li>

<style>
/* CozyWish Notification Dropdown - Professional Design System */

/* CSS Custom Properties */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
}

/* CozyWish Notification Dropdown Styles */
.cw-notification-dropdown {
    width: 380px;
    max-height: 500px;
    border: none;
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-lg);
    padding: 0;
    overflow: hidden;
    font-family: var(--cw-font-primary);
}

.cw-notification-badge {
    background: var(--cw-brand-primary) !important;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.cw-notification-header {
    background: var(--cw-gradient-brand-button);
    color: white;
    padding: 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.cw-notification-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="notification-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23notification-pattern)"/></svg>') repeat;
    opacity: 0.3;
    z-index: 1;
}

.cw-notification-header > * {
    position: relative;
    z-index: 2;
}

.cw-notification-icon-header {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
    box-shadow: var(--cw-shadow-sm);
}

.cw-header-title {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: white;
}

.cw-header-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.cw-unread-badge {
    background: white;
    color: var(--cw-brand-primary);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    box-shadow: var(--cw-shadow-sm);
}

.cw-notification-actions {
    padding: 12px 20px;
    background: var(--cw-accent-light);
    border-bottom: 1px solid var(--cw-brand-accent);
}

.cw-quick-action-btn {
    background: none;
    border: none;
    color: var(--cw-neutral-600);
    font-size: 12px;
    cursor: pointer;
    transition: color 0.2s ease;
    font-family: var(--cw-font-primary);
    font-weight: 500;
}

.cw-quick-action-btn:hover {
    color: var(--cw-brand-primary);
}

.cw-notification-body {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px 0;
}

.cw-notification-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 20px;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid var(--cw-brand-accent);
    transition: all 0.2s ease;
    position: relative;
}

.cw-notification-item:hover {
    background-color: var(--cw-accent-light);
    text-decoration: none;
    color: inherit;
    transform: translateX(2px);
}

.cw-notification-item.unread {
    background-color: var(--cw-accent-light);
    border-left: 3px solid var(--cw-brand-primary);
}

.cw-notification-icon {
    margin-right: 12px;
    flex-shrink: 0;
}

.cw-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    position: relative;
    box-shadow: var(--cw-shadow-sm);
}

.cw-icon.cw-review-icon {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
}

.cw-icon.cw-booking-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.cw-icon.cw-payment-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.cw-icon.cw-system-icon {
    background: var(--cw-gradient-brand-button);
    color: white;
}

.cw-response-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
    border: 2px solid white;
    box-shadow: var(--cw-shadow-sm);
}

.cw-notification-details {
    flex: 1;
    min-width: 0;
}

.cw-notification-title {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    font-size: 14px;
    color: var(--cw-brand-primary);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cw-unread-indicator {
    width: 8px;
    height: 8px;
    background: var(--cw-brand-primary);
    border-radius: 50%;
    flex-shrink: 0;
}

.cw-venue-highlight {
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 12px;
}

.cw-rating-preview {
    color: #fbbf24;
    font-size: 11px;
    margin-left: 4px;
}

.cw-message-preview {
    color: var(--cw-neutral-600);
    font-size: 12px;
    margin-top: 2px;
}

.cw-notification-message {
    font-size: 12px;
    color: var(--cw-neutral-600);
    line-height: 1.4;
    margin-bottom: 4px;
}

.cw-notification-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
}

.cw-notification-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.cw-notification-badge.review {
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
}

.cw-notification-badge.booking {
    background: #dcfce7;
    color: #166534;
}

.cw-notification-badge.payment {
    background: #dbeafe;
    color: #1e40af;
}

.cw-notification-badge.system {
    background: var(--cw-accent-dark);
    color: var(--cw-brand-primary);
}

.cw-notification-time {
    font-size: 10px;
    color: var(--cw-neutral-600);
}

.cw-empty-notifications {
    text-align: center;
    padding: 40px 20px;
    color: var(--cw-neutral-600);
}

.cw-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    color: var(--cw-brand-primary);
}

.cw-empty-text h6 {
    margin-bottom: 8px;
    color: var(--cw-brand-primary);
    font-family: var(--cw-font-heading);
    font-weight: 600;
}

.cw-empty-text p {
    font-size: 14px;
    margin: 0;
    color: var(--cw-neutral-600);
}

.cw-notification-footer {
    padding: 12px 20px;
    background: var(--cw-accent-light);
    border-top: 1px solid var(--cw-brand-accent);
    text-align: center;
}

.cw-view-all-btn {
    color: var(--cw-brand-primary);
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    font-family: var(--cw-font-heading);
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.cw-view-all-btn:hover {
    color: white;
    background: var(--cw-brand-primary);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--cw-shadow-sm);
}

/* CozyWish Scrollbar styling */
.cw-notification-body::-webkit-scrollbar {
    width: 4px;
}

.cw-notification-body::-webkit-scrollbar-track {
    background: var(--cw-accent-light);
}

.cw-notification-body::-webkit-scrollbar-thumb {
    background: var(--cw-brand-accent);
    border-radius: 4px;
}

.cw-notification-body::-webkit-scrollbar-thumb:hover {
    background: var(--cw-accent-dark);
}

/* CozyWish Mobile responsive */
@media (max-width: 576px) {
    .cw-notification-dropdown {
        width: 320px;
        margin-right: -20px;
    }

    .cw-notification-item {
        padding: 10px 16px;
    }

    .cw-notification-header {
        padding: 16px;
    }

    .cw-icon {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .cw-notification-title {
        font-size: 13px;
    }

    .cw-notification-message {
        font-size: 11px;
    }
}
</style>
