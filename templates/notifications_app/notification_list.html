{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}Notifications - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* CozyWish Notification List - Professional Design */

    /* Page Header */
    .notifications-header {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .notifications-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-secondary-950);
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .notifications-icon {
        background: var(--cw-gradient-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 1.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .notifications-subtitle {
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* Stats Cards */
    .notification-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .stat-number {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        font-size: 2rem;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .filter-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-secondary-950);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
    }

    .filter-controls {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .filter-controls .form-control,
    .filter-controls .form-select {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        transition: all 0.2s ease;
    }

    .filter-controls .form-control:focus,
    .filter-controls .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .quick-actions-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-secondary-950);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    /* Professional Notification Cards */
    .notification-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        box-shadow: var(--cw-shadow-sm);
    }

    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .notification-card.unread {
        border-left: 4px solid var(--cw-brand-primary);
        background: var(--cw-gradient-card-subtle);
    }

    .notification-content {
        padding: 1.5rem;
        position: relative;
    }

    .notification-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .notification-icon {
        background: var(--cw-gradient-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: var(--cw-brand-primary);
        flex-shrink: 0;
        box-shadow: var(--cw-shadow-sm);
    }

    .notification-meta {
        flex: 1;
        min-width: 0;
    }

    .notification-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-secondary-950);
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .notification-message {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        line-height: 1.5;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .notification-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid var(--cw-neutral-200);
        margin-top: 1rem;
    }

    .notification-time {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-500);
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
    }

    .notification-actions {
        display: flex;
        gap: 0.5rem;
    }

    /* Category Badges */
    .category-badge {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-brand-primary);
        border-radius: 9999px;
        padding: 0.25rem 0.75rem;
        font-size: 0.625rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.125rem;
        transition: all 0.15s ease;
    }

    .category-badge.booking {
        border-color: #28a745;
        color: #28a745;
        background: #f8fff9;
    }
    .category-badge.payment {
        border-color: #007bff;
        color: #007bff;
        background: #f8fbff;
    }
    .category-badge.review {
        border-color: #ffc107;
        color: #856404;
        background: #fffdf8;
    }
    .category-badge.announcement {
        border-color: #dc3545;
        color: #dc3545;
        background: #fff8f8;
    }
    .category-badge.system {
        border-color: #6c757d;
        color: #6c757d;
        background: #f8f9fa;
    }

    /* Status Indicators */
    .status-indicator {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.1875rem;
    }

    .status-indicator.unread { background: #dc3545; }
    .status-indicator.read { background: #6c757d; }

    /* Enhanced Loading States */
    .notification-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 1rem;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Compact Responsive Design */
    @media (max-width: 768px) {
        .notification-stats {
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-controls {
            grid-template-columns: 1fr;
            gap: 0.375rem;
        }

        .filter-section {
            padding: 0.5rem;
        }

        .filter-title {
            font-size: 0.8125rem;
            margin-bottom: 0.375rem;
        }

        .action-buttons {
            justify-content: center;
            flex-direction: column;
            align-items: stretch;
        }

        .notification-header {
            flex-direction: row;
            align-items: center;
            gap: 0.5rem;
        }

        .notification-footer {
            flex-direction: column;
            gap: 0.5rem;
            align-items: stretch;
        }

        .notification-actions {
            justify-content: center;
        }

        .notification-content {
            padding: 0.5rem;
        }

        .stat-card {
            padding: 0.5rem 0.75rem;
        }

        .stat-number {
            font-size: 1rem;
        }

        .stat-label {
            font-size: 0.625rem;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{% if user.role == 'customer' %}{% url 'dashboard_app:customer_dashboard' %}{% elif user.role == 'service_provider' %}{% url 'dashboard_app:provider_dashboard' %}{% else %}{% url 'dashboard_app:admin_dashboard' %}{% endif %}">
                <i class="fas fa-home me-2"></i>Dashboard
            </a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-bell me-2"></i>Notifications
        </li>
    </ol>
</nav>

<!-- Page Header -->
<div class="notifications-header">
    <div class="notifications-title" id="notifications-main-heading">
        <div class="notifications-icon">
            <i class="fas fa-bell"></i>
        </div>
        Notifications
    </div>
    <p class="notifications-subtitle">
        Manage your notifications and stay updated with important information
    </p>
</div>

<!-- Notification Statistics -->
<div class="notification-stats" role="region" aria-labelledby="stats-heading">
    <h2 id="stats-heading" class="visually-hidden">Notification Statistics</h2>
    <div class="stat-card" role="status" aria-label="Unread notifications count">
        <div class="stat-number" id="unreadCountStat" aria-live="polite">{{ unread_count }}</div>
        <div class="stat-label">Unread</div>
    </div>
    <div class="stat-card" role="status" aria-label="Total notifications count">
        <div class="stat-number" aria-live="polite">{{ total_notifications }}</div>
        <div class="stat-label">Total</div>
    </div>
    <div class="stat-card" role="status" aria-label="Currently showing notifications count">
        <div class="stat-number" aria-live="polite">{{ notifications|length }}</div>
        <div class="stat-label">Showing</div>
    </div>
</div>

<!-- System Messages -->
{% if messages %}
<div class="messages mb-2">
    {% for message in messages %}
    <div class="alert alert-info alert-dismissible fade show" role="alert" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.5rem; padding: 0.75rem;">
        <i class="fas fa-info-circle me-2"></i>{{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Advanced Filter Section -->
<div class="filter-section" role="search" aria-labelledby="filter-heading">
    <div class="filter-title" id="filter-heading">
        <i class="fas fa-filter" aria-hidden="true"></i>
        Filter & Search
    </div>
    <form method="get" aria-label="Filter and search notifications" role="search">
        <div class="filter-controls">
            <div class="input-group{% if search_query %} active-filter{% endif %}">
                <label for="search-input" class="visually-hidden">Search notifications</label>
                <input type="text" name="search" id="search-input"
                       aria-label="Search notifications by title or message"
                       aria-describedby="search-help"
                       class="form-control" placeholder="Search notifications by title or message..."
                       value="{{ search_query|default:'' }}">
                <button class="btn btn-outline-primary" type="submit"
                        aria-label="Execute search" title="Search">
                    <i class="fas fa-search" aria-hidden="true"></i>
                    <span class="visually-hidden">Search</span>
                </button>
                <div id="search-help" class="visually-hidden">Enter keywords to search in notification titles and messages</div>
            </div>
            <label for="notification-type-select" class="visually-hidden">Filter by notification type</label>
            <select name="notification_type" id="notification-type-select"
                    class="form-select{% if selected_type %} active-filter{% endif %}"
                    aria-label="Filter by notification type">
                <option value="">All Types</option>
                <option value="booking" {% if selected_type == 'booking' %}selected{% endif %}>
                    Bookings
                </option>
                <option value="payment" {% if selected_type == 'payment' %}selected{% endif %}>
                    Payments
                </option>
                <option value="review" {% if selected_type == 'review' %}selected{% endif %}>
                    Reviews
                </option>
                <option value="announcement" {% if selected_type == 'announcement' %}selected{% endif %}>
                    Announcements
                </option>
                <option value="system" {% if selected_type == 'system' %}selected{% endif %}>
                    System
                </option>
            </select>
            <div class="form-check form-switch{% if show_read %} active-filter{% endif %}">
                <input class="form-check-input" type="checkbox" id="showRead"
                       name="show_read" value="true" {% if show_read %}checked{% endif %}
                       onchange="this.form.submit()"
                       aria-describedby="show-read-help">
                <label class="form-check-label" for="showRead">Include Read</label>
                <div id="show-read-help" class="visually-hidden">Toggle to include read notifications in results</div>
            </div>
            <button type="submit" class="btn btn-primary" aria-label="Apply filters">
                <i class="fas fa-search me-1" aria-hidden="true"></i>Apply
            </button>
        </div>
        {% if search_query or selected_type or show_read %}
        <div class="mt-2">
            <a href="{% url 'notifications_app:notification_list' %}"
               class="btn btn-outline-secondary btn-sm"
               aria-label="Clear all filters and show all notifications">
                <i class="fas fa-times me-1" aria-hidden="true"></i>Clear Filters
            </a>
        </div>
        {% endif %}
    </form>
</div>

<!-- Quick Actions Section -->
<div class="quick-actions">
    <div class="quick-actions-title">
        <i class="fas fa-bolt"></i>
        Quick Actions
    </div>
    <div class="action-buttons">
        <form method="post" action="{% url 'notifications_app:mark_all_notifications_read' %}"
              class="d-inline" id="mark-all-read-form">
            {% csrf_token %}
            <button type="button" class="action-btn" data-bs-toggle="modal"
                    data-bs-target="#markReadConfirmModal" {% if unread_count == 0 %}disabled{% endif %}>
                <i class="fas fa-check-double"></i>
                Mark All Read ({{ unread_count }})
            </button>
        </form>
        <button type="button" class="action-btn" id="selectAllBtn">
            <i class="fas fa-check-square"></i>
            Select All
        </button>
        <button type="button" class="action-btn" id="bulkReadBtn" disabled>
            <i class="fas fa-envelope-open"></i>
            Mark Selected Read
        </button>
        <button type="button" class="action-btn" id="bulkDeleteBtn" disabled>
            <i class="fas fa-trash"></i>
            Delete Selected
        </button>
    </div>
</div>

<!-- Loading Skeleton -->
<div id="notification-skeleton" class="d-none">
    <div class="notification-card mb-3">
        <div class="notification-content">
            <div class="skeleton-shimmer" style="height: 60px; border-radius: 0.5rem;"></div>
        </div>
    </div>
    <div class="notification-card mb-3">
        <div class="notification-content">
            <div class="skeleton-shimmer" style="height: 60px; border-radius: 0.5rem;"></div>
        </div>
    </div>
    <div class="notification-card mb-3">
        <div class="notification-content">
            <div class="skeleton-shimmer" style="height: 60px; border-radius: 0.5rem;"></div>
        </div>
    </div>
</div>

<!-- Notification List -->
{% if notifications %}
<div class="notification-list" id="notification-list" role="main" aria-labelledby="notifications-main-heading">
    <h2 class="visually-hidden">Notification Items</h2>
    {% for notification in notifications %}
    <article class="notification-card {% if notification.read_status == 'unread' %}unread{% endif %}"
             data-notification-id="{{ notification.id }}"
             role="article"
             aria-labelledby="notification-title-{{ notification.id }}"
             aria-describedby="notification-message-{{ notification.id }}"
             tabindex="0">
        <div class="notification-content">
            <div class="notification-header">
                <div class="form-check">
                    <input class="form-check-input notification-checkbox" type="checkbox"
                           value="{{ notification.id }}" id="notification-{{ notification.id }}"
                           aria-label="Select notification: {{ notification.title|truncatechars:50 }}">
                </div>
                <div class="notification-icon" aria-hidden="true" role="img"
                     aria-label="{{ notification.get_notification_type_display }} notification">
                    {% if notification.notification_type == 'booking' %}
                        <i class="fas fa-calendar-check"></i>
                    {% elif notification.notification_type == 'payment' %}
                        <i class="fas fa-credit-card"></i>
                    {% elif notification.notification_type == 'review' %}
                        <i class="fas fa-star"></i>
                    {% elif notification.notification_type == 'announcement' %}
                        <i class="fas fa-bullhorn"></i>
                    {% else %}
                        <i class="fas fa-bell"></i>
                    {% endif %}
                </div>
                <div class="notification-meta">
                    <div class="notification-title" id="notification-title-{{ notification.id }}">
                        <span class="status-indicator {% if notification.read_status == 'unread' %}unread{% else %}read{% endif %}"
                              aria-label="{% if notification.read_status == 'unread' %}Unread{% else %}Read{% endif %} notification"
                              role="status"></span>
                        {{ notification.title }}
                        {% if notification.read_status == 'unread' %}
                            <span class="badge bg-danger text-white ms-2" aria-label="New notification">New</span>
                        {% endif %}
                    </div>
                    <div class="notification-message" id="notification-message-{{ notification.id }}">
                        {{ notification.message|truncatechars:200 }}
                    </div>
                    <div class="d-flex align-items-center gap-2 mt-2">
                        <span class="category-badge {{ notification.notification_type }}"
                              role="status" aria-label="Category: {{ notification.get_notification_type_display }}">
                            <i class="fas {% if notification.notification_type == 'booking' %}fa-calendar-check{% elif notification.notification_type == 'payment' %}fa-credit-card{% elif notification.notification_type == 'review' %}fa-star{% elif notification.notification_type == 'announcement' %}fa-bullhorn{% else %}fa-cog{% endif %}"
                               aria-hidden="true"></i>
                            {{ notification.get_notification_type_display }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="notification-footer">
                <div class="notification-time">
                    <i class="fas fa-clock" aria-hidden="true"></i>
                    <time datetime="{{ notification.created_at|date:'c' }}"
                          aria-label="Created {{ notification.created_at|timesince }} ago">
                        {{ notification.created_at|timesince }} ago
                    </time>
                    <small class="text-muted ms-2">{{ notification.created_at|date:"M d, Y g:i A" }}</small>
                </div>
                <div class="notification-actions" role="group" aria-label="Notification actions">
                    <a href="{% url 'notifications_app:notification_detail' notification.id %}"
                       class="btn btn-sm btn-outline-primary"
                       aria-label="View details for {{ notification.title|truncatechars:30 }}">
                        <i class="fas fa-eye" aria-hidden="true"></i> View
                    </a>
                    {% if notification.read_status == 'read' %}
                    <form method="post" action="{% url 'notifications_app:mark_notification_unread' notification.id %}"
                          class="d-inline notification-action-form">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-secondary"
                                aria-label="Mark as unread: {{ notification.title|truncatechars:30 }}">
                            <i class="fas fa-envelope" aria-hidden="true"></i> Unread
                        </button>
                    </form>
                    {% else %}
                    <form method="post" action="{% url 'notifications_app:mark_notification_read' notification.id %}"
                          class="d-inline notification-action-form">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-success"
                                aria-label="Mark as read: {{ notification.title|truncatechars:30 }}">
                            <i class="fas fa-envelope-open" aria-hidden="true"></i> Read
                        </button>
                    </form>
                    {% endif %}
                    <form method="post" action="{% url 'notifications_app:delete_notification' notification.id %}"
                          class="d-inline notification-action-form">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                aria-label="Delete notification: {{ notification.title|truncatechars:30 }}"
                                onclick="return confirm('Are you sure you want to delete this notification?')">
                            <i class="fas fa-trash" aria-hidden="true"></i> Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </article>
    {% endfor %}
</div>

                    {% if notifications.has_other_pages %}
                    <nav aria-label="Notification pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if notifications.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ notifications.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for i in notifications.paginator.page_range %}
                            {% if notifications.number == i %}
                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                            {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
                            {% endif %}
                            {% endfor %}

                            {% if notifications.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ notifications.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

{% else %}
<!-- Enhanced Professional Empty State -->
<div class="notification-card empty-state-card">
    <div class="notification-content text-center py-5">
        <div class="mb-5">
            <div class="empty-state-icon mx-auto mb-4">
                <i class="fas fa-bell-slash"></i>
            </div>
            <h3 class="empty-state-title mb-3">No Notifications Yet</h3>
            <p class="empty-state-message mb-4">
                You're all caught up! When you have new bookings, payments, or reviews, they'll appear here.
            </p>
            <div class="empty-state-features mb-4">
                <div class="feature-item">
                    <i class="fas fa-calendar-check"></i>
                    <span>Booking Updates</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-credit-card"></i>
                    <span>Payment Confirmations</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-star"></i>
                    <span>Review Notifications</span>
                </div>
            </div>
        </div>
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            {% if user.role == 'customer' %}
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary empty-state-btn">
                    <i class="fas fa-search me-2"></i>Browse Venues
                </a>
                <a href="{% url 'booking_cart_app:cart_view' %}" class="btn btn-outline-primary empty-state-btn">
                    <i class="fas fa-shopping-cart me-2"></i>View Cart
                </a>
            {% elif user.role == 'service_provider' %}
                <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn btn-primary empty-state-btn">
                    <i class="fas fa-tachometer-alt me-2"></i>Provider Dashboard
                </a>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-outline-primary empty-state-btn">
                    <i class="fas fa-store me-2"></i>Manage Venue
                </a>
            {% endif %}
        </div>
    </div>
</div>

<style>
.empty-state-card {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border: 2px dashed rgba(0, 0, 0, 0.2);
    min-height: 400px;
    display: flex;
    align-items: center;
}

.empty-state-icon {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border: 3px solid rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: rgba(0, 0, 0, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.empty-state-title {
    font-family: var(--font-heading);
    font-weight: 700;
    color: black;
    font-size: 1.8rem;
}

.empty-state-message {
    color: rgba(0, 0, 0, 0.7);
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto;
}

.empty-state-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.9rem;
    font-weight: 500;
}

.feature-item i {
    color: rgba(0, 0, 0, 0.4);
}

.empty-state-btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 2rem;
    transition: all 0.3s ease;
}

.empty-state-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
    .empty-state-features {
        flex-direction: column;
        gap: 1rem;
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
    }
}
</style>
{% endif %}

<!-- Professional Pagination -->
{% if notifications.has_other_pages %}
<div class="d-flex justify-content-center mt-4">
    <nav aria-label="Notification pagination">
        <ul class="pagination pagination-lg">
            {% if notifications.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                   aria-label="First">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                   aria-label="Previous">
                    <i class="fas fa-angle-left"></i>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
            </li>
            <li class="page-item disabled">
                <span class="page-link"><i class="fas fa-angle-left"></i></span>
            </li>
            {% endif %}

            {% for i in notifications.paginator.page_range %}
            {% if notifications.number == i %}
            <li class="page-item active">
                <span class="page-link">{{ i }}</span>
            </li>
            {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
            </li>
            {% endif %}
            {% endfor %}

            {% if notifications.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                   aria-label="Next">
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                   aria-label="Last">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link"><i class="fas fa-angle-right"></i></span>
            </li>
            <li class="page-item disabled">
                <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
            </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

<!-- Footer Actions -->
<div class="d-flex justify-content-between align-items-center mt-2 pt-2" style="border-top: 1px solid rgba(0, 0, 0, 0.1);">
    <a href="{% if user.role == 'customer' %}{% url 'dashboard_app:customer_dashboard' %}{% elif user.role == 'service_provider' %}{% url 'dashboard_app:provider_dashboard' %}{% else %}{% url 'dashboard_app:admin_dashboard' %}{% endif %}"
       class="btn btn-outline-primary btn-sm">
        <i class="fas fa-arrow-left me-1"></i>Back
    </a>
    <a href="{% url 'notifications_app:notification_preferences' %}" class="btn btn-primary btn-sm">
        <i class="fas fa-cog me-1"></i>Settings
    </a>
</div>
{% endblock %}



{% block extra_js %}
<div class="modal fade" id="markReadConfirmModal" tabindex="-1" aria-labelledby="markReadConfirmLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="markReadConfirmLabel">Mark all as read?</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        This will mark all notifications as read. Continue?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" id="confirmMarkReadBtn" class="btn btn-primary">Yes, mark all read</button>
      </div>
    </div>
  </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Professional Notification List JavaScript

        // Initialize loading skeleton
        const skeleton = document.getElementById('notification-skeleton');
        const list = document.getElementById('notification-list');
        if (skeleton && list) {
            skeleton.classList.remove('d-none');
            setTimeout(() => {
                skeleton.classList.add('d-none');
                list.style.opacity = '1';
            }, 500);
        }

        // Toast notification system
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container') || createToastContainer();
            const toastEl = document.createElement('div');
            toastEl.className = `toast text-bg-${type} mb-2`;
            toastEl.role = 'alert';
            toastEl.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            toastContainer.appendChild(toastEl);
            const toast = new bootstrap.Toast(toastEl);
            toast.show();
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1100';
            document.body.appendChild(container);
            return container;
        }

        // Handle notification action forms
        const actionForms = document.querySelectorAll('.notification-action-form');
        const bulkForms = document.querySelectorAll('#mark-all-read-form');

        actionForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(form);
                const url = form.getAttribute('action');
                const notificationCard = form.closest('.notification-card');
                const actionType = url.includes('delete') ? 'delete' :
                                 (url.includes('mark_notification_read') ? 'mark_read' :
                                 (url.includes('mark_notification_unread') ? 'mark_unread' : 'other'));

                // Add loading state
                const button = form.querySelector('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                button.disabled = true;

                fetch(url, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update unread count in stats and navigation
                        if (data.unread_count !== undefined) {
                            const unreadStat = document.getElementById('unreadCountStat');
                            if (unreadStat) unreadStat.textContent = data.unread_count;

                            const navBadge = document.querySelector('.nav-link .badge');
                            if (navBadge) {
                                navBadge.textContent = data.unread_count;
                                navBadge.style.display = data.unread_count > 0 ? 'inline' : 'none';
                            }
                        }

                        if (actionType === 'delete' && notificationCard) {
                            // Animate removal
                            notificationCard.style.transition = 'all 0.3s ease';
                            notificationCard.style.transform = 'translateX(100%)';
                            notificationCard.style.opacity = '0';
                            setTimeout(() => notificationCard.remove(), 300);
                        } else if (actionType === 'mark_read' && notificationCard) {
                            notificationCard.classList.remove('unread');
                            const statusIndicator = notificationCard.querySelector('.status-indicator');
                            if (statusIndicator) statusIndicator.className = 'status-indicator read';
                            const badge = notificationCard.querySelector('.badge.bg-danger');
                            if (badge) badge.remove();
                            form.action = form.action.replace('mark_notification_read', 'mark_notification_unread');
                            button.innerHTML = '<i class="fas fa-envelope"></i> Unread';
                        } else if (actionType === 'mark_unread' && notificationCard) {
                            notificationCard.classList.add('unread');
                            const statusIndicator = notificationCard.querySelector('.status-indicator');
                            if (statusIndicator) statusIndicator.className = 'status-indicator unread';
                            if (!notificationCard.querySelector('.badge.bg-danger')) {
                                const badge = document.createElement('span');
                                badge.className = 'badge bg-danger text-white ms-2';
                                badge.textContent = 'New';
                                notificationCard.querySelector('.notification-title').appendChild(badge);
                            }
                            form.action = form.action.replace('mark_notification_unread', 'mark_notification_read');
                            button.innerHTML = '<i class="fas fa-envelope-open"></i> Read';
                        }

                        showToast(data.message || 'Action completed successfully');
                    } else {
                        showToast(data.message || 'Action failed', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred. Please try again.', 'danger');
                })
                .finally(() => {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            });
        });

        // Handle bulk form submissions
        bulkForms.forEach(form => {
            form.addEventListener('submit', function(e){
                e.preventDefault();

                const formData = new FormData(form);
                const url = form.getAttribute('action');

                fetch(url, {
                    method: 'POST',
                    body: formData,
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update unread count
                        if (data.unread_count !== undefined) {
                            const unreadStat = document.getElementById('unreadCountStat');
                            if (unreadStat) unreadStat.textContent = data.unread_count;

                            const navBadge = document.querySelector('.nav-link .badge');
                            if (navBadge) {
                                navBadge.textContent = data.unread_count;
                                navBadge.style.display = data.unread_count > 0 ? 'inline' : 'none';
                            }
                        }

                        // Update all notification cards
                        const notificationCards = document.querySelectorAll('.notification-card');
                        notificationCards.forEach(card => {
                            if (url.includes('mark-all-read')) {
                                card.classList.remove('unread');
                                const statusIndicator = card.querySelector('.status-indicator');
                                if (statusIndicator) statusIndicator.className = 'status-indicator read';
                                const badge = card.querySelector('.badge.bg-danger');
                                if (badge) badge.remove();
                                const btnForm = card.querySelector('form[action*="mark_notification_read"]');
                                if (btnForm) {
                                    btnForm.action = btnForm.action.replace('mark_notification_read', 'mark_notification_unread');
                                    btnForm.querySelector('button').innerHTML = '<i class="fas fa-envelope"></i> Unread';
                                }
                            }
                        });

                        showToast(data.message || 'All notifications marked as read');
                    } else {
                        showToast(data.message || 'Action failed', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred. Please try again.', 'danger');
                });
            });
        });

        // Professional Bulk Actions and Checkbox Management
        const selectAllBtn = document.getElementById('selectAllBtn');
        const bulkReadBtn = document.getElementById('bulkReadBtn');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const checkboxes = document.querySelectorAll('.notification-checkbox');
        let allSelected = false;

        function updateBulkButtons() {
            const checkedBoxes = document.querySelectorAll('.notification-checkbox:checked');
            const hasChecked = checkedBoxes.length > 0;

            bulkReadBtn.disabled = !hasChecked;
            bulkDeleteBtn.disabled = !hasChecked;

            // Update select all button text
            if (selectAllBtn) {
                if (checkedBoxes.length === checkboxes.length && checkboxes.length > 0) {
                    selectAllBtn.innerHTML = '<i class="fas fa-square"></i> Deselect All';
                    allSelected = true;
                } else {
                    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> Select All';
                    allSelected = false;
                }
            }
        }

        // Select/Deselect all functionality
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !allSelected;
                });
                updateBulkButtons();
            });
        }

        // Bulk read action
        if (bulkReadBtn) {
            bulkReadBtn.addEventListener('click', function() {
                const selectedIds = Array.from(document.querySelectorAll('.notification-checkbox:checked')).map(cb => cb.value);
                if (selectedIds.length === 0) return;

                const formData = new FormData();
                selectedIds.forEach(id => formData.append('ids', id));

                fetch('{% url 'notifications_app:bulk_mark_notifications_read' %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update selected notifications
                        selectedIds.forEach(id => {
                            const checkbox = document.querySelector(`.notification-checkbox[value="${id}"]`);
                            const card = checkbox.closest('.notification-card');
                            if (card) {
                                card.classList.remove('unread');
                                const statusIndicator = card.querySelector('.status-indicator');
                                if (statusIndicator) statusIndicator.className = 'status-indicator read';
                                const badge = card.querySelector('.badge.bg-danger');
                                if (badge) badge.remove();
                            }
                            checkbox.checked = false;
                        });

                        // Update counts
                        if (data.unread_count !== undefined) {
                            const unreadStat = document.getElementById('unreadCountStat');
                            if (unreadStat) unreadStat.textContent = data.unread_count;
                        }

                        updateBulkButtons();
                        showToast(`${selectedIds.length} notifications marked as read`);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Failed to mark notifications as read', 'danger');
                });
            });
        }

        // Bulk delete action
        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', function() {
                const selectedIds = Array.from(document.querySelectorAll('.notification-checkbox:checked')).map(cb => cb.value);
                if (selectedIds.length === 0) return;

                if (!confirm(`Are you sure you want to delete ${selectedIds.length} selected notifications? This action cannot be undone.`)) {
                    return;
                }

                // Note: You'll need to implement bulk delete endpoint
                showToast('Bulk delete functionality coming soon', 'info');
            });
        }

        // Add event listeners to checkboxes
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkButtons);
        });

        // Initialize bulk button states
        updateBulkButtons();

        // Modal confirmation for mark all as read
        const confirmBtn = document.getElementById('confirmMarkReadBtn');
        const markForm = document.getElementById('mark-all-read-form');
        if (confirmBtn && markForm) {
            confirmBtn.addEventListener('click', function(){
                const button = confirmBtn;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                button.disabled = true;

                setTimeout(() => {
                    markForm.submit();
                }, 500);
            });
        }

        // Add smooth animations to notification cards
        const notificationCards = document.querySelectorAll('.notification-card');
        notificationCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Add hover effects for better interactivity
        notificationCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
