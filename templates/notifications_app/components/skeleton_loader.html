<!-- CozyWish Enhanced Skeleton Loading Component for Notifications -->
<div class="cw-skeleton-container" data-skeleton-type="{{ skeleton_type|default:'notifications' }}">
    {% if skeleton_type == 'notifications' or not skeleton_type %}
        <!-- Notification List Skeleton -->
        <div class="cw-notification-skeleton-wrapper">
            {% for i in "123456" %}
            <div class="cw-notification-skeleton-item" style="animation-delay: {{ forloop.counter0|mul:0.1 }}s;">
                <div class="cw-notification-skeleton-content">
                    <!-- Icon skeleton -->
                    <div class="cw-skeleton-icon"></div>

                    <!-- Content skeleton -->
                    <div class="cw-skeleton-content">
                        <!-- Title and badge -->
                        <div class="cw-skeleton-header">
                            <div class="cw-skeleton-title"></div>
                            <div class="cw-skeleton-badge"></div>
                        </div>

                        <!-- Message -->
                        <div class="cw-skeleton-message">
                            <div class="cw-skeleton-line"></div>
                            <div class="cw-skeleton-line cw-short"></div>
                        </div>

                        <!-- Meta info -->
                        <div class="cw-skeleton-meta">
                            <div class="cw-skeleton-time"></div>
                            <div class="cw-skeleton-category"></div>
                        </div>
                    </div>

                    <!-- Actions skeleton -->
                    <div class="cw-skeleton-actions">
                        <div class="cw-skeleton-action-btn"></div>
                        <div class="cw-skeleton-action-btn"></div>
                        <div class="cw-skeleton-action-btn"></div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

    {% elif skeleton_type == 'notification_detail' %}
        <!-- Single Notification Detail Skeleton -->
        <div class="cw-notification-detail-skeleton">
            <div class="cw-skeleton-header-large">
                <div class="cw-skeleton-icon-large"></div>
                <div class="cw-skeleton-title-large"></div>
                <div class="cw-skeleton-badge-large"></div>
            </div>

            <div class="cw-skeleton-content-area">
                <div class="cw-skeleton-paragraph">
                    <div class="cw-skeleton-line"></div>
                    <div class="cw-skeleton-line"></div>
                    <div class="cw-skeleton-line cw-medium"></div>
                </div>

                <div class="cw-skeleton-details-box">
                    <div class="cw-skeleton-detail-row">
                        <div class="cw-skeleton-label"></div>
                        <div class="cw-skeleton-value"></div>
                    </div>
                    <div class="cw-skeleton-detail-row">
                        <div class="cw-skeleton-label"></div>
                        <div class="cw-skeleton-value"></div>
                    </div>
                    <div class="cw-skeleton-detail-row">
                        <div class="cw-skeleton-label"></div>
                        <div class="cw-skeleton-value cw-long"></div>
                    </div>
                </div>

                <div class="cw-skeleton-action-area">
                    <div class="cw-skeleton-primary-btn"></div>
                    <div class="cw-skeleton-secondary-btn"></div>
                </div>
            </div>
        </div>

    {% elif skeleton_type == 'dashboard_cards' %}
        <!-- Dashboard Cards Skeleton -->
        <div class="cw-dashboard-skeleton-grid">
            {% for i in "1234" %}
            <div class="cw-dashboard-card-skeleton" style="animation-delay: {{ forloop.counter0|mul:0.15 }}s;">
                <div class="cw-skeleton-card-header">
                    <div class="cw-skeleton-card-icon"></div>
                    <div class="cw-skeleton-card-title"></div>
                </div>
                <div class="cw-skeleton-card-content">
                    <div class="cw-skeleton-number"></div>
                    <div class="cw-skeleton-label-small"></div>
                </div>
                <div class="cw-skeleton-card-footer">
                    <div class="cw-skeleton-trend"></div>
                </div>
            </div>
            {% endfor %}
        </div>
        
    {% elif skeleton_type == 'review_notifications' %}
        <!-- Review Notifications Skeleton -->
        <div class="cw-review-skeleton-wrapper">
            {% for i in "123" %}
            <div class="cw-review-skeleton-item" style="animation-delay: {{ forloop.counter0|mul:0.12 }}s;">
                <div class="cw-skeleton-review-header">
                    <div class="cw-skeleton-avatar"></div>
                    <div class="cw-skeleton-review-info">
                        <div class="cw-skeleton-customer-name"></div>
                        <div class="cw-skeleton-venue-name"></div>
                        <div class="cw-skeleton-stars">
                            <div class="cw-skeleton-star"></div>
                            <div class="cw-skeleton-star"></div>
                            <div class="cw-skeleton-star"></div>
                            <div class="cw-skeleton-star"></div>
                            <div class="cw-skeleton-star"></div>
                        </div>
                    </div>
                </div>
                <div class="cw-skeleton-review-content">
                    <div class="cw-skeleton-line"></div>
                    <div class="cw-skeleton-line cw-medium"></div>
                </div>
                <div class="cw-skeleton-review-actions">
                    <div class="cw-skeleton-action-btn cw-small"></div>
                    <div class="cw-skeleton-action-btn cw-small"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% endif %}
</div>

<style>
/* CozyWish Skeleton Loading Styles */

/* CSS Custom Properties */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-100: #f5f5f5;
    --cw-neutral-200: #e5e5e5;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Base Skeleton Styles */
.cw-skeleton-container {
    width: 100%;
    animation: cwContainerFadeIn 0.6s ease-out;
}

@keyframes cwContainerFadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced shimmer effect for CozyWish */
.cw-skeleton-container [class*="cw-skeleton-"]:not(.cw-skeleton-container):not(.cw-skeleton-wrapper):not(.cw-skeleton-item) {
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(250, 225, 215, 0.4) 20%,
        rgba(250, 225, 215, 0.8) 50%,
        rgba(250, 225, 215, 0.4) 80%,
        transparent 100%
    ), linear-gradient(90deg, var(--cw-neutral-100) 0%, var(--cw-neutral-200) 50%, var(--cw-neutral-100) 100%);
    background-size: 200% 100%, 100% 100%;
    animation: cwEnhancedShimmer 2s infinite ease-in-out;
    border-radius: 0.375rem;
}

@keyframes cwEnhancedShimmer {
    0% { background-position: 200% 0, 0 0; }
    100% { background-position: -200% 0, 0 0; }
}

/* Notification List Skeleton */
.cw-notification-skeleton-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cw-notification-skeleton-item {
    padding: 1rem;
    background: white;
    border: 1px solid var(--cw-neutral-200);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-sm);
    animation: cwItemSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes cwItemSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cw-notification-skeleton-content {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.cw-skeleton-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.cw-skeleton-content {
    flex: 1;
    min-width: 0;
}

.cw-skeleton-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.cw-skeleton-title {
    height: 1rem;
    width: 12.5rem;
    border-radius: 0.5rem;
}

.cw-skeleton-badge {
    height: 1.25rem;
    width: 3.75rem;
    border-radius: 9999px;
}

.cw-skeleton-message {
    margin-bottom: 0.75rem;
}

.cw-skeleton-line {
    height: 0.875rem;
    margin-bottom: 0.375rem;
    border-radius: 0.4375rem;
}

.cw-skeleton-line:last-child {
    margin-bottom: 0;
}

.cw-skeleton-line.cw-short {
    width: 60%;
}

.cw-skeleton-line.cw-medium {
    width: 75%;
}

.cw-skeleton-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cw-skeleton-time {
    height: 0.75rem;
    width: 5rem;
    border-radius: 0.375rem;
}

.cw-skeleton-category {
    height: 1rem;
    width: 6.25rem;
    border-radius: 0.5rem;
}

.cw-skeleton-actions {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    flex-shrink: 0;
}

.cw-skeleton-action-btn {
    height: 1.75rem;
    width: 4.0625rem;
    border-radius: 0.375rem;
}

.cw-skeleton-action-btn.cw-small {
    height: 1.5rem;
    width: 3.125rem;
}

/* Notification Detail Skeleton */
.cw-notification-detail-skeleton {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid var(--cw-neutral-200);
    box-shadow: var(--cw-shadow-md);
}

.cw-skeleton-header-large {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--cw-neutral-100);
}

.cw-skeleton-icon-large {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.cw-skeleton-title-large {
    height: 1.5rem;
    flex: 1;
    border-radius: 0.75rem;
}

.cw-skeleton-badge-large {
    height: 1.75rem;
    width: 5rem;
    border-radius: 9999px;
}

.cw-skeleton-content-area {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cw-skeleton-paragraph {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cw-skeleton-details-box {
    background: var(--cw-accent-light);
    border-radius: 0.75rem;
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cw-skeleton-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cw-skeleton-label {
    height: 0.875rem;
    width: 7.5rem;
    border-radius: 0.4375rem;
}

.cw-skeleton-value {
    height: 1rem;
    width: 9.375rem;
    border-radius: 0.5rem;
}

.cw-skeleton-value.cw-long {
    width: 12.5rem;
}

.cw-skeleton-action-area {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.cw-skeleton-primary-btn {
    height: 2.75rem;
    width: 8.75rem;
    border-radius: 0.5rem;
}

.cw-skeleton-secondary-btn {
    height: 2.75rem;
    width: 7.5rem;
    border-radius: 0.5rem;
}

/* Dashboard Cards Skeleton */
.cw-dashboard-skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15.625rem, 1fr));
    gap: 1.25rem;
}

.cw-dashboard-card-skeleton {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--cw-neutral-200);
    box-shadow: var(--cw-shadow-sm);
    animation: cwCardSlideUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes cwCardSlideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cw-skeleton-card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.cw-skeleton-card-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
}

.cw-skeleton-card-title {
    height: 1rem;
    flex: 1;
    border-radius: 0.5rem;
}

.cw-skeleton-card-content {
    margin-bottom: 1rem;
    text-align: center;
}

.cw-skeleton-number {
    height: 2rem;
    width: 5rem;
    margin: 0 auto 0.5rem;
    border-radius: 1rem;
}

.cw-skeleton-label-small {
    height: 0.75rem;
    width: 3.75rem;
    margin: 0 auto;
    border-radius: 0.375rem;
}

.cw-skeleton-card-footer {
    border-top: 1px solid var(--cw-neutral-100);
    padding-top: 0.75rem;
}

.cw-skeleton-trend {
    height: 0.875rem;
    width: 7.5rem;
    border-radius: 0.4375rem;
}

/* Review Notifications Skeleton */
.cw-review-skeleton-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.cw-review-skeleton-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1.25rem;
    border: 1px solid var(--cw-neutral-200);
    box-shadow: var(--cw-shadow-sm);
    animation: cwReviewSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

@keyframes cwReviewSlideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.cw-skeleton-review-header {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.cw-skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.cw-skeleton-review-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.cw-skeleton-customer-name {
    height: 1rem;
    width: 8.75rem;
    border-radius: 0.5rem;
}

.cw-skeleton-venue-name {
    height: 0.875rem;
    width: 11.25rem;
    border-radius: 0.4375rem;
}

.cw-skeleton-stars {
    display: flex;
    gap: 0.25rem;
}

.cw-skeleton-star {
    width: 1rem;
    height: 1rem;
    border-radius: 0.125rem;
}

.cw-skeleton-review-content {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cw-skeleton-review-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cw-notification-skeleton-content {
        flex-direction: column;
        gap: 0.75rem;
    }

    .cw-skeleton-actions {
        flex-direction: row;
        justify-content: center;
    }

    .cw-skeleton-header-large {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .cw-skeleton-detail-row {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .cw-skeleton-action-area {
        flex-direction: column;
        align-items: center;
    }

    .cw-dashboard-skeleton-grid {
        grid-template-columns: 1fr;
    }

    .cw-skeleton-review-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

/* Loading indicators */
.cw-skeleton-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--cw-brand-primary), transparent);
    animation: cwProgressBar 2s infinite;
    z-index: 9999;
}

@keyframes cwProgressBar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pulse animation for important elements */
.cw-skeleton-icon,
.cw-skeleton-icon-large,
.cw-skeleton-avatar {
    animation: cwPulse 2s infinite ease-in-out;
}

@keyframes cwPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Staggered animation delays for natural loading feeling */
.cw-notification-skeleton-item:nth-child(1) { animation-delay: 0.1s; }
.cw-notification-skeleton-item:nth-child(2) { animation-delay: 0.2s; }
.cw-notification-skeleton-item:nth-child(3) { animation-delay: 0.3s; }
.cw-notification-skeleton-item:nth-child(4) { animation-delay: 0.4s; }
.cw-notification-skeleton-item:nth-child(5) { animation-delay: 0.5s; }
.cw-notification-skeleton-item:nth-child(6) { animation-delay: 0.6s; }

.cw-dashboard-card-skeleton:nth-child(1) { animation-delay: 0.1s; }
.cw-dashboard-card-skeleton:nth-child(2) { animation-delay: 0.25s; }
.cw-dashboard-card-skeleton:nth-child(3) { animation-delay: 0.4s; }
.cw-dashboard-card-skeleton:nth-child(4) { animation-delay: 0.55s; }

.cw-review-skeleton-item:nth-child(1) { animation-delay: 0.15s; }
.cw-review-skeleton-item:nth-child(2) { animation-delay: 0.3s; }
.cw-review-skeleton-item:nth-child(3) { animation-delay: 0.45s; }
</style>

<script>
// CozyWish Skeleton Loader JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const skeletons = document.querySelectorAll('.cw-skeleton-container');

    skeletons.forEach(skeleton => {
        // Auto-hide after 10 seconds as fallback
        setTimeout(() => {
            if (skeleton.parentNode) {
                skeleton.style.transition = 'opacity 0.5s ease-out';
                skeleton.style.opacity = '0';
                setTimeout(() => {
                    if (skeleton.parentNode) {
                        skeleton.remove();
                    }
                }, 500);
            }
        }, 10000);
    });
});

// Helper function to show/hide CozyWish skeleton
window.cwToggleSkeleton = function(containerId, show = true) {
    const container = document.getElementById(containerId);
    const skeleton = container?.querySelector('.cw-skeleton-container');

    if (show && !skeleton) {
        // Create and show skeleton
        const skeletonHtml = document.querySelector('[data-skeleton-template]')?.innerHTML;
        if (skeletonHtml) {
            container.innerHTML = skeletonHtml;
        }
    } else if (!show && skeleton) {
        // Hide skeleton with animation
        skeleton.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        skeleton.style.opacity = '0';
        skeleton.style.transform = 'translateY(-10px)';
        setTimeout(() => skeleton.remove(), 300);
    }
};

// Preload optimization for CozyWish
window.cwPreloadSkeletons = function() {
    const skeletonTypes = ['notifications', 'notification_detail', 'dashboard_cards', 'review_notifications'];
    skeletonTypes.forEach(type => {
        const template = document.createElement('div');
        template.style.display = 'none';
        template.setAttribute('data-skeleton-type', type);
        template.innerHTML = '<!-- CozyWish skeleton content for ' + type + ' -->';
        document.body.appendChild(template);
    });
};
</script> 