{% load static %}

<!-- CozyWish Review Notifications Dashboard Widget -->
<div class="cw-review-notification-widget" id="reviewNotificationWidget">
    <!-- Widget Header -->
    <div class="cw-widget-header">
        <div class="cw-widget-title">
            <div class="cw-title-icon">
                <i class="fas fa-star text-brand-cw"></i>
            </div>
            <div class="cw-title-text">
                <h6 class="fw-bold text-brand-cw mb-1">Review Notifications</h6>
                <span class="cw-subtitle text-neutral-cw">Recent customer feedback</span>
            </div>
        </div>
        <div class="cw-widget-actions">
            {% if unread_review_notifications > 0 %}
                <span class="cw-notification-count-badge">{{ unread_review_notifications }}</span>
            {% endif %}
            <a href="{% url 'notifications_app:notification_list' %}?notification_type=review"
               class="cw-view-all-link" title="View All Review Notifications">
                <i class="fas fa-external-link-alt"></i>
            </a>
        </div>
    </div>

    <!-- Widget Body -->
    <div class="cw-widget-body">
        {% if review_notifications %}
            <div class="cw-notification-list">
                {% for notification in review_notifications %}
                    <div class="cw-review-notification-item {% if notification.read_status == 'unread' %}cw-unread{% endif %}">
                        <div class="cw-notification-content">
                            <div class="cw-notification-header">
                                <div class="cw-notification-type-info">
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        <div class="cw-type-icon cw-response">
                                            <i class="fas fa-reply"></i>
                                        </div>
                                        <span class="cw-type-label">Response Sent</span>
                                    {% else %}
                                        <div class="cw-type-icon cw-review">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="cw-type-label">New Review</span>
                                    {% endif %}
                                </div>
                                {% if notification.read_status == 'unread' %}
                                    <span class="cw-new-badge">NEW</span>
                                {% endif %}
                            </div>

                            <div class="cw-notification-details">
                                {% if notification.metadata.venue_name %}
                                    <div class="cw-venue-name text-brand-cw fw-medium">{{ notification.metadata.venue_name }}</div>
                                {% endif %}

                                {% if notification.metadata.customer_name %}
                                    <div class="cw-customer-info">
                                        <span class="cw-customer-name text-neutral-cw">{{ notification.metadata.customer_name }}</span>
                                        {% if notification.metadata.review_rating %}
                                            <div class="cw-rating-display">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= notification.metadata.review_rating %}
                                                        <i class="fas fa-star cw-star-filled"></i>
                                                    {% else %}
                                                        <i class="far fa-star cw-star-empty"></i>
                                                    {% endif %}
                                                {% endfor %}
                                                <span class="cw-rating-number text-neutral-cw">{{ notification.metadata.review_rating }}/5</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}

                                {% if notification.metadata.review_preview %}
                                    <div class="cw-review-preview">
                                        <p class="text-neutral-cw fst-italic">"{{ notification.metadata.review_preview|truncatechars:80 }}"</p>
                                    </div>
                                {% endif %}

                                <div class="cw-notification-meta">
                                    <span class="cw-timestamp text-neutral-cw">{{ notification.created_at|timesince }} ago</span>
                                    {% if notification.metadata.notification_priority == 'high' %}
                                        <span class="cw-priority-badge cw-high">High Priority</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="cw-notification-actions">
                                {% if notification.related_object_type == 'Review' %}
                                    {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}" class="cw-action-btn cw-primary" title="View Review">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% endif %}
                                    {% if not notification.metadata.has_response %}
                                        <a href="{% url 'review_app:provider_respond_to_review' notification.related_object_id %}" class="cw-action-btn cw-success" title="Respond">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                    {% endif %}
                                {% else %}
                                    {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}" class="cw-action-btn cw-info" title="View Response">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% endif %}
                                {% endif %}

                                <form method="post" action="{% if notification.read_status == 'unread' %}{% url 'notifications_app:mark_notification_read' notification.id %}{% else %}{% url 'notifications_app:mark_notification_unread' notification.id %}{% endif %}" class="cw-mark-read-form">
                                    {% csrf_token %}
                                    <button type="submit" class="cw-action-btn cw-outline" title="{% if notification.read_status == 'unread' %}Mark as Read{% else %}Mark as Unread{% endif %}">
                                        <i class="fas {% if notification.read_status == 'unread' %}fa-envelope-open{% else %}fa-envelope{% endif %}"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="cw-empty-state">
                <div class="cw-empty-icon">
                    <i class="fas fa-star text-brand-accent-cw"></i>
                </div>
                <div class="cw-empty-text">
                    <h6 class="fw-bold text-brand-cw">No review notifications</h6>
                    <p class="text-neutral-cw mb-0">You'll see customer reviews and responses here</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Widget Footer -->
    {% if review_notifications %}
        <div class="cw-widget-footer">
            <a href="{% url 'review_app:provider_venue_reviews' %}" class="cw-footer-link">
                <i class="fas fa-chart-bar me-1"></i>View Review Dashboard
            </a>
            <a href="{% url 'notifications_app:notification_list' %}?notification_type=review" class="cw-footer-link">
                <i class="fas fa-list me-1"></i>All Notifications
            </a>
        </div>
    {% endif %}
</div>

<style>
/* CozyWish Review Notification Dashboard Widget Styles */

/* CSS Custom Properties */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-100: #f5f5f5;
    --cw-neutral-200: #e5e5e5;
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Semantic Colors */
    --cw-success: #059669;
    --cw-warning: #d97706;
    --cw-error: #dc2626;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
}

/* Widget Container */
.cw-review-notification-widget {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--cw-neutral-200);
}

.cw-review-notification-widget:hover {
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-lg);
}

/* Widget Header */
.cw-widget-header {
    background: var(--cw-gradient-card-subtle);
    padding: 1.25rem;
    border-bottom: 1px solid var(--cw-neutral-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cw-widget-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cw-title-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--cw-gradient-brand-button);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    box-shadow: var(--cw-shadow-sm);
}

.cw-title-text h6 {
    margin: 0;
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 1rem;
}

.cw-subtitle {
    font-size: 0.875rem;
    color: var(--cw-neutral-600);
    margin: 0;
}

.cw-widget-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cw-notification-count-badge {
    background: var(--cw-error);
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
    box-shadow: var(--cw-shadow-sm);
    animation: cwPulse 2s infinite;
}

@keyframes cwPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.cw-view-all-link {
    color: var(--cw-neutral-600);
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cw-view-all-link:hover {
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    transform: translateY(-1px);
}

/* Widget Body */
.cw-widget-body {
    padding: 0;
    max-height: 25rem;
    overflow-y: auto;
}

.cw-notification-list {
    display: flex;
    flex-direction: column;
}

/* Notification Items */
.cw-review-notification-item {
    padding: 1.25rem;
    border-bottom: 1px solid var(--cw-neutral-100);
    transition: all 0.2s ease;
    position: relative;
    background: white;
}

.cw-review-notification-item:hover {
    background: var(--cw-accent-light);
    transform: translateX(2px);
}

.cw-review-notification-item.cw-unread {
    background: var(--cw-gradient-card-subtle);
    border-left: 4px solid var(--cw-brand-primary);
}

.cw-review-notification-item.cw-unread::before {
    content: '';
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background: var(--cw-brand-primary);
    border-radius: 50%;
    animation: cwNewNotificationGlow 2s infinite;
}

@keyframes cwNewNotificationGlow {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.2); }
}

.cw-notification-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.cw-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cw-notification-type-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cw-type-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
}

.cw-type-icon.cw-review {
    background: var(--cw-gradient-brand-button);
    box-shadow: var(--cw-shadow-sm);
}

.cw-type-icon.cw-response {
    background: var(--cw-success);
    box-shadow: var(--cw-shadow-sm);
}

.cw-type-label {
    font-weight: 600;
    color: var(--cw-neutral-700);
    font-size: 0.875rem;
}

.cw-new-badge {
    background: var(--cw-error);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.625rem;
    font-weight: 700;
    letter-spacing: 0.025em;
    box-shadow: var(--cw-shadow-sm);
    animation: cwNewBadgePulse 2s infinite;
}

@keyframes cwNewBadgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
/* Notification Details */
.cw-notification-details {
    margin-bottom: 0.75rem;
}

.cw-venue-name {
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.375rem;
    font-size: 0.875rem;
}

.cw-customer-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.cw-customer-name {
    color: var(--cw-neutral-600);
    font-size: 0.8125rem;
}

.cw-rating-display {
    display: flex;
    align-items: center;
    gap: 0.125rem;
}

.cw-star-filled {
    color: var(--cw-warning);
    font-size: 0.75rem;
}

.cw-star-empty {
    color: var(--cw-neutral-200);
    font-size: 0.75rem;
}

.cw-rating-number {
    margin-left: 0.25rem;
    font-size: 0.6875rem;
    color: var(--cw-neutral-600);
    font-weight: 500;
}

.cw-review-preview {
    background: var(--cw-accent-light);
    border-left: 3px solid var(--cw-brand-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    margin: 0.5rem 0;
}

.cw-review-preview p {
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

.cw-notification-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.cw-timestamp {
    color: var(--cw-neutral-600);
    font-size: 0.6875rem;
}

.cw-priority-badge.cw-high {
    background: var(--cw-error);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 0.5rem;
    font-size: 0.5625rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Notification Actions */
.cw-notification-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--cw-neutral-100);
    justify-content: flex-end;
}

.cw-action-btn {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.6875rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--cw-shadow-sm);
}

.cw-action-btn.cw-primary {
    background: var(--cw-gradient-brand-button);
    color: white;
}

.cw-action-btn.cw-primary:hover {
    background: var(--cw-brand-light);
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-success {
    background: var(--cw-success);
    color: white;
}

.cw-action-btn.cw-success:hover {
    background: #047857;
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-info {
    background: #0284c7;
    color: white;
}

.cw-action-btn.cw-info:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-outline {
    background: transparent;
    color: var(--cw-neutral-600);
    border: 1px solid var(--cw-neutral-200);
}

.cw-action-btn.cw-outline:hover {
    background: var(--cw-accent-light);
    color: var(--cw-brand-primary);
    transform: translateY(-1px);
}

.cw-mark-read-form {
    display: inline;
}

/* Empty State */
.cw-empty-state {
    text-align: center;
    padding: 2.5rem 1.25rem;
    color: var(--cw-neutral-600);
}

.cw-empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--cw-brand-accent);
}

.cw-empty-text h6 {
    margin-bottom: 0.5rem;
    color: var(--cw-brand-primary);
}

.cw-empty-text p {
    font-size: 0.875rem;
    margin: 0;
}

/* Widget Footer */
.cw-widget-footer {
    background: var(--cw-accent-light);
    padding: 0.75rem 1.25rem;
    border-top: 1px solid var(--cw-neutral-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cw-footer-link {
    color: var(--cw-neutral-600);
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.cw-footer-link:hover {
    color: var(--cw-brand-primary);
    background: var(--cw-brand-accent);
    text-decoration: none;
    transform: translateY(-1px);
}

/* Scrollbar styling */
.cw-widget-body::-webkit-scrollbar {
    width: 4px;
}

.cw-widget-body::-webkit-scrollbar-track {
    background: var(--cw-accent-light);
}

.cw-widget-body::-webkit-scrollbar-thumb {
    background: var(--cw-brand-accent);
    border-radius: 4px;
}

.cw-widget-body::-webkit-scrollbar-thumb:hover {
    background: var(--cw-accent-dark);
}

/* Mobile responsive */
@media (max-width: 576px) {
    .cw-widget-header {
        padding: 0.75rem 1rem;
    }

    .cw-widget-body {
        padding: 1rem;
    }

    .cw-notification-content {
        padding: 0.75rem;
    }

    .cw-customer-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .cw-notification-actions {
        gap: 0.375rem;
    }

    .cw-action-btn {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.625rem;
    }

    .cw-widget-footer {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
</style>

<script>
// CozyWish Review Notification Widget JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const widget = document.getElementById('reviewNotificationWidget');
    if (!widget) return;

    // Handle mark as read/unread forms
    const markReadForms = widget.querySelectorAll('.cw-mark-read-form');
    markReadForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const url = form.getAttribute('action');
            const button = form.querySelector('button');
            const originalIcon = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the notification item
                    const notificationItem = form.closest('.cw-review-notification-item');
                    if (notificationItem) {
                        if (form.action.includes('mark_notification_read')) {
                            notificationItem.classList.remove('cw-unread');
                            button.innerHTML = '<i class="fas fa-envelope"></i>';
                            form.action = form.action.replace('mark_notification_read', 'mark_notification_unread');
                        } else {
                            notificationItem.classList.add('cw-unread');
                            button.innerHTML = '<i class="fas fa-envelope-open"></i>';
                            form.action = form.action.replace('mark_notification_unread', 'mark_notification_read');
                        }
                    }

                    // Update badge count
                    if (data.unread_count !== undefined) {
                        const badge = widget.querySelector('.cw-notification-count-badge');
                        if (badge) {
                            if (data.unread_count > 0) {
                                badge.textContent = data.unread_count;
                                badge.style.display = 'inline-block';
                            } else {
                                badge.style.display = 'none';
                            }
                        }
                    }
                } else {
                    button.innerHTML = originalIcon;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.innerHTML = originalIcon;
            })
            .finally(() => {
                button.disabled = false;
            });
        });
    });

    // Auto-refresh widget every 30 seconds for new notifications
    setInterval(function() {
        // Only refresh if the page is visible
        if (!document.hidden) {
            const currentCount = widget.querySelector('.cw-notification-count-badge')?.textContent || 0;

            // Simple check for new notifications
            fetch('/notifications/unread/?notification_type=review', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.unread_count > currentCount) {
                    // New notification arrived - show visual indicator
                    widget.style.animation = 'cwNewNotificationPulse 1s ease-in-out';
                    setTimeout(() => {
                        widget.style.animation = '';
                    }, 1000);
                }
            })
            .catch(error => {
                console.log('Auto-refresh failed:', error);
            });
        }
    }, 30000); // 30 seconds
});

// CSS animation for new notification pulse
const cwStyle = document.createElement('style');
cwStyle.textContent = `
@keyframes cwNewNotificationPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); box-shadow: 0 0 20px rgba(47, 22, 15, 0.2); }
}
`;
document.head.appendChild(cwStyle);
</script> 