{% load static %}

<!-- CozyWish Review Notification Badge Component -->
<div class="cw-review-notification-badge" data-notification-type="{{ notification.notification_type }}" data-review-id="{{ review.id|default:'' }}">
    <!-- Main Badge -->
    <div class="cw-notification-badge-main">
        {% if notification.notification_type == 'review' %}
            <div class="cw-badge-icon cw-review-icon">
                <i class="fas fa-star"></i>
                {% if notification.related_object_type == 'ReviewResponse' %}
                    <div class="cw-response-indicator">
                        <i class="fas fa-reply"></i>
                    </div>
                {% endif %}
            </div>
        {% endif %}

        <div class="cw-badge-content">
            <div class="cw-badge-title">
                {% if notification.related_object_type == 'ReviewResponse' %}
                    <i class="fas fa-reply me-2"></i>Provider Response
                {% elif notification.related_object_type == 'Review' %}
                    <i class="fas fa-star me-2"></i>New Review
                {% else %}
                    {{ notification.title }}
                {% endif %}
            </div>

            <div class="cw-badge-details">
                {% if notification.related_object_type == 'ReviewResponse' %}
                    {% if review %}
                        <div class="cw-response-details">
                            <div class="cw-venue-name">{{ review.venue.venue_name }}</div>
                            <div class="cw-provider-name">{{ review.venue.service_provider.business_name }}</div>
                            <div class="cw-original-rating">
                                <span class="cw-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                <span class="cw-rating-text">{{ review.rating }}/5</span>
                            </div>
                        </div>
                    {% endif %}
                {% elif notification.related_object_type == 'Review' %}
                    {% if review %}
                        <div class="cw-review-details">
                            <div class="cw-customer-name">{{ review.customer.get_full_name|default:review.customer.email }}</div>
                            <div class="cw-venue-name">{{ review.venue.venue_name }}</div>
                            <div class="cw-review-rating">
                                <span class="cw-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                <span class="cw-rating-text">{{ review.rating }}/5</span>
                            </div>
                        </div>
                    {% endif %}
                {% endif %}
            </div>

            <div class="cw-badge-meta">
                <span class="cw-time-ago">
                    <i class="fas fa-clock me-1"></i>{{ notification.created_at|timesince }} ago
                </span>
                {% if notification.read_status == 'unread' %}
                    <span class="cw-unread-indicator">NEW</span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="cw-badge-actions">
        {% if notification.related_object_type == 'ReviewResponse' %}
            <a href="{% url 'review_app:customer_review_detail' review.id %}" class="cw-action-btn cw-primary" title="View Conversation">
                <i class="fas fa-eye"></i>
            </a>
            <a href="{% url 'venues_app:venue_detail' review.venue.slug %}" class="cw-action-btn cw-secondary" title="Visit Venue">
                <i class="fas fa-external-link-alt"></i>
            </a>
        {% elif notification.related_object_type == 'Review' %}
            <a href="{% url 'review_app:provider_review_detail' review.id %}" class="cw-action-btn cw-primary" title="View Review">
                <i class="fas fa-eye"></i>
            </a>
            {% if not review.response %}
                <a href="{% url 'review_app:provider_respond_to_review' review.id %}" class="cw-action-btn cw-success" title="Respond">
                    <i class="fas fa-reply"></i>
                </a>
            {% endif %}
        {% endif %}

        <!-- Mark as read/unread -->
        <form method="post" action="{% if notification.read_status == 'unread' %}{% url 'notifications_app:mark_notification_read' notification.id %}{% else %}{% url 'notifications_app:mark_notification_unread' notification.id %}{% endif %}" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="cw-action-btn {% if notification.read_status == 'unread' %}cw-info{% else %}cw-outline{% endif %}" title="{% if notification.read_status == 'unread' %}Mark as Read{% else %}Mark as Unread{% endif %}">
                <i class="fas {% if notification.read_status == 'unread' %}fa-envelope-open{% else %}fa-envelope{% endif %}"></i>
            </button>
        </form>
    </div>
</div>

<style>
/* CozyWish Design System - Review Notification Badge */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.cw-review-notification-badge {
    background: white;
    border: 2px solid var(--cw-brand-accent);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: var(--cw-shadow-md);
    position: relative;
    overflow: hidden;
    font-family: var(--cw-font-primary);
}

.cw-review-notification-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(47, 22, 15, 0.15);
    border-color: var(--cw-brand-primary);
}

.cw-review-notification-badge[data-notification-type="review"]:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
}

.cw-notification-badge-main {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.cw-badge-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    position: relative;
    flex-shrink: 0;
    box-shadow: var(--cw-shadow-sm);
}

.cw-badge-icon.cw-review-icon {
    background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    color: white;
}

.cw-response-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: #059669;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.625rem;
    color: white;
    border: 2px solid white;
}

.cw-badge-content {
    flex: 1;
    min-width: 0;
}

.cw-badge-title {
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    font-family: var(--cw-font-heading);
}

.cw-badge-details {
    margin-bottom: 0.75rem;
}

.cw-response-details, .cw-review-details {
    background: var(--cw-accent-light);
    border-radius: 0.5rem;
    padding: 0.75rem;
    border-left: 3px solid var(--cw-brand-primary);
}

.cw-venue-name {
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.cw-provider-name, .cw-customer-name {
    color: var(--cw-neutral-600);
    font-size: 0.8125rem;
    margin-bottom: 0.5rem;
}

.cw-original-rating, .cw-review-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cw-stars {
    color: #d97706;
    font-size: 0.875rem;
}

.cw-rating-text {
    font-size: 0.75rem;
    color: var(--cw-neutral-600);
    font-weight: 500;
}

.cw-badge-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.cw-time-ago {
    color: var(--cw-neutral-600);
    font-size: 0.75rem;
    font-weight: 500;
}

.cw-unread-indicator {
    background: var(--cw-brand-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.625rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cw-badge-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 0.75rem;
    border-top: 1px solid var(--cw-brand-accent);
}

.cw-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--cw-shadow-sm);
}

.cw-action-btn.cw-primary {
    background: var(--cw-brand-primary);
    color: white;
}

.cw-action-btn.cw-primary:hover {
    background: var(--cw-brand-light);
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-secondary {
    background: var(--cw-neutral-600);
    color: white;
}

.cw-action-btn.cw-secondary:hover {
    background: var(--cw-neutral-700);
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-success {
    background: #059669;
    color: white;
}

.cw-action-btn.cw-success:hover {
    background: #047857;
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-info {
    background: #0284c7;
    color: white;
}

.cw-action-btn.cw-info:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-1px);
}

.cw-action-btn.cw-outline {
    background: white;
    color: var(--cw-neutral-600);
    border: 1px solid var(--cw-brand-accent);
}

.cw-action-btn.cw-outline:hover {
    background: var(--cw-accent-light);
    color: var(--cw-brand-primary);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 576px) {
    .cw-review-notification-badge {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .cw-notification-badge-main {
        gap: 0.75rem;
    }

    .cw-badge-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .cw-badge-title {
        font-size: 0.875rem;
    }

    .cw-venue-name {
        font-size: 0.8125rem;
    }

    .cw-response-details, .cw-review-details {
        padding: 0.5rem;
    }

    .cw-badge-actions {
        gap: 0.375rem;
    }

    .cw-action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.6875rem;
    }

    .cw-badge-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

@media (max-width: 768px) {
    .cw-review-notification-badge {
        border-radius: 0.75rem;
    }

    .cw-badge-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>