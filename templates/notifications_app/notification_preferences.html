{% extends 'notifications_app/base_notifications.html' %}

{% block title %}Notification Preferences - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* CozyWish Notification Preferences - Professional Design */

    /* Page Header */
    .preferences-header {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
        position: relative;
    }

    .preferences-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-secondary-950);
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        line-height: 1.2;
    }

    .preferences-icon {
        background: var(--cw-gradient-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.3s ease;
    }

    .preferences-icon:hover {
        transform: rotate(15deg) scale(1.05);
        box-shadow: var(--cw-shadow-md);
    }

    .preferences-subtitle {
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
        font-size: 1.1rem;
        margin-bottom: 0;
        font-weight: 400;
        line-height: 1.4;
    }

    .preference-category {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.3s ease;
        position: relative;
    }

    .preference-category:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .category-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-secondary-950);
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-neutral-200);
    }

    .category-icon {
        background: var(--cw-gradient-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.3s ease;
    }

    .category-icon:hover {
        transform: rotate(10deg) scale(1.1);
        box-shadow: var(--cw-shadow-md);
    }

    .preference-item {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, white 100%);
        border: 2px solid rgba(0, 0, 0, 0.15);
        border-radius: 1.25rem;
        padding: 2rem;
        margin-bottom: 1.5rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
    }

    .preference-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: black;
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .preference-item:hover {
        transform: translateY(-4px) scale(1.01);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        border-color: black;
    }

    .preference-item:hover::before {
        transform: scaleX(1);
    }

    .preference-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        gap: 2rem;
    }

    .preference-info h4 {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        margin-bottom: 0.75rem;
        font-size: 1.25rem;
        line-height: 1.3;
    }

    .preference-info p {
        color: rgba(0, 0, 0, 0.75);
        margin: 0;
        font-size: 1rem;
        line-height: 1.5;
        font-weight: 400;
    }

    .preference-controls {
        display: flex;
        gap: 2.5rem;
        align-items: center;
        flex-shrink: 0;
    }

    .control-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem;
        border-radius: 1rem;
        transition: all 0.3s ease;
    }

    .control-group:hover {
        background: rgba(0, 0, 0, 0.03);
    }

    .control-label {
        font-family: var(--font-primary);
        font-weight: 600;
        color: black;
        font-size: 1rem;
        text-align: center;
    }

    /* Enhanced Professional Toggle Switches */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 70px;
        height: 40px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
        border: 3px solid black;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 40px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .toggle-slider:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        transform: scale(1.05);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 28px;
        width: 28px;
        left: 3px;
        bottom: 3px;
        background: linear-gradient(135deg, black 0%, rgba(0, 0, 0, 0.9) 100%);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    input:checked + .toggle-slider {
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        border-color: #28a745;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(30px);
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    .toggle-switch:active .toggle-slider:before {
        width: 32px;
    }

    .action-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        padding: 2.5rem 0;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, transparent 100%);
        border-radius: 1.5rem;
        margin-top: 2rem;
    }

    .action-btn {
        background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
        color: black;
        border: 3px solid black;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-family: var(--font-primary);
        font-weight: 600;
        font-size: 1.05rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-decoration: none;
        cursor: pointer;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
        color: black;
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    }

    .action-btn.primary {
        background: linear-gradient(135deg, black 0%, rgba(0, 0, 0, 0.9) 100%);
        color: white;
        border-color: black;
    }

    .action-btn.primary:hover {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, black 100%);
        color: white;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 768px) {
        .preferences-title {
            font-size: 2.2rem;
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .preferences-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .preference-header {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .preference-controls {
            justify-content: center;
            gap: 2rem;
        }

        .control-group {
            padding: 0.75rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .action-btn {
            padding: 0.8rem 2rem;
            font-size: 1rem;
        }

        .preference-category {
            padding: 2rem 1.5rem;
        }

        .preference-item {
            padding: 1.5rem;
        }

        .category-title {
            font-size: 1.3rem;
        }

        .category-icon {
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
        }
    }

    @media (max-width: 480px) {
        .preferences-header {
            padding: 2rem 1.5rem;
        }

        .preferences-title {
            font-size: 2rem;
        }

        .preference-controls {
            gap: 1.5rem;
        }

        .toggle-switch {
            width: 60px;
            height: 35px;
        }

        .toggle-slider:before {
            height: 25px;
            width: 25px;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(25px);
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{% url 'notifications_app:notification_list' %}">
                <i class="fas fa-bell me-1"></i>Notifications
            </a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-cog me-1"></i>Preferences
        </li>
    </ol>
</nav>

<!-- Professional Header -->
<div class="preferences-header">
    <div class="preferences-title">
        <div class="preferences-icon">
            <i class="fas fa-cog"></i>
        </div>
        <div>
            <h1 class="mb-0">Notification Preferences</h1>
            <p class="preferences-subtitle">Customize how and when you receive notifications</p>
        </div>
    </div>
</div>

<!-- Preferences Form -->
<form method="post" id="preferencesForm">
    {% csrf_token %}

    <!-- Booking Notifications -->
    <div class="preference-category">
        <div class="category-title">
            <div class="category-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            Booking Notifications
        </div>

        <div class="preference-item">
            <div class="preference-header">
                <div class="preference-info">
                    <h4>Booking Confirmations</h4>
                    <p>Get notified when your bookings are confirmed or updated</p>
                </div>
                <div class="preference-controls">
                    <div class="control-group">
                        <span class="control-label">Email</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_booking_email"
                                   {% if notification_types.booking.email_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Dashboard</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_booking_dashboard"
                                   {% if notification_types.booking.dashboard_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Notifications -->
    <div class="preference-category">
        <div class="category-title">
            <div class="category-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            Payment Notifications
        </div>

        <div class="preference-item">
            <div class="preference-header">
                <div class="preference-info">
                    <h4>Payment Confirmations</h4>
                    <p>Receive updates about successful payments and receipts</p>
                </div>
                <div class="preference-controls">
                    <div class="control-group">
                        <span class="control-label">Email</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_payment_email"
                                   {% if notification_types.payment.email_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Dashboard</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_payment_dashboard"
                                   {% if notification_types.payment.dashboard_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Notifications -->
    <div class="preference-category">
        <div class="category-title">
            <div class="category-icon">
                <i class="fas fa-star"></i>
            </div>
            Review Notifications
        </div>

        <div class="preference-item">
            <div class="preference-header">
                <div class="preference-info">
                    <h4>Review Updates</h4>
                    <p>Get notified about new reviews and responses</p>
                </div>
                <div class="preference-controls">
                    <div class="control-group">
                        <span class="control-label">Email</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_review_email"
                                   {% if notification_types.review.email_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Dashboard</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_review_dashboard"
                                   {% if notification_types.review.dashboard_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Announcement Notifications -->
    <div class="preference-category">
        <div class="category-title">
            <div class="category-icon">
                <i class="fas fa-bullhorn"></i>
            </div>
            Announcements
        </div>

        <div class="preference-item">
            <div class="preference-header">
                <div class="preference-info">
                    <h4>Platform Updates</h4>
                    <p>Stay informed about important platform announcements</p>
                </div>
                <div class="preference-controls">
                    <div class="control-group">
                        <span class="control-label">Email</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_announcement_email"
                                   {% if notification_types.announcement.email_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Dashboard</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_announcement_dashboard"
                                   {% if notification_types.announcement.dashboard_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Notifications -->
    <div class="preference-category">
        <div class="category-title">
            <div class="category-icon">
                <i class="fas fa-cog"></i>
            </div>
            System Notifications
        </div>

        <div class="preference-item">
            <div class="preference-header">
                <div class="preference-info">
                    <h4>System Updates</h4>
                    <p>Receive notifications about system maintenance and updates</p>
                </div>
                <div class="preference-controls">
                    <div class="control-group">
                        <span class="control-label">Email</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_system_email"
                                   {% if notification_types.system.email_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Dashboard</span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="preference_system_dashboard"
                                   {% if notification_types.system.dashboard_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <button type="submit" class="action-btn primary">
            <i class="fas fa-save"></i>
            Save Preferences
        </button>
        <a href="{% url 'notifications_app:notification_list' %}" class="action-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Notifications
        </a>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to preference items
    const preferenceItems = document.querySelectorAll('.preference-item');
    preferenceItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Form submission with loading state
    const form = document.getElementById('preferencesForm');
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;

        // Re-enable after a delay (form will submit)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
});
</script>
{% endblock %}
