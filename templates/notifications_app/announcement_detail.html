{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}{{ announcement.title }} - CozyWish{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Announcement Detail - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Announcement Detail Styles */
    .announcement-detail-container {
        background: var(--cw-accent-light);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .announcement-card {
        background: white;
        border: 1px solid rgba(47, 22, 15, 0.1);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .announcement-header {
        background: var(--cw-gradient-card);
        padding: 2rem;
        border-bottom: 1px solid rgba(47, 22, 15, 0.1);
        position: relative;
    }

    .announcement-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="announcement-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23announcement-pattern)"/></svg>') repeat;
        opacity: 0.3;
        z-index: 1;
    }

    .announcement-header-content {
        position: relative;
        z-index: 2;
    }

    .announcement-icon {
        width: 60px;
        height: 60px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .announcement-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .announcement-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .announcement-meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .announcement-body {
        padding: 2rem;
    }

    .announcement-content {
        font-size: 1.1rem;
        line-height: 1.7;
        color: var(--cw-neutral-700);
        margin-bottom: 2rem;
    }

    .announcement-content p {
        margin-bottom: 1.5rem;
    }

    .announcement-content p:last-child {
        margin-bottom: 0;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .announcement-detail-container {
            padding: 1rem 0;
        }

        .announcement-header {
            padding: 1.5rem;
        }

        .announcement-title {
            font-size: 1.5rem;
        }

        .announcement-body {
            padding: 1.5rem;
        }

        .announcement-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<div class="announcement-detail-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Announcement Card -->
                <div class="announcement-card">
                    <!-- Header -->
                    <div class="announcement-header">
                        <div class="announcement-header-content">
                            <div class="announcement-icon">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <h1 class="announcement-title">{{ announcement.title }}</h1>
                            <div class="announcement-meta">
                                <div class="announcement-meta-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>{{ announcement.created_at|date:'F d, Y' }}</span>
                                </div>
                                <div class="announcement-meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ announcement.created_at|date:'g:i A' }}</span>
                                </div>
                                {% if announcement.target_audience %}
                                <div class="announcement-meta-item">
                                    <i class="fas fa-users"></i>
                                    <span>{{ announcement.get_target_audience_display }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Body -->
                    <div class="announcement-body">
                        <div class="announcement-content">
                            {{ announcement.announcement_text|linebreaks }}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-3 justify-content-center">
                    <a href="{% url 'notifications_app:notification_list' %}" class="btn-cw-secondary">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Notifications</span>
                    </a>
                    {% if user.role == 'customer' %}
                    <a href="{% url 'venues_app:venue_list' %}" class="btn-cw-primary">
                        <i class="fas fa-search"></i>
                        <span>Discover Venues</span>
                    </a>
                    {% elif user.role == 'service_provider' %}
                    <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn-cw-primary">
                        <i class="fas fa-chart-line"></i>
                        <span>Provider Dashboard</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
