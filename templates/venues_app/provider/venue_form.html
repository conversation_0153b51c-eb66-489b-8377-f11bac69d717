{% extends 'base.html' %}

{% block title %}{{ title }} - CozyWish{% endblock %}

{% load widget_tweaks %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Form */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venue Form Section */
    .venue-form-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    .venue-form-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Page Header */
    .page-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Cards */
    .card-cw {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-brand {
        border: 2px solid var(--cw-brand-primary);
        background: var(--cw-gradient-card);
        box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
    }

    .card-header-cw {
        background: var(--cw-gradient-card-subtle);
        border-bottom: 1px solid var(--cw-brand-accent);
        padding: 1.5rem;
    }

    .card-header-cw h5 {
        color: var(--cw-brand-primary);
        margin-bottom: 0;
        font-weight: 600;
    }

    .card-body-cw {
        padding: 2rem;
    }

    /* Form Controls */
    .form-label-cw {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-select-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback-cw {
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    /* Section Headers */
    .section-header {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin: 2rem 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Form Actions */
    .form-actions {
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--cw-neutral-200);
    }

    /* Current Image Preview */
    .current-image-preview {
        margin-top: 1rem;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .current-image-preview img {
        border-radius: 0.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .venue-form-section {
            padding: 2rem 0;
        }

        .page-title {
            font-size: 2rem;
        }

        .card-body-cw {
            padding: 1.5rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-form-section">
    <div class="container venue-form-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">{{ title }}</h1>
            <p class="page-subtitle">{% if 'Edit' in title %}Update your venue information{% else %}Add a new venue to your profile{% endif %}</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card-cw mb-4">
                    <div class="card-header-cw">
                        <h5><i class="fas fa-store me-2"></i>Venue Information</h5>
                    </div>
                    <div class="card-body-cw">
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}

                            <div class="mb-3">
                                <label for="{{ form.venue_name.id_for_label }}" class="form-label form-label-cw">Venue Name</label>
                                <input type="text" name="{{ form.venue_name.name }}" id="{{ form.venue_name.id_for_label }}" class="form-control form-control-cw {% if form.venue_name.errors %}is-invalid{% endif %}" value="{{ form.venue_name.value|default:'' }}" placeholder="Enter your venue name">
                                {% if form.venue_name.errors %}
                                <div class="invalid-feedback invalid-feedback-cw">
                                    {{ form.venue_name.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.categories.id_for_label }}" class="form-label form-label-cw">Categories</label>
                                {{ form.categories|add_class:"form-select form-select-cw" }}
                                {% if form.categories.errors %}
                                <div class="invalid-feedback invalid-feedback-cw d-block">
                                    {{ form.categories.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.tags.id_for_label }}" class="form-label form-label-cw">Tags</label>
                                {{ form.tags|add_class:"form-select form-select-cw" }}
                                {% if form.tags.errors %}
                                <div class="invalid-feedback invalid-feedback-cw d-block">
                                    {{ form.tags.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text form-text-cw">Hold Ctrl (or Cmd on Mac) to select multiple tags.</div>
                            </div>

                            <h5 class="section-header"><i class="fas fa-map-marker-alt me-2"></i>Location Information</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.state.id_for_label }}" class="form-label form-label-cw">State</label>
                                    {{ form.state|add_class:"form-select form-select-cw" }}
                                    {% if form.state.errors %}
                                    <div class="invalid-feedback invalid-feedback-cw d-block">
                                        {{ form.state.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.county.id_for_label }}" class="form-label form-label-cw">County</label>
                                    {{ form.county|add_class:"form-control form-control-cw" }}
                                    {% if form.county.errors %}
                                    <div class="invalid-feedback invalid-feedback-cw d-block">
                                        {{ form.county.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label form-label-cw">City</label>
                                {{ form.city|add_class:"form-control form-control-cw" }}
                                {% if form.city.errors %}
                                <div class="invalid-feedback invalid-feedback-cw d-block">
                                    {{ form.city.errors }}
                                </div>
                                {% endif %}
                                <input type="hidden" name="us_city_id" id="id_us_city_id">
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.street_number.id_for_label }}" class="form-label form-label-cw">Street Number</label>
                                    <input type="text" name="{{ form.street_number.name }}" id="{{ form.street_number.id_for_label }}" class="form-control form-control-cw {% if form.street_number.errors %}is-invalid{% endif %}" value="{{ form.street_number.value|default:'' }}" placeholder="123">
                                    {% if form.street_number.errors %}
                                    <div class="invalid-feedback invalid-feedback-cw">
                                        {{ form.street_number.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-8 mb-3">
                                    <label for="{{ form.street_name.id_for_label }}" class="form-label form-label-cw">Street Name</label>
                                    <input type="text" name="{{ form.street_name.name }}" id="{{ form.street_name.id_for_label }}" class="form-control form-control-cw {% if form.street_name.errors %}is-invalid{% endif %}" value="{{ form.street_name.value|default:'' }}" placeholder="Main Street">
                                    {% if form.street_name.errors %}
                                    <div class="invalid-feedback invalid-feedback-cw">
                                        {{ form.street_name.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.short_description.id_for_label }}" class="form-label form-label-cw">About Your Venue</label>
                                <textarea name="{{ form.short_description.name }}" id="{{ form.short_description.id_for_label }}" class="form-control form-control-cw {% if form.short_description.errors %}is-invalid{% endif %}" rows="5" placeholder="Describe your venue, services, and what makes it special...">{{ form.short_description.value|default:'' }}</textarea>
                                {% if form.short_description.errors %}
                                <div class="invalid-feedback invalid-feedback-cw">
                                    {{ form.short_description.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <h5 class="section-header"><i class="fas fa-image me-2"></i>Featured Image</h5>

                            <div class="mb-3">
                                <label for="{{ form.main_image.id_for_label }}" class="form-label form-label-cw">Upload Featured Image</label>
                                <input type="file" name="{{ form.main_image.name }}" id="{{ form.main_image.id_for_label }}" class="form-control form-control-cw {% if form.main_image.errors %}is-invalid{% endif %}" accept="image/jpeg,image/jpg,image/png,image/webp" data-preview="true" data-max-size="5242880" data-min-width="300" data-min-height="200">
                                {% if form.main_image.errors %}
                                <div class="invalid-feedback invalid-feedback-cw">
                                    {{ form.main_image.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text form-text-cw">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Upload a high-quality featured image for your venue (JPEG, PNG, WebP - max 5MB, min 300×200px). 
                                    <strong>WebP recommended</strong> for best quality and smaller file sizes. 
                                    Images are automatically optimized and EXIF data removed for privacy.
                                </div>
                                {% if form.instance.main_image %}
                                <div class="current-image-preview">
                                    <small class="text-muted fw-bold">Current image:</small><br>
                                    <img src="{{ form.instance.main_image.url }}" alt="Current main image" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                </div>
                                {% endif %}
                            </div>

                            <div class="form-actions d-flex flex-column flex-md-row gap-3 justify-content-md-end">
                                <a href="{% url 'venues_app:provider_venues' %}" class="btn btn-cw-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-cw-primary">
                                    <i class="fas fa-save me-2"></i>Save Venue
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card-cw mb-4">
                    <div class="card-header-cw">
                        <h5><i class="fas fa-lightbulb me-2"></i>Guidelines</h5>
                    </div>
                    <div class="card-body-cw">
                        <div class="mb-4">
                            <h6 class="mb-3 text-brand-cw fw-bold">Venue Information</h6>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2 d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Provide an accurate and descriptive venue name</span>
                                </li>
                                <li class="mb-2 d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Select the most appropriate category for your venue</span>
                                </li>
                                <li class="mb-2 d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Add relevant tags to help customers find your venue</span>
                                </li>
                                <li class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Write a detailed description of your venue and services</span>
                                </li>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h6 class="mb-3 text-brand-cw fw-bold">Location Information</h6>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2 d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Enter your complete and accurate address</span>
                                </li>
                                <li class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-brand-cw me-2 mt-1"></i>
                                    <span>Make sure your location is easy to find for customers</span>
                                </li>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h6 class="mb-3 text-brand-cw fw-bold">Next Steps</h6>
                            <p class="text-neutral-cw mb-3">After saving your venue, you'll need to:</p>
                            <ol class="mb-0 text-neutral-cw">
                                <li class="mb-2">Add additional venue images (up to 7 total)</li>
                                <li class="mb-2">Set your opening hours</li>
                                <li class="mb-2">Add services you offer</li>
                                <li>Add team members (optional)</li>
                            </ol>
                        </div>

                        {% if form.instance.pk %}
                        <div class="d-grid">
                            <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-images me-2"></i>Manage Images
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Professional Tips Card -->
                <div class="card-cw-brand">
                    <div class="card-body-cw text-center">
                        <i class="fas fa-spa fa-3x text-brand-cw mb-3"></i>
                        <h6 class="text-brand-cw fw-bold mb-2">Professional Tip</h6>
                        <p class="text-neutral-cw mb-0 small">High-quality photos and detailed descriptions help customers choose your venue. Take time to showcase what makes your business special!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // County and city are now text inputs, no dynamic loading needed

        // Add some interactive feedback for form fields
        const formControls = document.querySelectorAll('.form-control-cw, .form-select-cw');

        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.style.transform = 'translateY(-1px)';
            });

            control.addEventListener('blur', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>

<style>
    /* Additional utility classes */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
</style>
{% endblock %}