{% extends 'venues_app/base_venues.html' %}

{% block title %}{{ venue.name }} - Provider Dashboard - CozyWish{% endblock %}

{% block venues_extra_css %}
<style>
    /* CozyWish Design System - Enhanced Provider Dashboard */
    .dashboard-section {
        padding: 2rem 0;
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1.5rem;
    }

    /* Enhanced Dashboard Header with Progress */
    .dashboard-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-neutral-200);
    }

    .venue-progress-section {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 2rem;
        align-items: center;
        margin-bottom: 2rem;
    }

    /* Circular Progress Indicator */
    .progress-circle-container {
        position: relative;
        width: 120px;
        height: 120px;
    }

    .progress-circle {
        width: 120px;
        height: 120px;
        transform: rotate(-90deg);
    }

    .progress-circle-bg {
        fill: none;
        stroke: var(--cw-neutral-200);
        stroke-width: 8;
    }

    .progress-circle-fill {
        fill: none;
        stroke: var(--cw-brand-primary);
        stroke-width: 8;
        stroke-linecap: round;
        stroke-dasharray: 0 565;
        transition: stroke-dasharray 0.8s ease-out;
    }

    .progress-percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
    }

    .progress-label {
        text-align: center;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-weight: 600;
    }

    /* Quick Stats Grid */
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-light) 100%);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
    }

    .stat-label {
        font-size: 0.75rem;
        color: var(--cw-neutral-600);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Tabbed Interface */
    .dashboard-tabs {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-neutral-200);
        overflow: hidden;
    }

    .tab-navigation {
        display: flex;
        background: var(--cw-gradient-card-subtle);
        border-bottom: 2px solid var(--cw-brand-accent);
        overflow-x: auto;
    }

    .tab-button {
        flex: 1;
        padding: 1.25rem 1.5rem;
        border: none;
        background: transparent;
        color: var(--cw-neutral-600);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        min-width: 140px;
    }

    .tab-button:hover {
        background: rgba(47, 22, 15, 0.05);
        color: var(--cw-brand-primary);
    }

    .tab-button.active {
        background: var(--cw-brand-primary);
        color: white;
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--cw-brand-primary);
    }

    .tab-content {
        padding: 2rem;
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Status Badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 1rem;
    }

    .status-badge.approved {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .status-badge.pending {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        color: white;
    }

    .status-badge.rejected {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .status-badge.draft {
        background: linear-gradient(135deg, var(--cw-neutral-500) 0%, var(--cw-neutral-600) 100%);
        color: white;
    }

    /* Quick Actions */
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .quick-action-btn.secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    .quick-action-btn.secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
    }

    /* Section Cards within tabs */
    .section-card {
        background: var(--cw-gradient-card-subtle);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Completion Checklist */
    .completion-checklist {
        list-style: none;
        padding: 0;
    }

    .checklist-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: white;
        border-radius: 0.5rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .checklist-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 0.75rem;
    }

    .checklist-icon.completed {
        background: #10b981;
        color: white;
    }

    .checklist-icon.incomplete {
        background: var(--cw-neutral-300);
        color: var(--cw-neutral-600);
    }

    .checklist-text {
        flex: 1;
    }

    .checklist-action {
        margin-left: auto;
    }

    /* Preview Window */
    .preview-window {
        background: white;
        border-radius: 0.75rem;
        border: 2px solid var(--cw-brand-accent);
        padding: 1.5rem;
        max-height: 500px;
        overflow-y: auto;
    }

    .preview-header {
        text-align: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .venue-progress-section {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .tab-navigation {
            flex-direction: column;
        }

        .tab-button {
            min-width: auto;
        }

        .quick-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block venues_content %}
<div class="dashboard-section">
    <div class="dashboard-container">
        <!-- Enhanced Dashboard Header with Progress Indicator -->
        <div class="dashboard-header">
            <div class="venue-progress-section">
                <div>
                    <h1 class="dashboard-title">{{ venue.venue_name }}</h1>
                    <p class="dashboard-subtitle">
                        <i class="fas fa-map-marker-alt me-2"></i>{{ venue.full_address }}
                    </p>
                    <div class="status-badge {% if venue.approval_status == 'approved' %}approved{% elif venue.approval_status == 'pending' %}pending{% elif venue.approval_status == 'rejected' %}rejected{% else %}draft{% endif %}">
                        <i class="fas {% if venue.approval_status == 'approved' %}fa-check-circle{% elif venue.approval_status == 'pending' %}fa-clock{% elif venue.approval_status == 'rejected' %}fa-times-circle{% else %}fa-edit{% endif %} me-2"></i>
                        {{ venue.get_approval_status_display }}
                    </div>
                </div>
                
                <!-- Circular Progress Indicator -->
                <div class="text-center">
                    <div class="progress-circle-container">
                        <svg class="progress-circle" viewBox="0 0 200 200">
                            <circle class="progress-circle-bg" cx="100" cy="100" r="90"></circle>
                            <circle class="progress-circle-fill" cx="100" cy="100" r="90" 
                                    data-percentage="{{ venue.completeness_score|default:0 }}"></circle>
                        </svg>
                        <div class="progress-percentage">{{ venue.completeness_score|default:0 }}%</div>
                    </div>
                    <div class="progress-label">Setup Complete</div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ venue.services.count }}</div>
                    <div class="stat-label">Services</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ venue.images.count }}</div>
                    <div class="stat-label">Images</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ venue.faqs.count }}</div>
                    <div class="stat-label">FAQs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ venue.reviews.count }}</div>
                    <div class="stat-label">Reviews</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="{% url 'venues_app:venue_edit' %}" class="quick-action-btn">
                    <i class="fas fa-edit"></i> Quick Edit
                </a>
                <a href="{% url 'venues_app:manage_venue_images' %}" class="quick-action-btn secondary">
                    <i class="fas fa-camera"></i> Add Images
                </a>
                <a href="{% url 'venues_app:manage_faqs' %}" class="quick-action-btn secondary">
                    <i class="fas fa-question-circle"></i> Manage FAQs
                </a>
                <a href="{% url 'venues_app:manage_services' %}" class="quick-action-btn secondary">
                    <i class="fas fa-spa"></i> Add Services
                </a>
                {% if venue.approval_status == 'approved' %}
                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="quick-action-btn secondary">
                    <i class="fas fa-eye"></i> View Public
                </a>
                {% endif %}
            </div>
        </div>

        <!-- Enhanced Tabbed Interface -->
        <div class="dashboard-tabs">
            <nav class="tab-navigation">
                <button class="tab-button active" data-tab="overview">
                    <i class="fas fa-tachometer-alt"></i> Overview
                </button>
                {% if venue.approval_status != 'approved' or approval_timeline|length > 1 %}
                <button class="tab-button" data-tab="approval">
                    <i class="fas fa-gavel"></i> Approval
                </button>
                {% endif %}
                <button class="tab-button" data-tab="information">
                    <i class="fas fa-info-circle"></i> Information
                </button>
                <button class="tab-button" data-tab="gallery">
                    <i class="fas fa-images"></i> Gallery
                </button>
                <button class="tab-button" data-tab="services">
                    <i class="fas fa-spa"></i> Services
                </button>
                <button class="tab-button" data-tab="faqs">
                    <i class="fas fa-question-circle"></i> FAQs
                </button>
                <button class="tab-button" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </button>
            </nav>

            <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="fas fa-tasks"></i> Setup Progress
                                </h3>
                                <ul class="completion-checklist">
                                    <li class="checklist-item">
                                        <div class="checklist-icon {% if venue.venue_name %}completed{% else %}incomplete{% endif %}">
                                            <i class="fas {% if venue.venue_name %}fa-check{% else %}fa-times{% endif %}"></i>
                                        </div>
                                        <div class="checklist-text">Basic venue information</div>
                                        {% if not venue.venue_name %}
                                        <div class="checklist-action">
                                            <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-sm btn-cw-primary">Complete</a>
                                        </div>
                                        {% endif %}
                                    </li>
                                    <li class="checklist-item">
                                        <div class="checklist-icon {% if venue.images.count %}completed{% else %}incomplete{% endif %}">
                                            <i class="fas {% if venue.images.count %}fa-check{% else %}fa-times{% endif %}"></i>
                                        </div>
                                        <div class="checklist-text">Upload venue images ({{ venue.images.count }}/5)</div>
                                        {% if venue.images.count < 3 %}
                                        <div class="checklist-action">
                                            <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-sm btn-cw-primary">Add Images</a>
                                        </div>
                                        {% endif %}
                                    </li>
                                    <li class="checklist-item">
                                        <div class="checklist-icon {% if venue.services.count %}completed{% else %}incomplete{% endif %}">
                                            <i class="fas {% if venue.services.count %}fa-check{% else %}fa-times{% endif %}"></i>
                                        </div>
                                        <div class="checklist-text">Add services ({{ venue.services.count }})</div>
                                        {% if not venue.services.count %}
                                        <div class="checklist-action">
                                            <a href="{% url 'venues_app:manage_services' %}" class="btn btn-sm btn-cw-primary">Add Services</a>
                                        </div>
                                        {% endif %}
                                    </li>
                                    <li class="checklist-item">
                                        <div class="checklist-icon {% if venue.faqs.count %}completed{% else %}incomplete{% endif %}">
                                            <i class="fas {% if venue.faqs.count %}fa-check{% else %}fa-times{% endif %}"></i>
                                        </div>
                                        <div class="checklist-text">Add FAQs ({{ venue.faqs.count }}/5)</div>
                                        {% if venue.faqs.count < 3 %}
                                        <div class="checklist-action">
                                            <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-sm btn-cw-primary">Add FAQs</a>
                                        </div>
                                        {% endif %}
                                    </li>
                                    <li class="checklist-item">
                                        <div class="checklist-icon {% if venue.phone and venue.email %}completed{% else %}incomplete{% endif %}">
                                            <i class="fas {% if venue.phone and venue.email %}fa-check{% else %}fa-times{% endif %}"></i>
                                        </div>
                                        <div class="checklist-text">Contact information</div>
                                        {% if not venue.phone or not venue.email %}
                                        <div class="checklist-action">
                                            <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-sm btn-cw-primary">Update</a>
                                        </div>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="fas fa-eye"></i> Live Preview
                                </h3>
                                <div class="preview-window">
                                    <div class="preview-header">
                                        <h4>{{ venue.venue_name }}</h4>
                                        <p class="text-muted">{{ venue.full_address }}</p>
                                    </div>
                                    {% if venue.images.first %}
                                        <img src="{{ venue.images.first.image.url }}" alt="{{ venue.venue_name }}" 
                                             class="img-fluid rounded mb-2" style="max-height: 150px; width: 100%; object-fit: cover;">
                                    {% endif %}
                                    <p>{{ venue.short_description|default:"No description available" }}</p>
                                </div>
                                <div class="mt-3">
                                    <a href="{% url 'venues_app:venue_preview' %}" class="btn btn-cw-accent w-100">
                                        <i class="fas fa-external-link-alt me-2"></i> Full Preview
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Approval Workflow Components -->
                {% if venue.approval_status != 'approved' or approval_timeline|length > 1 %}
                <div class="tab-pane" id="approval">
                    <!-- Approval Timeline -->
                    {% include 'venues_app/components/approval_timeline.html' %}
                    
                    <!-- Approval Impact Preview -->
                    {% include 'venues_app/components/approval_impact_preview.html' %}
                    
                    <!-- Approval Guidance -->
                    {% include 'venues_app/components/approval_guidance.html' %}
                </div>
                {% endif %}

                <!-- Information Tab -->
                <div class="tab-pane" id="information">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i> Venue Information
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Venue Name</label>
                                    <p>{{ venue.venue_name|default:"Not set" }}</p>
                                </div>
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Description</label>
                                    <p>{{ venue.short_description|default:"No description" }}</p>
                                </div>
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Categories</label>
                                    <div>
                                        {% for category in venue.categories.all %}
                                            <span class="badge bg-primary me-1">{{ category.category_name }}</span>
                                        {% empty %}
                                            <span class="text-muted">No categories selected</span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Phone</label>
                                    <p>{{ venue.phone|default:"Not provided" }}</p>
                                </div>
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Email</label>
                                    <p>{{ venue.email|default:"Not provided" }}</p>
                                </div>
                                <div class="info-group mb-3">
                                    <label class="form-label fw-bold">Website</label>
                                    <p>
                                        {% if venue.website_url %}
                                            <a href="{{ venue.website_url }}" target="_blank">{{ venue.website_url }}</a>
                                        {% else %}
                                            Not provided
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-cw-primary">
                                <i class="fas fa-edit me-2"></i> Edit Information
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Gallery Tab -->
                <div class="tab-pane" id="gallery">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-images"></i> Venue Gallery ({{ venue.images.count }}/5)
                        </h3>
                        {% if venue.images.exists %}
                            <div class="row">
                                {% for image in venue.images.all %}
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <img src="{{ image.image.url }}" class="card-img-top" alt="{{ image.caption }}"
                                             style="height: 200px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <small class="text-muted">{{ image.caption|default:"No caption" }}</small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No images uploaded yet</h5>
                                <p class="text-muted">Add images to showcase your venue</p>
                            </div>
                        {% endif %}
                        <div class="mt-3">
                            <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-cw-primary">
                                <i class="fas fa-camera me-2"></i> Manage Images
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Services Tab -->
                <div class="tab-pane" id="services">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-spa"></i> Services ({{ venue.services.count }})
                        </h3>
                        {% if venue.services.exists %}
                            <div class="row">
                                {% for service in venue.services.all %}
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ service.service_name }}</h6>
                                            <p class="card-text">{{ service.description|truncatewords:15 }}</p>
                                            <p class="text-muted mb-0">${{ service.price }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No services added yet</h5>
                                <p class="text-muted">Add services to showcase what you offer</p>
                            </div>
                        {% endif %}
                        <div class="mt-3">
                            <a href="{% url 'venues_app:manage_services' %}" class="btn btn-cw-primary">
                                <i class="fas fa-plus me-2"></i> Manage Services
                            </a>
                        </div>
                    </div>
                </div>

                <!-- FAQs Tab -->
                <div class="tab-pane" id="faqs">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-question-circle"></i> Frequently Asked Questions ({{ venue.faqs.count }}/5)
                        </h3>
                        {% if venue.faqs.exists %}
                            <div class="accordion" id="faqAccordion">
                                {% for faq in venue.faqs.all %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#faq{{ forloop.counter }}">
                                            {{ faq.question }}
                                        </button>
                                    </h2>
                                    <div id="faq{{ forloop.counter }}" class="accordion-collapse collapse" 
                                         data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            {{ faq.answer }}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No FAQs added yet</h5>
                                <p class="text-muted">Add frequently asked questions to help customers</p>
                            </div>
                        {% endif %}
                        <div class="mt-3">
                            <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-cw-primary">
                                <i class="fas fa-plus me-2"></i> Manage FAQs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane" id="settings">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-cog"></i> Venue Settings
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Visibility Settings</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" {% if venue.show_faqs %}checked{% endif %}>
                                        <label class="form-check-label">Show FAQs</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" {% if venue.show_team_members %}checked{% endif %}>
                                        <label class="form-check-label">Show Team Members</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" {% if venue.show_social_media %}checked{% endif %}>
                                        <label class="form-check-label">Show Social Media Links</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Last Updated</label>
                                    <p class="small text-muted">
                                        Description: {{ venue.description_updated_at|date:"M d, Y"|default:"Never" }}<br>
                                        Contact: {{ venue.contact_updated_at|date:"M d, Y"|default:"Never" }}<br>
                                        Hours: {{ venue.hours_updated_at|date:"M d, Y"|default:"Never" }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'venues_app:manage_venue_visibility' %}" class="btn btn-cw-primary">
                                <i class="fas fa-cog me-2"></i> Manage Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
    
    // Animate progress circle
    const progressCircle = document.querySelector('.progress-circle-fill');
    if (progressCircle) {
        const percentage = progressCircle.getAttribute('data-percentage');
        const circumference = 2 * Math.PI * 90;
        const offset = circumference - (percentage / 100) * circumference;
        
        setTimeout(() => {
            progressCircle.style.strokeDasharray = `${circumference - offset} ${circumference}`;
        }, 500);
    }
});
</script>
{% endblock %}
