{% extends 'venues_app/base_venues.html' %}

{% block title %}{{ title }} - {{ venue.name }} - CozyWish{% endblock %}

{% load widget_tweaks %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <h1>{{ title }}</h1>
            <p class="lead">{% if 'Edit' in title %}Update service details{% else %}Add a new service{% endif %} for {{ venue.name }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:provider_venues' %}" class="text-decoration-none">My Venues</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="text-decoration-none">{{ venue.name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                </ol>
            </nav>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Service Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.service_title.id_for_label }}" class="form-label">Service Title</label>
                            <input type="text" name="{{ form.service_title.name }}" id="{{ form.service_title.id_for_label }}" class="form-control {% if form.service_title.errors %}is-invalid{% endif %}" value="{{ form.service_title.value|default:'' }}">
                            {% if form.service_title.errors %}
                            <div class="invalid-feedback">
                                {{ form.service_title.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.short_description.id_for_label }}" class="form-label">Description</label>
                            <textarea name="{{ form.short_description.name }}" id="{{ form.short_description.id_for_label }}" class="form-control {% if form.short_description.errors %}is-invalid{% endif %}" rows="4">{{ form.short_description.value|default:'' }}</textarea>
                            {% if form.short_description.errors %}
                            <div class="invalid-feedback">
                                {{ form.short_description.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.price_min.id_for_label }}" class="form-label">Minimum Price ($)</label>
                                <input type="number" name="{{ form.price_min.name }}" id="{{ form.price_min.id_for_label }}" class="form-control {% if form.price_min.errors %}is-invalid{% endif %}" value="{{ form.price_min.value|default:'' }}" step="0.01" min="0">
                                {% if form.price_min.errors %}
                                <div class="invalid-feedback">
                                    {{ form.price_min.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.price_max.id_for_label }}" class="form-label">Maximum Price ($) <span class="text-muted">(Optional)</span></label>
                                <input type="number" name="{{ form.price_max.name }}" id="{{ form.price_max.id_for_label }}" class="form-control {% if form.price_max.errors %}is-invalid{% endif %}" value="{{ form.price_max.value|default:'' }}" step="0.01" min="0">
                                {% if form.price_max.errors %}
                                <div class="invalid-feedback">
                                    {{ form.price_max.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">Duration (minutes)</label>
                            <input type="number" name="{{ form.duration_minutes.name }}" id="{{ form.duration_minutes.id_for_label }}" class="form-control {% if form.duration_minutes.errors %}is-invalid{% endif %}" value="{{ form.duration_minutes.value|default:'' }}" min="1">
                            {% if form.duration_minutes.errors %}
                            <div class="invalid-feedback">
                                {{ form.duration_minutes.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- is_active field removed as it's not in the ServiceForm -->

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Service Information</h6>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Provide a clear and descriptive service name
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Set accurate pricing and duration
                        </li>
                        <li>
                            <i class="fas fa-check-circle me-2"></i> Write a detailed description of what the service includes
                        </li>
                    </ul>

                    <h6 class="mb-3">Pricing Tips</h6>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Research competitor pricing in your area
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Consider offering discounts for new customers
                        </li>
                        <li>
                            <i class="fas fa-check-circle me-2"></i> Ensure your pricing reflects the quality and duration of service
                        </li>
                    </ul>

                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i> Services marked as "Active" will be visible to customers on your venue page.
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Information</h5>
                </div>
                <div class="card-body">
                    <h5 class="mb-2">{{ venue.name }}</h5>
                    <p class="mb-3">
                        <i class="fas fa-map-marker-alt me-1"></i> {{ venue.city }}, {{ venue.state }}
                    </p>
                    <p class="mb-3">
                        <i class="fas fa-spa me-1"></i> {{ venue.services.count }} services
                    </p>
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-arrow-left me-2"></i>Back to Venue
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block venues_extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Price validation
        const priceMinField = document.getElementById('{{ form.price_min.id_for_label }}');
        const priceMaxField = document.getElementById('{{ form.price_max.id_for_label }}');

        if (priceMinField && priceMaxField) {
            priceMaxField.addEventListener('change', function() {
                const priceMin = parseFloat(priceMinField.value);
                const priceMax = parseFloat(this.value);

                if (priceMax && priceMax < priceMin) {
                    this.setCustomValidity('Maximum price must be greater than minimum price');
                } else {
                    this.setCustomValidity('');
                }
            });

            priceMinField.addEventListener('change', function() {
                if (priceMaxField.value) {
                    const priceMin = parseFloat(this.value);
                    const priceMax = parseFloat(priceMaxField.value);

                    if (priceMax < priceMin) {
                        priceMaxField.setCustomValidity('Maximum price must be greater than minimum price');
                    } else {
                        priceMaxField.setCustomValidity('');
                    }
                }
            });
        }
    });
</script>
{% endblock %}