{% extends 'venues_app/base_venues.html' %}
{% load widget_tweaks i18n %}

{% block title %}{{ action }} Amenity - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <div>
                        <h1 class="display-font text-brand-cw mb-2">{{ action }} Amenity</h1>
                        <p class="lead text-neutral-cw mb-0">{% if action == 'Edit' %}Update{% else %}Add{% endif %} amenity for {{ venue.venue_name }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Amenity Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <h5 class="mb-0 text-brand-cw fw-bold">
                        <i class="fas fa-star me-2"></i>Amenity Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label class="form-label text-brand-cw fw-semibold" for="{{ form.amenity_type.id_for_label }}">
                                    {% trans "Amenity Type" %} <span class="text-danger">*</span>
                                </label>
                                {{ form.amenity_type|add_class:'form-control form-control-cw' }}
                                {% if form.amenity_type.help_text %}
                                    <div class="form-text text-neutral-cw">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.amenity_type.help_text }}
                                    </div>
                                {% endif %}
                                {% for error in form.amenity_type.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-4">
                                <label class="form-label text-brand-cw fw-semibold" for="{{ form.custom_name.id_for_label }}">
                                    {% trans "Custom Name" %}
                                </label>
                                {{ form.custom_name|add_class:'form-control form-control-cw' }}
                                {% if form.custom_name.help_text %}
                                    <div class="form-text text-neutral-cw">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.custom_name.help_text }}
                                    </div>
                                {% else %}
                                    <div class="form-text text-neutral-cw">
                                        <i class="fas fa-info-circle me-1"></i>Optional: Override the default amenity name
                                    </div>
                                {% endif %}
                                {% for error in form.custom_name.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label text-brand-cw fw-semibold" for="{{ form.description.id_for_label }}">
                                {% trans "Description" %}
                            </label>
                            {{ form.description|add_class:'form-control form-control-cw' }}
                            {% if form.description.help_text %}
                                <div class="form-text text-neutral-cw">
                                    <i class="fas fa-info-circle me-1"></i>{{ form.description.help_text }}
                                </div>
                            {% else %}
                                <div class="form-text text-neutral-cw">
                                    <i class="fas fa-info-circle me-1"></i>Provide additional details about this amenity
                                </div>
                            {% endif %}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-4">
                            <div class="bg-light-cw rounded-cw p-3">
                                <div class="form-check">
                                    {{ form.is_active|add_class:'form-check-input' }}
                                    <label class="form-check-label text-brand-cw fw-semibold" for="{{ form.is_active.id_for_label }}">
                                        <i class="fas fa-toggle-on me-2"></i>{% trans "Active (visible to customers)" %}
                                    </label>
                                    <div class="form-text text-neutral-cw mt-2">
                                        <i class="fas fa-info-circle me-1"></i>Active amenities are displayed to customers when viewing your venue
                                    </div>
                                </div>
                                {% for error in form.is_active.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-end">
                            <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-cw-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Amenity
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
