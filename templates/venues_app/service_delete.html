{% extends 'base.html' %}

{% block title %}Delete Service - {{ object.service_title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Delete */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Service wrapper */
    .service-wrapper {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--cw-font-primary);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-warning {
        border: 2px solid var(--cw-brand-accent);
        background: var(--cw-accent-light);
        box-shadow: var(--cw-shadow-md);
    }

    .card-cw-accent {
        border: 1px solid var(--cw-brand-accent);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-sm);
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
    }
    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    .btn-cw-danger {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-danger:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-1px);
    }

    /* Text Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
    .text-danger { color: var(--cw-brand-primary) !important; font-weight: 600; }

    /* Background Colors */
    .bg-light-cw { background-color: var(--cw-accent-light) !important; }

    /* Header styling */
    .service-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .service-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--cw-brand-primary);
    }

    .service-header p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Badge styling */
    .badge-cw {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    /* Alert styling */
    .alert-cw-warning {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    /* Icon styling */
    .icon-wrapper {
        background: var(--cw-accent-light);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        color: var(--cw-brand-primary);
    }

    .icon-wrapper.rounded-circle {
        border-radius: 50% !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="service-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="service-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2 class="text-danger">Delete Service</h2>
                        <p>Permanently remove this service from {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Warning Card -->
                <div class="card-cw-warning">
                    <div class="card-header bg-light-cw border-0 p-4">
                        <h5 class="mb-0 text-brand-cw">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirm Service Deletion
                        </h5>
                    </div>
                    <div class="card-body p-4">

                        <!-- Service Details -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h5 class="mb-2 text-brand-cw">{{ object.service_title }}</h5>
                                <p class="text-neutral-cw mb-3">{{ object.short_description|truncatewords:30 }}</p>

                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="mb-2">
                                            <strong class="text-brand-cw">Price:</strong>
                                            <span class="text-neutral-cw">{{ object.price_display }}</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="mb-2">
                                            <strong class="text-brand-cw">Duration:</strong>
                                            <span class="text-neutral-cw">{{ object.duration_display }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <strong class="text-brand-cw">Status:</strong>
                                    {% if object.is_active %}
                                        <span class="badge-cw">Active</span>
                                    {% else %}
                                        <span class="badge-cw">Inactive</span>
                                    {% endif %}
                                </div>

                                <div class="mb-2">
                                    <strong class="text-brand-cw">Created:</strong>
                                    <span class="text-neutral-cw">{{ object.created_at|date:"M d, Y" }}</span>
                                </div>
                            </div>

                            <div class="col-md-4 text-center">
                                <div class="icon-wrapper rounded p-4">
                                    <i class="fas fa-spa fa-3x text-brand-cw mb-3"></i>
                                    <p class="mb-0 small text-neutral-cw">This service will be permanently deleted</p>
                                </div>
                            </div>
                        </div>

                        <!-- Warning Message -->
                        <div class="alert-cw-warning">
                            <h6 class="alert-heading text-brand-cw">
                                <i class="fas fa-exclamation-triangle me-2"></i>Important Warning
                            </h6>
                            <p class="mb-2 text-neutral-cw">This action cannot be undone. Deleting this service will:</p>
                            <ul class="mb-0 text-neutral-cw">
                                <li>Permanently remove the service from your venue</li>
                                <li>Make the service unavailable for new bookings</li>
                                <li>Potentially affect existing bookings (if any)</li>
                                <li>Remove all service-related data</li>
                            </ul>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="post">
                            {% csrf_token %}

                            <div class="d-flex flex-column gap-3 mt-4">
                                <button type="submit" class="btn-cw-danger">
                                    <i class="fas fa-trash me-2"></i>Yes, Delete Service
                                </button>
                                <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>

                    </div>
                </div>

                <!-- Alternative Actions -->
                <div class="card-cw-accent mt-4">
                    <div class="card-body p-4">
                        <h6 class="text-brand-cw">
                            <i class="fas fa-lightbulb me-2"></i>Alternative Actions
                        </h6>
                        <p class="text-neutral-cw">Instead of deleting, you might consider:</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-wrapper rounded-circle p-2 me-3">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div>
                                        <strong class="text-brand-cw">Edit Service</strong>
                                        <br>
                                        <small class="text-neutral-cw">Update pricing, description, or duration</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-wrapper rounded-circle p-2 me-3">
                                        <i class="fas fa-pause"></i>
                                    </div>
                                    <div>
                                        <strong class="text-brand-cw">Deactivate Service</strong>
                                        <br>
                                        <small class="text-neutral-cw">Temporarily hide without deleting</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="{% url 'venues_app:service_edit' object.pk %}" class="btn-cw-secondary me-2">
                                <i class="fas fa-edit me-2"></i>Edit Instead
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
