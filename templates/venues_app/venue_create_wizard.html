{% extends 'base.html' %}

{% block title %}Create Your Venue - Step {{ step_number }} of {{ total_steps }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Enhanced Venue Creation Wizard Styles -->
<style>
    :root {
        --cw-primary: #2F160F;
        --cw-accent: #fae1d7;
        --cw-success: #10b981;
        --cw-warning: #f59e0b;
        --cw-error: #ef4444;
    }

    /* Progress Bar */
    .wizard-progress {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: #e5e7eb;
        z-index: 1;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        flex: 1;
        text-align: center;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .step-circle.completed {
        background: var(--cw-success);
        color: white;
    }

    .step-circle.active {
        background: var(--cw-primary);
        color: white;
    }

    /* Fix for progress bar text visibility - override global black color rules */
    .wizard-progress .progress-steps .step-circle.active {
        background: var(--cw-primary) !important;
        color: white !important;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .step-title.active {
        color: var(--cw-primary);
        font-weight: 600;
    }

    /* Wizard Content */
    .wizard-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .step-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .step-header h2 {
        color: var(--cw-primary);
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #6b7280;
        font-size: 1.125rem;
    }

    /* Enhanced Form Fields */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--cw-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    .form-control.is-valid {
        border-color: var(--cw-success);
    }

    .form-control.is-invalid {
        border-color: var(--cw-error);
    }

    /* Character Counter */
    .character-counter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .character-count {
        font-weight: 500;
    }

    .character-count.warning {
        color: var(--cw-warning);
    }

    .character-count.error {
        color: var(--cw-error);
    }

    /* Real-time Validation */
    .field-feedback {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .field-feedback.success {
        color: var(--cw-success);
    }

    .field-feedback.error {
        color: var(--cw-error);
    }

    /* Category Selection */
    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .category-card {
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .category-card:hover {
        border-color: var(--cw-primary);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.1);
    }

    .category-card.selected {
        border-color: var(--cw-primary);
        background: var(--cw-accent);
    }

    .category-card input[type="checkbox"] {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 1.25rem;
        height: 1.25rem;
    }

    /* Navigation Buttons */
    .wizard-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
    }

    .btn-wizard {
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: var(--cw-primary);
        color: white;
    }

    .btn-primary:hover {
        background: #1f0a08;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
    }

    /* Auto-save Indicator */
    .auto-save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .auto-save-indicator.show {
        opacity: 1;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .progress-steps {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .progress-step {
            flex: 0 0 auto;
            min-width: 80px;
        }

        .step-circle {
            width: 32px;
            height: 32px;
            font-size: 0.875rem;
        }

        .wizard-content {
            padding: 1.5rem;
        }

        .category-grid {
            grid-template-columns: 1fr;
        }

        .wizard-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-wizard {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-creation-wizard">
    <div class="container">
        <!-- Progress Bar -->
        <div class="wizard-progress">
            <div class="progress-steps">
                {% for step_key, step_name in step_choices %}
                    <div class="progress-step">
                        <div class="step-circle {% if step_key == current_step %}active{% elif step_key in progress_data %}completed{% endif %}">
                            {% if step_key == current_step %}
                                {{ step_number }}
                            {% elif step_key in progress_data %}
                                ✓
                            {% else %}
                                {{ forloop.counter }}
                            {% endif %}
                        </div>
                        <div class="step-title {% if step_key == current_step %}active{% endif %}">
                            {{ step_name }}
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
            </div>
        </div>

        <!-- Wizard Content -->
        <div class="wizard-content">
            <div class="step-header">
                <h2>{{ current_step_title }}</h2>
                <p class="step-description">
                    {% if current_step == 'basic' %}
                        Let's start with the essential information about your venue.
                    {% elif current_step == 'location' %}
                        Help customers find you with accurate location details.
                    {% elif current_step == 'categories' %}
                        Choose categories that best represent your services.
                    {% elif current_step == 'contact' %}
                        Add contact information to build trust with customers.
                    {% elif current_step == 'final' %}
                        Review your information and choose how to save your venue.
                    {% endif %}
                </p>
            </div>

            <form method="post" id="wizardForm" data-step="{{ current_step }}">
                {% csrf_token %}
                
                {% if current_step == 'basic' %}
                    <div class="form-group">
                        <label class="form-label" for="{{ form.venue_name.id_for_label }}">
                            Venue Name <span class="text-danger">*</span>
                        </label>
                        {{ form.venue_name|add_class:"form-control" }}
                        <div class="field-feedback" id="venue-name-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.short_description.id_for_label }}">
                            Description <span class="text-danger">*</span>
                        </label>
                        {{ form.short_description|add_class:"form-control" }}
                        <div class="character-counter">
                            <span class="character-count" id="description-count">0 / 500</span>
                            <span class="counter-hint">Minimum 10 characters</span>
                        </div>
                        <div class="field-feedback" id="description-feedback"></div>
                    </div>

                {% elif current_step == 'location' %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="{{ form.state.id_for_label }}">
                                    State <span class="text-danger">*</span>
                                </label>
                                {{ form.state|add_class:"form-control" }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="{{ form.county.id_for_label }}">
                                    County <span class="text-danger">*</span>
                                </label>
                                {{ form.county|add_class:"form-control" }}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.city.id_for_label }}">
                            City <span class="text-danger">*</span>
                        </label>
                        {{ form.city|add_class:"form-control" }}
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label" for="{{ form.street_number.id_for_label }}">
                                    Street Number <span class="text-danger">*</span>
                                </label>
                                {{ form.street_number|add_class:"form-control" }}
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="form-group">
                                <label class="form-label" for="{{ form.street_name.id_for_label }}">
                                    Street Name <span class="text-danger">*</span>
                                </label>
                                {{ form.street_name|add_class:"form-control" }}
                            </div>
                        </div>
                    </div>

                {% elif current_step == 'categories' %}
                    <div class="form-group">
                        <label class="form-label">
                            Select Categories <span class="text-danger">*</span>
                        </label>
                        <p class="text-muted">Choose up to 3 categories that best describe your venue</p>
                        
                        <div class="category-grid">
                            {% for choice in form.categories %}
                                <div class="category-card" data-category="{{ choice.data.value }}">
                                    {{ choice.tag }}
                                    <div class="category-content">
                                        <h5>{{ choice.choice_label }}</h5>
                                        <p class="text-muted">{{ choice.choice_label }} services and treatments</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                {% elif current_step == 'contact' %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Contact information is optional but helps build trust with customers and improves your venue's visibility.
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.phone.id_for_label }}">
                            Phone Number
                        </label>
                        {{ form.phone|add_class:"form-control" }}
                        <div class="field-feedback" id="phone-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.email.id_for_label }}">
                            Email Address
                        </label>
                        {{ form.email|add_class:"form-control" }}
                        <div class="field-feedback" id="email-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.website_url.id_for_label }}">
                            Website URL
                        </label>
                        {{ form.website_url|add_class:"form-control" }}
                        <div class="field-feedback" id="website-feedback"></div>
                    </div>

                {% elif current_step == 'final' %}
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Almost Done!</h5>
                        <p>Review your venue information and choose how to save it.</p>
                    </div>

                    <!-- Status Selection -->
                    <div class="form-group">
                        <label class="form-label">Choose Action</label>
                        
                        <div class="status-options">
                            {% for choice in form.venue_status %}
                                <div class="status-card" data-status="{{ choice.data.value }}">
                                    {{ choice.tag }}
                                    <div class="status-content">
                                        <h5>{{ choice.choice_label }}</h5>
                                        <p>
                                            {% if choice.data.value == 'draft' %}
                                                Save privately for later editing and submit when ready.
                                            {% else %}
                                                Submit for admin review to make visible to customers.
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- Navigation -->
                <div class="wizard-navigation">
                    <div>
                        {% if not is_first_step %}
                            <a href="{% url 'venues_app:venue_create_wizard' step=previous_step %}" class="btn-wizard btn-secondary">
                                <i class="fas fa-arrow-left"></i> Previous
                            </a>
                        {% endif %}
                    </div>
                    
                    <div>
                        {% if is_final_step %}
                            <button type="submit" class="btn-wizard btn-primary">
                                <i class="fas fa-check"></i> Create Venue
                            </button>
                        {% else %}
                            <button type="submit" class="btn-wizard btn-primary">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Auto-save Indicator -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        <i class="fas fa-save"></i> Progress saved automatically
    </div>
</section>

<script>
// Enhanced venue creation wizard functionality
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('wizardForm');
    const currentStep = form.dataset.step;
    
    // Auto-save functionality
    let autoSaveTimeout;
    const autoSaveDelay = 2000; // 2 seconds
    
    function autoSave() {
        const formData = new FormData(form);
        formData.append('action', 'save_progress');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAutoSaveIndicator();
            }
        })
        .catch(error => console.error('Auto-save error:', error));
    }
    
    function showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        indicator.classList.add('show');
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 2000);
    }
    
    // Real-time validation
    function validateField(fieldName, fieldValue) {
        const formData = new FormData();
        formData.append('action', 'validate_field');
        formData.append('field_name', fieldName);
        formData.append('field_value', fieldValue);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const feedback = document.getElementById(fieldName.replace('_', '-') + '-feedback');
            if (feedback) {
                if (data.is_valid) {
                    feedback.innerHTML = '<i class="fas fa-check"></i> Looks good!';
                    feedback.className = 'field-feedback success';
                } else {
                    feedback.innerHTML = '<i class="fas fa-times"></i> ' + data.errors.join(', ');
                    feedback.className = 'field-feedback error';
                }
            }
        })
        .catch(error => console.error('Validation error:', error));
    }
    
    // Setup field listeners
    const watchedFields = ['venue_name', 'short_description', 'phone', 'email', 'website_url'];
    watchedFields.forEach(fieldName => {
        const field = document.getElementById('id_' + fieldName);
        if (field) {
            field.addEventListener('input', function() {
                // Clear previous timeout
                clearTimeout(autoSaveTimeout);
                
                // Real-time validation
                if (this.value.trim()) {
                    validateField(fieldName, this.value);
                }
                
                // Character counter
                if (fieldName === 'short_description') {
                    updateCharacterCounter(this);
                }
                
                // Auto-save
                autoSaveTimeout = setTimeout(autoSave, autoSaveDelay);
            });
        }
    });
    
    // Character counter
    function updateCharacterCounter(field) {
        const counter = document.getElementById('description-count');
        const maxLength = 500;
        const currentLength = field.value.length;
        
        if (counter) {
            counter.textContent = currentLength + ' / ' + maxLength;
            
            if (currentLength > maxLength * 0.9) {
                counter.className = 'character-count warning';
            } else if (currentLength > maxLength) {
                counter.className = 'character-count error';
            } else {
                counter.className = 'character-count';
            }
        }
    }
    
    // Category selection
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const checkbox = this.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);
                
                // Limit to 3 selections
                const selected = document.querySelectorAll('.category-card input[type="checkbox"]:checked');
                if (selected.length > 3) {
                    checkbox.checked = false;
                    this.classList.remove('selected');
                    alert('You can select up to 3 categories only.');
                }
                
                // Auto-save
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(autoSave, autoSaveDelay);
            }
        });
    });
    
    // Status selection
    document.querySelectorAll('.status-card').forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                // Remove selected class from all
                document.querySelectorAll('.status-card').forEach(c => c.classList.remove('selected'));
                
                // Select this one
                radio.checked = true;
                this.classList.add('selected');
            }
        });
    });
    
    // Initialize character counter
    const descField = document.getElementById('id_short_description');
    if (descField) {
        updateCharacterCounter(descField);
    }
});
</script>

{% if show_guided_tour %}
<!-- Guided Tour Integration -->
<script src="{% static 'js/intro.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tourSteps = {{ tour_steps|safe }};
    
    if (tourSteps && tourSteps.length > 0) {
        const intro = introJs();
        
        intro.setOptions({
            steps: tourSteps.map(step => ({
                element: step.target,
                intro: `<h4>${step.title}</h4><p>${step.content}</p>`,
                position: step.placement || 'bottom'
            })),
            showProgress: true,
            showBullets: false,
            exitOnOverlayClick: false,
            disableInteraction: false
        });
        
        // Show tour after a brief delay
        setTimeout(() => {
            intro.start();
        }, 1000);
    }
});
</script>
{% endif %}
{% endblock %} 