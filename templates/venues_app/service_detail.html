{% extends 'base.html' %}

{% block title %}{{ service.service_title }} - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

<style>
    /* Enhanced Service Detail Styling */
    :root {
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Hero Section */
    .service-hero {
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: var(--cw-shadow-lg);
        border: 1px solid var(--cw-brand-accent);
        margin-bottom: 2rem;
    }

    .hero-content {
        padding: 3rem;
        position: relative;
    }

    .hero-image {
        height: 300px;
        background: var(--cw-gradient-card-subtle);
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .hero-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .hero-image .placeholder {
        font-size: 4rem;
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 2rem;
        border-radius: 50%;
    }

    .service-badge {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        background: var(--cw-brand-primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .service-badge.featured {
        background: #f59e0b;
    }

    .service-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .service-subtitle {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .venue-link {
        color: var(--cw-neutral-600);
        text-decoration: none;
        font-weight: 500;
        font-size: 1.125rem;
        transition: color 0.2s ease;
    }

    .venue-link:hover {
        color: var(--cw-brand-primary);
        text-decoration: underline;
    }

    .rating-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .star-rating {
        color: #fbbf24;
        font-size: 1.125rem;
    }

    .rating-text {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Key Information */
    .key-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .info-card {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
    }

    .info-icon {
        width: 3rem;
        height: 3rem;
        background: var(--cw-brand-primary);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin: 0 auto 1rem;
    }

    .info-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .info-label {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .price-display {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
    }

    .price-original {
        text-decoration: line-through;
        color: var(--cw-neutral-600);
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }

    .discount-badge {
        background: #ef4444;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    /* Content Sections */
    .content-section {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .section-header {
        background: var(--cw-accent-light);
        border-bottom: 1px solid var(--cw-brand-accent);
        padding: 1.5rem 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 2rem;
        height: 2rem;
        background: var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .section-content {
        padding: 2rem;
    }

    /* Booking Section */
    .booking-section {
        position: sticky;
        top: 2rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        border: 2px solid var(--cw-brand-primary);
        padding: 2rem;
        text-align: center;
    }

    .booking-price {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .booking-duration {
        color: var(--cw-neutral-600);
        font-weight: 500;
        margin-bottom: 1.5rem;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.125rem;
        width: 100%;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        width: 100%;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Features List */
    .features-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .feature-item:last-child {
        border-bottom: none;
    }

    .feature-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        flex-shrink: 0;
    }

    .feature-content h6 {
        margin: 0 0 0.25rem 0;
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    .feature-content p {
        margin: 0;
        color: var(--cw-neutral-600);
        font-size: 0.9375rem;
    }

    /* Related Services */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .service-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-sm);
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        overflow: hidden;
        text-decoration: none;
        color: inherit;
    }

    .service-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--cw-shadow-lg);
        text-decoration: none;
        color: inherit;
    }

    .service-card-image {
        height: 150px;
        background: var(--cw-gradient-card-subtle);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .service-card-content {
        padding: 1.5rem;
    }

    .service-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .service-card-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
    }

    /* Breadcrumb */
    .breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        box-shadow: var(--cw-shadow-sm);
        margin-bottom: 2rem;
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Tabs */
    .nav-tabs-cw {
        border: none;
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }

    .nav-link-cw {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .nav-link-cw:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    .nav-link-cw.active {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    /* Map */
    .venue-map {
        height: 300px;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        overflow: hidden;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .hero-content {
            padding: 2rem;
        }

        .service-title {
            font-size: 2rem;
        }

        .key-info {
            grid-template-columns: 1fr;
        }

        .section-content {
            padding: 1.5rem;
        }

        .booking-section {
            position: static;
            margin-top: 2rem;
        }

        .services-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Enhanced Booking Widget */
    .booking-widget {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        border: 2px solid var(--cw-brand-accent);
        overflow: hidden;
        position: sticky;
        top: 2rem;
    }

    .booking-widget-header {
        background: var(--cw-gradient-brand-button);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }

    .booking-widget-header h3 {
        color: white;
        margin: 0 0 0.5rem 0;
        font-size: 1.5rem;
    }

    .booking-widget-header .price-display {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }

    .booking-widget-body {
        padding: 1.5rem;
    }

    /* Visual Calendar */
    .calendar-container {
        margin-bottom: 1.5rem;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.5rem;
    }

    .calendar-nav {
        background: var(--cw-brand-primary);
        color: white;
        border: none;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .calendar-nav:hover {
        background: var(--cw-brand-light);
        transform: scale(1.1);
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 0.25rem;
        margin-bottom: 1rem;
    }

    .calendar-day-header {
        text-align: center;
        padding: 0.5rem;
        font-weight: 600;
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .calendar-day {
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
        position: relative;
    }

    .calendar-day:hover {
        background: var(--cw-brand-accent);
    }

    .calendar-day.available {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
    }

    .calendar-day.available:hover {
        background: var(--cw-brand-accent);
        border-color: var(--cw-brand-primary);
    }

    .calendar-day.selected {
        background: var(--cw-brand-primary);
        color: white;
    }

    .calendar-day.unavailable {
        color: #ccc;
        cursor: not-allowed;
    }

    .calendar-day.other-month {
        color: #ccc;
    }

    /* Time Slot Grid */
    .time-slots-container {
        margin-bottom: 1.5rem;
    }

    .time-slots-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
        max-height: 200px;
        overflow-y: auto;
        padding: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        background: var(--cw-accent-light);
    }

    .time-slot {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .time-slot:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-sm);
    }

    .time-slot.selected {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
    }

    .time-slot.unavailable {
        background: #f5f5f5;
        border-color: #e5e5e5;
        color: #999;
        cursor: not-allowed;
    }

    .time-slot-time {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .time-slot-availability {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    /* Booking Actions */
    .booking-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .btn-book-now {
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 2rem;
        font-size: 1.125rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-book-now:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-book-now:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .booking-summary {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        display: none;
    }

    .booking-summary.show {
        display: block;
    }

    .booking-summary h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        color: var(--cw-brand-primary);
    }

    .booking-summary p {
        margin: 0.25rem 0;
        font-size: 0.875rem;
        color: var(--cw-neutral-700);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_search' %}">Venues</a></li>
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">{{ venue.venue_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ service.service_title }}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Service Hero -->
            <div class="service-hero">
                <div class="hero-image">
                    {% if service.service_image %}
                        <img src="{{ service.service_image.url }}" alt="{{ service.service_title }}">
                    {% elif venue.main_image %}
                        <img src="{{ venue.main_image.url }}" alt="{{ service.service_title }}">
                    {% else %}
                        <div class="placeholder">
                            <i class="fas fa-spa"></i>
                        </div>
                    {% endif %}
                </div>
                
                <div class="hero-content">
                    {% if service.is_featured %}
                        <div class="service-badge featured">
                            <i class="fas fa-star"></i>
                            Featured Service
                        </div>
                    {% endif %}
                    
                    <h1 class="service-title">{{ service.service_title }}</h1>
                    
                    <div class="service-subtitle">
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="venue-link">
                            <i class="fas fa-building me-2"></i>{{ venue.venue_name }}
                        </a>
                        
                        {% if venue.get_average_rating %}
                            <div class="rating-display">
                                <div class="star-rating">
                                    {% for i in "12345"|make_list %}
                                        {% if forloop.counter <= venue.get_average_rating|floatformat:0 %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="rating-text">{{ venue.get_average_rating|floatformat:1 }} ({{ venue.get_review_count }} reviews)</span>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Key Information Cards -->
                    <div class="key-info">
                        <div class="info-card">
                            <div class="info-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="info-value">
                                {% if service.discounted_price %}
                                    <span class="price-original">${{ service.price_min }}</span>
                                    <span class="price-display">${{ service.discounted_price }}</span>
                                    <div class="discount-badge mt-2">{{ service.get_discount_percentage }}% OFF</div>
                                {% else %}
                                    <span class="price-display">{{ service.price_display }}</span>
                                {% endif %}
                            </div>
                            <div class="info-label">Price</div>
                        </div>

                        <div class="info-card">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-value">{{ service.duration_display }}</div>
                            <div class="info-label">Duration</div>
                        </div>

                        <div class="info-card">
                            <div class="info-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="info-value">{{ service.category_display }}</div>
                            <div class="info-label">Category</div>
                        </div>

                        {% if service.requires_booking %}
                            <div class="info-card">
                                <div class="info-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="info-value">{{ service.min_advance_booking_hours }}h</div>
                                <div class="info-label">Min. Advance</div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Content Tabs -->
            <div class="nav-tabs-cw">
                <a href="#overview" class="nav-link-cw active" data-tab="overview">Overview</a>
                <a href="#details" class="nav-link-cw" data-tab="details">Details</a>
                <a href="#policies" class="nav-link-cw" data-tab="policies">Policies</a>
                <a href="#location" class="nav-link-cw" data-tab="location">Location</a>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                    <div class="content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                Service Description
                            </h3>
                        </div>
                        <div class="section-content">
                            <p class="lead">{{ service.short_description }}</p>
                        </div>
                    </div>

                    {% if service.service_features %}
                        <div class="content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-list-check"></i>
                                    </div>
                                    What's Included
                                </h3>
                            </div>
                            <div class="section-content">
                                <ul class="features-list">
                                    <!-- Example features - you can make this dynamic -->
                                    <li class="feature-item">
                                        <div class="feature-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="feature-content">
                                            <h6>Professional Service</h6>
                                            <p>Delivered by certified professionals with years of experience</p>
                                        </div>
                                    </li>
                                    <li class="feature-item">
                                        <div class="feature-icon">
                                            <i class="fas fa-leaf"></i>
                                        </div>
                                        <div class="feature-content">
                                            <h6>Premium Products</h6>
                                            <p>High-quality, organic products used in all treatments</p>
                                        </div>
                                    </li>
                                    <li class="feature-item">
                                        <div class="feature-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="feature-content">
                                            <h6>Flexible Scheduling</h6>
                                            <p>Book appointments that fit your schedule</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Details Tab -->
                <div class="tab-pane" id="details" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                Service Details
                            </h3>
                        </div>
                        <div class="section-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-brand-cw mb-3">Booking Information</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <strong>Requires Booking:</strong> 
                                            {% if service.requires_booking %}Yes{% else %}No{% endif %}
                                        </li>
                                        {% if service.requires_booking %}
                                            <li class="mb-2">
                                                <strong>Min. Advance Booking:</strong> 
                                                {{ service.min_advance_booking_hours }} hours
                                            </li>
                                            <li class="mb-2">
                                                <strong>Max. Advance Booking:</strong> 
                                                {{ service.max_advance_booking_days }} days
                                            </li>
                                        {% endif %}
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-brand-cw mb-3">Service Options</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <strong>Customizable:</strong> 
                                            {% if service.price_max %}Yes{% else %}Fixed Service{% endif %}
                                        </li>
                                        <li class="mb-2">
                                            <strong>Group Service:</strong> 
                                            Contact venue for details
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Policies Tab -->
                <div class="tab-pane" id="policies" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-file-contract"></i>
                                </div>
                                Booking Policies
                            </h3>
                        </div>
                        <div class="section-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-brand-cw mb-3">Cancellation Policy</h6>
                                    <p>Cancellations made 24 hours in advance will receive a full refund. Cancellations made less than 24 hours in advance may be subject to a cancellation fee.</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-brand-cw mb-3">Rescheduling Policy</h6>
                                    <p>Appointments can be rescheduled up to 4 hours before the scheduled time without any additional fees.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Location Tab -->
                <div class="tab-pane" id="location" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                Location & Directions
                            </h3>
                        </div>
                        <div class="section-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-brand-cw mb-3">Address</h6>
                                    <p>{{ venue.get_full_address }}</p>
                                    
                                    <h6 class="text-brand-cw mb-3 mt-4">Contact</h6>
                                    <p>
                                        <strong>Email:</strong> {{ venue.service_provider.user.email }}<br>
                                        {% if venue.service_provider.phone_number %}
                                            <strong>Phone:</strong> {{ venue.service_provider.phone_number }}
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <div class="venue-map" id="venue-map"></div>
                                    <a href="https://www.google.com/maps/dir/?api=1&destination={{ venue.latitude }},{{ venue.longitude }}" 
                                       target="_blank" class="btn-cw-secondary mt-3">
                                        <i class="fas fa-directions"></i>Get Directions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Services -->
            {% if related_services %}
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-spa"></i>
                            </div>
                            More Services at {{ venue.venue_name }}
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="services-grid">
                            {% for related_service in related_services %}
                                <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug=related_service.slug %}" class="service-card">
                                    <div class="service-card-image">
                                        {% if related_service.service_image %}
                                            <img src="{{ related_service.service_image.url }}" alt="{{ related_service.service_title }}" style="width: 100%; height: 100%; object-fit: cover;">
                                        {% else %}
                                            <i class="fas fa-spa" style="font-size: 2rem; color: var(--cw-brand-accent);"></i>
                                        {% endif %}
                                    </div>
                                    <div class="service-card-content">
                                        <h6 class="service-card-title">{{ related_service.service_title }}</h6>
                                        <div class="service-card-price">{{ related_service.price_display }}</div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Booking Card -->
            <div class="booking-section">
                <div class="booking-price">
                    {% if service.discounted_price %}
                        <span class="price-original">${{ service.price_min }}</span>
                        ${{ service.discounted_price }}
                    {% else %}
                        {{ service.price_display }}
                    {% endif %}
                </div>
                <div class="booking-duration">{{ service.duration_display }} session</div>
                
                {% if user.is_authenticated and user.is_customer %}
                    <a href="{% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn-cw-primary">
                        <i class="fas fa-shopping-cart"></i>Add to Cart
                    </a>
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn-cw-secondary">
                        <i class="fas fa-store"></i>View All Services
                    </a>
                {% else %}
                    <a href="{% url 'accounts_app:customer_login' %}?next={% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn-cw-primary">
                        <i class="fas fa-sign-in-alt"></i>Login to Book
                    </a>
                    <a href="{% url 'accounts_app:customer_signup' %}" class="btn-cw-secondary">
                        <i class="fas fa-user-plus"></i>Create Account
                    </a>
                {% endif %}

                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Secure booking & payment
                    </small>
                </div>
            </div>

            <!-- Venue Quick Info -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        Venue Information
                    </h3>
                </div>
                <div class="section-content">
                    <h6 class="text-brand-cw">{{ venue.venue_name }}</h6>
                    <p class="text-muted mb-3">{{ venue.about_venue|truncatewords:20 }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Total Services:</span>
                        <strong>{{ venue.services.count }}</strong>
                    </div>
                    
                    {% if venue.get_average_rating %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Rating:</span>
                            <strong>{{ venue.get_average_rating|floatformat:1 }}/5.0</strong>
                        </div>
                    {% endif %}
                    
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn-cw-secondary">
                        <i class="fas fa-eye"></i>View Full Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Back to top button -->
<button id="back-to-top" class="btn btn-primary rounded-circle position-fixed d-none" style="bottom: 2rem; right: 2rem; width: 50px; height: 50px; z-index: 1000;">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Booking Preview Modal -->
<div class="modal fade" id="bookingPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Your Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Service Preview -->
                <div class="service-preview mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            {% if service.service_image %}
                            <img src="{{ service.service_image.url }}" alt="{{ service.service_title }}" class="img-fluid rounded">
                            {% else %}
                            <div class="placeholder-img">
                                <i class="fas fa-spa"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h4>{{ service.service_title }}</h4>
                            <p class="text-muted">{{ venue.venue_name }}</p>
                            <p class="mb-1"><strong>Duration:</strong> {{ service.duration_minutes }} minutes</p>
                            <p class="mb-0">{{ service.short_description|truncatewords:15 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Booking Details -->
                <div class="booking-details">
                    <h5>Booking Details</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Date:</strong> <span id="modalSelectedDate">-</span></p>
                            <p><strong>Time:</strong> <span id="modalSelectedTime">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Duration:</strong> {{ service.duration_minutes }} minutes</p>
                            <p><strong>Price:</strong> <span class="h5 text-success">{{ service.price_display }}</span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </button>
                <button type="button" class="btn btn-success" onclick="confirmAddToCart()">
                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabLinks = document.querySelectorAll('.nav-link-cw');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active classes
            tabLinks.forEach(l => l.classList.remove('active'));
            tabPanes.forEach(p => {
                p.classList.remove('active');
                p.style.display = 'none';
            });
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Show corresponding pane
            const tabId = this.getAttribute('data-tab');
            const targetPane = document.getElementById(tabId);
            if (targetPane) {
                targetPane.classList.add('active');
                targetPane.style.display = 'block';
            }
        });
    });

    // Initialize map if coordinates are available
    {% if venue.latitude and venue.longitude %}
        const venueMap = L.map('venue-map').setView([{{ venue.latitude }}, {{ venue.longitude }}], 15);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(venueMap);

        L.marker([{{ venue.latitude }}, {{ venue.longitude }}])
            .addTo(venueMap)
            .bindPopup('<strong>{{ venue.venue_name }}</strong><br>{{ venue.get_full_address }}')
            .openPopup();
    {% endif %}

    // Back to top button
    const backToTop = document.getElementById('back-to-top');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTop.classList.remove('d-none');
        } else {
            backToTop.classList.add('d-none');
        }
    });

    backToTop.addEventListener('click', function() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});

// Enhanced booking widget functionality
class ServiceBookingWidget {
    constructor(serviceId) {
        this.serviceId = serviceId;
        this.currentDate = new Date();
        this.selectedDate = null;
        this.selectedTime = null;
        this.availabilityData = {};
        
        this.init();
    }

    init() {
        this.renderCalendar();
        this.attachEventListeners();
        this.loadAvailability();
    }

    attachEventListeners() {
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.renderCalendar();
            this.loadAvailability();
        });

        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.renderCalendar();
            this.loadAvailability();
        });
    }

    renderCalendar() {
        const monthNames = ["January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"];
        
        document.getElementById('currentMonth').textContent = 
            `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;

        const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const calendarGrid = document.getElementById('calendarGrid');
        calendarGrid.innerHTML = '';

        // Day headers
        const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        dayHeaders.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });

        // Calendar days
        const today = new Date();
        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = date.getDate();
            
            if (date.getMonth() !== this.currentDate.getMonth()) {
                dayElement.classList.add('other-month');
            } else if (date < today) {
                dayElement.classList.add('unavailable');
            } else {
                dayElement.classList.add('available');
                dayElement.addEventListener('click', () => this.selectDate(date));
            }
            
            calendarGrid.appendChild(dayElement);
        }
    }

    selectDate(date) {
        // Remove previous selection
        document.querySelectorAll('.calendar-day.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to clicked date
        event.target.classList.add('selected');
        
        this.selectedDate = date;
        this.selectedTime = null;
        this.updateBookingSummary();
        this.loadTimeSlots(date);
    }

    async loadAvailability() {
        // Load availability data for the current month
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth() + 1;
        
        try {
            const response = await fetch(`/bookings/ajax/availability/${this.serviceId}/?year=${year}&month=${month}`);
            const data = await response.json();
            this.availabilityData = data.availability || {};
            this.updateCalendarAvailability();
        } catch (error) {
            console.error('Error loading availability:', error);
        }
    }

    updateCalendarAvailability() {
        // Update calendar days based on availability data
        document.querySelectorAll('.calendar-day.available').forEach(dayEl => {
            const date = this.getDateFromElement(dayEl);
            const dateStr = date.toISOString().split('T')[0];
            
            if (this.availabilityData[dateStr]) {
                dayEl.style.background = 'var(--cw-accent-light)';
                dayEl.title = `${this.availabilityData[dateStr]} slots available`;
            }
        });
    }

    async loadTimeSlots(date) {
        const timeSlotsGrid = document.getElementById('timeSlotsGrid');
        timeSlotsGrid.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading times...</div>';

        try {
            const dateStr = date.toISOString().split('T')[0];
            const response = await fetch(`/bookings/ajax/slots/${this.serviceId}/?date=${dateStr}`);
            const data = await response.json();
            
            timeSlotsGrid.innerHTML = '';
            
            if (data.slots && data.slots.length > 0) {
                data.slots.forEach(slot => {
                    const slotElement = document.createElement('div');
                    slotElement.className = 'time-slot';
                    slotElement.innerHTML = `
                        <div class="time-slot-time">${slot.display}</div>
                        <div class="time-slot-availability">${slot.available_spots} available</div>
                    `;
                    
                    slotElement.addEventListener('click', () => this.selectTime(slot.time, slot.display));
                    timeSlotsGrid.appendChild(slotElement);
                });
            } else {
                timeSlotsGrid.innerHTML = '<div class="text-center p-3 text-muted">No available times for this date</div>';
            }
        } catch (error) {
            console.error('Error loading time slots:', error);
            timeSlotsGrid.innerHTML = '<div class="text-center p-3 text-danger">Error loading times</div>';
        }
    }

    selectTime(time, display) {
        // Remove previous selection
        document.querySelectorAll('.time-slot.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to clicked time
        event.target.classList.add('selected');
        
        this.selectedTime = { time, display };
        this.updateBookingSummary();
        this.enableBookingButton();
    }

    updateBookingSummary() {
        const summary = document.getElementById('bookingSummary');
        const dateSpan = document.getElementById('selectedDate');
        const timeSpan = document.getElementById('selectedTime');

        if (this.selectedDate) {
            dateSpan.textContent = this.selectedDate.toLocaleDateString();
            summary.classList.add('show');
        }

        if (this.selectedTime) {
            timeSpan.textContent = this.selectedTime.display;
        }
    }

    enableBookingButton() {
        const bookBtn = document.getElementById('bookNowBtn');
        if (this.selectedDate && this.selectedTime) {
            bookBtn.disabled = false;
            bookBtn.style.background = 'var(--cw-gradient-brand-button)';
        }
    }

    getDateFromElement(element) {
        // Helper method to get date from calendar element
        return new Date(); // Simplified for now
    }
}

// Global functions
function showBookingPreview() {
    if (bookingWidget.selectedDate && bookingWidget.selectedTime) {
        document.getElementById('modalSelectedDate').textContent = bookingWidget.selectedDate.toLocaleDateString();
        document.getElementById('modalSelectedTime').textContent = bookingWidget.selectedTime.display;
        
        const modal = new bootstrap.Modal(document.getElementById('bookingPreviewModal'));
        modal.show();
    }
}

async function confirmAddToCart() {
    if (!bookingWidget.selectedDate || !bookingWidget.selectedTime) return;

    try {
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        formData.append('selected_date', bookingWidget.selectedDate.toISOString().split('T')[0]);
        formData.append('selected_time_slot', bookingWidget.selectedTime.time);
        formData.append('quantity', 1);

        const response = await fetch(`/bookings/add-to-cart/${bookingWidget.serviceId}/`, {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            window.location.href = '/bookings/cart/';
        } else {
            throw new Error('Failed to add to cart');
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        alert('Error adding to cart. Please try again.');
    }
}

function shareService() {
    if (navigator.share) {
        navigator.share({
            title: '{{ service.service_title }}',
            text: 'Check out this service at {{ venue.venue_name }}',
            url: window.location.href
        });
    } else {
        // Fallback
        navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
    }
}

// Initialize widget
let bookingWidget;
document.addEventListener('DOMContentLoaded', function() {
    bookingWidget = new ServiceBookingWidget({{ service.id }});
});
</script>
{% endblock %}
