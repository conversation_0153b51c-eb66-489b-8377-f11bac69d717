{% extends 'base.html' %}
{% load discount_tags %}
{% load review_tags %}

{% block title %}Venues - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Professional Venue List */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Secondary Colors */
        --cw-secondary-50: #f9f7f4;
        --cw-secondary-100: #f1ebe2;
        --cw-secondary-200: #e3d5c4;
        --cw-secondary-300: #d1b89e;
        --cw-secondary-400: #bc9876;
        --cw-secondary-500: #ad7f5a;
        --cw-secondary-600: #a0704e;
        --cw-secondary-700: #855a42;
        --cw-secondary-800: #6c4a39;
        --cw-secondary-900: #583d30;
        --cw-secondary-950: #2f1f18;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;
        --cw-neutral-950: #0a0a0a;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
    }

    /* Main Section */
    .venues-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 3rem 0;
    }

    .venues-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Page Header */
    .page-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .page-title {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-secondary-950);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .page-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Sidebar Cards */
    .sidebar-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        overflow: hidden;
    }

    .sidebar-card-header {
        background: var(--cw-gradient-brand-button);
        color: white;
        padding: 1rem 1.5rem;
        border-bottom: none;
    }

    .sidebar-card-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        margin: 0;
        font-size: 1.125rem;
    }

    .sidebar-card-body {
        padding: 1.5rem;
    }

    /* Form Controls */
    .form-control-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-select-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        width: 100%;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .card-cw:hover {
        transform: translateY(-4px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .card-img-container {
        position: relative;
        overflow: hidden;
        height: 220px;
    }

    .card-img-top {
        height: 100%;
        width: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .card-cw:hover .card-img-top {
        transform: scale(1.05);
    }

    .card-body {
        padding: 1.5rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .card-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1.25rem;
        line-height: 1.3;
    }

    .card-title a {
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    .card-title a:hover {
        color: var(--cw-brand-light);
    }

    /* Rating and Location */
    .venue-meta {
        margin-bottom: 1rem;
    }

    .rating {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .rating-score {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-left: 0.5rem;
    }

    .location {
        color: var(--cw-neutral-600);
        font-weight: 500;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .venue-actions {
        margin-top: auto;
        padding-top: 1rem;
        border-top: 1px solid var(--cw-neutral-200);
    }

    /* Badges */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    .badge-cw-secondary {
        background: var(--cw-secondary-100);
        color: var(--cw-secondary-800);
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    /* Pagination */
    .pagination-wrapper {
        margin-top: 3rem;
        padding: 2rem;
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .pagination .page-link {
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-neutral-200);
        background: white;
        margin: 0 0.25rem;
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .pagination .page-link:hover {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
        transform: translateY(-1px);
    }

    .pagination .page-item.active .page-link {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-sm);
    }

    /* No Results Alert */
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .no-results i {
        font-size: 4rem;
        color: var(--cw-brand-primary);
        opacity: 0.7;
        margin-bottom: 2rem;
    }

    .no-results h4 {
        font-family: var(--cw-font-heading);
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .no-results p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-size: 1.125rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.25rem;
        }

        .sidebar-card-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venues-section">
    <div class="venues-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Discover Venues</h1>
            <p class="page-subtitle">Find the perfect spa and wellness venues in your area</p>
        </div>

        <div class="row">
            <!-- Search and Filter Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar-card">
                    <div class="sidebar-card-header">
                        <h5 class="sidebar-card-title">Search</h5>
                    </div>
                    <div class="sidebar-card-body">
                        <form method="get" action="{% url 'venues_app:venue_list' %}">
                            <div class="mb-3">
                                <label for="query" class="form-label form-label-cw">Keyword</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-search text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control form-control-cw" id="query" name="query"
                                           value="{{ search_form.query.value|default:'' }}"
                                           placeholder="Search venues, services, or categories...">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="location" class="form-label form-label-cw">Location</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-map-marker-alt text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control form-control-cw" id="id_location" name="location"
                                           value="{{ search_form.location.value|default:'' }}"
                                           placeholder="City, State, or County" list="locationList" autocomplete="off">
                                </div>
                                <datalist id="locationList"></datalist>
                            </div>
                            <button type="submit" class="btn btn-cw-primary" aria-label="Search venues">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </form>
                    </div>
                </div>

                <div class="sidebar-card">
                    <div class="sidebar-card-header">
                        <h5 class="sidebar-card-title">Filter</h5>
                    </div>
                    <div class="sidebar-card-body">
                        <form method="get" action="{% url 'venues_app:venue_list' %}">
                            <!-- Preserve search parameters -->
                            {% if search_form.query.value %}
                            <input type="hidden" name="query" value="{{ search_form.query.value }}">
                            {% endif %}
                            {% if search_form.location.value %}
                            <input type="hidden" name="location" value="{{ search_form.location.value }}">
                            {% endif %}
                            {% if search_form.category.value %}
                            <input type="hidden" name="category" value="{{ search_form.category.value }}">
                            {% endif %}

                            <div class="mb-3">
                                <label for="sort_by" class="form-label form-label-cw">Sort By</label>
                                <select class="form-select form-select-cw" id="sort_by" name="sort_by">
                                    <option value="">Sort by</option>
                                    <option value="rating_high" {% if filter_form.sort_by.value == 'rating_high' %}selected{% endif %}>Rating: High to Low</option>
                                    <option value="rating_low" {% if filter_form.sort_by.value == 'rating_low' %}selected{% endif %}>Rating: Low to High</option>
                                    <option value="price_high" {% if filter_form.sort_by.value == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                                    <option value="price_low" {% if filter_form.sort_by.value == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                                    <option value="discount" {% if filter_form.sort_by.value == 'discount' %}selected{% endif %}>Discount: High to Low</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="venue_type" class="form-label form-label-cw">Venue Type</label>
                                <select class="form-select form-select-cw" id="venue_type" name="venue_type">
                                    <option value="">All Types</option>
                                    <option value="all" {% if filter_form.venue_type.value == 'all' %}selected{% endif %}>All Genders</option>
                                    <option value="male" {% if filter_form.venue_type.value == 'male' %}selected{% endif %}>Male Only</option>
                                    <option value="female" {% if filter_form.venue_type.value == 'female' %}selected{% endif %}>Female Only</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="id_state" class="form-label form-label-cw">State</label>
                                {{ filter_form.state }}
                            </div>

                            <div class="mb-3">
                                <label for="id_county" class="form-label form-label-cw">County</label>
                                {{ filter_form.county }}
                            </div>

                            <div class="mb-3">
                                <label for="id_city" class="form-label form-label-cw">City</label>
                                {{ filter_form.city }}
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="has_discount" name="has_discount" {% if filter_form.has_discount.value %}checked{% endif %}>
                                <label class="form-check-label form-label-cw" for="has_discount">Has Discount</label>
                            </div>

                            <button type="submit" class="btn btn-cw-primary" aria-label="Apply filters">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Venues List -->
            <div class="col-lg-9">
                {% if page_obj %}
                <div class="row g-4">
                    {% for venue in page_obj %}
                    <div class="col-lg-4 col-md-6">
                        <div class="card-cw position-relative">
                            <!-- Discount Badge -->
                            {% venue_discount_summary venue %}

                            <!-- New on CozyWish Badge -->
                            {% venue_badge_card venue %}

                            <div class="card-img-container">
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                    <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}"
                                         class="card-img-top" alt="{{ venue.name }}">
                                </a>
                            </div>

                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                        {{ venue.name }}
                                    </a>
                                </h5>

                                <div class="venue-meta">
                                    <div class="rating">
                                        <span class="rating-score">
                                            {% if venue.get_average_rating %}
                                                {{ venue.get_average_rating }}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <span class="badge-cw-primary">New</span>
                                            {% endif %}
                                        </span>
                                        <span class="review-count">({{ venue.get_review_count }} review{{ venue.get_review_count|pluralize }})</span>
                                    </div>
                                    <div class="location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ venue.city }}, {{ venue.state }}
                                    </div>
                                </div>

                                <div class="venue-actions">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge-cw-secondary">{{ venue.category.category_name|default:"Spa & Wellness" }}</span>
                                        {% if venue|has_venue_discounts or venue|has_platform_discounts %}
                                            <a href="{% url 'discount_app:venue_discounts' venue_id=venue.id %}" class="btn btn-sm btn-cw-secondary">
                                                <i class="fas fa-tags me-1"></i>View Deals
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <div class="pagination-wrapper">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}

                {% else %}
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h4>No venues found</h4>
                    <p>No venues found matching your criteria. Please try a different search or filter.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const stateSelect = document.getElementById('id_state');
  const countySelect = document.getElementById('id_county');
  const citySelect = document.getElementById('id_city');

  function fetchOptions(type, params, target) {
    const url = new URL('{% url "venues_app:get_location_data" %}', window.location.origin);
    url.searchParams.set('type', type);
    Object.entries(params).forEach(([k,v]) => url.searchParams.set(k, v));
    fetch(url)
      .then(r => r.json())
      .then(data => {
        target.innerHTML = '<option value="">Select '+type.slice(0,-1)+'</option>';
        data.options.forEach(opt => {
          const el = document.createElement('option');
          el.value = opt.value;
          el.textContent = opt.label;
          target.appendChild(el);
        });
      });
  }

  if(stateSelect){
    stateSelect.addEventListener('change', function(){
      countySelect.innerHTML = '<option value="">Select county</option>';
      citySelect.innerHTML = '<option value="">Select city</option>';
      if(this.value){
        fetchOptions('counties', {state:this.value}, countySelect);
      }
    });
  }

  if(countySelect){
    countySelect.addEventListener('change', function(){
      citySelect.innerHTML = '<option value="">Select city</option>';
      const state = stateSelect.value;
      if(state){
        fetchOptions('cities', {state:state, county:this.value}, citySelect);
      }
    });
  }
});
</script>
{% endblock %}
