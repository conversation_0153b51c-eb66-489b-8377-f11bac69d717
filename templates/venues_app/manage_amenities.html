{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage Amenities - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Manage Amenities */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Page Section */
    .amenities-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    .amenities-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .amenity-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .amenity-item:last-child {
        border-bottom: none;
    }

    .amenity-info h5 {
        margin: 0;
        font-weight: 600;
        color: black;
    }

    .amenity-info p {
        margin: 0.25rem 0 0 0;
        color: #666;
        font-size: 0.875rem;
    }

    .amenity-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .btn-outline-primary {
        border: 1px solid black;
        color: black;
        background: white;
    }

    .btn-outline-primary:hover {
        background-color: black;
        color: white;
    }

    .btn-outline-danger {
        border: 1px solid #dc3545;
        color: #dc3545;
        background: white;
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        color: white;
    }

    .add-amenity-form {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: var(--font-primary);
    }

    .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        background-color: black;
        border-color: black;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-outline-secondary:hover {
        background-color: black;
        color: white;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .no-amenities-message {
        text-align: center;
        padding: 3rem 2rem;
        color: #666;
    }

    .no-amenities-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
    }

    /* Add Amenity Form Section */
    .add-amenity-section {
        padding: 2rem;
    }

    .form-section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
    }

    .amenity-limit-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .amenity-limit-info i {
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
    }

    /* Form Styles */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.95rem;
    }

    .form-control {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-check-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-label {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Badge */
    .badge-cw-secondary {
        background: var(--cw-neutral-600);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        margin-left: 0.5rem;
    }

    /* Error Messages */
    .text-danger {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-family: var(--cw-font-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .amenities-section {
            padding: 2rem 0;
        }

        .amenities-container {
            padding: 0 0.5rem;
        }

        .amenities-header,
        .amenities-list-section,
        .add-amenity-section {
            padding: 1.5rem;
        }

        .amenity-content {
            flex-direction: column;
            gap: 1rem;
        }

        .amenity-actions {
            align-self: flex-start;
        }

        .amenities-title {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="amenities-section">
    <div class="amenities-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:venue_edit' %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            {% trans "Back to Venue Management" %}
        </a>

        <!-- Main Card -->
        <div class="amenities-card">
            <!-- Header -->
            <div class="amenities-header">
                <div class="content">
                    <h1 class="amenities-title">{% trans "Manage Amenities" %}</h1>
                    <p class="amenities-subtitle">Add and manage amenities and features available at your venue</p>
                </div>
            </div>

            <!-- Current Amenities Section -->
            <div class="amenities-list-section">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>
                    Current Amenities ({{ amenities.count }}/{{ max_amenities }})
                </h2>

                {% if amenities %}
                    {% for amenity in amenities %}
                        <div class="amenity-item">
                            <div class="amenity-content">
                                <div class="amenity-info">
                                    <h5>
                                        {% if amenity.custom_name %}
                                            {{ amenity.custom_name }}
                                        {% else %}
                                            {{ amenity.get_amenity_type_display }}
                                        {% endif %}
                                        {% if not amenity.is_active %}
                                            <span class="badge-cw-secondary">Inactive</span>
                                        {% endif %}
                                    </h5>
                                    {% if amenity.description %}
                                        <p>{{ amenity.description }}</p>
                                    {% endif %}
                                </div>
                                <div class="amenity-actions">
                                    <a href="{% url 'venues_app:edit_amenity' amenity.id %}" class="btn-cw-secondary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'venues_app:delete_amenity' amenity.id %}" class="btn-cw-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-amenities-message">
                        <i class="fas fa-star"></i>
                        <p>{% trans "No amenities added yet. Add your first amenity below." %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Add New Amenity Section -->
            {% if can_add_amenity %}
            <div class="add-amenity-section">
                <h2 class="form-section-title">
                    <i class="fas fa-plus"></i>
                    {% trans "Add New Amenity" %}
                </h2>

                <div class="amenity-limit-info">
                    <i class="fas fa-info-circle"></i>
                    You can add up to {{ max_amenities }} amenities for your venue.
                </div>

                <form method="post" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.amenity_type.id_for_label }}">{% trans "Amenity Type" %}</label>
                            {{ form.amenity_type|add_class:'form-control' }}
                            {% for error in form.amenity_type.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.custom_name.id_for_label }}">{% trans "Custom Name" %}</label>
                            {{ form.custom_name|add_class:'form-control' }}
                            {% for error in form.custom_name.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" for="{{ form.description.id_for_label }}">{% trans "Description" %}</label>
                        {{ form.description|add_class:'form-control' }}
                        {% for error in form.description.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_active|add_class:'form-check-input' }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {% trans "Active (visible to customers)" %}
                            </label>
                        </div>
                        {% for error in form.is_active.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <button type="submit" class="btn-cw-primary">
                        <i class="fas fa-plus"></i>
                        {% trans "Add Amenity" %}
                    </button>
                </form>
            </div>
            {% else %}
            <div class="add-amenity-section">
                <div class="amenity-limit-info">
                    <i class="fas fa-exclamation-triangle"></i>
                    You have reached the maximum limit of {{ max_amenities }} amenities for your venue.
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
