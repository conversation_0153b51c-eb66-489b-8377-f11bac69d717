{% extends 'venues_app/base_venues.html' %}
{% load widget_tweaks i18n %}

{% block title %}Delete Venue - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <div>
                        <h1 class="display-font text-brand-cw mb-2">Delete Venue</h1>
                        <p class="lead text-neutral-cw mb-0">Confirm deletion of your venue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <!-- Warning -->
                    <div class="alert alert-danger mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fs-3 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-2">Warning: This action cannot be undone!</h5>
                                <p class="mb-0">Deleting your venue will permanently remove all associated data including services, reviews, and bookings.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Venue Details -->
                    <div class="venue-summary mb-4">
                        <h5 class="mb-3">You are about to delete:</h5>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6 class="card-title">{{ venue.venue_name }}</h6>
                                        <p class="card-text text-muted mb-2">{{ venue.short_description|truncatewords:15 }}</p>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                {{ venue.full_address }}
                                            </small>
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <span class="badge bg-primary">{{ venue.services.count }} Service{{ venue.services.count|pluralize }}</span>
                                        <br>
                                        <span class="badge bg-info mt-1">{{ venue.reviews.count }} Review{{ venue.reviews.count|pluralize }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post">
                        {% csrf_token %}

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Yes, Delete Venue
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %} 