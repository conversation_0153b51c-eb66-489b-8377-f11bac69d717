{% extends 'base.html' %}

{% block title %}Report Issue - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Flag Venue */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venues wrapper - professional background */
    .venues-wrapper {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--cw-font-primary);
    }

    .flag-venue-container {
        max-width: 800px;
        margin: 0 auto;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    /* Venue preview */
    .venue-preview {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    .venue-preview img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .venue-preview .bg-light {
        background: var(--cw-accent-light) !important;
        border: 1px solid var(--cw-brand-accent);
    }

    /* Warning box */
    .warning-box {
        background: white;
        border: 1px solid var(--cw-warning, #d97706);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    .warning-icon {
        color: var(--cw-warning, #d97706);
        font-size: 1.5rem;
    }

    /* Form section */
    .form-section {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    /* Custom Forms */
    .venues-wrapper .form-control-cw {
        border: 2px solid var(--cw-neutral-200, #e5e5e5);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
    }
    .venues-wrapper .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .venues-wrapper .form-label {
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-primary);
    }

    .venues-wrapper .form-control, .venues-wrapper .form-select {
        border: 2px solid var(--cw-neutral-200, #e5e5e5);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        width: 100%;
    }

    .venues-wrapper .form-control:focus, .venues-wrapper .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        color: var(--cw-neutral-800);
    }

    .reason-category-help {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .venues-wrapper .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
    }

    /* Custom Buttons */
    .venues-wrapper .btn {
        font-family: var(--cw-font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
    }

    .btn-cw-cancel {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        background: white;
        transition: all 0.2s ease;
    }
    .btn-cw-cancel:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    .btn-cw-submit {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }
    .btn-cw-submit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    /* Legacy button support */
    .btn-cancel {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary) !important;
        background: white;
    }

    .btn-cancel:hover {
        background: var(--cw-brand-primary);
        color: white !important;
    }

    .btn-submit {
        background: var(--cw-gradient-brand-button);
        color: white !important;
        border: none;
    }

    .btn-submit:hover {
        background: var(--cw-brand-light);
        color: white !important;
    }

    /* Error styling */
    .venues-wrapper .text-danger {
        color: var(--cw-error, #dc2626) !important;
        font-weight: 500;
    }

    .venues-wrapper .text-muted {
        color: var(--cw-neutral-600) !important;
    }

    .venues-wrapper .text-warning {
        color: var(--cw-warning, #d97706) !important;
        font-weight: 500;
    }

    /* Character counter styling */
    #char-count {
        font-weight: 500;
        color: var(--cw-neutral-600);
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-5">
        <div class="flag-venue-container">
            <!-- Page Header -->
            <div class="text-center mb-4">
                <h2><i class="fas fa-flag me-2"></i>Flag Venue - Report an Issue</h2>
                <p>Help us maintain quality by reporting inappropriate content or issues</p>
            </div>

            <!-- Venue Preview -->
            <div class="venue-preview">
                <div class="row align-items-center">
                    <div class="col-auto">
                        {% if venue.main_image %}
                            <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="img-fluid">
                        {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border-radius: 8px;">
                                <i class="fas fa-image fa-2x"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col">
                        <h5 class="mb-1">{{ venue.venue_name }}</h5>
                        <p class="text-muted mb-1">{{ venue.service_provider.business_name }}</p>
                        <small class="text-muted">{{ venue.full_address }}</small>
                    </div>
                </div>
            </div>

            <!-- Warning Box -->
            <div class="warning-box">
                <div class="d-flex align-items-start">
                    <i class="fas fa-exclamation-triangle warning-icon me-3 mt-1"></i>
                    <div>
                        <h6 class="mb-2">Important Notice</h6>
                        <p class="mb-0">
                            Please only report genuine issues. False reports may result in account restrictions.
                            Our team will review all reports carefully and take appropriate action.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Flag Form -->
            <div class="form-section">
                <form method="post" novalidate>
                    {% csrf_token %}

                    <!-- Reason Category -->
                    <div class="mb-4">
                        <label for="{{ form.reason_category.id_for_label }}" class="form-label fw-bold">
                            {{ form.reason_category.label }}
                        </label>
                        {{ form.reason_category }}
                        <div class="reason-category-help">
                            Select the category that best describes the issue you're reporting.
                        </div>
                        {% if form.reason_category.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.reason_category.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Detailed Reason -->
                    <div class="mb-4">
                        <label for="{{ form.reason.id_for_label }}" class="form-label fw-bold">
                            {{ form.reason.label }}
                        </label>
                        {{ form.reason }}
                        {% if form.reason.help_text %}
                            <div class="form-text">{{ form.reason.help_text }}</div>
                        {% endif %}
                        {% if form.reason.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Character Counter -->
                    <div class="mb-4">
                        <small class="text-muted">
                            <span id="char-count">0</span> / 1000 characters
                        </small>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex flex-column flex-sm-row justify-content-between gap-3">
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-flag me-2"></i>Submit Report
                        </button>
                    </div>
                </form>
            </div>

            <!-- Additional Information -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Your report will be reviewed within 24-48 hours. Thank you for helping us maintain a safe platform.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for reason field
    const reasonField = document.getElementById('{{ form.reason.id_for_label }}');
    const charCount = document.getElementById('char-count');

    if (reasonField && charCount) {
        function updateCharCount() {
            const count = reasonField.value.length;
            charCount.textContent = count;

            // Change styling based on character count - brand theme
            if (count > 900) {
                charCount.style.color = 'var(--cw-error, #dc2626)';
                charCount.style.fontWeight = '600';
            } else if (count > 800) {
                charCount.style.color = 'var(--cw-warning, #d97706)';
                charCount.style.fontWeight = '500';
            } else {
                charCount.style.color = 'var(--cw-neutral-600)';
                charCount.style.fontWeight = '400';
            }
        }

        // Update on input
        reasonField.addEventListener('input', updateCharCount);

        // Initial update
        updateCharCount();
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const reasonCategory = document.getElementById('{{ form.reason_category.id_for_label }}');
            const reasonDetails = document.getElementById('{{ form.reason.id_for_label }}');

            if (!reasonCategory.value) {
                e.preventDefault();
                alert('Please select a reason category.');
                reasonCategory.focus();
                return;
            }

            if (!reasonDetails.value.trim() || reasonDetails.value.trim().length < 10) {
                e.preventDefault();
                alert('Please provide detailed information (at least 10 characters).');
                reasonDetails.focus();
                return;
            }

            // Confirm submission
            if (!confirm('Are you sure you want to submit this report? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
