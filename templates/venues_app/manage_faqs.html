{% extends "venues_app/base_venues.html" %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}
{% load math_filters %}

{% block title %}Manage FAQs - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block venues_extra_css %}
<style>
    /* Enhanced FAQ Management Interface */
    .faqs-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .faqs-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Enhanced Header */
    .faqs-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-neutral-200);
    }

    .faqs-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .faqs-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
        margin-bottom: 1.5rem;
    }

    .progress-indicator {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(var(--cw-brand-primary) calc(var(--progress, 0) * 1%), var(--cw-neutral-200) 0);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .progress-circle::before {
        content: attr(data-progress) '%';
        position: absolute;
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--cw-brand-primary);
    }

    .progress-text {
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    /* Back Button */
    .btn-cw-back {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        margin-bottom: 1.5rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Main Card */
    .faqs-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-neutral-200);
        overflow: hidden;
    }

    /* Category Tabs */
    .faq-categories {
        display: flex;
        background: var(--cw-gradient-card-subtle);
        border-bottom: 2px solid var(--cw-brand-accent);
        overflow-x: auto;
    }

    .category-tab {
        flex: 1;
        padding: 1rem 1.5rem;
        border: none;
        background: transparent;
        color: var(--cw-neutral-600);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        min-width: 140px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .category-tab:hover {
        background: rgba(47, 22, 15, 0.05);
        color: var(--cw-brand-primary);
    }

    .category-tab.active {
        background: var(--cw-brand-primary);
        color: white;
    }

    .category-tab.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--cw-brand-primary);
    }

    /* Section Styling */
    .section-wrapper {
        padding: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    /* FAQ Templates Grid */
    .templates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .template-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .template-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .template-question {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
    }

    .template-answer {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .template-apply-btn {
        width: 100%;
        padding: 0.75rem;
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .template-apply-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    /* FAQ List */
    .faqs-list {
        min-height: 200px;
    }

    .faq-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .faq-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .faq-item.dragging {
        opacity: 0.7;
        transform: rotate(2deg);
    }

    .faq-header {
        background: white;
        padding: 1.25rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        cursor: move;
    }

    .drag-handle {
        color: var(--cw-neutral-400);
        cursor: grab;
        font-size: 1.25rem;
    }

    .drag-handle:active {
        cursor: grabbing;
    }

    .faq-order {
        background: var(--cw-brand-primary);
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .faq-content {
        flex: 1;
    }

    .faq-question {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .faq-answer {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .faq-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .btn-faq-action {
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }

    .btn-cw-secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-brand-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    .btn-cw-danger {
        background: #dc2626;
        color: white;
        border: 1px solid #dc2626;
    }

    .btn-cw-danger:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        color: white;
        text-decoration: none;
    }

    /* Empty State */
    .no-faqs-message {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-500);
    }

    .no-faqs-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--cw-neutral-400);
    }

    /* Add FAQ Form */
    .add-faq-section {
        border-top: 2px solid var(--cw-brand-accent);
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
    }

    .faq-form {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    .character-counter {
        font-size: 0.75rem;
        color: var(--cw-neutral-500);
        text-align: right;
        margin-top: 0.25rem;
    }

    .character-counter.warning {
        color: #d97706;
    }

    .character-counter.danger {
        color: #dc2626;
    }

    .help-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        margin-top: 0.5rem;
    }

    /* Form Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .faqs-container {
            padding: 0 0.5rem;
        }

        .faqs-header,
        .section-wrapper,
        .add-faq-section {
            padding: 1.5rem;
        }

        .faqs-title {
            font-size: 1.75rem;
        }

        .templates-grid {
            grid-template-columns: 1fr;
        }

        .faq-categories {
            flex-direction: column;
        }

        .category-tab {
            min-width: auto;
        }

        .faq-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .faq-actions {
            align-self: flex-start;
        }

        .form-actions {
            flex-direction: column;
        }
    }

    /* Drag and Drop Styling */
    .sortable-ghost {
        opacity: 0.4;
    }

    .sortable-chosen {
        background: var(--cw-brand-accent);
    }

    .drop-zone {
        border: 2px dashed var(--cw-brand-primary);
        border-radius: 0.75rem;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
        color: var(--cw-brand-primary);
        background: rgba(47, 22, 15, 0.05);
    }

    .drop-zone.active {
        background: rgba(47, 22, 15, 0.1);
        border-color: var(--cw-brand-light);
    }
</style>
{% endblock %}

{% block venues_content %}
<section class="faqs-section">
    <div class="faqs-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            {% trans "Back to Venue Management" %}
        </a>

        <!-- Enhanced Header -->
        <div class="faqs-header">
            <div>
                <h1 class="faqs-title">{% trans "Manage FAQs" %}</h1>
                <p class="faqs-subtitle">Organize and manage frequently asked questions for {{ venue.venue_name }}</p>
            </div>
            
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-circle" data-progress="{{ faqs.count|mul:20 }}" style="--progress: {{ faqs.count|mul:20 }};">
                </div>
                <div>
                    <div class="progress-text">{{ faqs.count }}/{{ max_faqs }} FAQs Complete</div>
                    <small class="text-muted">Help customers with common questions</small>
                </div>
            </div>
        </div>

        <!-- Main FAQ Management Card -->
        <div class="faqs-card">
            <!-- Category Navigation -->
            <nav class="faq-categories">
                <button class="category-tab active" data-category="all">
                    <i class="fas fa-list"></i> All FAQs
                </button>
                <button class="category-tab" data-category="general">
                    <i class="fas fa-info-circle"></i> General
                </button>
                <button class="category-tab" data-category="booking">
                    <i class="fas fa-calendar-alt"></i> Booking
                </button>
                <button class="category-tab" data-category="services">
                    <i class="fas fa-spa"></i> Services
                </button>
                <button class="category-tab" data-category="policies">
                    <i class="fas fa-shield-alt"></i> Policies
                </button>
                <button class="category-tab" data-category="pricing">
                    <i class="fas fa-dollar-sign"></i> Pricing
                </button>
            </nav>

            <!-- FAQ Templates Section -->
            {% if has_templates and can_add_faq %}
            <div class="section-wrapper templates-section">
                <h2 class="section-title">
                    <i class="fas fa-lightbulb"></i>
                    {% trans "Quick Start Templates" %}
                </h2>
                <p class="help-text mb-3">{% trans "Click on any template below to quickly add professional FAQs to your venue." %}</p>
                
                <div class="templates-grid">
                    {% for template in faq_templates %}
                    <div class="template-card" onclick="applyTemplate({{ forloop.counter0 }})">
                        <div class="template-question">{{ template.question }}</div>
                        <div class="template-answer">{{ template.answer|truncatewords:15 }}...</div>
                        <form method="post" style="margin: 0;" class="template-form">
                            {% csrf_token %}
                            <input type="hidden" name="apply_template" value="1">
                            <input type="hidden" name="template_index" value="{{ forloop.counter0 }}">
                            <button type="submit" class="template-apply-btn">
                                <i class="fas fa-plus"></i>
                                {% trans "Use This Template" %}
                            </button>
                        </form>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Current FAQs Section -->
            <div class="section-wrapper faqs-list-section">
                <h2 class="section-title">
                    <i class="fas fa-question-circle"></i>
                    Current FAQs ({{ faqs.count }}/{{ max_faqs }})
                </h2>

                {% if faqs %}
                    <div id="faqs-list" class="faqs-list">
                        {% for faq in faqs %}
                        <div class="faq-item" data-faq-id="{{ faq.id }}" data-order="{{ faq.order }}" data-category="general">
                            <div class="faq-header">
                                <div class="drag-handle">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="faq-order">{{ faq.order }}</div>
                                <div class="faq-content">
                                    <div class="faq-question">{{ faq.question }}</div>
                                    <p class="faq-answer">{{ faq.answer }}</p>
                                </div>
                                <div class="faq-actions">
                                    <a href="{% url 'venues_app:edit_faq' faq.id %}" class="btn-faq-action btn-cw-secondary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'venues_app:delete_faq' faq.id %}" class="btn-faq-action btn-cw-danger" 
                                       onclick="return confirm('Are you sure you want to delete this FAQ?')">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Reorder Instructions -->
                    <div class="help-text mt-3">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "Drag and drop FAQs to reorder them. Changes are saved automatically." %}
                    </div>
                {% else %}
                    <div class="no-faqs-message">
                        <i class="fas fa-question-circle"></i>
                        <h4>{% trans "No FAQs yet" %}</h4>
                        <p>{% trans "Add your first FAQ below to help customers understand your services better." %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Add New FAQ Section -->
            {% if can_add_faq %}
            <div class="add-faq-section">
                <h2 class="section-title">
                    <i class="fas fa-plus-circle"></i>
                    {% trans "Add New FAQ" %}
                </h2>
                
                <div class="faq-form">
                    <form method="post" id="faq-form" novalidate>
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label class="form-label" for="{{ form.question.id_for_label }}">
                                {% trans "Question" %} <span class="text-danger">*</span>
                            </label>
                            {{ form.question|add_class:'form-control'|attr:'data-counter:question-counter' }}
                            <div class="character-counter" id="question-counter">
                                0 / 255 characters
                            </div>
                            {% if form.question.help_text %}
                                <div class="help-text">{{ form.question.help_text }}</div>
                            {% endif %}
                            {% for error in form.question.errors %}
                                <div class="text-danger mt-1">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.answer.id_for_label }}">
                                {% trans "Answer" %} <span class="text-danger">*</span>
                            </label>
                            {{ form.answer|add_class:'form-control'|attr:'data-counter:answer-counter' }}
                            <div class="character-counter" id="answer-counter">
                                0 / 500 characters
                            </div>
                            {% if form.answer.help_text %}
                                <div class="help-text">{{ form.answer.help_text }}</div>
                            {% endif %}
                            {% for error in form.answer.errors %}
                                <div class="text-danger mt-1">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-cw-primary" id="submit-btn">
                                <i class="fas fa-plus"></i>
                                {% trans "Add FAQ" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            {% else %}
            <div class="section-wrapper">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "You have reached the maximum number of FAQs (5) for your venue." %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category filtering
    const categoryTabs = document.querySelectorAll('.category-tab');
    const faqItems = document.querySelectorAll('.faq-item');
    
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const category = tab.getAttribute('data-category');
            
            // Update active tab
            categoryTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Filter FAQs
            faqItems.forEach(item => {
                const itemCategory = item.getAttribute('data-category');
                if (category === 'all' || itemCategory === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Sortable FAQs
    const faqsList = document.getElementById('faqs-list');
    if (faqsList) {
        const sortable = Sortable.create(faqsList, {
            animation: 150,
            handle: '.drag-handle',
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                updateFAQOrder();
            }
        });
    }
    
    // Character counters
    const inputs = document.querySelectorAll('[data-counter]');
    inputs.forEach(input => {
        const counterId = input.getAttribute('data-counter');
        const counter = document.getElementById(counterId);
        const maxLength = input.getAttribute('maxlength') || 500;
        
        function updateCounter() {
            const length = input.value.length;
            counter.textContent = `${length} / ${maxLength} characters`;
            
            // Update styling based on remaining characters
            counter.classList.remove('warning', 'danger');
            if (length > maxLength * 0.9) {
                counter.classList.add('danger');
            } else if (length > maxLength * 0.75) {
                counter.classList.add('warning');
            }
        }
        
        input.addEventListener('input', updateCounter);
        updateCounter(); // Initial update
    });
    
    // Form validation
    const form = document.getElementById('faq-form');
    const submitBtn = document.getElementById('submit-btn');
    
    if (form) {
        form.addEventListener('input', validateForm);
        
        function validateForm() {
            const question = form.querySelector('[name="question"]').value.trim();
            const answer = form.querySelector('[name="answer"]').value.trim();
            
            const isValid = question.length >= 10 && answer.length >= 20;
            submitBtn.disabled = !isValid;
        }
        
        validateForm(); // Initial validation
    }
    
    // Template application
    window.applyTemplate = function(index) {
        const templateForms = document.querySelectorAll('.template-form');
        if (templateForms[index]) {
            templateForms[index].submit();
        }
    };
    
    // Update FAQ order via AJAX
    function updateFAQOrder() {
        const items = document.querySelectorAll('.faq-item');
        const orderData = [];
        
        items.forEach((item, index) => {
            const faqId = item.getAttribute('data-faq-id');
            orderData.push({
                id: faqId,
                order: index + 1
            });
            
            // Update visual order number
            const orderElement = item.querySelector('.faq-order');
            if (orderElement) {
                orderElement.textContent = index + 1;
            }
        });
        
        // TODO: Implement update_faq_order URL and view
        // For now, just show a message that order was updated visually
        showMessage('FAQ order updated visually!', 'success');
        
        /* Commented out until update_faq_order endpoint is implemented
        // Send AJAX request to update order
        fetch('/venues/update-faq-order/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({order_data: orderData})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showMessage('FAQ order updated successfully!', 'success');
            } else {
                showMessage('Error updating FAQ order. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error updating FAQ order. Please try again.', 'error');
        });
        */
    }
    
    // Message display function
    function showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} alert-dismissible fade show`;
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.faqs-container');
        container.insertBefore(messageDiv, container.firstChild);
        
        // Auto dismiss after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
});
</script>
{% endblock %}
