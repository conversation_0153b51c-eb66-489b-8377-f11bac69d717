{% load venue_filters %}

<!-- Enhanced Change Notification Component -->
<div class="enhanced-change-notification">
    {% if change_analysis %}
    <div class="change-notification-container">
        <div class="notification-header">
            <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="notification-content">
                <h4 class="notification-title">Changes Detected - Re-approval Required</h4>
                <p class="notification-message">
                    Your venue has been moved to pending status due to significant changes. 
                    Review the changes below and your venue will be reviewed by our team.
                </p>
            </div>
            <div class="notification-badge">
                <span class="severity-badge severity-{{ change_analysis.approval_recommendation }}">
                    {{ change_analysis.approval_recommendation|title }} Review
                </span>
            </div>
        </div>

        <!-- Change Summary -->
        <div class="change-summary">
            <h5 class="summary-title">
                <i class="fas fa-list-alt"></i> Change Summary
            </h5>
            <div class="change-stats">
                <div class="stat-item total">
                    <div class="stat-number">{{ change_analysis.total_changes|default:0 }}</div>
                    <div class="stat-label">Total Changes</div>
                </div>
                {% if change_analysis.severity_breakdown.critical > 0 %}
                <div class="stat-item critical">
                    <div class="stat-number">{{ change_analysis.severity_breakdown.critical }}</div>
                    <div class="stat-label">Critical</div>
                </div>
                {% endif %}
                {% if change_analysis.severity_breakdown.major > 0 %}
                <div class="stat-item major">
                    <div class="stat-number">{{ change_analysis.severity_breakdown.major }}</div>
                    <div class="stat-label">Major</div>
                </div>
                {% endif %}
                {% if change_analysis.severity_breakdown.moderate > 0 %}
                <div class="stat-item moderate">
                    <div class="stat-number">{{ change_analysis.severity_breakdown.moderate }}</div>
                    <div class="stat-label">Moderate</div>
                </div>
                {% endif %}
                {% if change_analysis.severity_breakdown.minor > 0 %}
                <div class="stat-item minor">
                    <div class="stat-number">{{ change_analysis.severity_breakdown.minor }}</div>
                    <div class="stat-label">Minor</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- What Changed -->
        {% if change_analysis.changed_fields %}
        <div class="changed-sections">
            <h5 class="sections-title">
                <i class="fas fa-edit"></i> Sections Modified
            </h5>
            <div class="sections-grid">
                {% for field in change_analysis.changed_fields %}
                <div class="section-card">
                    <div class="section-icon">
                        {% if field == 'venue_name' %}
                            <i class="fas fa-tag"></i>
                        {% elif field == 'short_description' %}
                            <i class="fas fa-align-left"></i>
                        {% elif field == 'phone' or field == 'email' %}
                            <i class="fas fa-phone"></i>
                        {% elif field == 'categories' %}
                            <i class="fas fa-tags"></i>
                        {% elif field == 'services' %}
                            <i class="fas fa-spa"></i>
                        {% elif field == 'images' %}
                            <i class="fas fa-images"></i>
                        {% elif field == 'operating_hours' %}
                            <i class="fas fa-clock"></i>
                        {% elif field == 'amenities' %}
                            <i class="fas fa-star"></i>
                        {% else %}
                            <i class="fas fa-edit"></i>
                        {% endif %}
                    </div>
                    <div class="section-content">
                        <h6 class="section-name">{{ field|title|underscore_to_spaces }}</h6>
                        <p class="section-description">
                            {% if field == 'venue_name' %}
                                Venue name was updated
                            {% elif field == 'short_description' %}
                                Description was modified
                            {% elif field == 'phone' %}
                                Phone number was changed
                            {% elif field == 'email' %}
                                Email address was updated
                            {% elif field == 'categories' %}
                                Service categories were modified
                            {% elif field == 'services' %}
                                Services were added, removed, or modified
                            {% elif field == 'images' %}
                                Venue images were updated
                            {% elif field == 'operating_hours' %}
                                Operating hours were changed
                            {% elif field == 'amenities' %}
                                Amenities were modified
                            {% else %}
                                {{ field|title }} information was updated
                            {% endif %}
                        </p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Impact Information -->
        <div class="impact-info">
            <h5 class="impact-title">
                <i class="fas fa-info-circle"></i> What This Means
            </h5>
            <div class="impact-cards">
                <div class="impact-card visibility">
                    <div class="impact-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="impact-content">
                        <h6>Venue Visibility</h6>
                        <p>Your venue remains live and visible to customers during the review process.</p>
                    </div>
                </div>
                <div class="impact-card booking">
                    <div class="impact-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="impact-content">
                        <h6>Booking Status</h6>
                        <p>Existing bookings are protected and new bookings can still be made.</p>
                    </div>
                </div>
                <div class="impact-card review">
                    <div class="impact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="impact-content">
                        <h6>Review Process</h6>
                        <p>Changes will be reviewed within 24-48 hours by our team.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="notification-actions">
            <h5 class="actions-title">
                <i class="fas fa-tasks"></i> Next Steps
            </h5>
            <div class="action-buttons">
                <a href="{% url 'venues_app:provider_venue_detail' venue.id %}" class="action-btn primary">
                    <i class="fas fa-eye"></i> Review Changes
                </a>
                <a href="{% url 'venues_app:venue_edit' %}" class="action-btn secondary">
                    <i class="fas fa-edit"></i> Make Adjustments
                </a>
                <a href="#" class="action-btn info" data-bs-toggle="modal" data-bs-target="#approvalHelpModal">
                    <i class="fas fa-question-circle"></i> Get Help
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.enhanced-change-notification {
    margin-bottom: 2rem;
}

.change-notification-container {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border: 2px solid #f59e0b;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.15);
}

.notification-header {
    background: white;
    padding: 2rem;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    border-bottom: 1px solid #fed7aa;
}

.notification-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.notification-message {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.6;
}

.notification-badge {
    flex-shrink: 0;
}

.severity-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.severity-badge.severity-required {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.severity-badge.severity-recommended {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #dbeafe;
}

.severity-badge.severity-optional {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.change-summary, .changed-sections, .impact-info, .notification-actions {
    padding: 2rem;
    border-bottom: 1px solid #fed7aa;
}

.notification-actions {
    border-bottom: none;
}

.summary-title, .sections-title, .impact-title, .actions-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.change-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    min-width: 80px;
    border: 2px solid;
    transition: all 0.2s ease;
}

.stat-item.total {
    border-color: #e5e7eb;
}

.stat-item.critical {
    border-color: #fecaca;
    background: #fef2f2;
}

.stat-item.major {
    border-color: #fed7aa;
    background: #fffbeb;
}

.stat-item.moderate {
    border-color: #dbeafe;
    background: #eff6ff;
}

.stat-item.minor {
    border-color: #bbf7d0;
    background: #f0fdf4;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.section-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
}

.section-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.section-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.section-content {
    flex: 1;
}

.section-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.section-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.impact-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.impact-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
}

.impact-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.impact-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.impact-content h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.impact-content p {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    border: 2px solid;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: white;
    color: #374151;
    border-color: #e5e7eb;
}

.action-btn.secondary:hover {
    background: #f9fafb;
    color: #1f2937;
    text-decoration: none;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.action-btn.info {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border-color: #8b5cf6;
}

.action-btn.info:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-header {
        flex-direction: column;
        text-align: center;
    }
    
    .change-stats {
        justify-content: center;
    }
    
    .sections-grid, .impact-cards {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .section-card, .impact-card {
        flex-direction: column;
        text-align: center;
    }
    
    .section-icon, .impact-icon {
        margin: 0 auto;
    }
}
</style> 