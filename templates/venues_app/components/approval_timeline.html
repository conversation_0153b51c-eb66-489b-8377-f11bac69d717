<!-- Enhanced Approval Timeline Component -->
<div class="approval-timeline-container">
    <div class="timeline-header">
        <h3 class="timeline-title">
            <i class="fas fa-history"></i> Approval Timeline
        </h3>
        <div class="timeline-status">
            {% if venue.approval_status == 'approved' %}
                <span class="status-badge approved">
                    <i class="fas fa-check-circle"></i> Approved
                </span>
            {% elif venue.approval_status == 'pending' %}
                <span class="status-badge pending">
                    <i class="fas fa-clock"></i> Under Review
                </span>
            {% elif venue.approval_status == 'rejected' %}
                <span class="status-badge rejected">
                    <i class="fas fa-times-circle"></i> Needs Updates
                </span>
            {% else %}
                <span class="status-badge draft">
                    <i class="fas fa-edit"></i> Draft
                </span>
            {% endif %}
        </div>
    </div>

    <div class="timeline-events">
        {% for event in approval_timeline %}
        <div class="timeline-event event-{{ event.status }}">
            <div class="event-icon">
                <i class="{{ event.icon }}"></i>
            </div>
            <div class="event-content">
                <div class="event-header">
                    <h4 class="event-title">{{ event.title }}</h4>
                    <span class="event-timestamp">
                        {% if event.timestamp %}
                            {{ event.timestamp|date:"M d, Y" }} at {{ event.timestamp|time:"g:i A" }}
                        {% else %}
                            Processing...
                        {% endif %}
                    </span>
                </div>
                <p class="event-description">{{ event.description }}</p>
                
                {% if event.details %}
                <div class="event-details">
                    <small class="text-muted">{{ event.details }}</small>
                </div>
                {% endif %}
                
                {% if event.changed_fields %}
                <div class="changed-fields">
                    <small class="text-muted">Changed sections:</small>
                    <div class="field-badges">
                        {% for field in event.changed_fields %}
                        <span class="field-badge">{{ field|title }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                {% if event.user %}
                <div class="event-user">
                    <small class="text-muted">
                        {% if event.user.get_full_name %}
                            by {{ event.user.get_full_name }}
                        {% else %}
                            by {{ event.user.email }}
                        {% endif %}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Timeline Progress Indicator -->
    <div class="timeline-progress">
        <div class="progress-steps">
            <div class="step {% if approval_timeline|length > 0 %}completed{% endif %}">
                <span>Created</span>
            </div>
            <div class="step {% if venue.approval_status == 'pending' or venue.approval_status == 'approved' %}completed{% elif venue.approval_status == 'rejected' %}error{% endif %}">
                <span>Submitted</span>
            </div>
            <div class="step {% if venue.approval_status == 'approved' %}completed{% elif venue.approval_status == 'rejected' %}error{% endif %}">
                <span>Reviewed</span>
            </div>
            <div class="step {% if venue.approval_status == 'approved' %}completed{% endif %}">
                <span>Live</span>
            </div>
        </div>
    </div>
</div>

<style>
.approval-timeline-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
}

.timeline-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.approved {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.status-badge.pending {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.status-badge.rejected {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status-badge.draft {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.timeline-events {
    position: relative;
    padding-left: 2rem;
}

.timeline-events::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e5e7eb, #d1d5db);
}

.timeline-event {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.event-icon {
    position: absolute;
    left: -2.25rem;
    top: 0.25rem;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    z-index: 1;
}

.event-completed .event-icon {
    background: linear-gradient(135deg, #16a34a, #15803d);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.event-pending .event-icon {
    background: linear-gradient(135deg, #d97706, #b45309);
    box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
}

.event-error .event-icon {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.event-attention .event-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.event-content {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.event-content:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.event-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.event-timestamp {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.event-description {
    color: #4b5563;
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.event-details {
    margin-bottom: 0.75rem;
}

.changed-fields {
    margin-bottom: 0.75rem;
}

.field-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.field-badge {
    background: #ede9fe;
    color: #7c3aed;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.event-user {
    font-size: 0.75rem;
    color: #6b7280;
}

.timeline-progress {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: 0;
    right: 0;
    height: 2px;
    background: #e5e7eb;
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step::before {
    content: '';
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: #e5e7eb;
    border: 3px solid white;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.completed::before {
    background: #16a34a;
}

.step.error::before {
    background: #dc2626;
}

.step span {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-align: center;
}

.step.completed span {
    color: #16a34a;
    font-weight: 600;
}

.step.error span {
    color: #dc2626;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .timeline-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .timeline-events {
        padding-left: 1rem;
    }
    
    .timeline-event {
        padding-left: 1.5rem;
    }
    
    .event-icon {
        left: -1.75rem;
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }
    
    .event-header {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }
}
</style> 