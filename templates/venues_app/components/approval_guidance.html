<!-- Approval Guidance Component -->
<div class="approval-guidance-container">
    <div class="guidance-header">
        <h3 class="guidance-title">
            <i class="fas fa-compass"></i> Approval Guidance
        </h3>
        <div class="estimated-time">
            <i class="fas fa-clock" style="color: #f59e0b;"></i>
            Estimated approval time: <strong>{{ approval_guidance.estimated_approval_time }}</strong>
        </div>
    </div>

    <!-- Auto-Approval Status -->
    {% if approval_guidance.auto_approval_eligible %}
    <div class="auto-approval-banner eligible">
        <div class="banner-content">
            <div class="banner-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <div class="banner-text">
                <h4>🎉 Auto-Approval Eligible!</h4>
                <p>Your venue meets all criteria for instant approval. Click below to activate!</p>
            </div>
            <div class="banner-action">
                <a href="{% url 'venues_app:trigger_auto_approval_check' venue.id %}" class="auto-approval-btn">
                    <i class="fas fa-bolt"></i> Activate Auto-Approval
                </a>
            </div>
        </div>
    </div>
    {% else %}
    <div class="auto-approval-banner not-eligible">
        <div class="banner-content">
            <div class="banner-icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="banner-text">
                <h4>Auto-Approval Progress</h4>
                <p>Complete the priority actions below to qualify for instant approval.</p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Priority Actions -->
    {% if approval_guidance.priority_actions %}
    <div class="guidance-section priority">
        <div class="section-header">
            <h4 class="section-title">
                <span class="priority-badge">HIGH PRIORITY</span>
                Critical Actions Required
            </h4>
            <div class="section-subtitle">
                Complete these first - they're required for approval
            </div>
        </div>
        <div class="actions-list">
            {% for action in approval_guidance.priority_actions %}
            <div class="action-item priority-action">
                <div class="action-icon critical">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="action-content">
                    <h5 class="action-title">{{ action.action }}</h5>
                    <p class="action-impact">{{ action.impact }}</p>
                    <div class="action-meta">
                        <span class="time-estimate">
                            <i class="fas fa-clock"></i> {{ action.estimated_time }}
                        </span>
                        <span class="section-tag">{{ action.section|title }}</span>
                    </div>
                </div>
                <div class="action-button">
                    <a href="{% url 'venues_app:venue_edit' %}#{{ action.section }}" class="action-btn critical">
                        Fix Now
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Recommendations -->
    {% if approval_guidance.recommendations %}
    <div class="guidance-section recommendations">
        <div class="section-header">
            <h4 class="section-title">
                <span class="recommendation-badge">RECOMMENDED</span>
                Improve Your Approval Chances
            </h4>
            <div class="section-subtitle">
                These improvements will help your venue get approved faster and rank higher
            </div>
        </div>
        <div class="actions-list">
            {% for action in approval_guidance.recommendations %}
            <div class="action-item recommendation-action">
                <div class="action-icon recommended">
                    <i class="fas fa-thumbs-up"></i>
                </div>
                <div class="action-content">
                    <h5 class="action-title">{{ action.action }}</h5>
                    <p class="action-impact">{{ action.impact }}</p>
                    <div class="action-meta">
                        <span class="time-estimate">
                            <i class="fas fa-clock"></i> {{ action.estimated_time }}
                        </span>
                        <span class="section-tag">{{ action.section|title }}</span>
                    </div>
                </div>
                <div class="action-button">
                    <a href="{% url 'venues_app:venue_edit' %}#{{ action.section }}" class="action-btn recommended">
                        Improve
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Quality Tips -->
    <div class="guidance-section tips">
        <div class="section-header">
            <h4 class="section-title">
                <i class="fas fa-lightbulb"></i> Pro Tips for Faster Approval
            </h4>
            <div class="section-subtitle">
                Following these best practices will help you get approved quickly
            </div>
        </div>
        <div class="tips-grid">
            {% for tip in approval_guidance.quality_tips %}
            <div class="tip-card">
                <div class="tip-icon">
                    <i class="fas fa-star"></i>
                </div>
                <p class="tip-text">{{ tip }}</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Category-Specific Tips -->
    {% if approval_guidance.completion_tips %}
    <div class="guidance-section category-tips">
        <div class="section-header">
            <h4 class="section-title">
                <i class="fas fa-bullseye"></i> Tips for Your Venue Type
            </h4>
            <div class="section-subtitle">
                Specialized advice for {{ venue.categories.first.name|default:"your venue type" }}
            </div>
        </div>
        <div class="category-tips-list">
            {% for tip in approval_guidance.completion_tips %}
            <div class="category-tip">
                <div class="tip-bullet">
                    <i class="fas fa-check-circle"></i>
                </div>
                <span class="tip-content">{{ tip }}</span>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions Panel -->
    <div class="quick-actions-panel">
        <h4 class="panel-title">Quick Actions</h4>
        <div class="quick-actions-grid">
            <a href="{% url 'venues_app:venue_edit' %}" class="quick-action edit">
                <i class="fas fa-edit"></i>
                <span>Edit Venue</span>
            </a>
            <a href="{% url 'venues_app:manage_services' %}" class="quick-action services">
                <i class="fas fa-spa"></i>
                <span>Manage Services</span>
            </a>
            <a href="{% url 'venues_app:manage_venue_images' %}" class="quick-action images">
                <i class="fas fa-camera"></i>
                <span>Add Photos</span>
            </a>
            <a href="{% url 'venues_app:manage_faqs' %}" class="quick-action faqs">
                <i class="fas fa-question-circle"></i>
                <span>Add FAQs</span>
            </a>
            <a href="{% url 'venues_app:manage_operating_hours' %}" class="quick-action hours">
                <i class="fas fa-clock"></i>
                <span>Set Hours</span>
            </a>
            <a href="{% url 'venues_app:venue_preview' %}" class="quick-action preview">
                <i class="fas fa-eye"></i>
                <span>Preview</span>
            </a>
        </div>
    </div>

    <!-- Help Section -->
    <div class="help-section">
        <div class="help-content">
            <h4 class="help-title">
                <i class="fas fa-question-circle"></i> Need Help?
            </h4>
            <p class="help-text">
                Our approval process is designed to ensure quality venues for customers. 
                If you have questions or need assistance, we're here to help!
            </p>
            <div class="help-actions">
                <a href="#" class="help-btn contact">
                    <i class="fas fa-headset"></i> Contact Support
                </a>
                <a href="#" class="help-btn guide">
                    <i class="fas fa-book"></i> Approval Guide
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.approval-guidance-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.guidance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
}

.guidance-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.estimated-time {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auto-approval-banner {
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid;
}

.auto-approval-banner.eligible {
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border-color: #22c55e;
}

.auto-approval-banner.not-eligible {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-color: #f59e0b;
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.banner-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.eligible .banner-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.not-eligible .banner-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.banner-text {
    flex: 1;
    min-width: 200px;
}

.banner-text h4 {
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: #1f2937;
}

.banner-text p {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.auto-approval-btn {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.auto-approval-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
    color: white;
    text-decoration: none;
}

.guidance-section {
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.priority-badge {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

.recommendation-badge {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

.section-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
}

.actions-list {
    display: grid;
    gap: 1rem;
}

.action-item {
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
}

.action-item:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.priority-action {
    border-color: #fecaca;
    background: #fef2f2;
}

.priority-action:hover {
    border-color: #dc2626;
}

.recommendation-action {
    border-color: #dbeafe;
    background: #eff6ff;
}

.recommendation-action:hover {
    border-color: #3b82f6;
}

.action-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.action-icon.critical {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.action-icon.recommended {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.action-impact {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0 0 0.75rem 0;
    line-height: 1.5;
}

.action-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.time-estimate {
    font-size: 0.75rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.section-tag {
    background: #e5e7eb;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.action-button {
    flex-shrink: 0;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    display: inline-block;
}

.action-btn.critical {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
}

.action-btn.critical:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.action-btn.recommended {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-btn.recommended:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.tip-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
}

.tip-card:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tip-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.tip-text {
    font-size: 0.875rem;
    color: #4b5563;
    margin: 0;
    line-height: 1.6;
}

.category-tips-list {
    display: grid;
    gap: 0.75rem;
}

.category-tip {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border-left: 4px solid #10b981;
}

.tip-bullet {
    color: #10b981;
    font-size: 1rem;
    margin-top: 0.125rem;
}

.tip-content {
    font-size: 0.875rem;
    color: #4b5563;
    line-height: 1.5;
}

.quick-actions-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1.5rem 0;
    text-align: center;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.quick-action {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.quick-action:hover {
    border-color: #3b82f6;
    background: #eff6ff;
    color: #1d4ed8;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.quick-action i {
    font-size: 1.5rem;
}

.help-section {
    background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
    border: 2px solid #e9d5ff;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
}

.help-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

.help-text {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0 0 1.5rem 0;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.help-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.help-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.help-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .guidance-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .banner-content {
        flex-direction: column;
        text-align: center;
    }
    
    .action-item {
        flex-direction: column;
        text-align: center;
    }
    
    .action-meta {
        justify-content: center;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .help-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style> 