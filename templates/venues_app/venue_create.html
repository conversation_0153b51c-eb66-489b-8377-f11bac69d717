{% extends 'base.html' %}

{% block title %}Create Your Venue - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Creation */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venue Creation Section */
    .venue-creation-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .venue-creation-container {
        max-width: 900px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Header */
    .venue-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem 1rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
        margin-bottom: 0;
    }

    .venue-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="venue-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23venue-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .venue-header .content {
        position: relative;
        z-index: 2;
    }

    .venue-header-icon {
        width: 60px;
        height: 60px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .venue-header-content h1 {
        font-family: var(--cw-font-display);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        line-height: 1.2;
    }

    .venue-header-content p {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Form Card Container */
    .venue-form-card {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
    }

    .venue-form-body {
        padding: 2rem;
    }

    /* Form Sections */
    .form-section-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-section-cw:hover {
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .form-section-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .section-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    .form-section-body {
        padding: 2rem;
    }

    /* Form Styling */
    .form-group-cw {
        margin-bottom: 2rem;
    }

    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: block;
    }

    .form-control-cw, .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
        width: 100%;
    }

    .form-control-cw:focus, .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw::placeholder {
        color: var(--cw-neutral-600);
        opacity: 0.7;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding: 0.5rem 0.75rem;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.375rem;
        display: block !important;
    }

    .invalid-feedback-cw::before {
        content: "\f071";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        margin-right: 0.5rem;
        color: #dc2626;
    }

    .form-control-cw.is-invalid, .form-select-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .required-cw {
        color: #dc2626;
        font-weight: 600;
    }

    /* Alert Styling */
    .alert-cw-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-cw-danger::before {
        content: "\f071";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        margin-right: 0.75rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    /* Venue Status Options */
    .venue-status-options {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .status-option {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        background: white;
        position: relative;
    }

    .status-option:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .status-option.selected {
        border-color: var(--cw-brand-primary);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-md);
    }

    .status-option input[type="radio"] {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 1.25rem;
        height: 1.25rem;
        accent-color: var(--cw-brand-primary);
    }

    .status-option-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .status-option-description {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Submit Section */
    .submit-section {
        text-align: center;
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        margin-top: 2rem;
    }

    .submit-note {
        margin-top: 1rem;
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    /* Category Selection Styling */
    .category-selection-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .category-option {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        background: white;
        position: relative;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .category-option:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
        background: var(--cw-gradient-card-subtle);
    }

    .category-option.selected {
        border-color: var(--cw-brand-primary);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-md);
    }

    .category-checkbox {
        width: 1.25rem !important;
        height: 1.25rem !important;
        margin: 0 !important;
        accent-color: var(--cw-brand-primary);
        flex-shrink: 0;
        margin-top: 0.125rem !important;
    }

    .category-content {
        flex: 1;
    }

    .category-name {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .category-description {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        line-height: 1.4;
        margin: 0;
    }

    .category-selection-note {
        background: var(--cw-brand-accent);
        border: 1px solid var(--cw-brand-primary-light);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
        color: var(--cw-brand-primary);
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-selection-note i {
        color: var(--cw-brand-primary);
        flex-shrink: 0;
    }

    /* Category Error Styling */
    .category-field-error .category-option {
        border-color: #dc2626;
    }

    .category-field-error .category-selection-note {
        background: #fef2f2;
        border-color: #fecaca;
        color: #dc2626;
    }

    /* Responsive Design for Categories */
    @media (max-width: 768px) {
        .category-selection-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }
        
        .category-option {
            padding: 1rem;
        }
        
        .category-name {
            font-size: 1rem;
        }
        
        .category-description {
            font-size: 0.85rem;
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .venue-creation-section {
            padding: 3rem 0;
        }

        .venue-creation-container {
            padding: 0 1.5rem;
        }

        .venue-header {
            padding: 2rem 2rem 1.5rem;
        }

        .venue-header-content h1 {
            font-size: 2.25rem;
        }

        .venue-header-content p {
            font-size: 1.125rem;
        }

        .venue-form-body {
            padding: 2rem;
        }

        .form-section-body {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .venue-creation-container {
            padding: 0 1rem;
        }

        .venue-header {
            padding: 1rem 1.5rem 0.75rem;
        }

        .venue-form-body {
            padding: 1.5rem;
        }

        .form-section-body {
            padding: 1.5rem;
        }

        .venue-header-content h1 {
            font-size: 1.75rem;
        }

        .venue-header-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-creation-section">
    <div class="venue-creation-container">
        <!-- Header -->
        <div class="venue-header">
            <div class="content">
                <div class="venue-header-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="venue-header-content">
                    <h1>Create Your Venue</h1>
                    <p>Set up your business venue to start attracting customers on CozyWish</p>
                </div>
            </div>
        </div>

        <div class="venue-form-card">
            <div class="venue-form-body">
                <form method="post">
                    {% csrf_token %}

                    <!-- Form-wide errors -->
                    {% if form.non_field_errors %}
                        <div class="alert-cw-danger">
                            <h6>Please correct the following errors:</h6>
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Basic Information -->
                    <div class="form-section-cw">
                        <div class="form-section-header">
                            <h3 class="section-title-cw">
                                <i class="fas fa-info-circle"></i>
                                Basic Information
                            </h3>
                        </div>
                        <div class="form-section-body">
                            <div class="form-group-cw">
                                <label class="form-label-cw">
                                    Venue Name <span class="required-cw">*</span>
                                </label>
                                {% if form.venue_name.errors %}
                                    {{ form.venue_name|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.venue_name.errors.0 }}</div>
                                {% else %}
                                    {{ form.venue_name|add_class:"form-control-cw" }}
                                {% endif %}
                                {% if form.venue_name.help_text %}
                                    <div class="form-text-cw">{{ form.venue_name.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group-cw">
                                <label class="form-label-cw">
                                    Short Description <span class="required-cw">*</span>
                                </label>
                                {% if form.short_description.errors %}
                                    {{ form.short_description|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.short_description.errors.0 }}</div>
                                {% else %}
                                    {{ form.short_description|add_class:"form-control-cw" }}
                                {% endif %}
                                {% if form.short_description.help_text %}
                                    <div class="form-text-cw">{{ form.short_description.help_text }}</div>
                                {% endif %}
                                <div class="form-text-cw">Briefly describe your venue and the services you offer (minimum 10 characters, maximum 500 characters)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Selection -->
                    <div class="form-section-cw">
                        <div class="form-section-header">
                            <h3 class="section-title-cw">
                                <i class="fas fa-tags"></i>
                                Categories
                            </h3>
                        </div>
                        <div class="form-section-body">
                            <div class="form-group-cw {% if form.categories.errors %}category-field-error{% endif %}">
                                <label class="form-label-cw">
                                    Select Categories <span class="required-cw">*</span>
                                </label>
                                
                                {% if form.categories.errors %}
                                    <div class="invalid-feedback-cw">{{ form.categories.errors.0 }}</div>
                                {% endif %}

                                <div class="category-selection-grid">
                                    {% for choice in form.categories %}
                                        <div class="category-option {% if choice.data.selected %}selected{% endif %}" 
                                             onclick="toggleCategoryOption(this, '{{ choice.data.value }}')">
                                            {{ choice.tag }}
                                            <div class="category-content">
                                                <span class="category-name">{{ choice.choice_label }}</span>
                                                {% if choice.choice_label == 'Spa & Wellness' %}
                                                    <p class="category-description">Full-service spa facilities offering comprehensive wellness treatments, relaxation services, and rejuvenation experiences.</p>
                                                {% elif choice.choice_label == 'Massage Therapy' %}
                                                    <p class="category-description">Professional massage therapy services including therapeutic massage, deep tissue, Swedish massage, and specialized bodywork treatments.</p>
                                                {% elif choice.choice_label == 'Beauty & Salon' %}
                                                    <p class="category-description">Beauty and salon services including hair styling, nail care, makeup application, and comprehensive beauty enhancement services.</p>
                                                {% elif choice.choice_label == 'Medical Spa' %}
                                                    <p class="category-description">Medical aesthetic treatments combining medical expertise with spa luxury, including advanced skincare and anti-aging treatments.</p>
                                                {% elif choice.choice_label == 'Fitness & Wellness' %}
                                                    <p class="category-description">Fitness and wellness centers offering yoga classes, fitness training, wellness coaching, and integrated health programs.</p>
                                                {% else %}
                                                    <p class="category-description">{{ choice.choice_label }} services and treatments.</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <div class="category-selection-note">
                                    <i class="fas fa-info-circle"></i>
                                    You can select up to 3 categories that best describe your venue. Choose categories that align with your primary services to help customers find you easily.
                                </div>

                                {% if form.categories.help_text %}
                                    <div class="form-text-cw">{{ form.categories.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="form-section-cw">
                        <div class="form-section-header">
                            <h3 class="section-title-cw">
                                <i class="fas fa-map-marker-alt"></i>
                                Location
                            </h3>
                        </div>
                        <div class="form-section-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group-cw">
                                        <label class="form-label-cw">
                                            State <span class="required-cw">*</span>
                                        </label>
                                        {% if form.state.errors %}
                                            {{ form.state|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.state.errors.0 }}</div>
                                        {% else %}
                                            {{ form.state|add_class:"form-control-cw" }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group-cw">
                                        <label class="form-label-cw">
                                            County <span class="required-cw">*</span>
                                        </label>
                                        {% if form.county.errors %}
                                            {{ form.county|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.county.errors.0 }}</div>
                                        {% else %}
                                            {{ form.county|add_class:"form-control-cw" }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group-cw">
                                <label class="form-label-cw">
                                    City <span class="required-cw">*</span>
                                </label>
                                {% if form.city.errors %}
                                    {{ form.city|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.city.errors.0 }}</div>
                                {% else %}
                                    {{ form.city|add_class:"form-control-cw" }}
                                {% endif %}
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group-cw">
                                        <label class="form-label-cw">
                                            Street Number <span class="required-cw">*</span>
                                        </label>
                                        {% if form.street_number.errors %}
                                            {{ form.street_number|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.street_number.errors.0 }}</div>
                                        {% else %}
                                            {{ form.street_number|add_class:"form-control-cw" }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="form-group-cw">
                                        <label class="form-label-cw">
                                            Street Name <span class="required-cw">*</span>
                                        </label>
                                        {% if form.street_name.errors %}
                                            {{ form.street_name|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.street_name.errors.0 }}</div>
                                        {% else %}
                                            {{ form.street_name|add_class:"form-control-cw" }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Venue Status Selection -->
                    <div class="form-section-cw">
                        <div class="form-section-header">
                            <h3 class="section-title-cw">
                                <i class="fas fa-check-circle"></i>
                                Venue Status
                            </h3>
                        </div>
                        <div class="form-section-body">
                            <div class="form-group-cw">
                                <label class="form-label-cw">
                                    Choose how to save your venue <span class="required-cw">*</span>
                                </label>
                                <div class="form-text-cw mb-3">You can save as draft to edit later, or submit for admin approval to make it visible to customers</div>

                                {% if form.venue_status.errors %}
                                    <div class="invalid-feedback-cw mb-3">{{ form.venue_status.errors.0 }}</div>
                                {% endif %}

                                <div class="venue-status-options">
                                    {% for choice in form.venue_status %}
                                        <div class="status-option {% if choice.data.selected %}selected{% endif %}" onclick="selectStatusOption(this, '{{ choice.data.value }}')">
                                            {{ choice.tag }}
                                            <div class="status-option-content">
                                                <div class="status-option-title">{{ choice.choice_label }}</div>
                                                {% if choice.data.value == 'draft' %}
                                                    <div class="status-option-description">
                                                        Save your venue privately. You can edit all details (description, images, categories, operating hours) and submit for approval when ready.
                                                    </div>
                                                {% else %}
                                                    <div class="status-option-description">
                                                        Submit your venue for admin review. Once approved, it will be visible to customers. You can still edit details after approval.
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                {% if form.venue_status.help_text %}
                                    <div class="form-text-cw">{{ form.venue_status.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Submit Section -->
                    <div class="submit-section">
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-save me-2"></i>Create Venue
                        </button>
                        <div class="submit-note">
                            <i class="fas fa-info-circle me-2"></i>
                            After creating your venue, you can add more details like images, categories, services, and operating hours from your venue management page.
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
    function selectStatusOption(element, value) {
        // Remove selected class from all options
        document.querySelectorAll('.status-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // Add selected class to clicked option
        element.classList.add('selected');
        
        // Check the radio button
        const radioButton = element.querySelector('input[type="radio"]');
        if (radioButton) {
            radioButton.checked = true;
        }
    }

    function toggleCategoryOption(element, value) {
        // Find the checkbox within this category option
        const checkbox = element.querySelector('input[type="checkbox"]');
        if (checkbox) {
            // Toggle the checkbox state
            checkbox.checked = !checkbox.checked;
            
            // Update visual state
            if (checkbox.checked) {
                element.classList.add('selected');
            } else {
                element.classList.remove('selected');
            }
        }
    }
</script>
{% endblock %}
