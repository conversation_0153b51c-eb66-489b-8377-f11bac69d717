{% extends 'base.html' %}

{% block title %}Submit Review - {{ venue.name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Submit Review */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Review Section */
    .review-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .review-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Review Card */
    .review-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .review-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(47, 22, 15, 0.1), 0 10px 10px -5px rgba(47, 22, 15, 0.04);
    }

    /* Review Header */
    .review-header {
        background: var(--cw-gradient-card-subtle);
        padding: 2rem 2.5rem 1.5rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .review-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="review-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23review-pattern)"/></svg>') repeat;
        opacity: 0.4;
        z-index: 1;
    }

    .review-header .content {
        position: relative;
        z-index: 2;
    }

    .review-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .review-icon i {
        font-size: 2rem;
        color: white;
    }

    .review-title {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .review-subtitle {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Review Body */
    .review-body {
        padding: 2.5rem;
    }

    /* Breadcrumb Styling */
    .breadcrumb-cw {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
    }

    .breadcrumb-cw .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .breadcrumb-cw .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-cw .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Form Styling */
    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
    }

    /* Star Rating */
    .star-rating-section {
        margin-bottom: 2rem;
    }

    .star-rating-input {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .star-container {
        position: relative;
    }

    .star-label {
        font-size: 2.5rem;
        color: var(--cw-brand-accent);
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;
        line-height: 1;
    }

    .star-label:hover,
    .star-label.active {
        color: var(--cw-brand-primary);
        transform: scale(1.1);
    }

    .star-rating-input input[type="radio"]:checked + .star-label {
        color: var(--cw-brand-primary);
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Error Messages */
    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: block;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    @media (min-width: 768px) {
        .action-buttons {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .review-container {
            padding: 0 0.5rem;
        }

        .review-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .review-body {
            padding: 1.5rem;
        }

        .review-title {
            font-size: 1.5rem;
        }

        .review-icon {
            width: 64px;
            height: 64px;
        }

        .review-icon i {
            font-size: 1.5rem;
        }

        .star-label {
            font-size: 2rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="review-section">
    <div class="review-container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-cw">
                <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
                <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">{{ venue.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Submit Review</li>
            </ol>
        </nav>

        <!-- Review Card -->
        <div class="review-card">
            <div class="review-header">
                <div class="content">
                    <div class="review-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h1 class="review-title">{% if existing_review %}Edit Your Review{% else %}Share Your Experience{% endif %}</h1>
                    <p class="review-subtitle">Tell others about your experience at {{ venue.name }}</p>
                </div>
            </div>

            <div class="review-body">
                <form method="post" action="{% url 'venues_app:submit_review' venue_slug=venue.slug %}">
                    {% csrf_token %}

                    <!-- Rating Section -->
                    <div class="star-rating-section">
                        <label class="form-label-cw">How would you rate your experience?</label>
                        <div class="star-rating-input">
                            {% for i in '12345' %}
                            <div class="star-container">
                                <input type="radio" name="{{ form.rating.name }}" id="star{{ i }}" value="{{ i }}" class="d-none" {% if form.rating.value|stringformat:"s" == i %}checked{% endif %}>
                                <label for="star{{ i }}" class="star-label" data-rating="{{ i }}">★</label>
                            </div>
                            {% endfor %}
                        </div>
                        {% if form.rating.errors %}
                        <div class="invalid-feedback-cw">
                            {{ form.rating.errors.0 }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Comment Section -->
                    <div class="mb-4">
                        <label for="{{ form.comment.id_for_label }}" class="form-label-cw">Share your detailed review</label>
                        <textarea name="{{ form.comment.name }}"
                                  id="{{ form.comment.id_for_label }}"
                                  class="form-control-cw {% if form.comment.errors %}is-invalid{% endif %}"
                                  rows="6"
                                  placeholder="Tell us about your experience - the service quality, atmosphere, staff, and anything else that would help other customers...">{{ form.comment.value|default:'' }}</textarea>
                        {% if form.comment.errors %}
                        <div class="invalid-feedback-cw">
                            {{ form.comment.errors.0 }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn-cw-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Venue
                        </a>
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-star me-2"></i>{% if existing_review %}Update Review{% else %}Submit Review{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    const starLabels = document.querySelectorAll('.star-label');
    const starInputs = document.querySelectorAll('input[name="{{ form.rating.name }}"]');

    // Initialize stars based on current value
    updateStars();

    // Add event listeners to star labels
    starLabels.forEach((label, index) => {
        // Hover effects
        label.addEventListener('mouseenter', () => {
            highlightStars(index);
        });

        label.addEventListener('mouseleave', () => {
            updateStars();
        });

        // Click to select
        label.addEventListener('click', () => {
            starInputs[index].checked = true;
            updateStars();
        });
    });

    function highlightStars(upToIndex) {
        starLabels.forEach((label, index) => {
            if (index <= upToIndex) {
                label.style.color = 'var(--cw-brand-primary)';
                label.classList.add('active');
            } else {
                label.style.color = 'var(--cw-brand-accent)';
                label.classList.remove('active');
            }
        });
    }

    function updateStars() {
        // Find the selected rating
        let selectedIndex = -1;
        starInputs.forEach((input, index) => {
            if (input.checked) {
                selectedIndex = index;
            }
        });

        // Update star colors based on selection
        starLabels.forEach((label, index) => {
            if (index <= selectedIndex) {
                label.style.color = 'var(--cw-brand-primary)';
                label.classList.add('active');
            } else {
                label.style.color = 'var(--cw-brand-accent)';
                label.classList.remove('active');
            }
        });
    }

    // Form validation enhancement
    const form = document.querySelector('form');
    const ratingInputs = document.querySelectorAll('input[name="{{ form.rating.name }}"]');
    const commentTextarea = document.querySelector('textarea[name="{{ form.comment.name }}"]');

    form.addEventListener('submit', function(e) {
        let isValid = true;

        // Check if rating is selected
        const ratingSelected = Array.from(ratingInputs).some(input => input.checked);
        if (!ratingSelected) {
            e.preventDefault();
            alert('Please select a rating before submitting your review.');
            isValid = false;
        }

        // Check if comment is provided
        if (commentTextarea.value.trim().length < 10) {
            e.preventDefault();
            alert('Please provide a detailed review (at least 10 characters).');
            isValid = false;
        }
    });

    // Character counter for textarea
    if (commentTextarea) {
        const maxLength = 1000;
        const counterDiv = document.createElement('div');
        counterDiv.className = 'text-muted small mt-2';
        counterDiv.style.textAlign = 'right';
        commentTextarea.parentNode.appendChild(counterDiv);

        function updateCounter() {
            const remaining = maxLength - commentTextarea.value.length;
            counterDiv.textContent = `${commentTextarea.value.length}/${maxLength} characters`;

            if (remaining < 50) {
                counterDiv.style.color = '#dc2626';
            } else {
                counterDiv.style.color = 'var(--cw-neutral-600)';
            }
        }

        commentTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
{% endblock %}
