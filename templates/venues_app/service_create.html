{% extends 'venues_app/base_venues.html' %}

{% block title %}Create Service - {{ venue.venue_name }} - CozyWish{% endblock %}

{% load widget_tweaks %}

{% block extra_css %}
<style>
    /* Multi-step form styling */
    .multi-step-form {
        max-width: 800px;
        margin: 0 auto;
    }

    .progress-bar-cw {
        background: var(--cw-accent-light);
        border-radius: 1rem;
        height: 0.5rem;
        margin-bottom: 2rem;
        overflow: hidden;
        position: relative;
    }

    .progress-fill {
        background: var(--cw-gradient-brand-button);
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 1rem;
    }

    .step-indicators {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        position: relative;
    }

    .step-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        flex: 1;
    }

    .step-circle {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 2px solid var(--cw-brand-accent);
        background: white;
        color: var(--cw-neutral-600);
    }

    .step-circle.active {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .step-circle.completed {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        max-width: 120px;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        min-height: 400px;
    }

    .step-section {
        display: none;
    }

    .step-section.active {
        display: block;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-number {
        display: inline-block;
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 2rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .step-heading {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: var(--cw-neutral-600);
        font-size: 1rem;
    }

    .field-group {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .field-group-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .field-group-icon {
        background: var(--cw-brand-primary);
        color: white;
        width: 2rem;
        height: 2rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .form-control-cw {
        border-radius: 0.5rem;
        border: 2px solid var(--cw-brand-accent);
        padding: 0.75rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-valid {
        border-color: #28a745;
    }

    .form-control-cw.is-invalid {
        border-color: #dc3545;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .valid-feedback {
        display: block;
        color: #28a745;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--cw-brand-accent);
    }

    .btn-nav {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-prev {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-accent);
    }

    .btn-prev:hover {
        background: var(--cw-brand-accent);
    }

    .btn-next, .btn-submit {
        background: var(--cw-gradient-brand-button);
        color: white;
    }

    .btn-next:hover, .btn-submit:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-next:disabled, .btn-submit:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .required-field {
        color: #dc3545;
    }

    .price-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .validation-message {
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    }

    .validation-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .validation-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }

    .validation-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .slug-preview {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
        font-family: monospace;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    @media (max-width: 768px) {
        .multi-step-form {
            padding: 0 1rem;
        }

        .step-content {
            padding: 1.5rem;
        }

        .price-row {
            grid-template-columns: 1fr;
        }

        .step-indicators {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .step-indicator {
            flex: none;
            min-width: 80px;
        }

        .form-buttons {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block venues_content %}
<div class="container my-5">
    <div class="multi-step-form">
        <!-- Header -->
        <div class="text-center mb-4">
            <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary mb-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Services
            </a>
            <h1 class="h2 text-brand-cw">Create New Service</h1>
            <p class="text-neutral-cw">Add a new service for {{ venue.venue_name }}</p>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar-cw">
            <div class="progress-fill" id="progressFill" style="width: 25%"></div>
        </div>

        <!-- Step Indicators -->
        <div class="step-indicators">
            <div class="step-indicator">
                <div class="step-circle active" data-step="1">1</div>
                <div class="step-title active">Basic Info</div>
            </div>
            <div class="step-indicator">
                <div class="step-circle" data-step="2">2</div>
                <div class="step-title">Pricing & Duration</div>
            </div>
            <div class="step-indicator">
                <div class="step-circle" data-step="3">3</div>
                <div class="step-title">Settings</div>
            </div>
            <div class="step-indicator">
                <div class="step-circle" data-step="4">4</div>
                <div class="step-title">Review</div>
            </div>
        </div>

        <!-- Form -->
        <form method="post" enctype="multipart/form-data" id="serviceForm" novalidate>
            {% csrf_token %}
            
            <div class="step-content">
                <!-- Step 1: Basic Information -->
                <div class="step-section active" data-step="1">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <h2 class="step-heading">Basic Information</h2>
                        <p class="step-description">Tell us about your service and what makes it special</p>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-tag"></i></span>
                            Service Identity
                        </h3>

                        <div class="mb-3">
                            <label for="{{ form.service_title.id_for_label }}" class="form-label">
                                Service Name <span class="required-field">*</span>
                            </label>
                            {{ form.service_title|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Choose a clear, descriptive name that differentiates this service</div>
                            <div class="invalid-feedback"></div>
                            <div class="valid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.custom_slug.id_for_label }}" class="form-label">
                                Custom URL Slug
                            </label>
                            {{ form.custom_slug|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Optional custom URL. Leave blank to auto-generate from service name</div>
                            <div class="slug-preview" id="slugPreview" style="display: none;">
                                URL will be: <span id="previewUrl"></span>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.service_category.id_for_label }}" class="form-label">
                                Service Category <span class="required-field">*</span>
                            </label>
                            {{ form.service_category|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Choose the category that best describes this service</div>
                            <div class="invalid-feedback"></div>
                            <div class="valid-feedback"></div>
                        </div>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-align-left"></i></span>
                            Service Description
                        </h3>

                        <div class="mb-3">
                            <label for="{{ form.short_description.id_for_label }}" class="form-label">
                                Description <span class="required-field">*</span>
                            </label>
                            {{ form.short_description|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Provide detailed information about what the service includes (minimum 10 characters)</div>
                            <div class="invalid-feedback"></div>
                            <div class="valid-feedback"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Pricing & Duration -->
                <div class="step-section" data-step="2">
                    <div class="step-header">
                        <div class="step-number">2</div>
                        <h2 class="step-heading">Pricing & Duration</h2>
                        <p class="step-description">Set competitive pricing and realistic time estimates</p>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-dollar-sign"></i></span>
                            Pricing Information
                        </h3>

                        <div class="price-row">
                            <div class="mb-3">
                                <label for="{{ form.price_min.id_for_label }}" class="form-label">
                                    Minimum Price ($) <span class="required-field">*</span>
                                </label>
                                {{ form.price_min|add_class:"form-control form-control-cw" }}
                                <div class="form-text">Starting price for this service</div>
                                <div class="invalid-feedback"></div>
                                <div class="valid-feedback"></div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.price_max.id_for_label }}" class="form-label">
                                    Maximum Price ($)
                                </label>
                                {{ form.price_max|add_class:"form-control form-control-cw" }}
                                <div class="form-text">Leave blank for fixed pricing</div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div id="pricingPreview" class="validation-message validation-success" style="display: none;">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="pricingText"></span>
                        </div>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-clock"></i></span>
                            Duration Settings
                        </h3>

                        <div class="mb-3">
                            <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                Duration (minutes) <span class="required-field">*</span>
                            </label>
                            {{ form.duration_minutes|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Typical duration (15-480 minutes). Must fit within venue operating hours</div>
                            <div class="invalid-feedback"></div>
                            <div class="valid-feedback"></div>
                        </div>

                        <div id="durationPreview" class="validation-message validation-success" style="display: none;">
                            <i class="fas fa-clock me-2"></i>
                            <span id="durationText"></span>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Settings -->
                <div class="step-section" data-step="3">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <h2 class="step-heading">Service Settings</h2>
                        <p class="step-description">Configure booking requirements and service options</p>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-calendar-check"></i></span>
                            Booking Settings
                        </h3>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.max_advance_booking_days.id_for_label }}" class="form-label">
                                    Max Advance Booking (days)
                                </label>
                                {{ form.max_advance_booking_days|add_class:"form-control form-control-cw" }}
                                <div class="form-text">Maximum days customers can book ahead (1-365)</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.min_advance_booking_hours.id_for_label }}" class="form-label">
                                    Min Advance Booking (hours)
                                </label>
                                {{ form.min_advance_booking_hours|add_class:"form-control form-control-cw" }}
                                <div class="form-text">Minimum hours customers must book ahead (1-168)</div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.requires_booking|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.requires_booking.id_for_label }}">
                                        Requires Advance Booking
                                    </label>
                                    <div class="form-text">Check if customers must book in advance</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.has_custom_availability|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.has_custom_availability.id_for_label }}">
                                        Custom Availability Hours
                                    </label>
                                    <div class="form-text">Different hours than venue hours</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field-group">
                        <h3 class="field-group-title">
                            <span class="field-group-icon"><i class="fas fa-image"></i></span>
                            Service Image & Status
                        </h3>

                        <div class="mb-3">
                            <label for="{{ form.service_image.id_for_label }}" class="form-label">
                                Service Image
                            </label>
                            {{ form.service_image|add_class:"form-control form-control-cw" }}
                            <div class="form-text">Optional image for this service (JPEG, PNG, WebP - max 2MB)</div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_active|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        Service Active
                                    </label>
                                    <div class="form-text">Whether customers can currently book this service</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_featured|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                        Featured Service
                                    </label>
                                    <div class="form-text">Highlight this service as a featured offering</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Review -->
                <div class="step-section" data-step="4">
                    <div class="step-header">
                        <div class="step-number">4</div>
                        <h2 class="step-heading">Review & Create</h2>
                        <p class="step-description">Review your service details before creating</p>
                    </div>

                    <div id="reviewContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="validation-message validation-error">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Navigation Buttons -->
                <div class="form-buttons">
                    <button type="button" class="btn-nav btn-prev" id="prevBtn" style="display: none;">
                        <i class="fas fa-arrow-left"></i>Previous
                    </button>
                    <div>
                        <span class="text-neutral-cw me-3" id="stepInfo">Step 1 of 4</span>
                    </div>
                    <button type="button" class="btn-nav btn-next" id="nextBtn">
                        Next<i class="fas fa-arrow-right"></i>
                    </button>
                    <button type="submit" class="btn-nav btn-submit" id="submitBtn" style="display: none;">
                        <i class="fas fa-plus"></i>Create Service
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('serviceForm');
    const steps = document.querySelectorAll('.step-section');
    const stepCircles = document.querySelectorAll('.step-circle');
    const stepTitles = document.querySelectorAll('.step-title');
    const progressFill = document.getElementById('progressFill');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    const stepInfo = document.getElementById('stepInfo');
    
    let currentStep = 1;
    const totalSteps = 4;

    // Initialize form
    updateStepDisplay();
    setupRealTimeValidation();
    setupSlugPreview();
    setupPricingPreview();

    // Navigation event listeners
    nextBtn.addEventListener('click', nextStep);
    prevBtn.addEventListener('click', prevStep);

    function nextStep() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                updateStepDisplay();
                if (currentStep === totalSteps) {
                    populateReview();
                }
            }
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
        }
    }

    function updateStepDisplay() {
        // Update step sections
        steps.forEach((step, index) => {
            step.classList.toggle('active', index + 1 === currentStep);
        });

        // Update step indicators
        stepCircles.forEach((circle, index) => {
            const stepNum = index + 1;
            circle.classList.toggle('active', stepNum === currentStep);
            circle.classList.toggle('completed', stepNum < currentStep);
        });

        stepTitles.forEach((title, index) => {
            title.classList.toggle('active', index + 1 === currentStep);
        });

        // Update progress bar
        const progress = (currentStep / totalSteps) * 100;
        progressFill.style.width = progress + '%';

        // Update buttons
        prevBtn.style.display = currentStep > 1 ? 'inline-flex' : 'none';
        nextBtn.style.display = currentStep < totalSteps ? 'inline-flex' : 'none';
        submitBtn.style.display = currentStep === totalSteps ? 'inline-flex' : 'none';

        // Update step info
        stepInfo.textContent = `Step ${currentStep} of ${totalSteps}`;

        // Scroll to top
        document.querySelector('.step-content').scrollIntoView({ behavior: 'smooth' });
    }

    function validateCurrentStep() {
        const currentStepElement = document.querySelector(`.step-section[data-step="${currentStep}"]`);
        const requiredFields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                clearFieldError(field);
            }
        });

        // Additional validation for specific steps
        if (currentStep === 1) {
            const serviceTitle = document.getElementById('{{ form.service_title.id_for_label }}');
            if (serviceTitle.value.length < 3) {
                showFieldError(serviceTitle, 'Service title must be at least 3 characters long');
                isValid = false;
            }

            const description = document.getElementById('{{ form.short_description.id_for_label }}');
            if (description.value.length < 10) {
                showFieldError(description, 'Description must be at least 10 characters long');
                isValid = false;
            }
        }

        if (currentStep === 2) {
            const priceMin = document.getElementById('{{ form.price_min.id_for_label }}');
            const priceMax = document.getElementById('{{ form.price_max.id_for_label }}');
            
            if (parseFloat(priceMin.value) <= 0) {
                showFieldError(priceMin, 'Minimum price must be greater than 0');
                isValid = false;
            }

            if (priceMax.value && parseFloat(priceMax.value) <= parseFloat(priceMin.value)) {
                showFieldError(priceMax, 'Maximum price must be greater than minimum price');
                isValid = false;
            }

            const duration = document.getElementById('{{ form.duration_minutes.id_for_label }}');
            const durationValue = parseInt(duration.value);
            if (durationValue < 15 || durationValue > 480) {
                showFieldError(duration, 'Duration must be between 15 and 480 minutes');
                isValid = false;
            }
        }

        return isValid;
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
    }

    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }

    function setupRealTimeValidation() {
        // Service title validation
        const serviceTitle = document.getElementById('{{ form.service_title.id_for_label }}');
        serviceTitle.addEventListener('input', function() {
            if (this.value.length >= 3) {
                clearFieldError(this);
            }
        });

        // Description validation
        const description = document.getElementById('{{ form.short_description.id_for_label }}');
        description.addEventListener('input', function() {
            if (this.value.length >= 10) {
                clearFieldError(this);
            }
        });

        // Price validation
        const priceMin = document.getElementById('{{ form.price_min.id_for_label }}');
        const priceMax = document.getElementById('{{ form.price_max.id_for_label }}');
        
        priceMin.addEventListener('input', validatePricing);
        priceMax.addEventListener('input', validatePricing);

        // Duration validation
        const duration = document.getElementById('{{ form.duration_minutes.id_for_label }}');
        duration.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value >= 15 && value <= 480) {
                clearFieldError(this);
            }
            updateDurationPreview();
        });
    }

    function validatePricing() {
        const priceMin = document.getElementById('{{ form.price_min.id_for_label }}');
        const priceMax = document.getElementById('{{ form.price_max.id_for_label }}');
        
        if (parseFloat(priceMin.value) > 0) {
            clearFieldError(priceMin);
        }

        if (priceMax.value && parseFloat(priceMax.value) > parseFloat(priceMin.value)) {
            clearFieldError(priceMax);
        }

        updatePricingPreview();
    }

    function setupSlugPreview() {
        const serviceTitle = document.getElementById('{{ form.service_title.id_for_label }}');
        const customSlug = document.getElementById('{{ form.custom_slug.id_for_label }}');
        const slugPreview = document.getElementById('slugPreview');
        const previewUrl = document.getElementById('previewUrl');

        function updateSlugPreview() {
            const title = serviceTitle.value;
            const slug = customSlug.value || title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
            
            if (slug) {
                previewUrl.textContent = `{{ venue.get_absolute_url }}services/${slug}/`;
                slugPreview.style.display = 'block';
            } else {
                slugPreview.style.display = 'none';
            }
        }

        serviceTitle.addEventListener('input', updateSlugPreview);
        customSlug.addEventListener('input', updateSlugPreview);
    }

    function setupPricingPreview() {
        updatePricingPreview();
        updateDurationPreview();
    }

    function updatePricingPreview() {
        const priceMin = document.getElementById('{{ form.price_min.id_for_label }}');
        const priceMax = document.getElementById('{{ form.price_max.id_for_label }}');
        const preview = document.getElementById('pricingPreview');
        const text = document.getElementById('pricingText');

        const min = parseFloat(priceMin.value) || 0;
        const max = parseFloat(priceMax.value) || 0;

        if (min > 0) {
            if (max > min) {
                text.textContent = `Customers will see pricing as: $${min.toFixed(2)} - $${max.toFixed(2)}`;
            } else {
                text.textContent = `Customers will see fixed pricing as: $${min.toFixed(2)}`;
            }
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }

    function updateDurationPreview() {
        const duration = document.getElementById('{{ form.duration_minutes.id_for_label }}');
        const preview = document.getElementById('durationPreview');
        const text = document.getElementById('durationText');

        const minutes = parseInt(duration.value) || 0;

        if (minutes > 0) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            
            let display = '';
            if (hours > 0 && mins > 0) {
                display = `${hours}h ${mins}m`;
            } else if (hours > 0) {
                display = `${hours}h`;
            } else {
                display = `${mins}m`;
            }
            
            text.textContent = `Duration will be displayed as: ${display}`;
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }

    function populateReview() {
        const reviewContent = document.getElementById('reviewContent');
        const serviceTitle = document.getElementById('{{ form.service_title.id_for_label }}').value;
        const category = document.getElementById('{{ form.service_category.id_for_label }}').selectedOptions[0]?.text || 'Not selected';
        const description = document.getElementById('{{ form.short_description.id_for_label }}').value;
        const priceMin = document.getElementById('{{ form.price_min.id_for_label }}').value;
        const priceMax = document.getElementById('{{ form.price_max.id_for_label }}').value;
        const duration = document.getElementById('{{ form.duration_minutes.id_for_label }}').value;
        const isActive = document.getElementById('{{ form.is_active.id_for_label }}').checked;
        const isFeatured = document.getElementById('{{ form.is_featured.id_for_label }}').checked;

        reviewContent.innerHTML = `
            <div class="field-group">
                <h3 class="field-group-title">
                    <span class="field-group-icon"><i class="fas fa-eye"></i></span>
                    Service Preview
                </h3>
                
                <div class="row">
                    <div class="col-md-8">
                        <h4 class="text-brand-cw mb-2">${serviceTitle}</h4>
                        <p class="text-muted mb-2"><i class="fas fa-tag me-2"></i>${category}</p>
                        <p class="mb-3">${description}</p>
                        
                        <div class="d-flex gap-4 mb-3">
                            <div>
                                <strong>Price:</strong> 
                                ${priceMax && parseFloat(priceMax) > parseFloat(priceMin) 
                                    ? `$${parseFloat(priceMin).toFixed(2)} - $${parseFloat(priceMax).toFixed(2)}`
                                    : `$${parseFloat(priceMin).toFixed(2)}`
                                }
                            </div>
                            <div>
                                <strong>Duration:</strong> ${duration} minutes
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            ${isActive ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Inactive</span>'}
                            ${isFeatured ? '<span class="badge bg-primary">Featured</span>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        if (currentStep !== totalSteps || !validateCurrentStep()) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Creating...';
    });
});
</script>
{% endblock %}
