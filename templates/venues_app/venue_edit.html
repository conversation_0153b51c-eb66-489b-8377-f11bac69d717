{% extends 'base.html' %}

{% block title %}Edit {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<link rel="stylesheet" href="{% static 'css/enhanced-contact-form.css' %}">

<style>
    /* CozyWish Design System - Venue Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venue Edit Section */
    .venue-edit-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .venue-edit-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header */
    .venue-header {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .venue-header-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--cw-shadow-md);
        flex-shrink: 0;
    }

    .venue-header-icon i {
        font-size: 2rem;
        color: white;
    }

    .venue-header-content h1 {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .venue-header-content p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .back-button {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-button:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Current Status Card */
    .current-status-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .current-status-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .status-badge-cw {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 0.875rem;
        border: 1px solid var(--cw-brand-primary);
    }

    /* Form Sections */
    .form-section-cw {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .form-section-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .section-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    /* Form Styling */
    .form-group-cw {
        margin-bottom: 2rem;
    }

    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
        display: block;
    }

    .form-control-cw, .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        width: 100%;
    }

    .form-control-cw:focus, .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        outline: none;
    }

    .form-control-cw.is-invalid, .form-select-cw.is-invalid {
        border-color: #dc2626;
    }

    .required-cw {
        color: #dc2626;
        font-weight: 600;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: block;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
        font-size: 1.1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        text-align: center;
    }

    @media (min-width: 768px) {
        .action-buttons {
            flex-direction: row;
            justify-content: center;
            gap: 1rem;
        }
    }

    /* Alert Styling */
    .alert-cw-danger {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .alert-cw-danger h5 {
        color: #991b1b;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .venue-header {
            flex-direction: column;
            text-align: center;
        }

        .venue-header-content h1 {
            font-size: 1.75rem;
        }

        .form-section-cw {
            padding: 1.5rem;
        }

        .section-title-cw {
            font-size: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-edit-section">
    <div class="venue-edit-container">
        <!-- Header -->
        <div class="venue-header">
            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to Details
            </a>
            <div class="venue-header-icon">
                <i class="fas fa-edit"></i>
            </div>
            <div class="venue-header-content">
                <h1>Edit Venue</h1>
                <p>Update your venue information and settings</p>
            </div>
        </div>

        <!-- Current Status -->
        {% if venue %}
        <div class="current-status-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ venue.venue_name }}</h5>
                    <p class="mb-0 text-muted">Current Status:
                        <span class="status-badge-cw">
                            {{ venue.get_approval_status_display }}
                        </span>
                    </p>
                </div>
                <div>
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-secondary btn-sm">
                        <i class="fas fa-eye me-1"></i>View Details
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Display form errors for debugging -->
        {% if form.errors %}
            <div class="alert-cw-danger">
                <h5>Form Errors:</h5>
                <ul>
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            <li><strong>{{ field }}:</strong> {{ error }}</li>
                        {% endfor %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Basic Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-info-circle"></i>
                    Basic Information
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Venue Name <span class="required-cw">*</span>
                    </label>
                    {{ form.venue_name|add_class:"form-control-cw" }}
                    {% if form.venue_name.help_text %}
                        <div class="form-text-cw">{{ form.venue_name.help_text }}</div>
                    {% endif %}
                    {% if form.venue_name.errors %}
                        <div class="invalid-feedback-cw">{{ form.venue_name.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Short Description <span class="required-cw">*</span>
                    </label>
                    {{ form.short_description|add_class:"form-control-cw" }}
                    {% if form.short_description.help_text %}
                        <div class="form-text-cw">{{ form.short_description.help_text }}</div>
                    {% endif %}
                    {% if form.short_description.errors %}
                        <div class="invalid-feedback-cw">{{ form.short_description.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Location Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-map-marker-alt"></i>
                    Location
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                State <span class="required-cw">*</span>
                            </label>
                            {{ form.state|add_class:"form-control-cw" }}
                            {% if form.state.errors %}
                                <div class="invalid-feedback-cw">{{ form.state.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                County <span class="required-cw">*</span>
                            </label>
                            {{ form.county|add_class:"form-control-cw" }}
                            {% if form.county.errors %}
                                <div class="invalid-feedback-cw">{{ form.county.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        City <span class="required-cw">*</span>
                    </label>
                    {{ form.city|add_class:"form-control-cw" }}
                    {% if form.city.errors %}
                        <div class="invalid-feedback-cw">{{ form.city.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Number <span class="required-cw">*</span>
                            </label>
                            {{ form.street_number|add_class:"form-control-cw" }}
                            {% if form.street_number.errors %}
                                <div class="invalid-feedback-cw">{{ form.street_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Name <span class="required-cw">*</span>
                            </label>
                            {{ form.street_name|add_class:"form-control-cw" }}
                            {% if form.street_name.errors %}
                                <div class="invalid-feedback-cw">{{ form.street_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories and Tags -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-tags"></i>
                    Categories & Tags
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Categories <span class="required-cw">*</span>
                    </label>
                    {{ form.categories }}
                    {% if form.categories.help_text %}
                        <div class="form-text-cw">{{ form.categories.help_text }}</div>
                    {% endif %}
                    {% if form.categories.errors %}
                        <div class="invalid-feedback-cw">{{ form.categories.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Tags
                    </label>
                    {{ form.tags|add_class:"form-control-cw" }}
                    {% if form.tags.help_text %}
                        <div class="form-text-cw">{{ form.tags.help_text }}</div>
                    {% endif %}
                    {% if form.tags.errors %}
                        <div class="invalid-feedback-cw">{{ form.tags.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="form-section-cw">
                <div class="section-header-enhanced">
                    <h3 class="section-title-cw">
                        <i class="fas fa-address-book"></i>
                        Contact Information
                    </h3>
                    <div class="section-actions">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="sync-contact-btn">
                            <i class="fas fa-sync"></i> Sync from Profile
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="validate-all-btn">
                            <i class="fas fa-check-circle"></i> Validate All
                        </button>
                    </div>
                </div>
                <p class="form-text-cw mb-4">
                    <i class="fas fa-info-circle text-info"></i>
                    Contact information helps customers reach you and builds trust. At least one contact method is recommended.
                </p>

                <!-- Primary Contact Methods -->
                <div class="contact-subsection">
                    <h4 class="subsection-title">
                        <i class="fas fa-phone"></i> Primary Contact
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fas fa-phone text-primary"></i>
                                    Phone Number 
                                    {% if venue.phone %}
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check"></i> Set
                                        </span>
                                    {% endif %}
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.phone|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="phone-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">Format: (*************</div>
                                    </div>
                                </div>
                                {% if form.phone.help_text %}
                                    <div class="form-text-cw">{{ form.phone.help_text }}</div>
                                {% endif %}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback-cw">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fas fa-envelope text-info"></i>
                                    Email Address
                                    {% if venue.email %}
                                        {% if venue.email_verified %}
                                            <span class="badge bg-success ms-2">
                                                <i class="fas fa-check-circle"></i> Verified
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning ms-2">
                                                <i class="fas fa-exclamation-triangle"></i> Unverified
                                            </span>
                                        {% endif %}
                                    {% endif %}
                                </label>
                                <div class="input-group-enhanced">
                                    <div class="input-group">
                                        {{ form.email|add_class:"form-control-cw" }}
                                        {% if venue.email and not venue.email_verified %}
                                            <button type="button" class="btn btn-outline-warning" id="verify-email-btn">
                                                <i class="fas fa-envelope"></i> Verify
                                            </button>
                                        {% endif %}
                                    </div>
                                    <div class="input-feedback" id="email-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">Enter a valid email address</div>
                                    </div>
                                </div>
                                {% if form.email.help_text %}
                                    <div class="form-text-cw">{{ form.email.help_text }}</div>
                                {% endif %}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback-cw">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Website & Online Presence -->
                <div class="contact-subsection">
                    <h4 class="subsection-title">
                        <i class="fas fa-globe"></i> Online Presence
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fas fa-external-link-alt text-success"></i>
                                    Website URL
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.website_url|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="website-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">Example: https://www.yourbusiness.com</div>
                                    </div>
                                </div>
                                {% if form.website_url.help_text %}
                                    <div class="form-text-cw">{{ form.website_url.help_text }}</div>
                                {% endif %}
                                {% if form.website_url.errors %}
                                    <div class="invalid-feedback-cw">{{ form.website_url.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group-cw">
                                <label class="form-label-cw">
                                    <i class="fas fa-tools"></i> Quick Actions
                                </label>
                                <div class="action-buttons-stack">
                                    <button type="button" class="btn btn-outline-primary w-100" id="sync-contact-btn">
                                        <i class="fas fa-copy"></i> Sync from Profile
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary w-100" id="test-website-btn">
                                        <i class="fas fa-external-link-alt"></i> Test Website
                                    </button>
                                </div>
                                <div class="form-text-cw">Verify your contact information</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media Links Section -->
            <div class="form-section-cw">
                <div class="section-header-enhanced">
                    <h3 class="section-title-cw">
                        <i class="fas fa-share-alt"></i>
                        Social Media Links
                    </h3>
                    <div class="section-toggle">
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggle-social-section">
                            <i class="fas fa-chevron-down"></i> Show Options
                        </button>
                    </div>
                </div>
                <p class="form-text-cw mb-3">Connect your social media accounts to help customers find and engage with your business.</p>

                <div class="social-media-grid" id="social-media-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fab fa-instagram text-danger"></i> Instagram
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.instagram_url|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="instagram-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">@username or full URL</div>
                                    </div>
                                </div>
                                {% if form.instagram_url.errors %}
                                    <div class="invalid-feedback-cw">{{ form.instagram_url.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fab fa-facebook text-primary"></i> Facebook
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.facebook_url|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="facebook-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">Page name or full URL</div>
                                    </div>
                                </div>
                                {% if form.facebook_url.errors %}
                                    <div class="invalid-feedback-cw">{{ form.facebook_url.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fab fa-twitter text-info"></i> Twitter
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.twitter_url|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="twitter-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">@username or full URL</div>
                                    </div>
                                </div>
                                {% if form.twitter_url.errors %}
                                    <div class="invalid-feedback-cw">{{ form.twitter_url.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-cw enhanced-field">
                                <label class="form-label-cw">
                                    <i class="fab fa-linkedin text-primary"></i> LinkedIn
                                </label>
                                <div class="input-group-enhanced">
                                    {{ form.linkedin_url|add_class:"form-control-cw" }}
                                    <div class="input-feedback" id="linkedin-feedback">
                                        <div class="validation-status"></div>
                                        <div class="format-hint">Company page or profile URL</div>
                                    </div>
                                </div>
                                {% if form.linkedin_url.errors %}
                                    <div class="invalid-feedback-cw">{{ form.linkedin_url.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-info"></i>
                    Additional Information
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Latitude
                            </label>
                            {{ form.latitude|add_class:"form-control-cw" }}
                            {% if form.latitude.help_text %}
                                <div class="form-text-cw">{{ form.latitude.help_text }}</div>
                            {% endif %}
                            {% if form.latitude.errors %}
                                <div class="invalid-feedback-cw">{{ form.latitude.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Longitude
                            </label>
                            {{ form.longitude|add_class:"form-control-cw" }}
                            {% if form.longitude.help_text %}
                                <div class="form-text-cw">{{ form.longitude.help_text }}</div>
                            {% endif %}
                            {% if form.longitude.errors %}
                                <div class="invalid-feedback-cw">{{ form.longitude.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Opening Notes
                    </label>
                    {{ form.opening_notes|add_class:"form-control-cw" }}
                    {% if form.opening_notes.help_text %}
                        <div class="form-text-cw">{{ form.opening_notes.help_text }}</div>
                    {% endif %}
                    {% if form.opening_notes.errors %}
                        <div class="invalid-feedback-cw">{{ form.opening_notes.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-save me-2"></i>Update Venue
                </button>
                <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</section>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/enhanced-contact-form.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced contact form validation
    const contactFormManager = new ContactFormManager();
    
    // Contact sync functionality
    const syncContactBtn = document.getElementById('sync-contact-btn');
    if (syncContactBtn) {
        syncContactBtn.addEventListener('click', function() {
            syncContactBtn.disabled = true;
            syncContactBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
            
            fetch('{% url "venues_app:sync_contact_info" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.synced_fields) {
                        // Update form fields with synced data
                        Object.keys(data.data).forEach(field => {
                            const input = document.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.value = data.data[field];
                            }
                        });
                        
                        // Show success message
                        showToast('success', 'Contact information synced successfully!');
                    } else {
                        showToast('info', 'No new contact information to sync.');
                    }
                } else {
                    showToast('error', data.error || 'Failed to sync contact information.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('error', 'An error occurred while syncing contact information.');
            })
            .finally(() => {
                syncContactBtn.disabled = false;
                syncContactBtn.innerHTML = '<i class="fas fa-copy"></i> Sync from Profile';
            });
        });
    }

    // Email verification functionality
    const verifyEmailBtn = document.getElementById('verify-email-btn');
    if (verifyEmailBtn) {
        verifyEmailBtn.addEventListener('click', function() {
            verifyEmailBtn.disabled = true;
            verifyEmailBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            fetch('{% url "venues_app:send_email_verification" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', 'Verification email sent! Please check your inbox.');
                    verifyEmailBtn.style.display = 'none';
                } else {
                    showToast('error', data.error || 'Failed to send verification email.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('error', 'An error occurred while sending verification email.');
            })
            .finally(() => {
                verifyEmailBtn.disabled = false;
                verifyEmailBtn.innerHTML = '<i class="fas fa-envelope"></i> Verify';
            });
        });
    }

    // Toast notification function
    function showToast(type, message) {
        const toastColors = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'info': 'bg-info',
            'warning': 'bg-warning'
        };

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white ${toastColors[type]} border-0 position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
});

class ContactFormManager {
    constructor() {
        this.validationRules = {
            phone: {
                pattern: /^[\+]?[1-9][\d]{0,15}$/,
                format: /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
                cleanPattern: /[\s\-\(\)]/g
            },
            email: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            },
            url: {
                pattern: /^https?:\/\/.+\..+/,
                social: {
                    instagram: /(?:instagram\.com\/|@)([a-zA-Z0-9_.]+)/,
                    facebook: /(?:facebook\.com\/|fb\.com\/)([a-zA-Z0-9_.]+)/,
                    twitter: /(?:twitter\.com\/|x\.com\/|@)([a-zA-Z0-9_]+)/,
                    linkedin: /linkedin\.com\/(in|company)\/([a-zA-Z0-9-]+)/
                }
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupRealTimeValidation();
        this.setupSocialMediaToggle();
        this.setupQuickActions();
        this.setupContactMethodDetection();
    }
    
    setupRealTimeValidation() {
        // Enhanced fields with real-time validation
        const enhancedFields = document.querySelectorAll('.enhanced-field input');
        
        enhancedFields.forEach(field => {
            const fieldName = this.getFieldType(field);
            const feedback = document.getElementById(`${fieldName}-feedback`);
            
            if (feedback) {
                // Set up validation on input
                field.addEventListener('input', debounce(() => {
                    this.validateField(field, fieldName, feedback);
                }, 300));
                
                // Validate on blur for immediate feedback
                field.addEventListener('blur', () => {
                    this.validateField(field, fieldName, feedback, true);
                });
                
                // Initial validation if field has value
                if (field.value.trim()) {
                    this.validateField(field, fieldName, feedback);
                }
            }
        });
    }
    
    getFieldType(field) {
        const name = field.name || field.id;
        if (name.includes('phone')) return 'phone';
        if (name.includes('email')) return 'email';
        if (name.includes('website')) return 'website';
        if (name.includes('instagram')) return 'instagram';
        if (name.includes('facebook')) return 'facebook';
        if (name.includes('twitter')) return 'twitter';
        if (name.includes('linkedin')) return 'linkedin';
        return 'generic';
    }
    
    async validateField(field, fieldType, feedback, showErrors = false) {
        const value = field.value.trim();
        const validationStatus = feedback.querySelector('.validation-status');
        const formatHint = feedback.querySelector('.format-hint');
        
        // Clear previous status
        validationStatus.className = 'validation-status';
        validationStatus.innerHTML = '';
        
        if (!value) {
            formatHint.style.display = 'block';
            return { isValid: true, isEmpty: true };
        }
        
        let isValid = false;
        let message = '';
        let isWarning = false;
        
        switch (fieldType) {
            case 'phone':
                const result = this.validatePhone(value);
                isValid = result.isValid;
                message = result.message;
                isWarning = result.isWarning;
                
                // Auto-format phone if valid
                if (result.formatted && result.formatted !== value) {
                    field.value = result.formatted;
                }
                break;
                
            case 'email':
                isValid = this.validationRules.email.pattern.test(value);
                message = isValid ? 'Valid email address' : 'Invalid email format';
                break;
                
            case 'website':
                isValid = this.validationRules.url.pattern.test(value);
                message = isValid ? 'Valid website URL' : 'Please enter a complete URL (http:// or https://)';
                
                // Auto-add https if missing
                if (!isValid && !value.startsWith('http')) {
                    const withHttps = 'https://' + value;
                    if (this.validationRules.url.pattern.test(withHttps)) {
                        field.value = withHttps;
                        isValid = true;
                        message = 'Added https:// prefix';
                        isWarning = true;
                    }
                }
                break;
                
            case 'instagram':
            case 'facebook':
            case 'twitter':
            case 'linkedin':
                const socialResult = this.validateSocialMedia(value, fieldType);
                isValid = socialResult.isValid;
                message = socialResult.message;
                isWarning = socialResult.isWarning;
                
                if (socialResult.formatted && socialResult.formatted !== value) {
                    field.value = socialResult.formatted;
                }
                break;
        }
        
        // Update UI
        this.updateValidationUI(validationStatus, formatHint, {
            isValid,
            message,
            isWarning,
            showErrors
        });
        
        return { isValid, message };
    }
    
    validatePhone(phone) {
        const cleaned = phone.replace(this.validationRules.phone.cleanPattern, '');
        
        if (cleaned.length < 10) {
            return {
                isValid: false,
                message: 'Phone number too short',
                formatted: phone
            };
        }
        
        if (cleaned.length > 15) {
            return {
                isValid: false,
                message: 'Phone number too long',
                formatted: phone
            };
        }
        
        // US phone number formatting
        if (cleaned.length === 10 && /^\d{10}$/.test(cleaned)) {
            const formatted = `(${cleaned.slice(0,3)}) ${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
            return {
                isValid: true,
                message: 'Valid US phone number',
                formatted: formatted
            };
        }
        
        // International format
        if (this.validationRules.phone.pattern.test(cleaned)) {
            return {
                isValid: true,
                message: 'Valid phone number',
                formatted: phone,
                isWarning: true
            };
        }
        
        return {
            isValid: false,
            message: 'Invalid phone number format',
            formatted: phone
        };
    }
    
    validateSocialMedia(value, platform) {
        // Handle @username format
        if (value.startsWith('@')) {
            const username = value.slice(1);
            if (username.length > 0) {
                const urls = {
                    instagram: `https://instagram.com/${username}`,
                    facebook: `https://facebook.com/${username}`,
                    twitter: `https://twitter.com/${username}`,
                    linkedin: `https://linkedin.com/in/${username}`
                };
                
                return {
                    isValid: true,
                    message: 'Converted to full URL',
                    formatted: urls[platform],
                    isWarning: true
                };
            }
        }
        
        // Validate URL format
        if (this.validationRules.url.pattern.test(value)) {
            const pattern = this.validationRules.url.social[platform];
            if (pattern && pattern.test(value)) {
                return {
                    isValid: true,
                    message: `Valid ${platform} URL`,
                    formatted: value
                };
            } else {
                return {
                    isValid: false,
                    message: `This doesn't appear to be a ${platform} URL`,
                    formatted: value
                };
            }
        }
        
        // Try to construct URL from username
        if (/^[a-zA-Z0-9_.]+$/.test(value)) {
            const urls = {
                instagram: `https://instagram.com/${value}`,
                facebook: `https://facebook.com/${value}`,
                twitter: `https://twitter.com/${value}`,
                linkedin: `https://linkedin.com/in/${value}`
            };
            
            return {
                isValid: true,
                message: 'Converted username to URL',
                formatted: urls[platform],
                isWarning: true
            };
        }
        
        return {
            isValid: false,
            message: 'Please enter a username or valid URL',
            formatted: value
        };
    }
    
    updateValidationUI(statusElement, hintElement, result) {
        const { isValid, message, isWarning, showErrors } = result;
        
        if (isValid) {
            statusElement.className = `validation-status ${isWarning ? 'warning' : 'success'}`;
            statusElement.innerHTML = `
                <i class="fas fa-${isWarning ? 'exclamation-triangle' : 'check'}"></i>
                ${message}
            `;
            hintElement.style.display = 'none';
        } else if (showErrors) {
            statusElement.className = 'validation-status error';
            statusElement.innerHTML = `
                <i class="fas fa-times"></i>
                ${message}
            `;
            hintElement.style.display = 'none';
        } else {
            statusElement.innerHTML = '';
            hintElement.style.display = 'block';
        }
    }
    
    setupSocialMediaToggle() {
        const toggleBtn = document.getElementById('toggle-social-section');
        const socialContent = document.getElementById('social-media-content');
        
        if (toggleBtn && socialContent) {
            // Check if any social media fields have values
            const socialFields = socialContent.querySelectorAll('input');
            const hasValues = Array.from(socialFields).some(field => field.value.trim());
            
            if (hasValues) {
                socialContent.style.display = 'block';
                toggleBtn.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Options';
            }
            
            toggleBtn.addEventListener('click', () => {
                const isVisible = socialContent.style.display !== 'none';
                socialContent.style.display = isVisible ? 'none' : 'block';
                toggleBtn.innerHTML = isVisible ? 
                    '<i class="fas fa-chevron-down"></i> Show Options' : 
                    '<i class="fas fa-chevron-up"></i> Hide Options';
            });
        }
    }
    
    setupQuickActions() {
        // Sync contact info button
        const syncBtn = document.getElementById('sync-contact-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', this.syncContactInfo.bind(this));
        }
        
        // Validate all button
        const validateBtn = document.getElementById('validate-all-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', this.validateAllFields.bind(this));
        }
        
        // Test website button
        const testWebsiteBtn = document.getElementById('test-website-btn');
        if (testWebsiteBtn) {
            testWebsiteBtn.addEventListener('click', this.testWebsite.bind(this));
        }
    }
    
    async syncContactInfo() {
        const syncBtn = document.getElementById('sync-contact-btn');
        const originalContent = syncBtn.innerHTML;
        
        syncBtn.disabled = true;
        syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
        
        try {
            const response = await fetch('{% url "venues_app:sync_contact_info" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                if (data.synced_fields && data.data) {
                    // Update form fields with synced data
                    Object.keys(data.data).forEach(field => {
                        const input = document.querySelector(`[name="${field}"]`);
                        if (input) {
                            input.value = data.data[field];
                            // Trigger validation
                            const fieldType = this.getFieldType(input);
                            const feedback = document.getElementById(`${fieldType}-feedback`);
                            if (feedback) {
                                this.validateField(input, fieldType, feedback);
                            }
                        }
                    });
                    
                    this.showMessage('Contact information synced successfully!', 'success');
                } else {
                    this.showMessage('No new contact information to sync.', 'info');
                }
            } else {
                this.showMessage(data.error || 'Failed to sync contact information.', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showMessage('An error occurred while syncing contact information.', 'error');
        } finally {
            syncBtn.disabled = false;
            syncBtn.innerHTML = originalContent;
        }
    }
    
    async validateAllFields() {
        const validateBtn = document.getElementById('validate-all-btn');
        const originalContent = validateBtn.innerHTML;
        
        validateBtn.disabled = true;
        validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';
        
        const enhancedFields = document.querySelectorAll('.enhanced-field input');
        let validCount = 0;
        let totalCount = 0;
        
        for (const field of enhancedFields) {
            if (field.value.trim()) {
                totalCount++;
                const fieldType = this.getFieldType(field);
                const feedback = document.getElementById(`${fieldType}-feedback`);
                
                if (feedback) {
                    const result = await this.validateField(field, fieldType, feedback, true);
                    if (result.isValid) {
                        validCount++;
                    }
                }
            }
        }
        
        setTimeout(() => {
            validateBtn.disabled = false;
            validateBtn.innerHTML = originalContent;
            
            if (totalCount === 0) {
                this.showMessage('No contact information to validate.', 'info');
            } else {
                this.showMessage(`Validation complete: ${validCount}/${totalCount} fields valid.`, 
                    validCount === totalCount ? 'success' : 'warning');
            }
        }, 1000);
    }
    
    testWebsite() {
        const websiteField = document.querySelector('[name*="website"]');
        if (websiteField && websiteField.value.trim()) {
            const url = websiteField.value.trim();
            if (this.validationRules.url.pattern.test(url)) {
                window.open(url, '_blank', 'noopener,noreferrer');
                this.showMessage('Website opened in new tab.', 'info');
            } else {
                this.showMessage('Please enter a valid website URL first.', 'warning');
            }
        } else {
            this.showMessage('Please enter a website URL first.', 'warning');
        }
    }
    
    setupContactMethodDetection() {
        // Detect if user has at least one contact method
        const contactFields = ['phone', 'email', 'website_url'];
        const inputs = contactFields.map(field => 
            document.querySelector(`[name*="${field}"]`)
        ).filter(Boolean);
        
        const checkContactMethods = () => {
            const hasContact = inputs.some(input => input.value.trim());
            const warning = document.querySelector('.contact-method-warning');
            
            if (!hasContact && !warning) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning contact-method-warning mt-3';
                warningDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Recommendation:</strong> Add at least one contact method to help customers reach you.
                `;
                
                const contactSection = document.querySelector('.contact-subsection');
                if (contactSection) {
                    contactSection.appendChild(warningDiv);
                }
            } else if (hasContact && warning) {
                warning.remove();
            }
        };
        
        inputs.forEach(input => {
            input.addEventListener('input', debounce(checkContactMethods, 500));
        });
        
        // Initial check
        checkContactMethods();
    }
    
    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.className = `contact-message message-${type}`;
        message.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            ${text}
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => message.remove(), 300);
        }, 4000);
    }
    
    getMessageIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
