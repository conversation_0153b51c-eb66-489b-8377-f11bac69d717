{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Admin{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin-venue-changes.css' %}">
<style>
    .changes-comparison-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .change-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    }
    
    .change-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        backdrop-filter: blur(10px);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .changes-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
        overflow: hidden;
    }
    
    .section-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .section-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }
    
    .severity-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .severity-critical {
        background: #ffeaa7;
        color: #d63031;
    }
    
    .severity-major {
        background: #fdcb6e;
        color: #e17055;
    }
    
    .severity-moderate {
        background: #a7d8f0;
        color: #0984e3;
    }
    
    .severity-minor {
        background: #d1f2eb;
        color: #00b894;
    }
    
    .field-changes {
        padding: 20px;
    }
    
    .field-change {
        display: grid;
        grid-template-columns: 1fr 50px 1fr;
        gap: 20px;
        align-items: start;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .field-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        grid-column: 1 / -1;
    }
    
    .change-before, .change-after {
        padding: 12px;
        border-radius: 6px;
        border: 2px solid transparent;
        min-height: 40px;
    }
    
    .change-before {
        background: #fff5f5;
        border-color: #fed7d7;
    }
    
    .change-after {
        background: #f0fff4;
        border-color: #c6f6d5;
    }
    
    .change-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #667eea;
        font-size: 1.5rem;
    }
    
    .change-value {
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 0.9rem;
        line-height: 1.4;
        word-break: break-word;
    }
    
    .empty-value {
        color: #999;
        font-style: italic;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn-approve {
        background: #00b894;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-approve:hover {
        background: #00a085;
        transform: translateY(-2px);
    }
    
    .btn-reject {
        background: #e17055;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-reject:hover {
        background: #d63031;
        transform: translateY(-2px);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }
    
    .recommendation-banner {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid;
    }
    
    .recommendation-required {
        background: #fff5f5;
        border-color: #e53e3e;
        color: #c53030;
    }
    
    .recommendation-recommended {
        background: #fef5e7;
        border-color: #dd6b20;
        color: #c05621;
    }
    
    .recommendation-optional {
        background: #e6fffa;
        border-color: #319795;
        color: #2c7a7b;
    }
    
    @media (max-width: 768px) {
        .field-change {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .change-arrow {
            transform: rotate(90deg);
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="changes-comparison-container">
    <!-- Change Header -->
    <div class="change-header">
        <h1><i class="fas fa-code-branch"></i> Change Analysis: {{ venue.venue_name }}</h1>
        <p class="mb-3">Review the changes made to this venue that triggered re-approval</p>
        
        <div class="change-stats">
            <div class="stat-card">
                <div class="stat-number">{{ comparison_data.total_changes }}</div>
                <div>Total Changes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ change_analysis.severity_breakdown.critical|default:0 }}</div>
                <div>Critical</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ change_analysis.severity_breakdown.major|default:0 }}</div>
                <div>Major</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ change_analysis.severity_breakdown.moderate|default:0 }}</div>
                <div>Moderate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ change_analysis.severity_breakdown.minor|default:0 }}</div>
                <div>Minor</div>
            </div>
        </div>
    </div>

    <!-- Approval Recommendation -->
    <div class="recommendation-banner recommendation-{{ comparison_data.approval_recommendation }}">
        <strong><i class="fas fa-lightbulb"></i> Approval Recommendation:</strong>
        {% if comparison_data.approval_recommendation == 'required' %}
            Manual approval <strong>required</strong> due to critical changes
        {% elif comparison_data.approval_recommendation == 'recommended' %}
            Manual approval <strong>recommended</strong> due to major changes
        {% elif comparison_data.approval_recommendation == 'optional' %}
            Manual approval <strong>optional</strong> - changes may be auto-approved for high-quality venues
        {% else %}
            Standard approval process
        {% endif %}
    </div>

    <!-- Changed Fields Details -->
    <div class="changes-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-edit"></i> Changed Fields
            </h3>
        </div>
        <div class="field-changes">
            {% if comparison_data.changed_fields %}
                {% for field in comparison_data.changed_fields %}
                <div class="field-change">
                    <div class="field-label">
                        {{ field|title|cut:'_' }}
                        <span class="severity-badge severity-{{ change_analysis.field_severities|default_if_none:'moderate' }}">
                            {% for change in change_analysis.change_summary.critical %}
                                {% if change.field == field %}Critical{% endif %}
                            {% endfor %}
                            {% for change in change_analysis.change_summary.major %}
                                {% if change.field == field %}Major{% endif %}
                            {% endfor %}
                            {% for change in change_analysis.change_summary.moderate %}
                                {% if change.field == field %}Moderate{% endif %}
                            {% endfor %}
                            {% for change in change_analysis.change_summary.minor %}
                                {% if change.field == field %}Minor{% endif %}
                            {% endfor %}
                        </span>
                    </div>
                    <div class="change-before">
                        <small class="text-muted">Before:</small>
                        <div class="change-value">
                            <span class="empty-value">[Previous value not stored]</span>
                        </div>
                    </div>
                    <div class="change-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="change-after">
                        <small class="text-muted">After:</small>
                        <div class="change-value">
                            {% if field == 'venue_name' %}{{ venue.venue_name }}
                            {% elif field == 'short_description' %}{{ venue.short_description|truncatewords:20 }}
                            {% elif field == 'phone' %}{{ venue.phone }}
                            {% elif field == 'email' %}{{ venue.email }}
                            {% elif field == 'website_url' %}{{ venue.website_url }}
                            {% elif field == 'city' %}{{ venue.city }}
                            {% elif field == 'state' %}{{ venue.state }}
                            {% else %}{{ venue|getattribute:field|default:'Not set' }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-muted">No specific field changes tracked for this approval request.</p>
            {% endif %}
        </div>
    </div>

    <!-- Change Timeline -->
    <div class="changes-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-clock"></i> Change Timeline
            </h3>
        </div>
        <div class="field-changes">
            <div class="timeline-item">
                <strong>Change Detected:</strong> {{ comparison_data.timestamp|date:"M d, Y H:i" }}<br>
                <strong>Details:</strong> {{ comparison_data.change_details|default:"Changes triggered re-approval process" }}
            </div>
        </div>
    </div>

    <!-- Venue Context -->
    <div class="changes-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-info-circle"></i> Venue Context
            </h3>
        </div>
        <div class="field-changes">
            <div class="row">
                <div class="col-md-6">
                    <strong>Provider:</strong> {{ venue.service_provider.business_name }}<br>
                    <strong>Contact:</strong> {{ venue.service_provider.user.email }}<br>
                    <strong>Location:</strong> {{ venue.city }}, {{ venue.state }}
                </div>
                <div class="col-md-6">
                    <strong>Original Approval:</strong> {{ venue.approved_at|date:"M d, Y H:i"|default:"N/A" }}<br>
                    <strong>Categories:</strong> 
                    {% for category in venue.categories.all %}
                        <span class="badge badge-info">{{ category.category_name }}</span>
                    {% endfor %}<br>
                    <strong>Services:</strong> {{ venue.services.count }}
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'venues_app:admin_venue_approval' venue.id %}" class="btn-approve">
            <i class="fas fa-gavel"></i> Review & Approve/Reject
        </a>
        <a href="{% url 'venues_app:admin_venue_detail' venue.id %}" class="btn-secondary">
            <i class="fas fa-eye"></i> View Full Details
        </a>
        <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Pending
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features for the change comparison
    console.log('Change comparison view loaded');
});
</script>
{% endblock %} 