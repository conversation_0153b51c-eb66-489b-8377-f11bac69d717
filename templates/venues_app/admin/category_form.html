{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}{{ action }} Category - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="display-font text-brand-cw mb-2">{{ action }} Category</h1>
                    <p class="lead text-neutral-cw mb-0">Manage venue categories for service providers</p>
                </div>
                <a href="{% url 'venues_app:admin_category_list' %}" class="btn btn-cw-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                </a>
            </div>
        </div>
    </div>

    <!-- Category Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <h5 class="mb-0 text-brand-cw fw-bold">
                        <i class="fas fa-tags me-2"></i>Category Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-4">
                            <label for="name" class="form-label text-brand-cw fw-semibold">
                                Category Name <span class="text-danger">*</span>
                            </label>
                            <input type="text"
                                   class="form-control form-control-cw"
                                   id="name"
                                   name="name"
                                   value="{% if category %}{{ category.category_name }}{% endif %}"
                                   placeholder="Enter category name (e.g., Massage Therapy, Facial Treatments)"
                                   required>
                            <div class="form-text text-neutral-cw">
                                <i class="fas fa-info-circle me-1"></i>Choose a clear, descriptive name for this category
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label text-brand-cw fw-semibold">Description</label>
                            <textarea class="form-control form-control-cw"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="Provide a detailed description of this category...">{% if category %}{{ category.category_description }}{% endif %}</textarea>
                            <div class="form-text text-neutral-cw">
                                <i class="fas fa-info-circle me-1"></i>Optional description to help service providers understand this category
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="bg-light-cw rounded-cw p-3">
                                <div class="form-check">
                                    <input class="form-check-input"
                                           type="checkbox"
                                           id="is_active"
                                           name="is_active"
                                           {% if not category or category.is_active %}checked{% endif %}>
                                    <label class="form-check-label text-brand-cw fw-semibold" for="is_active">
                                        <i class="fas fa-toggle-on me-2"></i>Active Category
                                    </label>
                                    <div class="form-text text-neutral-cw mt-2">
                                        <i class="fas fa-info-circle me-1"></i>Active categories are visible to service providers when creating venues. Inactive categories are hidden but preserved in the system.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-end">
                            <a href="{% url 'venues_app:admin_category_list' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-cw-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
