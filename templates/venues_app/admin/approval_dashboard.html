{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Venue Approval Dashboard - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="display-font text-brand-cw mb-2">Venue Approval Dashboard</h1>
                    <p class="lead text-neutral-cw mb-0">Manage venue approvals and monitor submission statistics</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-cw-primary">
                        <i class="fas fa-clock me-2"></i>Review Pending
                    </a>
                    <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-secondary">
                        <i class="fas fa-list me-2"></i>All Venues
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Statistics -->
    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-clock text-white fa-lg"></i>
                        </div>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ pending_venues|default:0 }}</div>
                    <div class="stat-label text-neutral-cw mb-3">Pending Approval</div>
                    <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-cw-accent-outline btn-sm w-100">
                        <i class="fas fa-eye me-1"></i>Review Pending
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-check text-white fa-lg"></i>
                        </div>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ approved_venues|default:0 }}</div>
                    <div class="stat-label text-neutral-cw mb-3">Approved Venues</div>
                    <a href="{% url 'venues_app:admin_venue_list' %}?status=approved" class="btn btn-cw-accent-outline btn-sm w-100">
                        <i class="fas fa-list me-1"></i>View Approved
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-times text-white fa-lg"></i>
                        </div>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ rejected_venues|default:0 }}</div>
                    <div class="stat-label text-neutral-cw mb-3">Rejected Venues</div>
                    <a href="{% url 'venues_app:admin_venue_list' %}?status=rejected" class="btn btn-cw-accent-outline btn-sm w-100">
                        <i class="fas fa-list me-1"></i>View Rejected
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-brand-cw rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-building text-white fa-lg"></i>
                        </div>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ total_venues|default:0 }}</div>
                    <div class="stat-label text-neutral-cw mb-3">Total Venues</div>
                    <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-accent-outline btn-sm w-100">
                        <i class="fas fa-list me-1"></i>View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <h5 class="mb-0 text-brand-cw fw-bold">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-cw-accent-outline w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-clock text-white fa-lg"></i>
                                </div>
                                <div class="fw-bold text-brand-cw mb-1">Review Pending</div>
                                <small class="text-neutral-cw text-center">Review venues awaiting approval</small>
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-accent-outline w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none">
                                <div class="bg-brand-cw rounded-circle d-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-list text-white fa-lg"></i>
                                </div>
                                <div class="fw-bold text-brand-cw mb-1">All Venues</div>
                                <small class="text-neutral-cw text-center">Browse all venue submissions</small>
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <a href="{% url 'venues_app:admin_category_list' %}" class="btn btn-cw-accent-outline w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-tags text-white fa-lg"></i>
                                </div>
                                <div class="fw-bold text-brand-cw mb-1">Categories</div>
                                <small class="text-neutral-cw text-center">Manage venue categories</small>
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <a href="{% url 'venues_app:admin_flagged_venues' %}" class="btn btn-cw-accent-outline w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none">
                                <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-flag text-white fa-lg"></i>
                                </div>
                                <div class="fw-bold text-brand-cw mb-1">Flagged Venues</div>
                                <small class="text-neutral-cw text-center">Review flagged content</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <h5 class="mb-0 text-brand-cw fw-bold">
                        <i class="fas fa-history me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body p-4">
                    {% if recent_pending %}
                        {% for venue in recent_pending %}
                        <div class="d-flex align-items-center mb-3 {% if not forloop.last %}border-bottom pb-3{% endif %}">
                            <div class="me-3">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold text-brand-cw mb-1">{{ venue.venue_name }}</div>
                                <small class="text-neutral-cw">
                                    <i class="fas fa-user me-1"></i>{{ venue.service_provider.user.email }}
                                    <i class="fas fa-calendar ms-3 me-1"></i>{{ venue.created_at|date:"M d, Y" }}
                                </small>
                            </div>
                            <div class="d-flex gap-2">
                                {% if venue.approval_status == 'pending' %}
                                    <span class="badge bg-warning text-dark">Pending</span>
                                {% elif venue.approval_status == 'approved' %}
                                    <span class="badge bg-success">Approved</span>
                                {% elif venue.approval_status == 'rejected' %}
                                    <span class="badge bg-danger">Rejected</span>
                                {% endif %}
                                <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-cw-accent-outline btn-sm">
                                    <i class="fas fa-eye me-1"></i>View
                                </a>
                            </div>
                        </div>
                        {% endfor %}

                        <div class="text-center mt-4">
                            <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-ghost">
                                <i class="fas fa-arrow-right me-1"></i>View All Venues
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-inbox text-neutral-cw fa-2x"></i>
                            </div>
                            <h6 class="text-brand-cw mb-2">No Recent Activity</h6>
                            <p class="text-neutral-cw mb-0">New venue submissions will appear here</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
