{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Category Management - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="display-font text-brand-cw mb-2">Category Management</h1>
                    <p class="lead text-neutral-cw mb-0">Manage venue categories for service providers</p>
                </div>
                <a href="{% url 'venues_app:admin_category_create' %}" class="btn btn-cw-primary">
                    <i class="fas fa-plus me-2"></i>Add New Category
                </a>
            </div>
        </div>
    </div>

    <!-- Categories List -->
    <div class="row">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0 text-brand-cw fw-bold">
                            <i class="fas fa-tags me-2"></i>All Categories
                        </h5>
                        {% if categories %}
                            <span class="badge badge-cw-secondary">{{ categories|length }} categories</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if categories %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light-cw">
                                    <tr>
                                        <th class="text-brand-cw fw-bold border-0 py-3 px-4">Category</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Description</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Status</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Venues</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Created</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3 px-4">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category in categories %}
                                    <tr class="border-light">
                                        <td class="py-3 px-4">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-tag text-brand-cw"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold text-brand-cw">{{ category.name }}</div>
                                                    <small class="text-neutral-cw">ID: {{ category.id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            <span class="text-neutral-cw">{{ category.description|truncatechars:60|default:"No description" }}</span>
                                        </td>
                                        <td class="py-3">
                                            {% if category.is_active %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Active
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-pause me-1"></i>Inactive
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="py-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-building text-brand-cw me-2"></i>
                                                <span class="fw-semibold text-brand-cw">{{ category.venues.count }}</span>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            <span class="text-neutral-cw">{{ category.created_at|date:"M d, Y" }}</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="d-flex gap-1">
                                                <a href="{% url 'venues_app:admin_category_edit' category.id %}"
                                                   class="btn btn-cw-accent-outline btn-sm"
                                                   title="Edit Category">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'venues_app:admin_category_toggle_status' category.id %}"
                                                   class="btn btn-cw-secondary btn-sm"
                                                   title="{% if category.is_active %}Deactivate{% else %}Activate{% endif %} Category">
                                                    {% if category.is_active %}
                                                        <i class="fas fa-pause"></i>
                                                    {% else %}
                                                        <i class="fas fa-play"></i>
                                                    {% endif %}
                                                </a>
                                                <a href="{% url 'venues_app:admin_category_delete' category.id %}"
                                                   class="btn btn-outline-danger btn-sm"
                                                   title="Delete Category"
                                                   onclick="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 80px; height: 80px;">
                                <i class="fas fa-tags text-neutral-cw fa-2x"></i>
                            </div>
                            <h5 class="text-brand-cw mb-2">No Categories Found</h5>
                            <p class="text-neutral-cw mb-4">Start by creating your first venue category to organize services.</p>
                            <a href="{% url 'venues_app:admin_category_create' %}" class="btn btn-cw-primary">
                                <i class="fas fa-plus me-2"></i>Create First Category
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
