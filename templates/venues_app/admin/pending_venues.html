{% extends 'venues_app/base_venues.html' %}
{% load static %}
{% load venue_filters %}

{% block title %}Pending Venues - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb bg-light-cw rounded-cw p-3">
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="text-brand-cw text-decoration-none">
                            <i class="fas fa-tachometer-alt me-1"></i>Admin Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active text-neutral-cw" aria-current="page">Pending Venues</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="display-font text-brand-cw mb-2">Pending Venues</h1>
                    <p class="lead text-neutral-cw mb-0">Review and approve venue submissions awaiting approval</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-secondary">
                        <i class="fas fa-list me-2"></i>All Venues
                    </a>
                    <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="btn btn-cw-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-clock text-white fa-lg"></i>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ pending_venues|length }}</div>
                    <div class="stat-label text-neutral-cw">Pending Review</div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card-cw h-100">
                <div class="card-body text-center p-4">
                    <div class="bg-brand-cw rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-bookmark text-white fa-lg"></i>
                    </div>
                    <div class="stat-number text-brand-cw mb-2">{{ page_obj.number|default:1 }}</div>
                    <div class="stat-label text-neutral-cw">Current Page</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Venues List -->
    {% if pending_venues %}
        <div class="row">
            <div class="col-12">
                <div class="card-cw">
                    <div class="card-header bg-light-cw border-0">
                        <h5 class="mb-0 text-brand-cw fw-bold">
                            <i class="fas fa-clock me-2"></i>Venues Awaiting Approval
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        {% for venue in pending_venues %}
                        <div class="border-bottom border-light {% if forloop.last %}border-0{% endif %}">
                            <div class="p-4">
                                <div class="row align-items-center">
                                    <div class="col-lg-2 col-md-3 mb-3 mb-md-0">
                                        {% if venue.get_primary_image %}
                                            <img src="{{ venue.get_primary_image }}"
                                                 alt="{{ venue.venue_name }}"
                                                 class="img-fluid rounded-cw shadow-cw-sm"
                                                 style="height: 120px; width: 100%; object-fit: cover;">
                                        {% else %}
                                            <div class="bg-light-cw rounded-cw d-flex align-items-center justify-content-center shadow-cw-sm" style="height: 120px;">
                                                <i class="fas fa-image text-neutral-cw fa-2x"></i>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="col-lg-7 col-md-6 mb-3 mb-lg-0">
                                        <div class="d-flex align-items-start justify-content-between mb-2">
                                            <h5 class="text-brand-cw fw-bold mb-1">{{ venue.venue_name }}</h5>
                                            <span class="badge bg-warning text-dark ms-2">
                                                <i class="fas fa-clock me-1"></i>{{ venue.get_approval_status_display }}
                                            </span>
                                        </div>

                                        <div class="row g-2 mb-3">
                                            <div class="col-sm-6">
                                                <small class="text-neutral-cw">
                                                    <i class="fas fa-user me-1 text-brand-cw"></i>{{ venue.service_provider.user.email }}
                                                </small>
                                            </div>
                                            <div class="col-sm-6">
                                                <small class="text-neutral-cw">
                                                    <i class="fas fa-map-marker-alt me-1 text-brand-cw"></i>{{ venue.city }}, {{ venue.state }}
                                                </small>
                                            </div>
                                            <div class="col-sm-6">
                                                <small class="text-neutral-cw">
                                                    <i class="fas fa-calendar me-1 text-brand-cw"></i>{{ venue.created_at|date:"M d, Y" }}
                                                </small>
                                            </div>
                                            {% if venue.categories.first %}
                                            <div class="col-sm-6">
                                                <small class="text-neutral-cw">
                                                    <i class="fas fa-tag me-1 text-brand-cw"></i>{{ venue.categories.first.category_name }}
                                                </small>
                                            </div>
                                            {% endif %}
                                        </div>

                                        {% if venue.short_description %}
                                        <p class="text-neutral-cw mb-0 small">{{ venue.short_description|truncatewords:25 }}</p>
                                        {% endif %}
                                    </div>

                                    <div class="col-lg-3 col-md-3 text-end">
                                        <div class="d-flex flex-column gap-2">
                                            <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}"
                                               class="btn btn-cw-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>Review Details
                                            </a>
                                            <div class="d-flex gap-1">
                                                <button class="btn btn-success btn-sm flex-fill"
                                                        onclick="quickApprove({{ venue.id }})"
                                                        title="Quick Approve">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-danger btn-sm flex-fill"
                                                        onclick="quickReject({{ venue.id }})"
                                                        title="Quick Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Pending venues pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link text-brand-cw" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-chevron-left me-1"></i>Previous
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link bg-brand-cw border-brand-cw">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link text-brand-cw" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link text-brand-cw" href="?page={{ page_obj.next_page_number }}">
                                    Next<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card-cw">
                    <div class="card-body text-center py-5">
                        <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 100px; height: 100px;">
                            <i class="fas fa-check-circle text-success fa-3x"></i>
                        </div>
                        <h3 class="text-brand-cw mb-3">No Pending Venues</h3>
                        <p class="text-neutral-cw mb-4">Excellent work! All venue submissions have been reviewed and processed.</p>
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                            <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="btn btn-cw-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Back to Dashboard
                            </a>
                            <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-list me-2"></i>View All Venues
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block venues_extra_js %}
<script>
function quickApprove(venueId) {
    if (confirm('Are you sure you want to approve this venue? This action will make it visible to customers.')) {
        // Add AJAX call to approve venue
        // This would need to be implemented in the backend
        console.log('Quick approve venue', venueId);
        // For now, redirect to detail page
        window.location.href = `/admin/venues/${venueId}/`;
    }
}

function quickReject(venueId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason && reason.trim()) {
        if (confirm('Are you sure you want to reject this venue?')) {
            // Add AJAX call to reject venue
            // This would need to be implemented in the backend
            console.log('Quick reject venue', venueId, 'with reason:', reason);
            // For now, redirect to detail page
            window.location.href = `/admin/venues/${venueId}/`;
        }
    } else if (reason !== null) {
        alert('Please provide a reason for rejection.');
    }
}
</script>
{% endblock %}
