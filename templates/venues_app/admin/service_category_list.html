{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Manage Service Categories - Admin{% endblock title %}

{% block extra_css %}
<style>
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        display: block;
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .category-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .category-card:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
        transform: translateY(-2px);
    }

    .category-card.inactive {
        opacity: 0.6;
        border-color: var(--cw-neutral-300);
    }

    .category-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .category-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
    }

    .category-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .category-status.active {
        background: var(--cw-success-light);
        color: var(--cw-success-dark);
    }

    .category-status.inactive {
        background: var(--cw-neutral-200);
        color: var(--cw-neutral-600);
    }

    .category-description {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .category-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
    }

    .category-stat {
        text-align: center;
    }

    .category-stat-number {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        display: block;
    }

    .category-stat-label {
        font-size: 0.75rem;
        color: var(--cw-neutral-600);
    }

    .category-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .filter-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .search-box {
        flex: 1;
        min-width: 250px;
        position: relative;
    }

    .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    .search-box i {
        position: absolute;
        top: 50%;
        left: 0.75rem;
        transform: translateY(-50%);
        color: var(--cw-neutral-500);
    }

    .status-filter select {
        padding: 0.75rem 1rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        background: white;
    }
</style>
{% endblock extra_css %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-primary mb-1">Service Categories</h1>
        <p class="text-muted mb-0">Manage service categories for better organization</p>
    </div>
    <a href="{% url 'venues_app:admin_service_category_create' %}" class="btn btn-cw-primary">
        <i class="fas fa-plus me-2"></i>Add New Category
    </a>
</div>

<!-- Statistics Cards -->
<div class="stats-cards">
    <div class="stat-card">
        <span class="stat-number">{{ total_categories }}</span>
        <div class="stat-label">Total Categories</div>
    </div>
    <div class="stat-card">
        <span class="stat-number">{{ active_categories }}</span>
        <div class="stat-label">Active Categories</div>
    </div>
    <div class="stat-card">
        <span class="stat-number">{{ inactive_categories }}</span>
        <div class="stat-label">Inactive Categories</div>
    </div>
</div>

<!-- Filter Controls -->
<div class="filter-controls">
    <div class="search-box">
        <form method="GET" class="d-flex">
            <div class="position-relative flex-grow-1">
                <i class="fas fa-search"></i>
                <input type="text" name="search" value="{{ search_query }}" 
                       placeholder="Search categories..." 
                       onchange="this.form.submit()">
            </div>
            {% if status_filter %}
                <input type="hidden" name="status" value="{{ status_filter }}">
            {% endif %}
        </form>
    </div>
    
    <div class="status-filter">
        <form method="GET" class="d-flex">
            <select name="status" onchange="this.form.submit()">
                <option value="">All Status</option>
                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active Only</option>
                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive Only</option>
            </select>
            {% if search_query %}
                <input type="hidden" name="search" value="{{ search_query }}">
            {% endif %}
        </form>
    </div>
</div>

<!-- Categories Grid -->
{% if service_categories %}
    <div class="category-grid">
        {% for category in service_categories %}
            <div class="category-card {% if not category.is_active %}inactive{% endif %}">
                <div class="category-status {% if category.is_active %}active{% else %}inactive{% endif %}">
                    {% if category.is_active %}Active{% else %}Inactive{% endif %}
                </div>
                
                <div class="category-header">
                    <div class="category-icon" style="background-color: {{ category.color_code|default:'#6b7280' }}">
                        <i class="{{ category.icon_class|default:'fas fa-folder' }}"></i>
                    </div>
                    <h3 class="category-name">{{ category.name }}</h3>
                </div>
                
                {% if category.description %}
                    <p class="category-description">{{ category.description|truncatewords:20 }}</p>
                {% endif %}
                
                <div class="category-stats">
                    <div class="category-stat">
                        <span class="category-stat-number">{{ category.service_count }}</span>
                        <div class="category-stat-label">Services</div>
                    </div>
                    <div class="category-stat">
                        <span class="category-stat-number">{{ category.venue_count }}</span>
                        <div class="category-stat-label">Venues</div>
                    </div>
                    <div class="category-stat">
                        <span class="category-stat-number">{{ category.sort_order }}</span>
                        <div class="category-stat-label">Sort Order</div>
                    </div>
                </div>
                
                <div class="category-actions">
                    <a href="{% url 'venues_app:admin_service_category_edit' category.id %}" 
                       class="btn btn-cw-secondary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                    
                    <a href="{% url 'venues_app:admin_service_category_toggle_status' category.id %}" 
                       class="btn btn-outline-secondary btn-sm"
                       onclick="return confirm('Toggle status for {{ category.name }}?')">
                        <i class="fas fa-{% if category.is_active %}eye-slash{% else %}eye{% endif %} me-1"></i>
                        {% if category.is_active %}Deactivate{% else %}Activate{% endif %}
                    </a>
                    
                    {% if category.service_count == 0 %}
                        <a href="{% url 'venues_app:admin_service_category_delete' category.id %}" 
                           class="btn btn-outline-danger btn-sm"
                           onclick="return confirm('Are you sure you want to delete {{ category.name }}? This action cannot be undone.')">
                            <i class="fas fa-trash me-1"></i>Delete
                        </a>
                    {% else %}
                        <span class="btn btn-outline-secondary btn-sm disabled" 
                              title="Cannot delete category with existing services">
                            <i class="fas fa-trash me-1"></i>Delete
                        </span>
                    {% endif %}
                    
                    <a href="{% url 'venues_app:services_by_category' category.slug %}" 
                       class="btn btn-outline-primary btn-sm" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>View
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-folder-open fa-4x text-muted"></i>
        </div>
        <h3 class="text-muted">No Service Categories Found</h3>
        <p class="text-muted mb-4">
            {% if search_query or status_filter %}
                No categories match your current filters.
            {% else %}
                Start by creating your first service category.
            {% endif %}
        </p>
        {% if not search_query and not status_filter %}
            <a href="{% url 'venues_app:admin_service_category_create' %}" class="btn btn-cw-primary">
                <i class="fas fa-plus me-2"></i>Create First Category
            </a>
        {% else %}
            <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary">
                <i class="fas fa-arrow-left me-2"></i>Clear Filters
            </a>
        {% endif %}
    </div>
{% endif %}
{% endblock content %} 