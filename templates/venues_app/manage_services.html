{% extends 'venues_app/base_venues.html' %}

{% block title %}Manage Services - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<style>
    /* Service Management Enhanced Styling */
    .services-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .services-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .services-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .services-header .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .services-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin: 0;
        font-family: var(--cw-font-heading);
    }

    .services-subtitle {
        color: var(--cw-neutral-600);
        margin: 0.5rem 0 0 0;
        font-size: 1.125rem;
    }

    /* Enhanced Quick Stats with Better Visual Hierarchy */
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        border: 2px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-sm);
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--cw-brand-primary);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .stat-card.stat-active::before {
        background: #10b981;
    }

    .stat-card.stat-featured::before {
        background: #f59e0b;
    }

    .stat-card.stat-categories::before {
        background: #8b5cf6;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
        line-height: 1;
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-weight: 500;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .stat-icon {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 2.5rem;
        height: 2.5rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        opacity: 0.3;
    }

    /* Enhanced Filters and Controls */
    .services-controls {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .filter-section {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: center;
        margin-bottom: 1rem;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        white-space: nowrap;
        font-size: 0.875rem;
    }

    .filter-select, .search-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .filter-select:focus, .search-input:focus {
        border-color: var(--cw-brand-primary);
        outline: none;
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    .search-group {
        position: relative;
        flex: 1;
        min-width: 250px;
    }

    .search-input {
        width: 100%;
        padding-left: 2.5rem;
    }

    .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--cw-neutral-600);
    }

    /* Enhanced View Toggle */
    .view-toggle {
        display: flex;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        padding: 0.25rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .view-toggle button {
        background: none;
        border: none;
        padding: 0.5rem 0.75rem;
        border-radius: 0.25rem;
        color: var(--cw-neutral-600);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .view-toggle button.active {
        background: var(--cw-brand-primary);
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .view-toggle button:hover:not(.active) {
        background: rgba(47, 22, 15, 0.1);
        color: var(--cw-brand-primary);
    }

    /* Enhanced Bulk Actions */
    .bulk-actions {
        display: none;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1.5rem;
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        color: white;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .bulk-actions.show {
        display: flex;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .bulk-select-all {
        font-weight: 600;
        color: white;
    }

    .bulk-actions .filter-select {
        background: rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.3);
        color: var(--cw-brand-primary);
    }

    .bulk-actions .btn-cw-secondary {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    .bulk-actions .btn-cw-secondary:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    /* Enhanced Service Categories */
    .category-section {
        margin-bottom: 2.5rem;
    }

    .category-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background: white;
        border-radius: 1rem;
        border: 2px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.3s ease;
    }

    .category-header:hover {
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .category-title {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-family: var(--cw-font-heading);
    }

    .category-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-count {
        background: var(--cw-brand-primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .toggle-category {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .toggle-category:hover {
        background: var(--cw-brand-primary);
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .toggle-category.collapsed i {
        transform: rotate(180deg);
    }

    /* Enhanced Service Grid */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        transition: all 0.3s ease;
    }

    .services-grid.list-view {
        grid-template-columns: 1fr;
    }

    .services-grid.list-view .service-card {
        display: flex;
        align-items: center;
        padding: 1.5rem;
    }

    .services-grid.list-view .service-card-header {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .services-grid.list-view .service-details {
        display: flex;
        gap: 2rem;
        margin: 0;
    }

    .services-grid.list-view .service-detail-item {
        background: none;
        border: none;
        padding: 0;
        text-align: left;
    }

    /* Enhanced Service Cards with Better Visual Hierarchy */
    .service-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-sm);
        border: 2px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .service-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .service-card.selected {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    /* Enhanced Status Indicators */
    .service-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: #dc2626; /* Red for inactive by default */
        z-index: 1;
    }

    .service-card[data-status="active"]::before {
        background: #10b981; /* Green for active */
    }

    .service-card[data-featured="true"]::after {
        content: '';
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 0.75rem;
        height: 0.75rem;
        background: #f59e0b;
        border-radius: 50%;
        box-shadow: 0 0 0 2px white, 0 0 0 4px #f59e0b;
        z-index: 2;
    }

    .service-card-header {
        padding: 1.5rem 1.5rem 0 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
        position: relative;
    }

    .service-checkbox {
        position: absolute;
        top: 1rem;
        left: 1rem;
        width: 1.25rem;
        height: 1.25rem;
        accent-color: var(--cw-brand-primary);
        z-index: 3;
    }

    .service-card.has-checkbox .service-card-header {
        padding-left: 3.5rem;
    }

    .service-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.75rem 0;
        line-height: 1.3;
        font-family: var(--cw-font-heading);
    }

    .service-category-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
        font-weight: 500;
        color: var(--cw-brand-primary);
    }

    .service-description {
        padding: 0 1.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.9375rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .service-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        padding: 0 1.5rem;
        margin-bottom: 1.5rem;
    }

    .service-detail-item {
        text-align: center;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .service-detail-item:hover {
        background: white;
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-sm);
    }

    .service-detail-value {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        margin-bottom: 0.25rem;
    }

    .service-detail-label {
        color: var(--cw-neutral-600);
        font-size: 0.8125rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    /* Enhanced Service Footer with Better Status Indicators */
    .service-footer {
        padding: 1.5rem;
        border-top: 1px solid var(--cw-brand-accent);
        background: var(--cw-accent-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .service-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .badge-cw-active {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .badge-cw-inactive {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .badge-cw-featured {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
        position: relative;
    }

    .badge-cw-featured::before {
        content: '⭐';
        margin-right: 0.25rem;
    }

    /* Enhanced Service Actions */
    .service-actions {
        position: relative;
    }

    .dropdown-toggle-cw {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dropdown-toggle-cw:hover {
        background: var(--cw-brand-primary);
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-back {
        border: 2px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        background: white;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        text-decoration: none;
        margin-bottom: 2rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    .btn-cw-disabled {
        background: var(--cw-neutral-600);
        color: white;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        cursor: not-allowed;
        opacity: 0.6;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 1rem;
        border: 2px dashed var(--cw-brand-accent);
        margin-bottom: 2rem;
    }

    .empty-state-icon {
        width: 4rem;
        height: 4rem;
        background: var(--cw-accent-light);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: var(--cw-brand-primary);
        font-size: 1.5rem;
    }

    .empty-state h3 {
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-family: var(--cw-font-heading);
    }

    .empty-state p {
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
        font-size: 1.125rem;
    }

    /* Comprehensive Responsive Design */
    @media (max-width: 1200px) {
        .services-container {
            max-width: 100%;
            padding: 0 1.5rem;
        }

        .services-grid {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        }
    }

    @media (max-width: 992px) {
        .services-title {
            font-size: 1.75rem;
        }

        .services-header .content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1.5rem;
        }

        .quick-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .services-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.25rem;
        }

        .filter-section {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .filter-group {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .search-group {
            min-width: auto;
        }

        .category-title {
            font-size: 1.25rem;
        }

        .category-icon {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 1rem;
        }

        .bulk-actions {
            flex-wrap: wrap;
            gap: 0.75rem;
            padding: 1rem;
        }

        .bulk-actions .filter-select {
            min-width: 150px;
        }
    }

    @media (max-width: 768px) {
        .services-section {
            padding: 1rem 0;
        }

        .services-container {
            padding: 0 1rem;
        }

        .services-header {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .services-title {
            font-size: 1.5rem;
        }

        .services-subtitle {
            font-size: 1rem;
        }

        .services-header .content {
            gap: 1rem;
        }

        .quick-stats {
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-value {
            font-size: 2rem;
        }

        .stat-icon {
            width: 2rem;
            height: 2rem;
            font-size: 1rem;
            top: 0.75rem;
            right: 0.75rem;
        }

        .services-controls {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .filter-select, .search-input {
            padding: 0.625rem 0.75rem;
            font-size: 0.875rem;
        }

        .search-input {
            padding-left: 2.25rem;
        }

        .search-icon {
            left: 0.625rem;
        }

        .view-toggle {
            justify-self: stretch;
        }

        .view-toggle button {
            flex: 1;
            padding: 0.625rem;
        }

        .services-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .services-grid.list-view .service-card {
            flex-direction: column;
            align-items: stretch;
            padding: 1rem;
        }

        .services-grid.list-view .service-card-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .services-grid.list-view .service-details {
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }

        .category-section {
            margin-bottom: 2rem;
        }

        .category-header {
            padding: 1rem;
            margin-bottom: 1rem;
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .category-title {
            font-size: 1.125rem;
            gap: 0.75rem;
        }

        .category-icon {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
        }

        .category-controls {
            justify-content: space-between;
        }

        .service-card {
            border-width: 1px;
        }

        .service-card-header {
            padding: 1rem 1rem 0 1rem;
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .service-card.has-checkbox .service-card-header {
            padding-left: 3rem;
        }

        .service-checkbox {
            top: 0.75rem;
            left: 0.75rem;
        }

        .service-title {
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
        }

        .service-actions {
            align-self: flex-end;
        }

        .service-description {
            padding: 0 1rem;
            margin-bottom: 1rem;
        }

        .service-details {
            padding: 0 1rem;
            margin-bottom: 1rem;
            gap: 0.75rem;
        }

        .service-detail-item {
            padding: 0.75rem;
        }

        .service-detail-value {
            font-size: 1rem;
        }

        .service-footer {
            padding: 1rem;
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .service-badges {
            justify-content: center;
        }

        .bulk-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .bulk-actions .filter-select,
        .bulk-actions .btn-cw-secondary {
            width: 100%;
        }

        .empty-state {
            padding: 2rem 1rem;
        }

        .empty-state-icon {
            width: 3rem;
            height: 3rem;
            font-size: 1.25rem;
        }
    }

    @media (max-width: 480px) {
        .services-section {
            padding: 0.5rem 0;
        }

        .services-container {
            padding: 0 0.75rem;
        }

        .services-header {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .services-title {
            font-size: 1.25rem;
        }

        .services-controls {
            padding: 0.75rem;
        }

        .stat-card {
            padding: 0.75rem;
        }

        .stat-value {
            font-size: 1.75rem;
        }

        .category-header {
            padding: 0.75rem;
        }

        .service-card-header {
            padding: 0.75rem 0.75rem 0 0.75rem;
        }

        .service-card.has-checkbox .service-card-header {
            padding-left: 2.5rem;
        }

        .service-checkbox {
            top: 0.5rem;
            left: 0.5rem;
            width: 1rem;
            height: 1rem;
        }

        .service-description {
            padding: 0 0.75rem;
        }

        .service-details {
            padding: 0 0.75rem;
            grid-template-columns: 1fr;
        }

        .service-footer {
            padding: 0.75rem;
        }

        .dropdown-toggle-cw {
            width: 2rem;
            height: 2rem;
            padding: 0.25rem;
        }

        .toggle-category {
            width: 2rem;
            height: 2rem;
            padding: 0.25rem;
        }
    }

    /* Dark Mode Support (if implemented) */
    @media (prefers-color-scheme: dark) {
        .service-card:hover {
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
        }
    }

    /* Print Styles */
    @media print {
        .services-section {
            background: white;
            padding: 0;
        }

        .services-controls,
        .bulk-actions,
        .service-actions,
        .service-checkbox {
            display: none !important;
        }

        .service-card {
            break-inside: avoid;
            box-shadow: none;
            border: 1px solid #ccc;
        }

        .services-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
        .service-card {
            border-width: 2px;
        }

        .service-card::before {
            height: 6px;
        }

        .badge-cw-active,
        .badge-cw-inactive,
        .badge-cw-featured {
            border: 2px solid currentColor;
        }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
        .service-card,
        .stat-card,
        .category-header,
        .dropdown-toggle-cw,
        .toggle-category,
        .service-detail-item {
            transition: none;
        }

        .bulk-actions.show {
            animation: none;
        }

        .service-card:hover,
        .stat-card:hover {
            transform: none;
        }
    }
</style>
{% endblock %}

{% block venues_content %}
<section class="services-section">
    <div class="services-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            Back to Venue Management
        </a>

        <!-- Header -->
        <div class="services-header">
            <div class="content">
                <div>
                    <h1 class="services-title">Manage Services</h1>
                    <p class="services-subtitle">{{ venue.venue_name }} - {{ services.count }}/{{ max_services }} services</p>
                </div>
                <div>
                    {% if can_add_service %}
                        <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                            <i class="fas fa-plus"></i>Add Service
                        </a>
                    {% else %}
                        <div class="btn-cw-disabled" title="Maximum {{ max_services }} services allowed">
                            <i class="fas fa-plus"></i>Add Service (Limit Reached)
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        {% if services %}
            <!-- Enhanced Quick Stats with Icons -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-spa"></i>
                    </div>
                    <div class="stat-value">{{ services.count }}</div>
                    <div class="stat-label">Total Services</div>
                </div>
                <div class="stat-card stat-active">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value">{{ services_active_count|default:"0" }}</div>
                    <div class="stat-label">Active Services</div>
                </div>
                <div class="stat-card stat-featured">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-value">{{ services_featured_count|default:"0" }}</div>
                    <div class="stat-label">Featured Services</div>
                </div>
                <div class="stat-card stat-categories">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-value">{{ services_categories_count|default:"0" }}</div>
                    <div class="stat-label">Categories Used</div>
                </div>
            </div>

            <!-- Controls -->
            <div class="services-controls">
                <div class="filter-section">
                    <div class="filter-group">
                        <label class="filter-label">Filter:</label>
                        <select class="filter-select" id="categoryFilter">
                            <option value="">All Categories</option>
                            {% for category in service_categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select class="filter-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active Only</option>
                            <option value="inactive">Inactive Only</option>
                            <option value="featured">Featured Only</option>
                        </select>
                    </div>

                    <div class="search-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" id="searchServices" placeholder="Search services...">
                    </div>

                    <div class="view-toggle">
                        <button type="button" class="active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulkActions">
                    <span class="bulk-select-all">
                        <strong id="selectedCount">0</strong> services selected
                    </span>
                    <select class="filter-select" id="bulkAction">
                        <option value="">Choose Action</option>
                        <option value="activate">Activate Selected</option>
                        <option value="deactivate">Deactivate Selected</option>
                        <option value="feature">Mark as Featured</option>
                        <option value="unfeature">Remove Featured</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button type="button" class="btn-cw-secondary" id="applyBulkAction">Apply</button>
                    <button type="button" class="btn-cw-secondary" id="clearSelection">Clear</button>
                </div>
            </div>

            <!-- Services Content -->
            <div id="servicesContent">
                {% regroup services by service_category as services_by_category %}
                
                {% for category_group in services_by_category %}
                    <div class="category-section" data-category="{{ category_group.grouper.id|default:'uncategorized' }}">
                        <div class="category-header">
                            <h2 class="category-title">
                                {% if category_group.grouper %}
                                    <span class="category-icon" style="background-color: {{ category_group.grouper.color_code|default:'#6b7280' }}">
                                        <i class="{{ category_group.grouper.icon_class|default:'fas fa-folder' }}"></i>
                                    </span>
                                    {{ category_group.grouper.name }}
                                {% else %}
                                    <span class="category-icon" style="background-color: #6b7280">
                                        <i class="fas fa-folder"></i>
                                    </span>
                                    Uncategorized
                                {% endif %}
                            </h2>
                            <div class="category-controls">
                                <span class="category-count">{{ category_group.list|length }}</span>
                                <button class="toggle-category" data-category="{{ category_group.grouper.id|default:'uncategorized' }}">
                                    <i class="fas fa-chevron-up"></i>
                                </button>
                            </div>
                        </div>

                        <div class="services-grid" id="category-{{ category_group.grouper.id|default:'uncategorized' }}">
                            {% for service in category_group.list %}
                                <div class="service-card" 
                                     data-service-id="{{ service.id }}"
                                     data-category="{{ service.service_category.id|default:'' }}"
                                     data-status="{% if service.is_active %}active{% else %}inactive{% endif %}"
                                     data-featured="{% if service.is_featured %}true{% else %}false{% endif %}"
                                     data-search="{{ service.service_title|lower }} {{ service.short_description|lower }}">
                                    
                                    <input type="checkbox" class="service-checkbox" value="{{ service.id }}">
                                    
                                    <div class="service-card-header has-checkbox">
                                        <div style="flex: 1;">
                                            <h3 class="service-title">{{ service.service_title }}</h3>
                                            {% if service.service_category %}
                                                <div class="service-category-badge">
                                                    <i class="{{ service.service_category.icon_class|default:'fas fa-tag' }}"></i>
                                                    {{ service.service_category.name }}
                                                </div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="service-actions">
                                            <div class="dropdown">
                                                <button class="dropdown-toggle-cw" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'venues_app:service_edit' service.pk %}">
                                                            <i class="fas fa-edit me-2"></i>Edit
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug=service.slug %}" target="_blank">
                                                            <i class="fas fa-eye me-2"></i>Preview
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="{% url 'venues_app:service_delete' service.pk %}">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="service-description">
                                        {{ service.short_description|truncatewords:20 }}
                                    </div>

                                    <div class="service-details">
                                        <div class="service-detail-item">
                                            <div class="service-detail-value">{{ service.price_display }}</div>
                                            <p class="service-detail-label">Price</p>
                                        </div>
                                        <div class="service-detail-item">
                                            <div class="service-detail-value">{{ service.duration_display }}</div>
                                            <p class="service-detail-label">Duration</p>
                                        </div>
                                    </div>

                                    <div class="service-footer">
                                        <div class="service-badges">
                                            {% if service.is_active %}
                                                <span class="badge-cw-active">Active</span>
                                            {% else %}
                                                <span class="badge-cw-inactive">Inactive</span>
                                            {% endif %}
                                            {% if service.is_featured %}
                                                <span class="badge-cw-featured">Featured</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-spa"></i>
                </div>
                <h3>No Services Yet</h3>
                <p>Start by adding your first service to attract customers and showcase your offerings.</p>
                {% if can_add_service %}
                    <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                        <i class="fas fa-plus"></i>Add Your First Service
                    </a>
                {% endif %}
            </div>
        {% endif %}

        <!-- Tips Section -->
        <div style="margin-top: 3rem;">
            <div style="background: var(--cw-accent-light); border: 1px solid var(--cw-brand-accent); border-radius: 1rem; padding: 2rem;">
                <h3 style="font-family: var(--cw-font-heading); font-weight: 600; color: var(--cw-brand-primary); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                    <i class="fas fa-lightbulb" style="color: var(--cw-brand-accent); background: var(--cw-brand-primary); padding: 0.5rem; border-radius: 0.5rem; font-size: 1rem;"></i>
                    Service Management Tips
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Organize services by categories for better customer experience
                            </li>
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Use bulk actions to efficiently manage multiple services
                            </li>
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Feature your most popular services to highlight them
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Maximum {{ max_services }} services per venue
                            </li>
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Inactive services won't be visible to customers
                            </li>
                            <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                Use the search and filters to quickly find services
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    const searchInput = document.getElementById('searchServices');
    const serviceCards = document.querySelectorAll('.service-card');
    const checkboxes = document.querySelectorAll('.service-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    const categoryToggles = document.querySelectorAll('.toggle-category');
    const viewToggle = document.querySelectorAll('.view-toggle button');

    // Filter functionality
    function filterServices() {
        const categoryValue = categoryFilter.value;
        const statusValue = statusFilter.value;
        const searchValue = searchInput.value.toLowerCase();

        serviceCards.forEach(card => {
            let visible = true;

            // Category filter
            if (categoryValue && card.dataset.category !== categoryValue) {
                visible = false;
            }

            // Status filter
            if (statusValue) {
                if (statusValue === 'active' && card.dataset.status !== 'active') visible = false;
                if (statusValue === 'inactive' && card.dataset.status !== 'inactive') visible = false;
                if (statusValue === 'featured' && card.dataset.featured !== 'true') visible = false;
            }

            // Search filter
            if (searchValue && !card.dataset.search.includes(searchValue)) {
                visible = false;
            }

            card.style.display = visible ? 'block' : 'none';
        });

        // Update category section visibility
        document.querySelectorAll('.category-section').forEach(section => {
            const visibleCards = section.querySelectorAll('.service-card[style*="display: block"], .service-card:not([style*="display: none"])');
            section.style.display = visibleCards.length > 0 ? 'block' : 'none';
        });
    }

    // Event listeners for filters
    categoryFilter.addEventListener('change', filterServices);
    statusFilter.addEventListener('change', filterServices);
    searchInput.addEventListener('input', filterServices);

    // Category toggle functionality
    categoryToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const categoryId = this.dataset.category;
            const categoryGrid = document.getElementById(`category-${categoryId}`);
            const icon = this.querySelector('i');
            
            if (categoryGrid.style.display === 'none') {
                categoryGrid.style.display = 'grid';
                icon.className = 'fas fa-chevron-up';
            } else {
                categoryGrid.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
            }
        });
    });

    // Checkbox selection
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.service-checkbox:checked');
        const count = checkedBoxes.length;
        
        selectedCount.textContent = count;
        bulkActions.classList.toggle('show', count > 0);
        
        // Update card styling
        serviceCards.forEach(card => {
            const checkbox = card.querySelector('.service-checkbox');
            card.classList.toggle('selected', checkbox.checked);
        });
    }

    // Bulk actions
    document.getElementById('applyBulkAction').addEventListener('click', function() {
        const action = document.getElementById('bulkAction').value;
        const selectedServices = Array.from(document.querySelectorAll('.service-checkbox:checked')).map(cb => cb.value);
        
        if (!action || selectedServices.length === 0) {
            alert('Please select an action and at least one service.');
            return;
        }

        if (confirm(`Are you sure you want to ${action} ${selectedServices.length} service(s)?`)) {
            // Show loading state
            const button = this;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            button.disabled = true;

            // Create form data
            const formData = new FormData();
            formData.append('action', action);
            selectedServices.forEach(serviceId => {
                formData.append('service_ids', serviceId);
            });

            // Add CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            formData.append('csrfmiddlewaretoken', csrfToken);

            // Make AJAX request
            fetch('{% url "venues_app:bulk_service_actions" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken,
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification(data.message, 'success');
                    
                    // If delete action, remove cards from DOM
                    if (action === 'delete') {
                        selectedServices.forEach(serviceId => {
                            const card = document.querySelector(`[data-service-id="${serviceId}"]`);
                            if (card) {
                                card.remove();
                            }
                        });
                    } else {
                        // For other actions, update the cards
                        selectedServices.forEach(serviceId => {
                            const card = document.querySelector(`[data-service-id="${serviceId}"]`);
                            if (card) {
                                updateServiceCard(card, action);
                            }
                        });
                    }
                    
                    // Clear selection
                    clearSelection();
                    
                    // Update statistics (you might want to reload the page or update stats dynamically)
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(data.error || 'An error occurred', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while processing the request', 'error');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    });

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    function updateServiceCard(card, action) {
        const badgesContainer = card.querySelector('.service-badges');
        if (!badgesContainer) return;

        switch (action) {
            case 'activate':
                // Update active badge
                const inactiveBadge = badgesContainer.querySelector('.badge-cw-inactive');
                if (inactiveBadge) {
                    inactiveBadge.className = 'badge-cw-active';
                    inactiveBadge.textContent = 'Active';
                }
                card.dataset.status = 'active';
                break;
                
            case 'deactivate':
                // Update inactive badge
                const activeBadge = badgesContainer.querySelector('.badge-cw-active');
                if (activeBadge) {
                    activeBadge.className = 'badge-cw-inactive';
                    activeBadge.textContent = 'Inactive';
                }
                card.dataset.status = 'inactive';
                break;
                
            case 'feature':
                // Add featured badge if not exists
                let featuredBadge = badgesContainer.querySelector('.badge-cw-featured');
                if (!featuredBadge) {
                    featuredBadge = document.createElement('span');
                    featuredBadge.className = 'badge-cw-featured';
                    featuredBadge.textContent = 'Featured';
                    badgesContainer.appendChild(featuredBadge);
                }
                card.dataset.featured = 'true';
                break;
                
            case 'unfeature':
                // Remove featured badge
                const existingFeaturedBadge = badgesContainer.querySelector('.badge-cw-featured');
                if (existingFeaturedBadge) {
                    existingFeaturedBadge.remove();
                }
                card.dataset.featured = 'false';
                break;
        }
    }

    // Add CSRF token to page (if not already present)
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = '{{ csrf_token }}';
        document.body.appendChild(csrfInput);
    }

    document.getElementById('clearSelection').addEventListener('click', clearSelection);

    function clearSelection() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateBulkActions();
    }

    // View toggle (placeholder for future implementation)
    viewToggle.forEach(button => {
        button.addEventListener('click', function() {
            viewToggle.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const view = this.dataset.view;
            // Here you would implement grid vs list view switching
            console.log('View changed to:', view);
        });
    });

    // Initialize
    filterServices();
});
</script>
{% endblock %}

