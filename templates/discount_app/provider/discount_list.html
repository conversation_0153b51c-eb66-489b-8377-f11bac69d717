{% extends 'discount_app/base_discount.html' %}
{% load static i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Manage Discounts" %} - CozyWish{% endblock %}

{% block discount_extra_css %}
<style>
    /* Provider discount list specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Dropdown menu styling */
    .discount-wrapper .dropdown-menu {
        border: 2px solid black;
        border-radius: 0.75rem;
        background-color: white;
    }

    .discount-wrapper .dropdown-item {
        color: black;
        font-family: var(--font-primary);
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .discount-wrapper .dropdown-item:hover {
        background-color: #f8f9fa;
        color: black;
    }

    /* Icon styling */
    .discount-icon {
        background-color: white;
        border: 2px solid black;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
    }

    /* Progress bar styling */
    .discount-wrapper .progress {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }

    .discount-wrapper .progress-bar {
        background-color: black;
    }
</style>
{% endblock %}

{% block discount_content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="section-title">{% trans "Manage Discounts" %}</h1>
                <p class="section-subtitle">{% trans "Create and manage discounts for" %} {{ venue.venue_name }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'discount_app:provider_discount_analytics' %}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-line me-2"></i>Analytics
                </a>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-plus me-2"></i>Create Discount
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{% url 'discount_app:create_service_discount' %}">
                                <i class="fas fa-concierge-bell me-2"></i>Service Discount
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'discount_app:create_venue_discount' %}">
                                <i class="fas fa-building me-2"></i>Venue Discount
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3" aria-label="Filter discounts">
                        <div class="col-md-4">
                            <label class="form-label">Search</label>
                            <input type="text" name="search" class="form-control" aria-label="Search discounts"
                                   placeholder="Search discounts..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Type</label>
                            <select name="type" class="form-select" aria-label="Discount type">
                                <option value="all" {% if discount_type_filter == 'all' %}selected{% endif %}>All Types</option>
                                <option value="service" {% if discount_type_filter == 'service' %}selected{% endif %}>Service Discounts</option>
                                <option value="venue" {% if discount_type_filter == 'venue' %}selected{% endif %}>Venue Discounts</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select" aria-label="Status filter">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Status</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                                <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>Scheduled</option>
                                <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>Expired</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col-12">
            <p class="text-muted mb-0">
                <i class="fas fa-info-circle me-1"></i>
                Showing {{ page_obj.object_list|length }} of {{ total_discounts }} discount{{ total_discounts|pluralize }}
            </p>
        </div>
    </div>

    <!-- Discounts List -->
    {% include 'discount_app/includes/discount_skeleton.html' %}
    <div id="discount-results" class="row" style="display:none">
        {% if page_obj.object_list %}
            {% for discount_item in page_obj.object_list %}
            <div class="col-12 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        {% if discount_item.type == 'service' %}
                                            <div class="discount-icon">
                                                <i class="fas fa-concierge-bell"></i>
                                            </div>
                                        {% else %}
                                            <div class="discount-icon">
                                                <i class="fas fa-building"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h5 class="mb-1">{{ discount_item.object.name }}</h5>
                                        <p class="text-muted mb-1">
                                            <strong>{{ discount_item.service_name }}</strong>
                                        </p>
                                        {% if discount_item.object.description %}
                                            <p class="text-muted small mb-0">{{ discount_item.object.description|truncatechars:100 }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-1">
                                        {% if discount_item.object.discount_type == 'percentage' %}
                                            {{ discount_item.object.discount_value }}%
                                        {% else %}
                                            ${{ discount_item.object.discount_value }}
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">
                                        {{ discount_item.object.get_discount_type_display }}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    {% if discount_item.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif discount_item.status == 'scheduled' %}
                                        <span class="badge bg-primary">Scheduled</span>
                                    {% elif discount_item.status == 'expired' %}
                                        <span class="badge bg-primary">Expired</span>
                                    {% endif %}
                                    <div class="progress mt-1" style="height:4px;">
                                        {% if discount_item.object.is_approved %}
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        {% elif discount_item.object.approved_by %}
                                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        {% else %}
                                            <div class="progress-bar" role="progressbar" style="width: 50%"></div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                {% if discount_item.type == 'service' %}
                                    <a href="{% url 'discount_app:service_discount_detail' discount_item.object.slug %}" class="btn btn-outline-secondary btn-sm me-1" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-secondary btn-sm me-1" data-bs-toggle="collapse" data-bs-target="#editForm{{ discount_item.object.id }}" aria-expanded="false" aria-controls="editForm{{ discount_item.object.id }}" title="Quick Edit">
                                        <i class="fas fa-pen"></i>
                                    </button>
                                    <a href="{% url 'discount_app:edit_service_discount' discount_item.object.slug %}" class="btn btn-outline-primary btn-sm me-1" title="Full Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{% url 'discount_app:delete_service_discount' discount_item.object.slug %}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this discount?')">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete"><i class="fas fa-trash"></i></button>
                                    </form>
                                {% else %}
                                    <a href="{% url 'discount_app:venue_discount_detail' discount_item.object.slug %}" class="btn btn-outline-secondary btn-sm me-1" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-secondary btn-sm me-1" data-bs-toggle="collapse" data-bs-target="#editForm{{ discount_item.object.id }}" aria-expanded="false" aria-controls="editForm{{ discount_item.object.id }}" title="Quick Edit">
                                        <i class="fas fa-pen"></i>
                                    </button>
                                    <a href="{% url 'discount_app:edit_venue_discount' discount_item.object.slug %}" class="btn btn-outline-primary btn-sm me-1" title="Full Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{% url 'discount_app:delete_venue_discount' discount_item.object.slug %}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this discount?')">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete"><i class="fas fa-trash"></i></button>
                                    </form>
                                {% endif %}
                            </div>
                        </div>
                        <div class="collapse" id="editForm{{ discount_item.object.id }}">
                            <form method="post" action="{% if discount_item.type == 'service' %}{% url 'discount_app:quick_edit_service_discount' discount_item.object.slug %}{% else %}{% url 'discount_app:quick_edit_venue_discount' discount_item.object.slug %}{% endif %}" class="row g-2 mt-2 align-items-end">
                                {% csrf_token %}
                                <div class="col-md-3">
                                    <label class="form-label small mb-0">Value</label>
                                    <input type="number" step="0.01" min="0.01" name="discount_value" value="{{ discount_item.object.discount_value }}" class="form-control form-control-sm" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label small mb-0">Start</label>
                                    <input type="datetime-local" name="start_date" value="{{ discount_item.object.start_date|date:'Y-m-d\TH:i' }}" class="form-control form-control-sm" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label small mb-0">End</label>
                                    <input type="datetime-local" name="end_date" value="{{ discount_item.object.end_date|date:'Y-m-d\TH:i' }}" class="form-control form-control-sm" required>
                                </div>
                                <div class="col-md-1 text-end">
                                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <div class="mb-3">
                            <img src="{% static 'img/no_discounts.svg' %}" alt="No discounts" style="width:120px;" class="img-fluid">
                        </div>
                        <h5>No Discounts Found</h5>
                        <p class="text-muted mb-4">
                            {% if search_query or discount_type_filter != 'all' or status_filter != 'all' %}
                                No discounts match your current filters. Try adjusting your search criteria.
                            {% else %}
                                You haven't created any discounts yet. Create your first discount to attract more customers!
                            {% endif %}
                        </p>
                        <a href="{% url 'discount_app:create_service_discount' %}" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-2"></i>Create Service Discount
                        </a>
                        <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Create Venue Discount
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Discount pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if discount_type_filter != 'all' %}&type={{ discount_type_filter }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
