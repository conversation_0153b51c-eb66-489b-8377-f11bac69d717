{% extends 'discount_app/base_discount.html' %}

{% block title %}{{ discount.name }} - Discount Details - CozyWish{% endblock %}

{% block extra_css %}
{% load static i18n %}
<link rel="stylesheet" href="{% static 'css/discount_app/discount_wireframe.css' %}">
{% endblock %}

{% block discount_content %}
<div class="container py-4">
    <div id="detail-skeleton" class="skeleton-card mb-4" style="height:200px;"></div>
    <div id="detail-content" style="display:none;">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="flex-grow-1">
                    <h1 class="h3 mb-1">{{ discount.name }}</h1>
                    <p class="text-muted mb-0">
                        {% if discount_type == 'service' %}
                            Service Discount for {{ discount.service.service_title }}
                        {% else %}
                            Venue-wide Discount
                        {% endif %}
                    </p>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-2"></i>{% trans "Actions" %}
                    </button>
                    <ul class="dropdown-menu">
                        {% if discount_type == 'service' %}
                            <li>
                                <a class="dropdown-item" href="{% url 'discount_app:edit_service_discount' discount.slug %}">
                                    <i class="fas fa-edit me-2"></i>Edit Discount
                                </a>
                            </li>
                        {% else %}
                            <li>
                                <a class="dropdown-item" href="{% url 'discount_app:edit_venue_discount' discount.slug %}">
                                    <i class="fas fa-edit me-2"></i>Edit Discount
                                </a>
                            </li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        {% if discount_type == 'service' %}
                            <li>
                                <form method="post" action="{% url 'discount_app:delete_service_discount' discount.slug %}"
                                      onsubmit="return confirm('Are you sure you want to delete this discount?')">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-trash me-2"></i>Delete Discount
                                    </button>
                                </form>
                            </li>
                        {% else %}
                            <li>
                                <form method="post" action="{% url 'discount_app:delete_venue_discount' discount.slug %}"
                                      onsubmit="return confirm('Are you sure you want to delete this discount?')">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-trash me-2"></i>Delete Discount
                                    </button>
                                </form>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
</div>

    <!-- Discount Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                {% if discount_type == 'service' %}
                                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                        <i class="fas fa-concierge-bell fa-2x text-primary"></i>
                                    </div>
                                {% else %}
                                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                        <i class="fas fa-building fa-2x text-success"></i>
                                    </div>
                                {% endif %}
                                <h4 class="mb-1">
                                    {% if discount.discount_type == 'percentage' %}
                                        {{ discount.discount_value }}%
                                    {% else %}
                                        ${{ discount.discount_value }}
                                    {% endif %}
                                </h4>
                                <small class="text-muted">{{ discount.get_discount_type_display }}</small>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Status</h6>
                                    {% if discount.get_status == 'active' %}
                                        <span class="badge bg-success fs-6">Active</span>
                                    {% elif discount.get_status == 'scheduled' %}
                                        <span class="badge bg-warning fs-6">Scheduled</span>
                                    {% elif discount.get_status == 'expired' %}
                                        <span class="badge bg-secondary fs-6">Expired</span>
                                    {% endif %}
                                    
                                    {% if not discount.is_approved %}
                                        <br><span class="badge bg-warning mt-2">Pending Approval</span>
                                    {% else %}
                                        <br><span class="badge bg-success mt-2">Approved</span>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Duration</h6>
                                    <p class="mb-0">
                                        <strong>Start:</strong> {{ discount.start_date|date:"M d, Y g:i A" }}<br>
                                        <strong>End:</strong> {{ discount.end_date|date:"M d, Y g:i A" }}
                                    </p>
                                </div>
                            </div>
                            {% if discount.description %}
                                <div class="mt-3">
                                    <h6 class="text-muted mb-2">Description</h6>
                                    <p class="mb-0">{{ discount.description }}</p>
                                </div>
                            {% endif %}
        </div>
    </div>
</div>
    </div>

    <!-- Discount Details -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Discount Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Type:</strong>
                        </div>
                        <div class="col-6">
                            {% if discount_type == 'service' %}
                                Service Discount
                            {% else %}
                                Venue Discount
                            {% endif %}
                        </div>
                    </div>
                    {% if discount_type == 'service' %}
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>Service:</strong>
                            </div>
                            <div class="col-6">
                                {{ discount.service.service_title }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>Service Price:</strong>
                            </div>
                            <div class="col-6">
                                {{ discount.service.price_display }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>Discounted Price:</strong>
                            </div>
                            <div class="col-6">
                                ${{ discount.get_discounted_service_price }}
                            </div>
                        </div>
                    {% else %}
                        {% if discount.min_booking_value %}
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Min. Booking:</strong>
                                </div>
                                <div class="col-6">
                                    ${{ discount.min_booking_value }}
                                </div>
                            </div>
                        {% endif %}
                        {% if discount.max_discount_amount %}
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Max. Discount:</strong>
                                </div>
                                <div class="col-6">
                                    ${{ discount.max_discount_amount }}
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Created:</strong>
                        </div>
                        <div class="col-6">
                            {{ discount.created_at|date:"M d, Y" }}
                        </div>
                    </div>
                    {% if discount.approved_at %}
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>Approved:</strong>
                            </div>
                            <div class="col-6">
                                {{ discount.approved_at|date:"M d, Y" }}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Usage Statistics
                    </h6>
                </div>
                <div class="card-body">
                    {% if usage_stats %}
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Total Uses:</span>
                                <strong>{{ usage_stats|length }}</strong>
                            </div>
                        </div>
                        <h6 class="mb-3">Recent Usage</h6>
                        <div class="list-group list-group-flush">
                            {% for usage in usage_stats|slice:":5" %}
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">{{ usage.used_at|date:"M d, Y g:i A" }}</small><br>
                                            <small>Saved: ${{ usage.get_savings_amount }}</small>
                                        </div>
                                        <span class="badge bg-primary">{{ usage.get_savings_percentage }}%</span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6>No Usage Yet</h6>
                            <p class="text-muted mb-0">This discount hasn't been used by any customers yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="mb-3">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                    <div class="d-flex gap-2 flex-wrap">
                        {% if discount_type == 'service' %}
                            <a href="{% url 'discount_app:edit_service_discount' discount.slug %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Edit Discount
                            </a>
                            <a href="{% url 'discount_app:create_service_discount' %}" class="btn btn-outline-success">
                                <i class="fas fa-plus me-2"></i>Create Another Service Discount
                            </a>
                        {% else %}
                            <a href="{% url 'discount_app:edit_venue_discount' discount.slug %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Edit Discount
                            </a>
                            <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-outline-success">
                                <i class="fas fa-plus me-2"></i>Create Another Venue Discount
                            </a>
                        {% endif %}
                        <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>View All Discounts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>
{% endblock %}
