{% extends "dashboard_app/base_dashboard.html" %}
{% load i18n %}
{% load static %}
{% block extra_css %}{% if block.super %}{{ block.super }}{% endif %}<link rel="stylesheet" href="{% static 'css/discount_app/discount_wireframe.css' %}">{% endblock %}

{% block title %}{% trans "Delete Discount" %} - {{ discount.name }}{% endblock %}

{% block dashboard_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-trash-alt me-2 text-danger"></i>
                        {% if discount_type == 'service' %}
                            {% trans "Delete Service Discount" %}
                        {% elif discount_type == 'venue' %}
                            {% trans "Delete Venue Discount" %}
                        {% else %}
                            {% trans "Delete Discount" %}
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>{% trans "Warning!" %}</strong> {% trans "This action cannot be undone." %}
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>{% trans "Discount Details" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Name" %}:</strong></td>
                                    <td>{{ discount.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Type" %}:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ discount_type|title }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Discount Value" %}:</strong></td>
                                    <td>
                                        {% if discount.discount_type == 'percentage' %}
                                            {{ discount.discount_value }}%
                                        {% else %}
                                            ${{ discount.discount_value }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Start Date" %}:</strong></td>
                                    <td>{{ discount.start_date|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "End Date" %}:</strong></td>
                                    <td>{{ discount.end_date|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status" %}:</strong></td>
                                    <td>
                                        {% if discount.is_active %}
                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Additional Information" %}</h5>
                            <table class="table table-borderless">
                                {% if discount_type == 'service' %}
                                    <tr>
                                        <td><strong>{% trans "Service" %}:</strong></td>
                                        <td>{{ discount.service.service_title }}</td>
                                    </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>{% trans "Venue" %}:</strong></td>
                                    <td>{{ venue.venue_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Created" %}:</strong></td>
                                    <td>{{ discount.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Usage Count" %}:</strong></td>
                                    <td>
                                        <span class="badge bg-info">{{ discount.get_usage_count }} {% trans "uses" %}</span>
                                    </td>
                                </tr>
                                {% if discount.max_uses %}
                                    <tr>
                                        <td><strong>{% trans "Max Uses" %}:</strong></td>
                                        <td>{{ discount.max_uses }}</td>
                                    </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {% if discount.description %}
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>{% trans "Description" %}</h5>
                                <p class="text-muted">{{ discount.description }}</p>
                            </div>
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-12">
                            <p class="text-danger">
                                <strong>{% trans "Are you sure you want to delete this discount?" %}</strong>
                            </p>
                            <p class="text-muted">
                                {% trans "This will permanently remove the discount and all associated data. Customers will no longer be able to use this discount." %}
                            </p>

                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger me-2">
                                    <i class="fas fa-trash-alt me-2"></i>{% trans "Yes, Delete Discount" %}
                                </button>
                            </form>
                            <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
