{% extends 'discount_app/base_discount_admin.html' %}

{% block title %}{{ discount.name }} - Discount Details - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/admin_app/admin_app.css' %}">
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .detail-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .usage-table {
        max-height: 400px;
        overflow-y: auto;
    }
    .discount-preview {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_dashboard' %}">Discount Management</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_list' discount_type=discount_type %}">{{ discount_type|title }} Discounts</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ discount.name }}</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title mb-1">
                            <i class="fas fa-info-circle me-2"></i>{{ discount.name }}
                        </h1>
                        <p class="text-muted">Detailed information and usage analytics</p>
                    </div>
                    <div>
                        {% if discount_type == 'platform' %}
                        <a href="{% url 'discount_app:admin_edit_platform_discount' discount_id=discount.id %}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <a href="{% url 'discount_app:admin_delete_platform_discount' discount_id=discount.id %}" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Delete
                        </a>
                        {% else %}
                        {% if not discount.is_approved %}
                        <a href="{% url 'discount_app:admin_approve_discount' discount_type=discount_type discount_id=discount.id %}" class="btn btn-success">
                            <i class="fas fa-check me-1"></i> Approve
                        </a>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Discount Information -->
            <div class="col-lg-8">
                <!-- Discount Preview -->
                <div class="detail-card">
                    <div class="discount-preview">
                        <h2>{{ discount.name }}</h2>
                        <div class="mt-3">
                            {% if discount.discount_type == 'percentage' %}
                            <span class="display-3 fw-bold">{{ discount.discount_value }}%</span>
                            <div>OFF</div>
                            {% else %}
                            <span class="display-3 fw-bold">${{ discount.discount_value }}</span>
                            <div>OFF</div>
                            {% endif %}
                        </div>
                        {% if discount.description %}
                        <p class="mt-3 mb-0">{{ discount.description }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Discount Details -->
                <div class="card detail-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Discount Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>
                                            {% if discount.discount_type == 'percentage' %}
                                            <span class="badge bg-primary">Percentage</span>
                                            {% else %}
                                            <span class="badge bg-secondary">Fixed Amount</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Value:</strong></td>
                                        <td>
                                            {% if discount.discount_type == 'percentage' %}
                                            {{ discount.discount_value }}%
                                            {% else %}
                                            ${{ discount.discount_value }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            {% if discount.get_status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                            {% elif discount.get_status == 'scheduled' %}
                                            <span class="badge bg-info">Scheduled</span>
                                            {% elif discount.get_status == 'expired' %}
                                            <span class="badge bg-secondary">Expired</span>
                                            {% else %}
                                            <span class="badge bg-warning">{{ discount.get_status|title }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% if discount_type != 'platform' %}
                                    <tr>
                                        <td><strong>Approval:</strong></td>
                                        <td>
                                            {% if discount.is_approved %}
                                            <span class="badge bg-success">Approved</span>
                                            {% if discount.approved_by %}
                                            <br><small class="text-muted">by {{ discount.approved_by.get_full_name|default:discount.approved_by.email }}</small>
                                            {% endif %}
                                            {% else %}
                                            <span class="badge bg-warning">Pending Approval</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endif %}
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Valid From:</strong></td>
                                        <td>{{ discount.start_date|date:"M d, Y g:i A" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Valid Until:</strong></td>
                                        <td>{{ discount.end_date|date:"M d, Y g:i A" }}</td>
                                    </tr>
                                    {% if discount.min_booking_value %}
                                    <tr>
                                        <td><strong>Min. Booking:</strong></td>
                                        <td>${{ discount.min_booking_value }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if discount.max_discount_amount %}
                                    <tr>
                                        <td><strong>Max. Discount:</strong></td>
                                        <td>${{ discount.max_discount_amount }}</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{{ discount.created_at|date:"M d, Y g:i A" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created by:</strong></td>
                                        <td>{{ discount.created_by.get_full_name|default:discount.created_by.email }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Related Entity Information -->
                        {% if related_entity %}
                        <hr>
                        <h6>Related {{ entity_type|title }}</h6>
                        {% if entity_type == 'venue' %}
                        <p><strong>Venue:</strong> <a href="{% url 'venues_app:venue_detail' related_entity.slug %}">{{ related_entity.venue_name }}</a></p>
                        <p><strong>Location:</strong> {{ related_entity.city }}, {{ related_entity.state }}</p>
                        {% elif entity_type == 'service' %}
                        <p><strong>Service:</strong> {{ related_entity.service_title }}</p>
                        <p><strong>Venue:</strong> <a href="{% url 'venues_app:venue_detail' related_entity.venue.slug %}">{{ related_entity.venue.venue_name }}</a></p>
                        <p><strong>Price:</strong> ${{ related_entity.price_min }} - ${{ related_entity.price_max }}</p>
                        {% elif entity_type == 'category' and related_entity %}
                        <p><strong>Category:</strong> {{ related_entity.name }}</p>
                        {% elif entity_type == 'category' %}
                        <p><strong>Category:</strong> All Categories</p>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>

                <!-- Recent Usage History -->
                <div class="card detail-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Recent Usage History</h5>
                    </div>
                    <div class="card-body">
                        {% if recent_usages %}
                        <div class="usage-table">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Original Price</th>
                                        <th>Discount</th>
                                        <th>Final Price</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for usage in recent_usages %}
                                    <tr>
                                        <td>{{ usage.user.get_full_name|default:usage.user.email }}</td>
                                        <td>${{ usage.original_price }}</td>
                                        <td class="text-success">${{ usage.discount_amount }}</td>
                                        <td><strong>${{ usage.final_price }}</strong></td>
                                        <td>{{ usage.used_at|date:"M d, Y g:i A" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <p class="text-muted">No usage history available for this discount.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="col-lg-4">
                <!-- Overall Stats -->
                <div class="card detail-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">Usage Statistics</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-6">
                                <div class="metric-value text-primary">{{ usage_stats.total_usage|default:0 }}</div>
                                <div class="metric-label">Total Uses</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-success">${{ usage_stats.total_savings|default:0|floatformat:2 }}</div>
                                <div class="metric-label">Total Savings</div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <div class="metric-value text-warning">${{ usage_stats.avg_discount|default:0|floatformat:2 }}</div>
                                <div class="metric-label">Avg. Discount</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-info">{{ savings_percentage }}%</div>
                                <div class="metric-label">Savings %</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Customers -->
                <div class="card detail-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">Top Customers</h5>
                    </div>
                    <div class="card-body">
                        {% if top_customers %}
                        <div class="usage-table">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Uses</th>
                                        <th>Savings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in top_customers %}
                                    <tr>
                                        <td>{{ customer.user__first_name }} {{ customer.user__last_name }}</td>
                                        <td>{{ customer.usage_count }}</td>
                                        <td>${{ customer.total_savings|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <p class="text-muted">No customer usage data available.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Monthly Usage Chart Data -->
                {% if monthly_usage %}
                <div class="card detail-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">Monthly Usage Trend</h5>
                    </div>
                    <div class="card-body">
                        {% for month_data in monthly_usage %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ month_data.month }}</span>
                            <div>
                                <span class="badge bg-primary">{{ month_data.usage_count }} uses</span>
                                <small class="text-muted">${{ month_data.total_savings|floatformat:2 }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any interactive features here
        console.log('Discount detail page loaded');
    });
</script>
{% endblock %}
