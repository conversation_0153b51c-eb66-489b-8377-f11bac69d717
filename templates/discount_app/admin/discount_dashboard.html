{% extends 'discount_app/base_discount_admin.html' %}

{% block title %}Discount Management Dashboard - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/admin_app/admin_app.css' %}">
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .stats-card {
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    .stats-card:hover {
        transform: translateY(-2px);
    }
    .analytics-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Discount Management</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-percentage me-2"></i>Discount Dashboard
                </h1>
                <p class="text-muted">Manage platform-wide discounts and monitor usage analytics</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="{% url 'discount_app:admin_create_platform_discount' %}" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-plus me-1"></i> Create Platform Discount
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{% url 'discount_app:admin_discount_list' discount_type='venue' %}" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-store me-1"></i> Manage Venue Discounts
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{% url 'discount_app:admin_discount_list' discount_type='service' %}" class="btn btn-info w-100 mb-2 text-white">
                                    <i class="fas fa-spa me-1"></i> Manage Service Discounts
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{% url 'discount_app:admin_discount_list' discount_type='platform' %}" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-globe me-1"></i> Manage Platform Discounts
                                </a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <a href="{% url 'discount_app:admin_usage_analytics' %}" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-chart-line me-1"></i> Usage Analytics
                                </a>
                            </div>
                            <div class="col-md-6">
                                {% if total_pending > 0 %}
                                <a href="{% url 'discount_app:admin_discount_list' discount_type='venue' %}?status=pending" class="btn btn-danger w-100 mb-2">
                                    <i class="fas fa-clock me-1"></i> Pending Approvals ({{ total_pending }})
                                </a>
                                {% else %}
                                <button class="btn btn-outline-secondary w-100 mb-2" disabled>
                                    <i class="fas fa-check me-1"></i> No Pending Approvals
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ total_venue_discounts }}</div>
                        <div class="metric-label">Total Venue Discounts</div>
                        <small class="text-success">{{ active_venue_discounts }} active</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ total_service_discounts }}</div>
                        <div class="metric-label">Total Service Discounts</div>
                        <small class="text-success">{{ active_service_discounts }} active</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ total_platform_discounts }}</div>
                        <div class="metric-label">Total Platform Discounts</div>
                        <small class="text-success">{{ active_platform_discounts }} active</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ total_discount_usages }}</div>
                        <div class="metric-label">Total Usage Count</div>
                        <small class="text-info">All time</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Analytics (Last 30 Days) -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analytics-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Usage Analytics ({{ analytics_period }})
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="metric-value text-info">{{ recent_usage_stats.total_usage|default:0 }}</div>
                                    <div class="metric-label">Total Uses</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="metric-value text-success">${{ recent_usage_stats.total_savings|default:0|floatformat:2 }}</div>
                                    <div class="metric-label">Total Savings</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="metric-value text-warning">${{ recent_usage_stats.avg_discount|default:0|floatformat:2 }}</div>
                                    <div class="metric-label">Average Discount</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="metric-value text-primary">{{ avg_savings_percentage }}%</div>
                                    <div class="metric-label">Average Savings %</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Discounts -->
        <div class="row">
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="card-title mb-0">Top Venue Discounts</h6>
                    </div>
                    <div class="card-body">
                        {% if popular_venue_discounts %}
                            {% for discount in popular_venue_discounts %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ discount.name|truncatechars:20 }}</strong><br>
                                    <small class="text-muted">{{ discount.venue.venue_name|truncatechars:25 }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">{{ discount.usage_count }} uses</span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No venue discount usage in the last {{ analytics_period }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">Top Service Discounts</h6>
                    </div>
                    <div class="card-body">
                        {% if popular_service_discounts %}
                            {% for discount in popular_service_discounts %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ discount.name|truncatechars:20 }}</strong><br>
                                    <small class="text-muted">{{ discount.service.service_title|truncatechars:25 }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info">{{ discount.usage_count }} uses</span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No service discount usage in the last {{ analytics_period }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="card-title mb-0">Top Platform Discounts</h6>
                    </div>
                    <div class="card-body">
                        {% if popular_platform_discounts %}
                            {% for discount in popular_platform_discounts %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ discount.name|truncatechars:20 }}</strong><br>
                                    <small class="text-muted">
                                        {% if discount.category %}{{ discount.category.name }}{% else %}All Categories{% endif %}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">{{ discount.usage_count }} uses</span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No platform discount usage in the last {{ analytics_period }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to cards
        const cards = document.querySelectorAll('.stats-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
            });
            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });
        });
    });
</script>
{% endblock %}
