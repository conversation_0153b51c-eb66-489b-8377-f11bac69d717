{% extends 'discount_app/base_discount_admin.html' %}

{% block title %}Delete Discount - Admin Dashboard - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/admin_app/admin_app.css' %}">
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .delete-warning {
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_dashboard' %}">Discount Management</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_list' discount_type=discount_type %}">{{ discount_type|title }} Discounts</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Delete Discount</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-trash-alt me-2 text-danger"></i>Delete Discount
                </h1>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Warning Message -->
                        <div class="alert delete-warning" role="alert">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Warning: This action cannot be undone!</h6>
                            <p class="mb-0">You are about to permanently delete this discount. This will remove all associated data and cannot be reversed.</p>
                        </div>

                        <!-- Discount Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Discount Details</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ discount.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>
                                            {% if discount.discount_type == 'percentage' %}
                                                <span class="badge bg-primary">{{ discount.discount_value }}% off</span>
                                            {% else %}
                                                <span class="badge bg-secondary">${{ discount.discount_value }} off</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            {% if discount.get_status == 'active' %}
                                                <span class="badge bg-success">Active</span>
                                            {% elif discount.get_status == 'scheduled' %}
                                                <span class="badge bg-info">Scheduled</span>
                                            {% elif discount.get_status == 'expired' %}
                                                <span class="badge bg-secondary">Expired</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ discount.get_status|title }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Valid Period:</strong></td>
                                        <td>{{ discount.start_date|date:"M d, Y" }} - {{ discount.end_date|date:"M d, Y" }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>Additional Information</h5>
                                {% if discount.description %}
                                <p><strong>Description:</strong><br>{{ discount.description }}</p>
                                {% endif %}
                                
                                {% if discount_type == 'platform' %}
                                    {% if discount.category %}
                                    <p><strong>Category:</strong> {{ discount.category.name }}</p>
                                    {% else %}
                                    <p><strong>Category:</strong> All Categories</p>
                                    {% endif %}
                                    
                                    {% if discount.is_featured %}
                                    <p><span class="badge bg-warning">Featured Discount</span></p>
                                    {% endif %}
                                {% endif %}
                                
                                <p><strong>Created:</strong> {{ discount.created_at|date:"M d, Y g:i A" }}</p>
                                <p><strong>Created by:</strong> {{ discount.created_by.get_full_name|default:discount.created_by.email }}</p>
                            </div>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="post" class="text-center" aria-label="Confirm delete discount">
                            {% csrf_token %}
                            <div class="d-flex justify-content-center gap-3">
                                <a href="{% url 'discount_app:admin_discount_list' discount_type=discount_type %}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-danger btn-lg">
                                    <i class="fas fa-trash-alt me-2"></i>Yes, Delete Discount
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confirmation dialog for extra safety
        const deleteForm = document.querySelector('form[method="post"]');
        if (deleteForm) {
            deleteForm.addEventListener('submit', function(e) {
                const confirmed = confirm('Are you absolutely sure you want to delete this discount? This action cannot be undone.');
                if (!confirmed) {
                    e.preventDefault();
                }
            });
        }
    });
</script>
{% endblock %}
