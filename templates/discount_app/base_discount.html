{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Discount App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Discount wrapper - clean white background */
        .discount-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .discount-wrapper h1, .discount-wrapper h2, .discount-wrapper h3,
        .discount-wrapper h4, .discount-wrapper h5, .discount-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .discount-wrapper p, .discount-wrapper span, .discount-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .discount-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .discount-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .discount-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .discount-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .discount-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .discount-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .discount-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .discount-wrapper .btn-success {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-success:hover {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-danger {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-danger:hover {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-warning {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-warning:hover {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-info {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-info:hover {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-secondary {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }

        .discount-wrapper .btn-secondary:hover {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        /* Form elements */
        .discount-wrapper .form-control, .discount-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .discount-wrapper .form-control:focus, .discount-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .discount-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Badges */
        .discount-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .discount-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .discount-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .discount-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .discount-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .discount-wrapper .badge.bg-info {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .discount-wrapper .badge.bg-secondary {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        /* Pagination */
        .discount-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .discount-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .discount-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .discount-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .discount-wrapper .text-primary {
            color: black !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .discount-wrapper {
                padding: 1rem 0;
            }

            .discount-wrapper .card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
    {% block discount_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="discount-wrapper">
    <div class="container py-4">
        {% block discount_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block discount_extra_js %}{% endblock %}
{% endblock %}
