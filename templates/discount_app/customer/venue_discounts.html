{% extends 'discount_app/base_discount.html' %}

{% block title %}{{ venue.name }} Discounts - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/discount_app/discount_wireframe.css' %}">
<style>
    .discount-type-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    .savings-highlight {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: bold;
        text-align: center;
    }
    .price-comparison {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    .original-price {
        text-decoration: line-through;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .discounted-price {
        color: #28a745;
        font-weight: bold;
        font-size: 1.1rem;
    }
    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: none;
        z-index: 1000;
    }
    .back-to-top.show {
        display: inline-block;
    }
</style>
{% endblock %}

{% block discount_content %}
<div class="container-fluid py-5 bg-light">
    <div class="container">
        <!-- Venue Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        {% if item.rating %}
                            <div class="text-warning small mb-2">
                                <i class="fas fa-star me-1"></i>{{ item.rating|floatformat:1 }}★
                            </div>
                        {% endif %}
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h1 class="h2 mb-2">
                                    <i class="fas fa-tags me-2 text-primary"></i>
                                    Discounts at {{ venue.name }}
                                </h1>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ venue.get_full_address }}
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Venue
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Discount Summary -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center border-primary">
                    <div class="card-body">
                        <i class="fas fa-spa fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary">{{ service_discounts_count }}</h4>
                        <p class="mb-0">Service Discount{{ service_discounts_count|pluralize }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <i class="fas fa-building fa-2x text-success mb-2"></i>
                        <h4 class="text-success">{{ venue_discounts_count }}</h4>
                        <p class="mb-0">Venue Discount{{ venue_discounts_count|pluralize }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center border-warning">
                    <div class="card-body">
                        <i class="fas fa-globe fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning">{{ platform_discounts_count }}</h4>
                        <p class="mb-0">Platform Discount{{ platform_discounts_count|pluralize }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Discounts List -->
        {% if discount_data %}
        <div class="row g-4">
            {% for item in discount_data %}
            <div class="col-lg-6">
                <div class="card discount-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            {{ item.discount.name }}
                            {% if item.badge %}
                                <span class="badge bg-success ms-2">{{ item.badge }}</span>
                            {% endif %}
                        </h5>
                        {% if item.type == 'service' %}
                            <span class="badge bg-primary discount-type-badge">Service</span>
                        {% elif item.type == 'venue' %}
                            <span class="badge bg-success discount-type-badge">Venue</span>
                        {% else %}
                            <span class="badge bg-warning text-dark discount-type-badge">Platform</span>
                        {% endif %}
                    </div>
                    
                    <div class="card-body">
                        {% if item.type == 'service' %}
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-spa me-1"></i>{{ item.service.title }}
                                </h6>
                                {% if item.original_price and item.discounted_price %}
                                    <div class="price-comparison">
                                        <span class="original-price">${{ item.original_price }}</span>
                                        <i class="fas fa-arrow-right text-muted"></i>
                                        <span class="discounted-price">${{ item.discounted_price }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        <div class="savings-highlight mb-3">
                            {{ item.savings_text }}
                        </div>
                        
                        {% if item.discount.description %}
                            <p class="text-muted mb-3">{{ item.discount.description }}</p>
                        {% endif %}
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">Valid From</small>
                                <div class="fw-bold">{{ item.discount.start_date|date:"M d, Y" }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Valid Until</small>
                                <div class="fw-bold">{{ item.discount.end_date|date:"M d, Y" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        {% if item.type == 'service' %}
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}"
                               class="btn btn-primary w-100">
                                <i class="fas fa-calendar-plus me-2"></i>Book This Service
                            </a>
                        {% else %}
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}"
                               class="btn btn-primary w-100">
                                <i class="fas fa-eye me-2"></i>View All Services
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% else %}
        <div class="row">
            <div class="col-12 text-center py-5">
                <div class="card shadow-sm">
                    <div class="card-body py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">No Active Discounts</h3>
                        <p class="text-muted">This venue doesn't have any active discounts at the moment.</p>
                        <div class="mt-4">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-primary me-2">
                                <i class="fas fa-eye me-2"></i>View Venue Details
                            </a>
                            <a href="{% url 'discount_app:featured_discounts' %}" class="btn btn-outline-primary">
                                <i class="fas fa-tags me-2"></i>Browse All Discounts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Call to Action -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center py-4">
                        <h4 class="mb-3">Ready to Book?</h4>
                        <p class="mb-3">Discounts are automatically applied when you add services to your cart!</p>
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-light btn-lg">
                            <i class="fas fa-calendar-plus me-2"></i>Start Booking
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<a href="#" id="back-to-top" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</a>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const backBtn = document.getElementById('back-to-top');
    if (backBtn) {
        window.addEventListener('scroll', function(){
            if(window.scrollY > 200){
                backBtn.classList.add('show');
            } else {
                backBtn.classList.remove('show');
            }
        });
        backBtn.addEventListener('click', function(e){
            e.preventDefault();
            window.scrollTo({top:0, behavior:'smooth'});
        });
    }
});
</script>
{% endblock %}
