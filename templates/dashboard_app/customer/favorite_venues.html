{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}My Favorite Venues - CozyWish{% endblock %}

{% block dashboard_title %}My Favorite Venues{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/customer_sidebar.html' %}
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:customer_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Favorite Venues</li>
  </ol>
</nav>
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-heart text-danger me-2"></i>
                My Favorite Venues
            </h1>
            <div class="d-flex gap-2">
                <a href="{% url 'dashboard_app:customer_dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Find More Venues
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Favorites Count -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            You have <strong>{{ total_favorites }}</strong> favorite venue{{ total_favorites|pluralize }}.
            {% if total_favorites > 0 %}
                You'll receive notifications when these venues offer special discounts!
            {% endif %}
        </div>
    </div>
</div>

<!-- Favorite Venues Grid -->
<div class="row">
    {% if favorite_venues %}
        {% for favorite in favorite_venues %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <!-- Venue Image -->
                <div class="position-relative">
                    {% if favorite.venue.main_image %}
                        <img src="{{ favorite.venue.main_image.url }}" alt="{{ favorite.venue.venue_name }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-spa fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <!-- Remove from Favorites Button -->
                    <button class="btn btn-danger btn-sm position-absolute top-0 end-0 m-2 remove-favorite-btn"
                            data-venue-id="{{ favorite.venue.id }}"
                            data-venue-name="{{ favorite.venue.venue_name }}"
                            title="Remove from favorites">
                        <i class="fas fa-heart"></i>
                    </button>
                    
                    <!-- Venue Status Badge -->
                    {% if favorite.venue.approval_status == 'approved' and favorite.venue.visibility == 'active' %}
                        <span class="badge bg-success position-absolute bottom-0 start-0 m-2">
                            <i class="fas fa-check-circle me-1"></i> Active
                        </span>
                    {% else %}
                        <span class="badge bg-warning position-absolute bottom-0 start-0 m-2">
                            <i class="fas fa-clock me-1"></i> Inactive
                        </span>
                    {% endif %}
                </div>

                <!-- Card Body -->
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ favorite.venue.venue_name }}</h5>
                    <p class="card-text text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        {{ favorite.venue.city }}, {{ favorite.venue.state }}
                    </p>
                    <p class="card-text flex-grow-1">
                        {{ favorite.venue.short_description|truncatechars:100 }}
                    </p>
                    
                    <!-- Venue Tags -->
                    {% if favorite.venue.tags %}
                    <div class="mb-3">
                        {% for tag in favorite.venue.tags|slice:":3" %}
                            <span class="badge bg-light text-dark me-1">{{ tag|title }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Added Date -->
                    <small class="text-muted mb-3">
                        <i class="fas fa-heart me-1"></i>
                        Added to favorites on {{ favorite.added_date|date:"M d, Y" }}
                    </small>
                    
                    <!-- Action Buttons -->
                    <div class="mt-auto">
                        <div class="d-grid gap-2">
                            <a href="{% url 'venues_app:venue_detail' favorite.venue.id %}" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i> View Details
                            </a>
                            {% if favorite.venue.approval_status == 'approved' and favorite.venue.visibility == 'active' %}
                                <a href="{% url 'venues_app:venue_detail' favorite.venue.id %}#services" class="btn btn-outline-success">
                                    <i class="fas fa-calendar-plus me-1"></i> Book Service
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="col-12">
            <nav aria-label="Favorite venues pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-heart-broken fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">No favorite venues yet</h4>
                    <p class="text-muted">
                        Start exploring venues and add them to your favorites to keep track of places you love!
                    </p>
                    <div class="mt-4">
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i> Explore Venues
                        </a>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            Tip: Click the heart icon on any venue to add it to your favorites!
                        </small>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle remove from favorites
    document.querySelectorAll('.remove-favorite-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const venueId = this.dataset.venueId;
            const venueName = this.dataset.venueName;

            if (confirm(`Are you sure you want to remove "${venueName}" from your favorites?`)) {
                // Send AJAX request to remove from favorites
                fetch(`{% url 'dashboard_app:remove_favorite_venue' '0' %}`.replace('0', venueId), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the card from the page
                        this.closest('.col-lg-4').remove();
                        
                        // Show success message
                        showMessage('success', data.message);
                        
                        // Update favorites count
                        const currentCount = parseInt(document.querySelector('.alert-info strong').textContent);
                        const newCount = currentCount - 1;
                        document.querySelector('.alert-info strong').textContent = newCount;
                        
                        // If no favorites left, reload page to show empty state
                        if (newCount === 0) {
                            setTimeout(() => location.reload(), 1000);
                        }
                    } else {
                        showMessage('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('error', 'An error occurred while removing the venue from favorites.');
                });
            }
        });
    });
});

// Function to show messages (assuming this exists in your base template)
function showMessage(type, message) {
    // Create and show a Bootstrap alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the container
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
{% endblock %}
