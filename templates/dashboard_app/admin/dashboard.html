{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Admin Dashboard - CozyWish{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/admin_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-cog"></i> Django Admin
    </a>
    <a href="{% url 'admin:accounts_app_customuser_changelist' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-users-cog"></i> Manage Users
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
{% include 'payments_app/includes/dashboard_skeleton.html' %}
<div id="dashboard-content" style="display:none;">
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
  </ol>
</nav>
<!-- Key Metrics Overview -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h3 class="text-primary">{{ total_users }}</h3>
                <p class="mb-0">Total Users</p>
                <small class="text-muted">
                    {{ customer_count }} customers, {{ provider_count }} providers
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x text-info mb-2"></i>
                <h3 class="text-info">{{ total_venues }}</h3>
                <p class="mb-0">Total Venues</p>
                <small class="text-muted">
                    {{ approved_venues }} approved, {{ pending_venues }} pending
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x text-warning mb-2"></i>
                <h3 class="text-warning">{{ total_bookings }}</h3>
                <p class="mb-0">Total Bookings</p>
                <small class="text-muted">
                    {{ confirmed_bookings }} confirmed, {{ pending_bookings }} pending
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                <h3 class="text-success">${{ total_revenue|floatformat:2 }}</h3>
                <p class="mb-0">Total Revenue</p>
                <small class="text-muted">From confirmed bookings</small>
            </div>
        </div>
    </div>
</div>

<!-- Booking Status Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Booking Status Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-warning rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Pending: {{ pending_bookings }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Confirmed: {{ confirmed_bookings }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-info rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Completed: {{ completed_bookings }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-danger rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Cancelled: {{ cancelled_bookings }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Venue Status Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-warning rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Pending: {{ pending_venues }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Approved: {{ approved_venues }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-danger rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Rejected: {{ rejected_venues }}</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-secondary rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                            <span>Staff: {{ staff_count }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock"></i> Recent Bookings</h5>
                <a href="{% url 'dashboard_app:admin_booking_analytics' %}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_bookings %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Venue</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in recent_bookings %}
                            <tr>
                                <td>{{ booking.customer.get_full_name|default:booking.customer.email }}</td>
                                <td>{{ booking.venue.name|truncatechars:20 }}</td>
                                <td>{{ booking.booking_date|date:"M d" }}</td>
                                <td>
                                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'completed' %}info{% else %}danger{% endif %}">
                                        {{ booking.status|title }}
                                    </span>
                                </td>
                                <td>${{ booking.total_price|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No recent bookings to display.</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-star"></i> Recent Reviews</h5>
                <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-primary">
                    Manage
                </a>
            </div>
            <div class="card-body">
                {% if recent_reviews %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Venue</th>
                                <th>Rating</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for review in recent_reviews %}
                            <tr>
                                <td>{{ review.customer.get_full_name|default:review.customer.email }}</td>
                                <td>{{ review.venue.name|truncatechars:20 }}</td>
                                <td>
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}
                                            <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                            <i class="far fa-star text-muted"></i>
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>{{ review.created_at|date:"M d" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No recent reviews to display.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-building"></i> Manage Venues
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'admin:accounts_app_customuser_changelist' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'discount_app:admin_discount_dashboard' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-percent"></i> Platform Discounts
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'dashboard_app:admin_system_health' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-heartbeat"></i> System Health
                        </a>
                    </div>
                </div>
            </div>
    </div>
</div>
</div>
</div>
{% endblock %}

{% block dashboard_js %}
{{ block.super }}
<script src="{% static 'js/dashboard.js' %}"></script>
{% endblock %}
