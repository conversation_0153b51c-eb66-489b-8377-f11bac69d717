{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}System Health - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .health-card {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .health-score {
        font-size: 2.5rem;
        font-weight: bold;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: 600;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-healthy { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-critical { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">System Health Monitor</h1>
                <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Overall Health Score -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card health-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Overall Health Score</h5>
                    <div class="health-score" style="color: {{ health_color }};">
                        {{ health_score }}%
                    </div>
                    <p class="card-text">
                        <span class="status-indicator status-{{ health_status|lower }}"></span>
                        {{ health_status|title }}
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card health-card">
                <div class="card-body">
                    <h5 class="card-title">Performance Metrics</h5>
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="metric-value">{{ performance_metrics.cpu_usage }}%</div>
                            <small class="text-muted">CPU Usage</small>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="metric-value">{{ performance_metrics.memory_usage }}%</div>
                            <small class="text-muted">Memory Usage</small>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="metric-value">{{ performance_metrics.disk_usage }}%</div>
                            <small class="text-muted">Disk Usage</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Health -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card health-card">
                <div class="card-body">
                    <h5 class="card-title">Application Components</h5>
                    <div class="row">
                        {% for component, status in app_health.items %}
                        <div class="col-md-3 text-center mb-3">
                            <span class="status-indicator status-{{ status }}"></span>
                            <strong>{{ component|title }}</strong>
                            <br>
                            <small class="text-muted">{{ status|title }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card health-card">
                <div class="card-body">
                    <h5 class="card-title">Database Statistics</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td>Total Users</td>
                                    <td class="text-end"><strong>{{ db_stats.users.total }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Active Customers</td>
                                    <td class="text-end"><strong>{{ db_stats.users.customers }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Service Providers</td>
                                    <td class="text-end"><strong>{{ db_stats.users.providers }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Total Venues</td>
                                    <td class="text-end"><strong>{{ db_stats.venues.total }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Active Venues</td>
                                    <td class="text-end"><strong>{{ db_stats.venues.active }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Total Bookings</td>
                                    <td class="text-end"><strong>{{ db_stats.bookings.total }}</strong></td>
                                </tr>
                                <tr>
                                    <td>Today's Bookings</td>
                                    <td class="text-end"><strong>{{ db_stats.bookings.today }}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card health-card">
                <div class="card-body">
                    <h5 class="card-title">Recent System Events</h5>
                    <div class="list-group list-group-flush">
                        {% for event in recent_events %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ event.message }}</h6>
                                    <small class="text-muted">{{ event.timestamp|timesince }} ago</small>
                                </div>
                                <span class="badge bg-{{ event.level|lower }} rounded-pill">{{ event.level }}</span>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group-item border-0 px-0">
                            <p class="text-muted mb-0">No recent events</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}
