{% extends 'dashboard_app/base_dashboard.html' %}

{% block title %}Platform Overview - Admin Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/admin_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-cog"></i> Django Admin
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:admin_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Platform Overview</li>
  </ol>
</nav>
<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar"></i> Date Range Filter</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="Platform overview filter">
                    <div class="col-md-3">
                        {{ form.period.label_tag }}
                        {{ form.period }}
                    </div>
                    <div class="col-md-3">
                        {{ form.start_date.label_tag }}
                        {{ form.start_date }}
                    </div>
                    <div class="col-md-3">
                        {{ form.end_date.label_tag }}
                        {{ form.end_date }}
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter"></i> Apply Filter
                        </button>
                        <a href="{% url 'dashboard_app:admin_platform_overview' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
                <div class="mt-2">
                    <small class="text-muted">
                        Showing data from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics for Period -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-user-plus fa-2x text-primary mb-2"></i>
                <h3 class="text-primary">{{ new_users }}</h3>
                <p class="mb-0">New Users</p>
                <small class="text-muted">Total: {{ total_users }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x text-info mb-2"></i>
                <h3 class="text-info">{{ new_venues }}</h3>
                <p class="mb-0">New Venues</p>
                <small class="text-muted">Total: {{ total_venues }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-calendar-plus fa-2x text-warning mb-2"></i>
                <h3 class="text-warning">{{ new_bookings }}</h3>
                <p class="mb-0">New Bookings</p>
                <small class="text-muted">Total: {{ total_bookings }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                <h3 class="text-success">${{ period_revenue|floatformat:2 }}</h3>
                <p class="mb-0">Period Revenue</p>
                <small class="text-muted">Total: ${{ total_revenue|floatformat:2 }}</small>
            </div>
        </div>
    </div>
</div>

<!-- User Distribution -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> User Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ customer_count }}</h4>
                            <p class="mb-0">Customers</p>
                            <small class="text-muted">{{ new_customers }} new this period</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ provider_count }}</h4>
                            <p class="mb-0">Providers</p>
                            <small class="text-muted">{{ new_providers }} new this period</small>
                        </div>
                    </div>
                </div>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-primary" role="progressbar" 
                         style="width: {% widthratio customer_count total_users 100 %}%">
                        Customers
                    </div>
                    <div class="progress-bar bg-info" role="progressbar" 
                         style="width: {% widthratio provider_count total_users 100 %}%">
                        Providers
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building"></i> Venue Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ approved_venues }}</h4>
                            <p class="mb-0">Approved</p>
                        </div>
                    </div>
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ pending_venues }}</h4>
                            <p class="mb-0">Pending</p>
                        </div>
                    </div>
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-secondary">{{ total_venues }}</h4>
                            <p class="mb-0">Total</p>
                        </div>
                    </div>
                </div>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {% widthratio approved_venues total_venues 100 %}%">
                        Approved
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar" 
                         style="width: {% widthratio pending_venues total_venues 100 %}%">
                        Pending
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Status Distribution -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Booking Status Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ pending_bookings }}</h4>
                            <p class="mb-0">Pending</p>
                            <div class="progress mt-2" style="height: 10px;">
                                <div class="progress-bar bg-warning" role="progressbar" 
                                     style="width: {% widthratio pending_bookings total_bookings 100 %}%">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ confirmed_bookings }}</h4>
                            <p class="mb-0">Confirmed</p>
                            <div class="progress mt-2" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {% widthratio confirmed_bookings total_bookings 100 %}%">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ completed_bookings }}</h4>
                            <p class="mb-0">Completed</p>
                            <div class="progress mt-2" style="height: 10px;">
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: {% widthratio completed_bookings total_bookings 100 %}%">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-danger">{{ cancelled_bookings }}</h4>
                            <p class="mb-0">Cancelled</p>
                            <div class="progress mt-2" style="height: 10px;">
                                <div class="progress-bar bg-danger" role="progressbar" 
                                     style="width: {% widthratio cancelled_bookings total_bookings 100 %}%">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Daily Statistics Chart -->
{% if daily_stats %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Daily Trends</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>New Users</th>
                                <th>New Bookings</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, stats in daily_stats.items %}
                            <tr>
                                <td>{{ stats.date|date:"M d, Y" }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ stats.new_users }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ stats.new_bookings }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">${{ stats.revenue|floatformat:2 }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
