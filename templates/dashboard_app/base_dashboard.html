{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - CozyWish{% endblock %}

{% block extra_css %}
    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Dashboard App - Brand Design System */
        
        /* CSS Variables */
        :root {
            /* Brand Colors */
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;

            /* Neutral Colors */
            --cw-neutral-100: #f5f5f5;
            --cw-neutral-200: #e5e5e5;
            --cw-neutral-600: #525252;
            --cw-neutral-700: #404040;
            --cw-neutral-800: #262626;

            /* Typography */
            --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

            /* Shadows */
            --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* Gradients */
            --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
            --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
            --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
            --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        }

        /* Dashboard wrapper */
        .dashboard-wrapper {
            background: #fafafa;
            min-height: 100vh;
            font-family: var(--cw-font-primary);
        }

        /* Sidebar styling */
        .dashboard-sidebar {
            background: white;
            border-right: 1px solid #e5e5e5;
            min-height: calc(100vh - 56px);
            padding: 2rem 0;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .dashboard-content {
            padding: 2rem;
            background: transparent;
        }

        /* Navigation links */
        .dashboard-sidebar .nav-link {
            color: var(--cw-neutral-700);
            padding: 0.875rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 1rem;
            font-family: var(--cw-font-primary);
            font-weight: 500;
            border: 1px solid transparent;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .dashboard-sidebar .nav-link:hover {
            background: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
            border-color: var(--cw-brand-light);
            transform: translateX(4px);
        }

        .dashboard-sidebar .nav-link.active {
            background: var(--cw-gradient-card);
            color: var(--cw-brand-primary);
            border-color: var(--cw-brand-primary);
            font-weight: 600;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-sidebar .nav-link i {
            margin-right: 0.75rem;
            width: 1.2rem;
            text-align: center;
        }

        /* Brand text */
        .brand-text {
            color: var(--cw-brand-primary);
            font-family: var(--cw-font-heading);
            font-weight: 700;
        }

        /* Typography */
        .dashboard-wrapper h1, .dashboard-wrapper h2, .dashboard-wrapper h3,
        .dashboard-wrapper h4, .dashboard-wrapper h5, .dashboard-wrapper h6 {
            font-family: var(--cw-font-heading);
            font-weight: 600;
            color: var(--cw-brand-primary);
            margin-bottom: 1rem;
        }

        .dashboard-wrapper p, .dashboard-wrapper span, .dashboard-wrapper div {
            font-family: var(--cw-font-primary);
            color: var(--cw-neutral-800);
        }

        /* Cards */
        .dashboard-wrapper .card {
            background: white;
            border: 1px solid var(--cw-brand-accent);
            border-radius: 1rem;
            box-shadow: var(--cw-shadow-md);
            transition: all 0.3s ease;
        }

        .dashboard-wrapper .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--cw-shadow-lg);
        }

        .dashboard-wrapper .card-header {
            background: var(--cw-gradient-card-subtle);
            border-bottom: 1px solid var(--cw-brand-accent);
            padding: 1.5rem 2rem;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .dashboard-wrapper .card-body {
            padding: 2rem;
        }

        /* Buttons */
        .dashboard-wrapper .btn {
            font-family: var(--cw-font-primary);
            font-weight: 600;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
        }

        .dashboard-wrapper .btn-primary {
            background: var(--cw-gradient-brand-button);
            color: white;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-wrapper .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: white;
        }

        .dashboard-wrapper .btn-outline-primary {
            background: white;
            color: var(--cw-brand-primary);
            border: 2px solid var(--cw-brand-primary);
        }

        .dashboard-wrapper .btn-outline-primary:hover {
            background: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
            border-color: var(--cw-brand-primary);
        }

        .dashboard-wrapper .btn-outline-secondary {
            background: white;
            color: var(--cw-neutral-700);
            border: 2px solid var(--cw-neutral-200);
        }

        .dashboard-wrapper .btn-outline-secondary:hover {
            background: var(--cw-neutral-100);
            color: var(--cw-neutral-800);
            border-color: var(--cw-neutral-300);
        }

        .dashboard-wrapper .btn-success {
            background: #059669;
            color: white;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-wrapper .btn-success:hover {
            background: #047857;
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: white;
        }

        .dashboard-wrapper .btn-danger {
            background: #dc2626;
            color: white;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-wrapper .btn-danger:hover {
            background: #b91c1c;
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: white;
        }

        .dashboard-wrapper .btn-warning {
            background: #d97706;
            color: white;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-wrapper .btn-warning:hover {
            background: #b45309;
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: white;
        }

        .dashboard-wrapper .btn-info {
            background: #0284c7;
            color: white;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-wrapper .btn-info:hover {
            background: #0369a1;
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: white;
        }

        /* Small buttons */
        .dashboard-wrapper .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Tables */
        .dashboard-wrapper .table {
            color: var(--cw-neutral-800);
            font-family: var(--cw-font-primary);
        }

        .dashboard-wrapper .table th {
            font-family: var(--cw-font-heading);
            font-weight: 600;
            color: var(--cw-brand-primary);
            border-bottom: 2px solid var(--cw-brand-accent);
            padding: 1rem 0.75rem;
        }

        .dashboard-wrapper .table td {
            border-bottom: 1px solid var(--cw-neutral-200);
            padding: 0.875rem 0.75rem;
            vertical-align: middle;
        }

        /* Form Controls */
        .dashboard-wrapper .form-control {
            border: 2px solid var(--cw-brand-accent);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-family: var(--cw-font-primary);
            transition: all 0.2s ease;
        }

        .dashboard-wrapper .form-control:focus {
            border-color: var(--cw-brand-primary);
            box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        }

        /* Badges */
        .dashboard-wrapper .badge {
            font-family: var(--cw-font-primary);
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
        }

        /* Alerts */
        .dashboard-wrapper .alert {
            border: none;
            border-radius: 0.75rem;
            font-family: var(--cw-font-primary);
        }

        .dashboard-wrapper .alert-success {
            background: rgba(5, 150, 105, 0.1);
            color: #047857;
            border-left: 4px solid #059669;
        }

        .dashboard-wrapper .alert-warning {
            background: rgba(217, 119, 6, 0.1);
            color: #b45309;
            border-left: 4px solid #d97706;
        }

        .dashboard-wrapper .alert-danger {
            background: rgba(220, 38, 38, 0.1);
            color: #b91c1c;
            border-left: 4px solid #dc2626;
        }

        .dashboard-wrapper .alert-info {
            background: rgba(2, 132, 199, 0.1);
            color: #0369a1;
            border-left: 4px solid #0284c7;
        }

        /* Breadcrumbs */
        .dashboard-wrapper .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 2rem;
        }

        .dashboard-wrapper .breadcrumb-item {
            font-family: var(--cw-font-primary);
            color: var(--cw-neutral-600);
        }

        .dashboard-wrapper .breadcrumb-item.active {
            color: var(--cw-brand-primary);
            font-weight: 600;
        }

        .dashboard-wrapper .breadcrumb-item + .breadcrumb-item::before {
            color: var(--cw-neutral-400);
        }

        /* Dashboard Header */
        .dashboard-header {
            background: white;
            border-bottom: 1px solid var(--cw-brand-accent);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            border-radius: 1rem;
            box-shadow: var(--cw-shadow-sm);
        }

        .dashboard-header h1 {
            margin-bottom: 0;
            font-family: var(--cw-font-heading);
            font-weight: 700;
            color: var(--cw-brand-primary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard-sidebar {
                padding: 1rem 0;
            }
            
            .dashboard-content {
                padding: 1rem;
            }
            
            .dashboard-sidebar .nav-link {
                margin: 0.25rem 0.5rem;
                padding: 0.75rem 1rem;
            }
            
            .dashboard-wrapper .card-body {
                padding: 1.5rem;
            }
        }

        /* Loading States */
        .skeleton {
            background: linear-gradient(90deg, var(--cw-neutral-100) 25%, var(--cw-brand-accent) 50%, var(--cw-neutral-100) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
    {% block dashboard_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="dashboard-wrapper">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block dashboard-sidebar">
                <div class="position-sticky pt-4">
                    <div class="text-center mb-4">
                        <h4 class="brand-text mb-0">CozyWish</h4>
                        <p class="text-muted small">Dashboard</p>
                    </div>
                    <ul class="nav flex-column px-3">
                        {% block sidebar_content %}
                        <!-- Sidebar content will be provided by child templates -->
                        {% endblock %}
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9 col-lg-10 dashboard-content">
                <!-- Mobile sidebar toggle -->
                <div class="d-md-none mb-3">
                    <button class="btn btn-outline-secondary sidebar-toggle" type="button">
                        <i class="fas fa-bars"></i> Menu
                    </button>
                </div>

                {% if messages %}
                <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
                    {% for message in messages %}
                    <div class="toast mb-2" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">{{ message }}</div>
                            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-4">
                    <h1 class="h2 brand-text">{% block dashboard_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block dashboard_actions %}
                        <!-- Dashboard actions will be provided by child templates -->
                        {% endblock %}
                    </div>
                </div>

                {% block dashboard_content %}
                <!-- Dashboard content will be provided by child templates -->
                {% endblock %}
            </div>
        </div>
    </div>
    <a href="#" id="back-to-top" class="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Common dashboard JavaScript functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                document.querySelector('.dashboard-sidebar').classList.toggle('show');
            });
        }


        // Back to top button
        const backBtn = document.getElementById('back-to-top');
        if (backBtn) {
            window.addEventListener('scroll', function(){
                if(window.scrollY > 200){
                    backBtn.classList.add('show');
                } else {
                    backBtn.classList.remove('show');
                }
            });
            backBtn.addEventListener('click', function(e){
                e.preventDefault();
                window.scrollTo({top:0, behavior:'smooth'});
            });
        }
    });
</script>
{% block dashboard_js %}
<!-- Dashboard-specific JavaScript will be provided by child templates -->
{% endblock %}
{% endblock %}
