{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Provider Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* CozyWish Provider Dashboard - Clean Professional Design */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Clean Dashboard Layout - Remove busy background */
    .dashboard-content {
        background: white !important;
        min-height: 100vh;
        padding: 2rem;
    }

    /* Welcome Header - Clean and Simple */
    .welcome-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid var(--cw-neutral-200);
        box-shadow: var(--cw-shadow-sm);
    }

    .welcome-title {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .welcome-date {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 0;
    }

    /* Simplified Stats Cards */
    .stats-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.2s ease;
        height: 100%;
    }

    .stats-card:hover {
        box-shadow: var(--cw-shadow-md);
        transform: translateY(-2px);
    }

    .stats-card.featured {
        border-color: var(--cw-brand-primary);
        border-width: 2px;
    }

    .stats-number {
        font-family: var(--cw-font-heading);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Content Cards - Clean and Simple */
    .content-card {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: var(--cw-shadow-sm);
    }

    .content-card-header {
        background: var(--cw-neutral-50);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    .content-card-title {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    .content-card-body {
        padding: 1.5rem;
    }

    /* Clean Action Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Booking Items - Clean List Style */
    .booking-item {
        padding: 1rem 0;
        border-bottom: 1px solid var(--cw-neutral-100);
    }

    .booking-item:last-child {
        border-bottom: none;
    }

    .booking-customer {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
    }

    .booking-service {
        color: var(--cw-neutral-700);
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .booking-time {
        color: var(--cw-neutral-600);
        font-size: 0.85rem;
    }

    .booking-status {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .booking-status.confirmed {
        background: #dcfce7;
        color: #166534;
    }

    .booking-status.pending {
        background: #fef3c7;
        color: #92400e;
    }

    /* Quick Actions - Simplified */
    .quick-action-btn {
        width: 100%;
        margin-bottom: 0.75rem;
        justify-content: flex-start;
        text-align: left;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--cw-neutral-600);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: var(--cw-neutral-400);
        margin-bottom: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .welcome-header {
            padding: 1.5rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 1.5rem;
        }

        .stats-card {
            padding: 1.25rem;
        }

        .stats-number {
            font-size: 2rem;
        }

        .content-card-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    {% if not venue %}
    <a href="{% url 'venues_app:venue_create' %}" class="btn btn-cw-primary" data-bs-toggle="tooltip" title="Add Venue">
        <i class="fas fa-plus me-2"></i>Add Venue
    </a>
    {% endif %}
    {% if venue %}
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-cw-secondary" data-bs-toggle="tooltip" title="Manage Services">
        <i class="fas fa-spa me-2"></i>Manage Services
    </a>
    {% endif %}
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-tachometer-alt me-2"></i>
            Provider Dashboard
        </li>
    </ol>
</nav>

<!-- Welcome Header -->
<div class="welcome-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="welcome-title">
                Welcome back, {{ provider_profile.business_name|default:"Service Provider" }}!
            </h1>
            <p class="welcome-date">
                <i class="fas fa-calendar me-1"></i>
                {{ today|date:"l, F d, Y" }}
            </p>
        </div>
        {% if venue %}
        <div>
            <a href="{% url 'venues_app:venue_manage' %}" class="btn-cw-primary">
                <i class="fas fa-cog me-2"></i>Manage Venue
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Stats Cards Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card featured">
            <div class="stats-number">{{ todays_bookings_count|default:0 }}</div>
            <div class="stats-label">Today's Bookings</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ total_bookings|default:0 }}</div>
            <div class="stats-label">Total Bookings</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">${{ monthly_earnings|floatformat:0|default:0 }}</div>
            <div class="stats-label">Monthly Earnings</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ pending_bookings|default:0 }}</div>
            <div class="stats-label">Pending Bookings</div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Today's Bookings -->
    <div class="col-lg-8 mb-4">
        <div class="content-card">
            <div class="content-card-header d-flex justify-content-between align-items-center">
                <h5 class="content-card-title">
                    <i class="fas fa-calendar-day me-2"></i>
                    Today's Bookings
                </h5>
                <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-sm btn-cw-secondary">
                    <i class="fas fa-list me-1"></i>View All
                </a>
            </div>
            <div class="content-card-body">
                {% if todays_bookings %}
                    {% for booking in todays_bookings %}
                    <div class="booking-item">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="booking-customer">{{ booking.customer.get_full_name|default:booking.customer.email }}</div>
                                <div class="booking-service">
                                    {% for item in booking.items.all|slice:":1" %}
                                        {{ item.service_title }}
                                    {% endfor %}
                                </div>
                                <div class="booking-time">
                                    <i class="fas fa-clock me-1"></i>
                                    {% for item in booking.items.all|slice:":1" %}
                                        {{ item.scheduled_time|time:"g:i A" }}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="fw-bold text-success mb-2">${{ booking.total_price }}</div>
                                <span class="booking-status {{ booking.status }}">
                                    {{ booking.get_status_display }}
                                </span>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="d-flex gap-1 justify-content-end">
                                    <a href="{% url 'booking_cart_app:booking_detail' booking.id %}"
                                       class="btn btn-sm btn-cw-secondary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if booking.status == 'pending' %}
                                    <button class="btn btn-sm btn-success"
                                            onclick="confirmBooking({{ booking.id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <h5>No bookings for today</h5>
                        <p>Your schedule is clear for today.</p>
                        <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-cw-secondary">
                            <i class="fas fa-calendar me-1"></i>View All Bookings
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="content-card mb-4">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="content-card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn-cw-primary quick-action-btn">
                        <i class="fas fa-calendar-day me-2"></i>Today's Schedule
                    </a>
                    <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="btn-cw-secondary quick-action-btn">
                        <i class="fas fa-chart-line me-2"></i>Earnings Report
                    </a>
                    {% if venue %}
                    <a href="{% url 'venues_app:venue_manage' %}" class="btn-cw-secondary quick-action-btn">
                        <i class="fas fa-cog me-2"></i>Venue Settings
                    </a>
                    {% endif %}
                    <a href="{% url 'dashboard_app:provider_team_management' %}" class="btn-cw-secondary quick-action-btn">
                        <i class="fas fa-users me-2"></i>Team Management
                    </a>
                </div>
            </div>
        </div>

        <!-- Performance Summary -->
        <div class="content-card">
            <div class="content-card-header">
                <h5 class="content-card-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    Performance Summary
                </h5>
            </div>
            <div class="content-card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="stats-number text-success">{{ confirmed_bookings|default:0 }}</div>
                        <div class="stats-label">Confirmed</div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stats-number text-warning">{{ pending_bookings|default:0 }}</div>
                        <div class="stats-label">Pending</div>
                    </div>
                    <div class="col-6">
                        <div class="stats-number text-info">{{ completed_bookings|default:0 }}</div>
                        <div class="stats-label">Completed</div>
                    </div>
                    <div class="col-6">
                        <div class="stats-number" style="color: var(--cw-brand-primary);">${{ total_earnings|floatformat:0|default:0 }}</div>
                        <div class="stats-label">Total Earned</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block dashboard_extra_js %}
<script>
// Enhanced Provider Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeProviderDashboard();
});

function initializeProviderDashboard() {
    // Add loading states to action buttons
    setupActionButtons();
    
    // Initialize any real-time updates
    setupRealTimeUpdates();
}

function confirmBooking(bookingId) {
    if (confirm('Are you sure you want to confirm this booking?')) {
        // Add loading state
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<span class="spinner"></span>';
        btn.disabled = true;
        
        // TODO: Implement booking confirmation
        fetch(`/booking_cart_app/booking/${bookingId}/confirm/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Booking confirmed successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || 'Error confirming booking', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Network error occurred', 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHTML;
            btn.disabled = false;
        });
    }
}

function setupActionButtons() {
    document.querySelectorAll('.btn-cw-primary, .btn-cw-secondary').forEach(function(btn) {
        if (btn.tagName === 'BUTTON') {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    this.classList.add('loading');
                }
            });
        }
    });
}

function setupRealTimeUpdates() {
    // TODO: Implement real-time updates for bookings
    // This could use WebSockets or periodic AJAX calls
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
