{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Provider Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* CozyWish Provider Dashboard - Enhanced Design System */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
    }

    /* Dashboard Content Background */
    .dashboard-content {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem;
    }

    /* Typography */
    .dashboard-content h1, .dashboard-content h2, .dashboard-content h3,
    .dashboard-content h4, .dashboard-content h5, .dashboard-content h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .dashboard-content p, .dashboard-content span, .dashboard-content div {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-800);
    }

    /* Enhanced Breadcrumb */
    .breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-item.active {
        color: var(--cw-neutral-600);
    }

    /* Enhanced Stats Cards */
    .stats-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
        min-height: 120px;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--cw-gradient-brand-button);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--cw-shadow-lg);
    }

    .stats-card .card-body {
        padding: 1.5rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .stats-card.featured {
        background: var(--cw-gradient-card);
        border: 2px solid var(--cw-brand-primary);
    }

    .stats-card.accent {
        background: var(--cw-gradient-accent);
        border: 2px solid var(--cw-brand-light);
    }

    .stats-card .card-title {
        font-family: var(--cw-font-heading);
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--cw-neutral-600);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .stats-card .stats-value {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
        line-height: 1.1;
    }

    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        box-shadow: var(--cw-shadow-sm);
        margin-bottom: 1rem;
    }

    .stats-card .stats-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
    }

    .stats-card .stats-text {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* Dashboard Cards */
    .dashboard-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .dashboard-card .card-header {
        background: var(--cw-gradient-card-subtle);
        border-bottom: 1px solid var(--cw-brand-accent);
        padding: 1.5rem 2rem;
    }

    .dashboard-card .card-header h5 {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    .dashboard-card .card-body {
        padding: 2rem;
    }

    /* Enhanced Action Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        text-decoration: none;
    }

    /* Booking Cards */
    .booking-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .booking-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--cw-gradient-brand-button);
    }

    .booking-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .booking-card-body {
        padding: 1rem 1rem 1rem 1.5rem;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .dashboard-content {
            padding: 1rem;
        }
        
        .stats-card {
            margin-bottom: 1rem;
            min-height: 100px;
        }
        
        .stats-card .card-body {
            padding: 1rem;
        }
        
        .stats-card .stats-value {
            font-size: 1.5rem;
        }
        
        .stats-card .stats-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        border: 2px solid var(--cw-brand-accent);
        border-top: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
        display: inline-block;
        margin-right: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    {% if not venue %}
    <a href="{% url 'venues_app:venue_create' %}" class="btn btn-cw-primary" data-bs-toggle="tooltip" title="Add Venue">
        <i class="fas fa-plus me-2"></i>Add Venue
    </a>
    {% endif %}
    {% if venue %}
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-cw-secondary" data-bs-toggle="tooltip" title="Manage Services">
        <i class="fas fa-spa me-2"></i>Manage Services
    </a>
    {% endif %}
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-tachometer-alt me-2"></i>
            Provider Dashboard
        </li>
    </ol>
</nav>

<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-2" style="font-family: var(--cw-font-heading); color: var(--cw-brand-primary);">
                    Welcome back, {{ provider_profile.business_name|default:"Service Provider" }}!
                </h1>
                <p class="text-muted mb-0">
                    <i class="fas fa-calendar me-1"></i>
                    {{ today|date:"l, F d, Y" }}
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'venues_app:venue_manage' %}" class="btn-cw-primary">
                    <i class="fas fa-cog"></i>
                    Manage Venue
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card featured">
            <div class="card-body">
                <div class="stats-content">
                    <div class="stats-text">
                        <div class="card-title">Today's Bookings</div>
                        <div class="stats-value">{{ todays_bookings_count|default:0 }}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="stats-content">
                    <div class="stats-text">
                        <div class="card-title">Total Bookings</div>
                        <div class="stats-value">{{ total_bookings|default:0 }}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card accent">
            <div class="card-body">
                <div class="stats-content">
                    <div class="stats-text">
                        <div class="card-title">Monthly Earnings</div>
                        <div class="stats-value">${{ monthly_earnings|floatformat:0|default:0 }}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="stats-content">
                    <div class="stats-text">
                        <div class="card-title">Pending Bookings</div>
                        <div class="stats-value">{{ pending_bookings|default:0 }}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Today's Bookings -->
    <div class="col-lg-8 mb-4">
        <div class="dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2 text-primary"></i>
                    Today's Bookings
                </h5>
                <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i>View All
                </a>
            </div>
            <div class="card-body">
                {% if todays_bookings %}
                    {% for booking in todays_bookings %}
                    <div class="booking-card">
                        <div class="booking-card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1 fw-bold">{{ booking.customer.get_full_name|default:booking.customer.email }}</h6>
                                    <div class="text-muted mb-2">
                                        <small>
                                            <i class="fas fa-clock me-1"></i>
                                            {% for item in booking.items.all|slice:":1" %}
                                                {{ item.scheduled_time|time:"g:i A" }}
                                            {% endfor %}
                                            <i class="fas fa-spa ms-2 me-1"></i>
                                            {% for item in booking.items.all|slice:":1" %}
                                                {{ item.service_title }}
                                            {% endfor %}
                                        </small>
                                    </div>
                                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        <i class="fas fa-{% if booking.status == 'confirmed' %}check-circle{% elif booking.status == 'pending' %}clock{% else %}question-circle{% endif %} me-1"></i>
                                        {{ booking.get_status_display }}
                                    </span>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="fw-bold text-success h6">${{ booking.total_price }}</div>
                                    {% if booking.customer_profile.phone_number %}
                                    <a href="tel:{{ booking.customer_profile.phone_number }}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-phone me-1"></i>Call
                                    </a>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex gap-1">
                                        <a href="{% url 'booking_cart_app:booking_detail' booking.id %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if booking.status == 'pending' %}
                                        <button class="btn btn-sm btn-success" 
                                                onclick="confirmBooking({{ booking.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-calendar-times fa-3x text-muted"></i>
                    </div>
                    <h6 class="text-muted">No bookings scheduled for today</h6>
                    <p class="text-muted mb-3">Check your upcoming bookings or manage your venue settings</p>
                    <a href="{% url 'venues_app:venue_manage' %}" class="btn-cw-primary">
                        <i class="fas fa-cog me-2"></i>Manage Venue
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="dashboard-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn-cw-primary">
                        <i class="fas fa-calendar-day me-2"></i>Today's Schedule
                    </a>
                    <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="btn-cw-secondary">
                        <i class="fas fa-chart-line me-2"></i>Earnings Report
                    </a>
                    <a href="{% url 'venues_app:venue_manage' %}" class="btn-cw-secondary">
                        <i class="fas fa-cog me-2"></i>Venue Settings
                    </a>
                    <a href="{% url 'dashboard_app:provider_team_management' %}" class="btn-cw-secondary">
                        <i class="fas fa-users me-2"></i>Team Management
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Performance -->
        <div class="dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2 text-info"></i>
                    Performance Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-success">{{ confirmed_bookings|default:0 }}</h4>
                            <small class="text-muted">Confirmed</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning">{{ pending_bookings|default:0 }}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info">{{ completed_bookings|default:0 }}</h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-primary">{{ total_earnings|floatformat:0|default:0 }}</h4>
                        <small class="text-muted">Total Earned</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block dashboard_extra_js %}
<script>
// Enhanced Provider Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeProviderDashboard();
});

function initializeProviderDashboard() {
    // Add loading states to action buttons
    setupActionButtons();
    
    // Initialize any real-time updates
    setupRealTimeUpdates();
}

function confirmBooking(bookingId) {
    if (confirm('Are you sure you want to confirm this booking?')) {
        // Add loading state
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<span class="spinner"></span>';
        btn.disabled = true;
        
        // TODO: Implement booking confirmation
        fetch(`/booking_cart_app/booking/${bookingId}/confirm/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Booking confirmed successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || 'Error confirming booking', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Network error occurred', 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHTML;
            btn.disabled = false;
        });
    }
}

function setupActionButtons() {
    document.querySelectorAll('.btn-cw-primary, .btn-cw-secondary').forEach(function(btn) {
        if (btn.tagName === 'BUTTON') {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    this.classList.add('loading');
                }
            });
        }
    });
}

function setupRealTimeUpdates() {
    // TODO: Implement real-time updates for bookings
    // This could use WebSockets or periodic AJAX calls
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
