<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your CozyWish Password</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* CozyWish Email Design System */
        :root {
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;
            --cw-neutral-600: #525252;
            --cw-neutral-700: #404040;
            --cw-neutral-800: #262626;
        }

        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Email styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #262626;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
            padding: 40px 30px;
            text-align: center;
            border-bottom: 2px solid #f1d4c4;
        }

        .email-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(47, 22, 15, 0.2);
        }

        .email-title {
            font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
            font-size: 28px;
            font-weight: 700;
            color: #2F160F;
            margin: 0 0 10px 0;
            line-height: 1.2;
        }

        .email-subtitle {
            font-size: 16px;
            color: #525252;
            margin: 0;
            line-height: 1.5;
        }

        .email-body {
            padding: 40px 30px;
        }

        .email-content {
            font-size: 16px;
            line-height: 1.6;
            color: #404040;
            margin-bottom: 30px;
        }

        .email-content p {
            margin: 0 0 20px 0;
        }

        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(47, 22, 15, 0.2);
            margin: 20px 0;
        }

        .reset-button:hover {
            background: linear-gradient(135deg, #4a2a1f 0%, #2F160F 100%);
        }

        .reset-link {
            background: #fef7f0;
            border: 1px solid #fae1d7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #2F160F;
        }

        .email-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .email-info strong {
            color: #0c4a6e;
            font-weight: 600;
        }

        .email-footer {
            background: #fef7f0;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #fae1d7;
        }

        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #525252;
        }

        .email-footer .brand-name {
            font-weight: 700;
            color: #2F160F;
            font-size: 16px;
        }

        .security-note {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .security-note p {
            margin: 0;
            color: #92400e;
            font-weight: 500;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px !important;
            }

            .email-title {
                font-size: 24px !important;
            }

            .email-logo {
                width: 60px !important;
                height: 60px !important;
                font-size: 1.5rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                🔑
            </div>
            <h1 class="email-title">Reset Your Password</h1>
            <p class="email-subtitle">CozyWish Account Security</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div class="email-content">
                <p><strong>Hello,</strong></p>

                <p>You're receiving this email because you requested a password reset for your customer account at <strong>CozyWish</strong>.</p>

                <p>To reset your password, click the button below:</p>

                <div style="text-align: center; margin: 30px 0;">
                    {% block reset_link %}
                    <a href="{{ protocol }}://{{ domain }}{% url 'accounts_app:customer_password_reset_confirm' uidb64=uid token=token %}" class="reset-button">
                        Reset My Password
                    </a>
                    {% endblock %}
                </div>

                <p>Or copy and paste this link into your browser:</p>

                <div class="reset-link">
                    {{ protocol }}://{{ domain }}{% url 'accounts_app:customer_password_reset_confirm' uidb64=uid token=token %}
                </div>

                <div class="email-info">
                    <p><strong>Account Email:</strong> {{ user.email }}</p>
                </div>

                <div class="security-note">
                    <p><strong>⚠️ Security Notice:</strong> This reset link will expire in 24 hours for your protection.</p>
                </div>

                <p>If you didn't request this password reset, please ignore this email. Your password won't be changed.</p>

                <p>If you're having trouble with the button above, you can also reset your password by visiting our website and using the "Forgot Password" option.</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>Thanks for using <span class="brand-name">CozyWish</span>!</p>
            <p>Your trusted spa and wellness marketplace</p>
            <p style="margin-top: 20px; font-size: 12px; color: #525252;">
                This email was sent to {{ user.email }}. If you have any questions, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>
