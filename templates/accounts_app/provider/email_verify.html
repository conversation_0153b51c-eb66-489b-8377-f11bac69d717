{% extends 'base.html' %}

{% block title %}Email Verification - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Email Verification */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Semantic Colors */
        --cw-success: #059669;
        --cw-success-light: #f0fff4;
        --cw-success-border: #bbf7d0;
        --cw-warning: #d97706;
        --cw-warning-light: #fffbeb;
        --cw-warning-border: #fed7aa;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Email Verification Section */
    .email-verify-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .email-verify-container {
        max-width: 700px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .email-verify-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .email-verify-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .email-verify-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="verify-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1.5" fill="%23bbf7d0" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23verify-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .email-verify-header .content {
        position: relative;
        z-index: 2;
    }

    .email-verify-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--cw-success) 0%, #10b981 100%);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .email-verify-icon.warning {
        background: linear-gradient(135deg, var(--cw-warning) 0%, #f59e0b 100%);
    }

    .email-verify-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .email-verify-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .email-verify-body {
        padding: 3rem;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-success {
        background: var(--cw-success-light);
        border-color: var(--cw-success-border);
        color: #166534;
    }

    .alert-cw-warning {
        background: var(--cw-warning-light);
        border-color: var(--cw-warning-border);
        color: #92400e;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        width: 100%;
        margin-bottom: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Tips Card */
    .tips-card {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-top: 2rem;
    }

    .tips-title {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 1.125rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        color: var(--cw-neutral-700);
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        position: relative;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .tips-list li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--cw-success);
        font-weight: bold;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .email-verify-section {
            padding: 3rem 0;
        }

        .email-verify-container {
            max-width: 600px;
            padding: 0 1.5rem;
        }

        .email-verify-header {
            padding: 2rem 2rem 1.5rem;
        }

        .email-verify-title {
            font-size: 2rem;
        }

        .email-verify-subtitle {
            font-size: 1rem;
        }

        .email-verify-body {
            padding: 2rem;
        }

        .tips-card {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .email-verify-container {
            padding: 0 1rem;
        }

        .email-verify-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .email-verify-body {
            padding: 1.5rem;
        }

        .email-verify-title {
            font-size: 1.75rem;
        }

        .email-verify-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .tips-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="email-verify-section">
    <div class="email-verify-container">
        <div class="email-verify-card">
            {% if validlink %}
            <!-- Successful Verification -->
            <div class="email-verify-header">
                <div class="content">
                    <div class="email-verify-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1 class="email-verify-title">Email Verified!</h1>
                    <p class="email-verify-subtitle">Your business account has been successfully verified</p>
                </div>
            </div>

            <div class="email-verify-body">
                <div class="alert-cw alert-cw-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Verification Complete!</strong> Your business account is now active and ready to use.
                </div>

                <div class="text-center mb-4">
                    <h3 style="color: var(--cw-brand-primary); margin-bottom: 1.5rem;">Next Steps:</h3>
                </div>

                <div class="d-grid">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-primary">
                        <i class="fas fa-user"></i>Complete Your Profile
                    </a>
                    <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-secondary">
                        <i class="fas fa-sign-in-alt"></i>Sign In to Your Account
                    </a>
                </div>

                <div class="tips-card">
                    <h3 class="tips-title">
                        <i class="fas fa-lightbulb"></i>Getting Started Tips
                    </h3>
                    <ul class="tips-list">
                        <li>Complete your business profile with detailed information</li>
                        <li>Add your services and pricing to attract customers</li>
                        <li>Upload high-quality photos of your venue</li>
                        <li>Start using CozyWish to manage your bookings</li>
                        <li>Set up your availability and booking preferences</li>
                    </ul>
                </div>
            </div>

            {% else %}
            <!-- Invalid/Expired Link -->
            <div class="email-verify-header">
                <div class="content">
                    <div class="email-verify-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="email-verify-title">Verification Failed</h1>
                    <p class="email-verify-subtitle">This verification link is invalid or has expired</p>
                </div>
            </div>

            <div class="email-verify-body">
                <div class="alert-cw alert-cw-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Verification Link Issue:</strong> The verification link you used is either invalid or has expired.
                </div>

                <div class="text-center mb-4">
                    <h3 style="color: var(--cw-brand-primary); margin-bottom: 1.5rem;">What can you do?</h3>
                </div>

                <div class="d-grid">
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-primary">
                        <i class="fas fa-user-plus"></i>Try Again - Sign Up
                    </a>
                    <a href="{% url 'accounts_app:service_provider_login' %}" class="btn-cw-secondary">
                        <i class="fas fa-sign-in-alt"></i>Already Verified? Sign In
                    </a>
                </div>

                <div class="tips-card">
                    <h3 class="tips-title">
                        <i class="fas fa-question-circle"></i>Need Help?
                    </h3>
                    <p style="color: var(--cw-neutral-700); margin-bottom: 1rem; font-size: 0.95rem;">If you continue to have issues with email verification:</p>
                    <ul class="tips-list">
                        <li>Check your spam/junk folder for the verification email</li>
                        <li>Make sure you're using the latest verification email</li>
                        <li>Contact support if the problem persists</li>
                        <li>Try signing up again with a different email address</li>
                    </ul>
                </div>

                <div class="text-center mt-4 pt-4" style="border-top: 1px solid var(--cw-brand-accent);">
                    <p style="color: var(--cw-neutral-600); margin-bottom: 0;">
                        Need assistance?
                        <a href="mailto:<EMAIL>" style="font-weight: 600;">Contact Support</a>
                    </p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
