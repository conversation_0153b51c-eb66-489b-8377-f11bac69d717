{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ action }} Team Member - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Team Member Form */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Team Member Form Section */
    .team-form-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .team-form-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Header */
    .team-header {
        text-align: center;
        margin-bottom: 3rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .team-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .team-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Info Alert */
    .team-info-alert {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: var(--cw-brand-primary);
        display: flex;
        align-items: center;
        gap: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .team-info-alert i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Form Card */
    .form-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1.5rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .form-card-body {
        padding: 3rem;
    }

    /* Form Styling */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control,
    .form-select {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
        opacity: 0.7;
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Profile Picture Upload Section */
    .profile-picture-upload-section {
        display: flex;
        align-items: flex-start;
        gap: 2rem;
        flex-wrap: wrap;
        margin-bottom: 1.5rem;
    }

    .profile-picture-upload-controls {
        flex: 1;
        min-width: 280px;
    }

    .profile-picture-preview-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;
        padding: 1rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
    }

    .profile-picture-preview-container:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .profile-picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        display: block;
    }

    .profile-picture-preview:hover {
        border-color: var(--cw-brand-primary);
    }

    .profile-picture-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-neutral-600);
        transition: all 0.3s ease;
    }

    .profile-picture-placeholder:hover {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
    }

    .profile-picture-upload-help {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Checkbox Styling */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        background-color: white;
        transition: all 0.2s ease;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        font-family: var(--cw-font-heading);
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-left: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        padding: 2rem 0;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 2rem;
    }

    /* Tips Card */
    .tips-card {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-top: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .tips-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
        color: var(--cw-neutral-700);
        line-height: 1.5;
    }

    .tips-list li:before {
        content: '•';
        color: var(--cw-brand-primary);
        font-weight: bold;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Alert Styling */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .alert-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .alert-info {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .team-form-section {
            padding: 3rem 0;
        }

        .team-form-container {
            padding: 0 1.5rem;
        }

        .team-header {
            padding: 2rem 2rem 1.5rem;
        }

        .team-title {
            font-size: 2rem;
        }

        .form-card-body {
            padding: 2rem;
        }

        .form-actions {
            flex-direction: column-reverse;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .profile-picture-upload-section {
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 1.5rem;
        }

        .profile-picture-upload-controls {
            min-width: auto;
            width: 100%;
            text-align: left;
        }

        .profile-picture-preview-container {
            margin-bottom: 0;
        }
    }

    @media (max-width: 576px) {
        .team-form-container {
            padding: 0 1rem;
        }

        .form-card-body {
            padding: 1.5rem;
        }

        .team-title {
            font-size: 1.75rem;
        }

        .tips-card {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Team Member Form JavaScript
 * Handles photo preview and form validation
 */
document.addEventListener('DOMContentLoaded', function() {
    // Photo preview functionality
    const photoInput = document.getElementById('{{ form.photo.id_for_label }}');
    if (photoInput) {
        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/webp'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, or WebP).');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-picture-preview');
                    const container = preview.parentElement;
                    
                    // Remove existing content
                    preview.remove();
                    
                    // Create new image element
                    const img = document.createElement('img');
                    img.id = 'profile-picture-preview';
                    img.src = e.target.result;
                    img.alt = 'Team Member Photo';
                    img.className = 'profile-picture-preview';
                    
                    // Add to container
                    container.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    }
});
</script>
{% endblock %}

{% block content %}
<section class="team-form-section">
    <div class="team-form-container">
        <!-- Header -->
        <div class="team-header">
            <h1 class="team-title">{{ action }} Team Member</h1>
            <p class="team-subtitle">
                {% if action == 'Add' %}
                    Add a new team member to your business
                {% else %}
                    Update team member information
                {% endif %}
            </p>
        </div>

        <!-- Team limit info for add -->
        {% if action == 'Add' %}
            <div class="team-info-alert">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Team Limit:</strong> You can add up to {{ max_members }} team members.
                    Currently you have {{ current_count }} member{{ current_count|pluralize }}.
                </div>
            </div>
        {% endif %}

        <!-- Display messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Form Card -->
        <div class="form-card">
            <div class="form-card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Team Member Name -->
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                        {{ form.name|add_class:"form-control"|attr:"placeholder:Enter team member name" }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        {% if form.name.help_text %}
                            <div class="form-text">{{ form.name.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Team Member Position -->
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.position.id_for_label }}">{{ form.position.label }}</label>
                        {{ form.position|add_class:"form-control"|attr:"placeholder:e.g., Licensed Massage Therapist" }}
                        {% if form.position.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.position.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        {% if form.position.help_text %}
                            <div class="form-text">{{ form.position.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Profile Picture Section -->
                    <div class="mb-4">
                        <h5 class="form-label">
                            <i class="fas fa-camera"></i> {{ form.photo.label }}
                        </h5>
                        <div class="profile-picture-upload-section">
                            <div>
                                <div class="profile-picture-preview-container">
                                    {% if action == 'Edit' and team_member.photo %}
                                        <img id="profile-picture-preview" src="{{ team_member.photo.url }}" alt="{{ team_member.name }}" class="profile-picture-preview">
                                    {% else %}
                                        <div id="profile-picture-preview" class="profile-picture-placeholder">
                                            <i class="fas fa-user fa-3x"></i>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="profile-picture-upload-controls">
                                <label class="form-label" for="{{ form.photo.id_for_label }}">Choose Profile Picture</label>
                                {{ form.photo|add_class:"form-control" }}
                                {% if form.photo.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.photo.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="profile-picture-upload-help">
                                    <div class="mb-2">
                                        <strong><i class="fas fa-info-circle me-1"></i>Upload Guidelines:</strong>
                                    </div>
                                    <ul class="mb-0 ps-3">
                                        <li>Recommended size: 400x400 pixels or larger</li>
                                        <li>Accepted formats: JPG, PNG, WebP</li>
                                        <li>Maximum file size: 5MB</li>
                                        <li>Square images work best for circular display</li>
                                    </ul>
                                </div>
                                {% if form.photo.help_text %}
                                    <div class="form-text mt-2">{{ form.photo.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Active Status -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_active|add_class:"form-check-input" }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                            {% if form.is_active.help_text %}
                                <div class="form-text">{{ form.is_active.help_text }}</div>
                            {% endif %}
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-actions">
                        <a href="{% url 'accounts_app:team_member_list' %}" class="btn-cw-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-{% if action == 'Add' %}plus{% else %}save{% endif %}"></i>
                            {% if action == 'Add' %}Add Team Member{% else %}Save Changes{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tips for team management -->
        <div class="tips-card">
            <h6 class="tips-title">
                <i class="fas fa-lightbulb"></i>
                Tips for Team Management
            </h6>
            <ul class="tips-list">
                <li>Use clear, professional photos for better customer trust</li>
                <li>Include specific titles (e.g., "Licensed Massage Therapist" vs "Therapist")</li>
                <li>You can temporarily deactivate team members without removing them</li>
                <li>Team member information helps customers choose the right service provider</li>
            </ul>
        </div>
    </div>
</section>
{% endblock %}
