{% extends 'accounts_app/base_account.html' %}
{% load static %}

{% block title %}Team Members - {{ profile.business_name }}{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Design System - Team Management */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Status Colors */
        --cw-success: #059669;
        --cw-warning: #d97706;
        --cw-error: #dc2626;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-hero: linear-gradient(135deg, var(--cw-accent-light) 0%, #ffffff 100%);
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Team Management Container */
    .team-management-container {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .team-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        gap: 1rem;
    }

    .team-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin: 0;
    }

    .team-subtitle {
        color: var(--cw-neutral-600);
        margin: 0;
        font-size: 1.1rem;
    }

    /* Team Stats Card */
    .team-stats-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    .team-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        display: block;
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: var(--cw-brand-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.4rem 0.8rem;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
        text-decoration: none;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    .btn-cw-danger {
        background: var(--cw-error);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-cw-danger:hover {
        background: #b91c1c;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Team Member Cards */
    .team-member-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .team-member-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .team-member-content {
        padding: 2rem;
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .team-member-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--cw-brand-accent);
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-avatar {
        border-color: var(--cw-brand-primary);
        transform: scale(1.05);
    }

    .team-member-placeholder {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--cw-accent-light);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid var(--cw-brand-accent);
        color: var(--cw-neutral-600);
        font-size: 1.8rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-placeholder {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .team-member-info {
        flex-grow: 1;
    }

    .team-member-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.25rem 0;
    }

    .team-member-position {
        color: var(--cw-neutral-600);
        margin: 0 0 0.5rem 0;
        font-weight: 500;
    }

    .team-member-status {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-active {
        background: rgba(5, 150, 105, 0.1);
        color: var(--cw-success);
        border: 1px solid rgba(5, 150, 105, 0.2);
    }

    .status-inactive {
        background: rgba(107, 114, 128, 0.1);
        color: var(--cw-neutral-600);
        border: 1px solid rgba(107, 114, 128, 0.2);
    }

    .team-member-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border: 2px dashed var(--cw-brand-accent);
        border-radius: 1rem;
        margin-bottom: 2rem;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: var(--cw-brand-accent);
        margin-bottom: 1rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .empty-state-text {
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .team-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .team-title {
            font-size: 1.75rem;
        }

        .team-stats {
            justify-content: center;
        }

        .team-member-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .team-member-actions {
            justify-content: center;
        }

        .stat-item {
            min-width: 120px;
        }
    }

    @media (max-width: 576px) {
        .team-management-container {
            padding: 1rem 0;
        }

        .team-stats-card {
            padding: 1.5rem;
        }

        .team-member-content {
            padding: 1rem;
        }

        .team-member-actions {
            flex-direction: column;
            width: 100%;
        }

        .btn-cw-secondary,
        .btn-cw-danger {
            justify-content: center;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="team-management-container">
    <div class="container">
        <!-- Header -->
        <div class="team-header">
            <div>
                <h1 class="team-title">Team Management</h1>
                <p class="team-subtitle">Manage your team members and their information</p>
            </div>
        </div>

        <!-- Team Statistics -->
        <div class="team-stats-card">
            <div class="team-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ team_count }}</span>
                    <span class="stat-label">Total Members</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ max_team_members }}</span>
                    <span class="stat-label">Maximum Allowed</span>
                </div>
                <div class="stat-item">
                    {% if can_add_member %}
                        <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                            <i class="fas fa-plus"></i>
                            Add Team Member
                        </a>
                    {% else %}
                        <span class="btn-cw-primary" style="opacity: 0.5; cursor: not-allowed;">
                            <i class="fas fa-plus"></i>
                            Limit Reached
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Team Members List -->
        {% if team_members %}
            <div class="row">
                {% for member in team_members %}
                <div class="col-lg-6">
                    <div class="team-member-card">
                        <div class="team-member-content">
                            <!-- Member Avatar -->
                            <div>
                                {% if member.photo %}
                                    <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="team-member-avatar">
                                {% else %}
                                    <div class="team-member-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Member Info -->
                            <div class="team-member-info">
                                <h3 class="team-member-name">{{ member.name }}</h3>
                                <p class="team-member-position">{{ member.position }}</p>
                                <div class="mb-2">
                                    <span class="team-member-status {% if member.is_active %}status-active{% else %}status-inactive{% endif %}">
                                        <i class="fas fa-circle" style="font-size: 0.5rem;"></i>
                                        {% if member.is_active %}Active{% else %}Inactive{% endif %}
                                    </span>
                                </div>

                                <!-- Actions -->
                                <div class="team-member-actions">
                                    <a href="{% url 'accounts_app:team_member_edit' member_id=member.id %}" class="btn-cw-secondary">
                                        <i class="fas fa-edit"></i>
                                        Edit
                                    </a>
                                    <form method="post" action="{% url 'accounts_app:team_member_toggle_status' member_id=member.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="btn-cw-secondary">
                                            <i class="fas fa-{% if member.is_active %}pause{% else %}play{% endif %}"></i>
                                            {% if member.is_active %}Deactivate{% else %}Activate{% endif %}
                                        </button>
                                    </form>
                                    <form method="post" action="{% url 'accounts_app:team_member_delete' member_id=member.id %}" style="display: inline;" onsubmit="return confirm('Are you sure you want to remove {{ member.name }} from your team? This action cannot be undone.');">
                                        {% csrf_token %}
                                        <button type="submit" class="btn-cw-danger">
                                            <i class="fas fa-trash"></i>
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="empty-state-title">No Team Members Yet</h2>
                <p class="empty-state-text">Start building your team by adding your first team member. You can add up to {{ max_team_members }} members.</p>
                {% if can_add_member %}
                    <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                        <i class="fas fa-plus"></i>
                        Add Your First Team Member
                    </a>
                {% endif %}
            </div>
        {% endif %}

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn-cw-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %} 