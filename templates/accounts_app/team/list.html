{% extends 'accounts_app/base_account.html' %}
{% load static %}

{% block title %}Team Members - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Team Management */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Status Colors */
        --cw-success: #059669;
        --cw-warning: #d97706;
        --cw-error: #dc2626;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-hero: linear-gradient(135deg, var(--cw-accent-light) 0%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Team Management Container */
    .team-management-container {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .team-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .team-header-content h1 {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        line-height: 1.2;
    }

    .team-subtitle {
        color: var(--cw-neutral-600);
        margin: 0;
        font-size: 1.1rem;
        font-weight: 500;
    }

    /* Team Stats Card */
    .team-stats-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    .team-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
        min-width: 120px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        display: block;
        font-family: var(--cw-font-heading);
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        font-weight: 500;
        margin-top: 0.25rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.75rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-family: var(--cw-font-heading);
        font-size: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: var(--cw-brand-accent);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
        text-decoration: none;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    .btn-cw-danger {
        background: var(--cw-error);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-cw-danger:hover {
        background: #b91c1c;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Team Member Cards */
    .team-member-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .team-member-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .team-member-content {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .team-member-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--cw-brand-accent);
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-avatar {
        border-color: var(--cw-brand-primary);
        transform: scale(1.05);
    }

    .team-member-placeholder {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--cw-accent-light);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid var(--cw-brand-accent);
        color: var(--cw-neutral-600);
        font-size: 1.8rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-placeholder {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .team-member-info {
        flex-grow: 1;
    }

    .team-member-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.25rem 0;
        font-family: var(--cw-font-heading);
    }

    .team-member-position {
        color: var(--cw-neutral-600);
        margin: 0 0 0.5rem 0;
        font-weight: 500;
    }

    .team-member-email {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin: 0;
    }

    .team-member-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 1rem;
        border: 1px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-md);
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-accent-light);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: var(--cw-brand-primary);
    }

    .empty-state p {
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .team-header {
            flex-direction: column;
            align-items: stretch;
            text-align: center;
        }

        .team-header-content h1 {
            font-size: 2rem;
        }

        .team-stats {
            justify-content: center;
        }

        .team-member-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .team-member-actions {
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .team-management-container {
            padding: 1rem 0;
        }

        .team-stats-card {
            padding: 1.5rem;
        }

        .team-member-content {
            padding: 1rem;
        }

        .btn-cw-primary {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="team-management-container">
    <div class="container">
        <!-- Page Header -->
        <div class="team-header">
            <div class="team-header-content">
                <h1><i class="fas fa-users"></i> Team Members</h1>
                <p class="team-subtitle">Manage your team members and their roles</p>
            </div>
            <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                <i class="fas fa-plus"></i>
                Add Team Member
            </a>
        </div>

        <!-- Team Statistics -->
        <div class="team-stats-card">
            <div class="team-stats">
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <div class="stat-label">Total Members</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <div class="stat-label">Active Staff</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">2</span>
                    <div class="stat-label">Managers</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Team Satisfaction</div>
                </div>
            </div>
        </div>

        <!-- Team Members List -->
        <div class="team-members-list">
            <!-- Sample Team Member Card -->
            <div class="team-member-card">
                <div class="team-member-content">
                    <div class="team-member-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">John Doe</h3>
                        <p class="team-member-position">Senior Therapist</p>
                        <p class="team-member-email"><EMAIL></p>
                    </div>
                    <div class="team-member-actions">
                        <a href="#" class="btn-cw-secondary">
                            <i class="fas fa-edit"></i>
                            Edit
                        </a>
                        <a href="#" class="btn-cw-danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sample Team Member Card with Avatar -->
            <div class="team-member-card">
                <div class="team-member-content">
                    <img src="https://via.placeholder.com/80x80" alt="Jane Smith" class="team-member-avatar">
                    <div class="team-member-info">
                        <h3 class="team-member-name">Jane Smith</h3>
                        <p class="team-member-position">Massage Therapist</p>
                        <p class="team-member-email"><EMAIL></p>
                    </div>
                    <div class="team-member-actions">
                        <a href="#" class="btn-cw-secondary">
                            <i class="fas fa-edit"></i>
                            Edit
                        </a>
                        <a href="#" class="btn-cw-danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sample Team Member Card -->
            <div class="team-member-card">
                <div class="team-member-content">
                    <div class="team-member-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">Michael Johnson</h3>
                        <p class="team-member-position">Receptionist</p>
                        <p class="team-member-email"><EMAIL></p>
                    </div>
                    <div class="team-member-actions">
                        <a href="#" class="btn-cw-secondary">
                            <i class="fas fa-edit"></i>
                            Edit
                        </a>
                        <a href="#" class="btn-cw-danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State (uncomment if no team members) -->
        <!--
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3>No Team Members Yet</h3>
            <p>Start building your team by adding your first team member</p>
            <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                <i class="fas fa-plus"></i>
                Add Your First Team Member
            </a>
        </div>
        -->
    </div>
</div>
{% endblock %}
