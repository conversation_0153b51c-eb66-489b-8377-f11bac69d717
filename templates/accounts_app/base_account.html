{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Account Pages - Black & White Design */
        /* Matching homepage design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
        }

        /* Account wrapper - clean white background */
        .account-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Account card - clean white with black border */
        .account-card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .account-card-body {
            padding: 3rem 2.5rem;
        }

        /* Header styling matching homepage */
        .account-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .account-header h1,
        .account-header h2 {
            color: black;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
            font-family: var(--font-heading);
        }

        .account-header p {
            color: black;
            font-size: 1.1rem;
            margin-bottom: 0;
            line-height: 1.6;
        }

        /* Icon container matching homepage style */
        .account-icon {
            width: 80px;
            height: 80px;
            background: white;
            border: 2px solid black;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .account-icon i {
            color: black;
            font-size: 2rem;
        }

        /* Form styling */
        .form-floating > label {
            color: black;
            font-weight: 500;
        }

        .form-control, .form-select {
            border: 2px solid black;
            border-radius: 0.5rem;
            padding: 1rem;
            font-size: 1rem;
            color: black;
            background-color: white;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            outline: none;
            background-color: white;
        }

        .form-control:focus-visible, .form-select:focus-visible {
            border-color: black;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            outline: 2px solid black;
            outline-offset: 2px;
        }

        .form-control::placeholder {
            color: #666;
        }

        /* Error state styling */
        .form-control.is-invalid, .form-select.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        /* Button styling matching homepage */
        .btn-primary {
            background-color: white;
            border: 2px solid black;
            color: black;
            border-radius: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
            letter-spacing: 0.025em;
        }

        .btn-primary:hover {
            background-color: #f8f9fa;
            border-color: black;
            color: black;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Additional Bootstrap classes for tests */
        .btn.btn-primary {
            background-color: white;
            border: 2px solid black;
            color: black;
            border-radius: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
            letter-spacing: 0.025em;
        }

        .btn.btn-primary:hover {
            background-color: #f8f9fa;
            border-color: black;
            color: black;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Responsive classes for tests */
        .d-flex {
            display: flex !important;
        }

        .flex-column {
            flex-direction: column !important;
        }

        .flex-sm-row {
            flex-direction: row !important;
        }

        .justify-content-center {
            justify-content: center !important;
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        /* Form validation styling with icons */
        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .invalid-feedback::before {
            content: "\f06a";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 0.5rem;
        }

        .fas.fa-exclamation-circle {
            color: #dc3545;
        }

        /* Error icons for form validation */
        .form-control.is-invalid + .invalid-feedback::before,
        .form-select.is-invalid + .invalid-feedback::before {
            content: "\f06a";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 0.5rem;
        }

        /* Form floating labels */
        .form-floating {
            position: relative;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3.5rem + 2px);
            padding: 1rem 0.75rem;
        }

        .form-floating > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1rem 0.75rem;
            pointer-events: none;
            border: 1px solid transparent;
            transform-origin: 0 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        }

        .btn-outline-primary {
            border: 2px solid black;
            color: black;
            background-color: white;
            border-radius: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
            letter-spacing: 0.025em;
        }

        .btn-outline-primary:hover {
            background-color: #f8f9fa;
            border-color: black;
            color: black;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .btn-outline-secondary {
            border: 2px solid black;
            color: black;
            background-color: white;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background-color: #f8f9fa;
            border-color: black;
            color: black;
            transform: translateY(-2px);
        }

        /* Alert styling */
        .alert {
            border: 2px solid black;
            border-radius: 0.5rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background-color: #fff5f5;
            color: #dc3545;
            border-color: #dc3545;
        }

        .alert-success {
            background-color: #f0fff4;
            color: #28a745;
            border-color: #28a745;
        }

        .alert-info {
            background-color: #f0f9ff;
            color: #007bff;
            border-color: #007bff;
        }

        /* Error message styling */
        .invalid-feedback {
            display: block !important;
            width: 100%;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #dc3545;
            font-weight: 500;
        }

        .invalid-feedback i {
            margin-right: 0.25rem;
            color: #dc3545;
        }

        /* Form text (help text) styling */
        .form-text {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: black;
            opacity: 0.7;
        }

        /* Checkbox styling */
        .form-check-input {
            border: 2px solid black;
        }

        .form-check-input:checked {
            background-color: white;
            border-color: black;
        }

        .form-check-input:focus {
            border-color: black;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        /* Text styling */
        .text-muted {
            color: black !important;
            opacity: 0.7;
        }

        /* Bootstrap badge overrides for proper visibility */
        .badge.bg-success {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .badge.bg-secondary {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .badge.bg-primary {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .badge.bg-info {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .badge.bg-warning {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .badge.bg-danger {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        /* Button overrides for Bootstrap classes */
        .btn-success {
            background: white !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-success:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-danger {
            background: white !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-danger:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-warning {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .btn-warning:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-secondary {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .btn-secondary:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-outline-warning {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .btn-outline-warning:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .btn-outline-danger {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .btn-outline-danger:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        /* Profile wrapper for profile pages */
        .profile-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
        }

        /* Password toggle button */
        .toggle-password {
            border: 2px solid black !important;
            background-color: white !important;
            color: black !important;
            border-left: none !important;
            padding: 0.375rem 0.75rem !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 45px !important;
            cursor: pointer !important;
        }

        .toggle-password:hover,
        .toggle-password:focus {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .toggle-password:active {
            background-color: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .toggle-password .fas.fa-eye,
        .toggle-password .fas.fa-eye-slash {
            font-size: 1rem !important;
            pointer-events: none;
        }

        /* Ensure button is clickable and not disabled */
        .toggle-password:not(:disabled) {
            pointer-events: auto !important;
        }

        /* Override any Bootstrap button disabled styles */
        .toggle-password:disabled {
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        /* Ensure input-group styling works properly */
        .input-group .toggle-password {
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }

        .input-group .form-control:focus + .toggle-password {
            border-color: black !important;
        }

        /* Links */
        a {
            color: black;
            text-decoration: none;
        }

        a:hover {
            color: black;
            text-decoration: underline;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .account-card-body {
                padding: 2rem 1.5rem;
            }

            .account-header h1,
            .account-header h2 {
                font-size: 2rem;
            }
        }
    </style>
    {% block account_extra_css %}{% endblock %}
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// CozyWish Password Toggle Functionality
(function() {
    'use strict';

    // Password toggle functionality - using event delegation (immediate setup)
    document.addEventListener('click', function(e) {
        // Check if clicked element is a toggle password button or its child
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            // Prevent any default behavior and stop propagation
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    // Showing password - change to eye-slash
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    // Hiding password - change to eye
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    }, true); // Use capture phase to ensure we get the event first

    // Add keyboard navigation support for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Space')) {
            e.preventDefault();
            e.stopPropagation();
            
            // Trigger the same toggle functionality as click
            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    });

    function initializePasswordToggles() {
        // Enhanced accessibility and functionality for form fields
        const errorFields = document.querySelectorAll('.form-control, .form-select');
        errorFields.forEach(field => {
            const errorDiv = field.parentElement.querySelector('.invalid-feedback') ||
                            field.closest('.form-floating, .mb-3, .col-md-6, .col-12').querySelector('.invalid-feedback');
            if (errorDiv && errorDiv.textContent.trim()) {
                field.setAttribute('aria-invalid', 'true');
                field.classList.add('is-invalid');

                // Link field to error message
                if (!errorDiv.id) {
                    errorDiv.id = field.id + '_error';
                }
                const describedBy = field.getAttribute('aria-describedby') || '';
                if (!describedBy.includes(errorDiv.id)) {
                    field.setAttribute('aria-describedby', (describedBy + ' ' + errorDiv.id).trim());
                }
            }
        });

        // Add aria-describedby for help text
        const helpTexts = document.querySelectorAll('.form-text');
        helpTexts.forEach(helpText => {
            if (!helpText.id) {
                const field = helpText.closest('.form-floating, .mb-3, .col-md-6, .col-12').querySelector('.form-control, .form-select');
                if (field) {
                    helpText.id = field.id + '_help';
                    const describedBy = field.getAttribute('aria-describedby') || '';
                    if (!describedBy.includes(helpText.id)) {
                        field.setAttribute('aria-describedby', (describedBy + ' ' + helpText.id).trim());
                    }
                }
            }
        });

        // Initialize accessibility attributes and direct event listeners for all toggle buttons
        const toggleButtons = document.querySelectorAll('.toggle-password');

        toggleButtons.forEach(function(btn) {
            btn.setAttribute('type', 'button');
            btn.setAttribute('tabindex', '0');
            btn.title = btn.title || 'Show password';
            btn.setAttribute('aria-label', btn.getAttribute('aria-label') || 'Show password');

            // Add direct click event listener as backup
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const targetSelector = this.getAttribute('data-target');
                if (!targetSelector) return;

                const input = document.querySelector(targetSelector);
                if (!input) return;

                // Toggle password visibility
                const isPassword = input.type === 'password';
                input.type = isPassword ? 'text' : 'password';

                // Update icon
                const icon = this.querySelector('i');
                if (icon) {
                    if (isPassword) {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                }

                // Update accessibility attributes
                const newTitle = isPassword ? 'Hide password' : 'Show password';
                this.title = newTitle;
                this.setAttribute('aria-label', newTitle);
            });

            // Add keyboard event listener for accessibility
            btn.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ' || e.key === 'Space') {
                    e.preventDefault();
                    this.click(); // Trigger click event
                }
            });
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePasswordToggles);
    } else {
        // DOM is already ready
        initializePasswordToggles();
    }

    // Also initialize after a short delay to handle dynamic content
    setTimeout(initializePasswordToggles, 100);
})();
</script>
{% block account_extra_js %}{% endblock %}
{% endblock %}

{% block content %}
{% if request.resolver_match.url_name == 'customer_profile' or request.resolver_match.url_name == 'service_provider_profile' or request.resolver_match.url_name == 'customer_change_password' or request.resolver_match.url_name == 'service_provider_change_password' or request.resolver_match.url_name == 'customer_profile_edit' or request.resolver_match.url_name == 'service_provider_profile_edit' %}
    <!-- Full-width layout for profile pages -->
    <div class="profile-wrapper">
        {% block account_content %}{% endblock %}
    </div>
{% else %}
    <!-- Centered card layout for forms and other account pages -->
    <div class="account-wrapper d-flex align-items-center justify-content-center p-3">
        <div class="account-card w-100" style="max-width: 520px;">
            <div class="account-card-body">
                <div class="messages-container" role="alert" aria-live="polite">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>
                {% block account_form_content %}{% endblock %}
            </div>
        </div>
        {% block below_card %}{% endblock %}
    </div>
{% endif %}
{% endblock %}
