{% extends 'booking_cart_app/base.html' %}

{% block title %}Booking Disputes - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .filter-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .dispute-card {
        border-radius: 8px;
        transition: transform 0.2s;
        border-left: 4px solid #dc3545;
    }
    .dispute-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .resolved-card {
        border-left: 4px solid #28a745;
        opacity: 0.8;
    }
    .stats-summary {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border-radius: 10px;
    }
    .priority-high {
        border-left-color: #dc3545 !important;
    }
    .priority-resolved {
        border-left-color: #28a745 !important;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Disputes</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>Booking Disputes
                </h1>
                <p class="text-muted">Manage and resolve booking disputes</p>
            </div>
        </div>

        <!-- Statistics Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card stats-summary">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h3 class="mb-1">{{ total_disputes }}</h3>
                                <small>Total Disputes</small>
                            </div>
                            <div class="col-md-3">
                                <h3 class="mb-1">{{ unresolved_disputes }}</h3>
                                <small>Unresolved</small>
                            </div>
                            <div class="col-md-3">
                                <h3 class="mb-1">{{ resolved_disputes }}</h3>
                                <small>Resolved</small>
                            </div>
                            <div class="col-md-3">
                                <a href="{% url 'booking_cart_app:admin_booking_analytics' %}" class="btn btn-light btn-sm">
                                    <i class="fas fa-chart-line me-1"></i>View Analytics
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-body">
                        <ul class="nav nav-pills justify-content-center">
                            <li class="nav-item">
                                <a class="nav-link {% if status_filter == 'unresolved' %}active{% endif %}" 
                                   href="{% url 'booking_cart_app:admin_dispute_list' %}?status=unresolved">
                                    <i class="fas fa-clock me-1"></i>Unresolved ({{ unresolved_disputes }})
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if status_filter == 'resolved' %}active{% endif %}" 
                                   href="{% url 'booking_cart_app:admin_dispute_list' %}?status=resolved">
                                    <i class="fas fa-check me-1"></i>Resolved ({{ resolved_disputes }})
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if status_filter == 'all' %}active{% endif %}" 
                                   href="{% url 'booking_cart_app:admin_dispute_list' %}?status=all">
                                    <i class="fas fa-list me-1"></i>All Disputes ({{ total_disputes }})
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disputes List -->
        <div class="row">
            <div class="col-12">
                {% if disputes %}
                    {% for dispute in disputes %}
                    <div class="card dispute-card {% if dispute.dispute_resolved_at %}resolved-card{% endif %} mb-3">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                <code>{{ dispute.booking_id|truncatechars:8 }}</code>
                                                {% if not dispute.dispute_resolved_at %}
                                                    <span class="badge bg-danger ms-2">Unresolved</span>
                                                {% else %}
                                                    <span class="badge bg-success ms-2">Resolved</span>
                                                {% endif %}
                                            </h6>
                                            <div class="text-muted small">
                                                <strong>Customer:</strong> {{ dispute.customer.email }} |
                                                <strong>Venue:</strong> {{ dispute.venue.venue_name|truncatechars:30 }} |
                                                <strong>Total:</strong> ${{ dispute.total_price|floatformat:2 }}
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="small text-muted">
                                                Filed {{ dispute.dispute_filed_at|timesince }} ago
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <strong>Filed by:</strong> {{ dispute.get_dispute_filed_by_display }}
                                        <span class="text-muted">on {{ dispute.dispute_filed_at|date:"M d, Y g:i A" }}</span>
                                    </div>

                                    <div class="mb-2">
                                        <strong>Reason:</strong> 
                                        <span class="text-muted">{{ dispute.dispute_reason|default:"No reason provided"|truncatechars:100 }}</span>
                                    </div>

                                    {% if dispute.dispute_resolved_at %}
                                    <div class="mb-2">
                                        <strong>Resolved:</strong> 
                                        <span class="text-success">{{ dispute.dispute_resolved_at|date:"M d, Y g:i A" }}</span>
                                        {% if dispute.dispute_resolution_notes %}
                                        <br><small class="text-muted">{{ dispute.dispute_resolution_notes|truncatechars:100 }}</small>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 text-end">
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'booking_cart_app:admin_booking_detail' dispute.booking_id %}" 
                                           class="btn btn-sm btn-outline-primary" title="View Booking Details">
                                            <i class="fas fa-eye"></i> Details
                                        </a>
                                        
                                        {% if not dispute.dispute_resolved_at %}
                                        <a href="{% url 'booking_cart_app:admin_resolve_dispute' dispute.booking_id %}" 
                                           class="btn btn-sm btn-danger" title="Resolve Dispute">
                                            <i class="fas fa-gavel"></i> Resolve
                                        </a>
                                        {% endif %}
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            {% if not dispute.dispute_resolved_at %}
                                                <i class="fas fa-clock text-warning"></i> Pending resolution
                                            {% else %}
                                                <i class="fas fa-check text-success"></i> Resolved
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-peace fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No disputes found</h5>
                            {% if status_filter == 'unresolved' %}
                                <p class="text-muted">Great! There are no unresolved disputes at the moment.</p>
                            {% elif status_filter == 'resolved' %}
                                <p class="text-muted">No resolved disputes to display.</p>
                            {% else %}
                                <p class="text-muted">No disputes have been filed yet.</p>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Pagination -->
        {% if disputes.has_other_pages %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Disputes pagination">
                    <ul class="pagination justify-content-center">
                        {% if disputes.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1&status={{ status_filter }}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ disputes.previous_page_number }}&status={{ status_filter }}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ disputes.number }} of {{ disputes.paginator.num_pages }}
                            </span>
                        </li>

                        {% if disputes.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ disputes.next_page_number }}&status={{ status_filter }}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ disputes.paginator.num_pages }}&status={{ status_filter }}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">Quick Actions</h6>
                        <div class="btn-group" role="group">
                            <a href="{% url 'booking_cart_app:admin_booking_list' %}" class="btn btn-outline-primary">
                                <i class="fas fa-list me-1"></i>All Bookings
                            </a>
                            <a href="{% url 'booking_cart_app:admin_booking_dashboard' %}" class="btn btn-outline-info">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                            <a href="{% url 'booking_cart_app:admin_booking_analytics' %}" class="btn btn-outline-success">
                                <i class="fas fa-chart-line me-1"></i>Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to dispute cards
        const disputeCards = document.querySelectorAll('.dispute-card');
        disputeCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('resolved-card')) {
                    this.style.borderLeftColor = '#dc3545';
                    this.style.borderLeftWidth = '6px';
                }
            });
            card.addEventListener('mouseleave', function() {
                this.style.borderLeftWidth = '4px';
            });
        });

        // Auto-refresh for unresolved disputes (every 30 seconds)
        if ('{{ status_filter }}' === 'unresolved' && {{ unresolved_disputes }} > 0) {
            setTimeout(function() {
                location.reload();
            }, 30000);
        }
    });
</script>
{% endblock %}
