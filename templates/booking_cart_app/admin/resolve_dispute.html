{% extends 'booking_cart_app/base.html' %}

{% block title %}Resolve Dispute - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .detail-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .dispute-alert {
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
    }
    .resolution-form {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .info-value {
        font-size: 1rem;
        color: #212529;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_dispute_list' %}">Disputes</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Resolve Dispute</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-gavel me-2"></i>Resolve Dispute
                </h1>
                <p class="text-muted">Booking ID: <code>{{ booking.booking_id }}</code></p>
            </div>
        </div>

        <!-- Dispute Alert -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert dispute-alert" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>Dispute Information
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2"><strong>Filed by:</strong> {{ booking.get_dispute_filed_by_display }}</p>
                            <p class="mb-2"><strong>Filed on:</strong> {{ booking.dispute_filed_at|date:"M d, Y g:i A" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2"><strong>Customer:</strong> {{ booking.customer.email }}</p>
                            <p class="mb-2"><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                        </div>
                    </div>
                    <p class="mb-0"><strong>Dispute Reason:</strong></p>
                    <p class="mb-0">{{ booking.dispute_reason|default:"No reason provided" }}</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Resolution Form -->
            <div class="col-lg-8">
                <div class="resolution-form p-4">
                    <h5 class="mb-4">
                        <i class="fas fa-clipboard-check me-2"></i>Dispute Resolution
                    </h5>

                    <form method="post" aria-label="Resolve dispute form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.resolution_status.label_tag }}
                                    {{ form.resolution_status }}
                                    {% if form.resolution_status.help_text %}
                                        <div class="form-text">{{ form.resolution_status.help_text }}</div>
                                    {% endif %}
                                    {% if form.resolution_status.errors %}
                                        <div class="text-danger">
                                            {% for error in form.resolution_status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.refund_amount.label_tag }}
                                    {{ form.refund_amount }}
                                    {% if form.refund_amount.help_text %}
                                        <div class="form-text">{{ form.refund_amount.help_text }}</div>
                                    {% endif %}
                                    {% if form.refund_amount.errors %}
                                        <div class="text-danger">
                                            {% for error in form.refund_amount.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.resolution_notes.label_tag }}
                            {{ form.resolution_notes }}
                            {% if form.resolution_notes.help_text %}
                                <div class="form-text">{{ form.resolution_notes.help_text }}</div>
                            {% endif %}
                            {% if form.resolution_notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.resolution_notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.notify_parties }}
                                {{ form.notify_parties.label_tag }}
                                {% if form.notify_parties.help_text %}
                                    <div class="form-text">{{ form.notify_parties.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-gavel me-1"></i>Resolve Dispute
                            </button>
                            <a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Booking Summary -->
            <div class="col-lg-4">
                <div class="card detail-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Booking Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="info-label">Booking ID</div>
                            <div class="info-value"><code>{{ booking.booking_id }}</code></div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Current Status</div>
                            <div class="info-value">
                                <span class="badge bg-danger">{{ booking.get_status_display }}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Total Amount</div>
                            <div class="info-value">${{ booking.total_price|floatformat:2 }}</div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Booking Date</div>
                            <div class="info-value">{{ booking.booking_date|date:"M d, Y g:i A" }}</div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Customer</div>
                            <div class="info-value">
                                {{ booking.customer.get_full_name|default:booking.customer.email }}<br>
                                <small class="text-muted">{{ booking.customer.email }}</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Venue</div>
                            <div class="info-value">
                                {{ booking.venue.venue_name }}<br>
                                <small class="text-muted">{{ booking.venue.service_provider.get_full_name|default:booking.venue.service_provider.email }}</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-grid gap-2">
                            <a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Full Details
                            </a>
                            <a href="{% url 'booking_cart_app:admin_dispute_list' %}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-list me-1"></i>All Disputes
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Booked Services -->
                <div class="card detail-card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Booked Services
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for item in booking.items.all %}
                        <div class="mb-3 {% if not forloop.last %}border-bottom pb-3{% endif %}">
                            <div class="fw-bold">{{ item.service_title }}</div>
                            <div class="text-muted small">
                                {{ item.scheduled_date|date:"M d, Y" }} at {{ item.scheduled_time|time:"g:i A" }}<br>
                                Duration: {{ item.duration_minutes }} minutes<br>
                                Quantity: {{ item.quantity }} × ${{ item.service_price|floatformat:2 }}
                            </div>
                            <div class="fw-bold text-end">${{ item.total_price|floatformat:2 }}</div>
                        </div>
                        {% endfor %}
                        <div class="border-top pt-3">
                            <div class="d-flex justify-content-between fw-bold">
                                <span>Total:</span>
                                <span>${{ booking.total_price|floatformat:2 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-focus on resolution notes textarea
        const resolutionNotes = document.querySelector('#id_resolution_notes');
        if (resolutionNotes) {
            resolutionNotes.focus();
        }

        // Show confirmation before submitting
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const confirmed = confirm('Are you sure you want to resolve this dispute? This action cannot be undone.');
                if (!confirmed) {
                    e.preventDefault();
                }
            });
        }

        // Update refund amount based on resolution status
        const statusSelect = document.querySelector('#id_resolution_status');
        const refundInput = document.querySelector('#id_refund_amount');
        
        if (statusSelect && refundInput) {
            statusSelect.addEventListener('change', function() {
                if (this.value === 'cancelled') {
                    refundInput.value = '{{ booking.total_price }}';
                } else {
                    refundInput.value = '';
                }
            });
        }
    });
</script>
{% endblock %}
