{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Booking Cart App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Booking wrapper - clean white background */
        .booking-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .booking-wrapper h1, .booking-wrapper h2, .booking-wrapper h3,
        .booking-wrapper h4, .booking-wrapper h5, .booking-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .booking-wrapper p, .booking-wrapper span, .booking-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .booking-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .booking-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .booking-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .booking-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .booking-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .booking-wrapper .form-control, .booking-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .booking-wrapper .form-control:focus, .booking-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .booking-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Tables */
        .booking-wrapper .table {
            color: black;
            font-family: var(--font-primary);
        }

        .booking-wrapper .table th {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            border-bottom: 2px solid black;
            background-color: white;
        }

        .booking-wrapper .table td {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Badges */
        .booking-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .booking-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .booking-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .booking-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .booking-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Pagination */
        .booking-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .booking-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .booking-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .booking-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .booking-wrapper .text-primary {
            color: black !important;
        }

        .booking-wrapper .text-success {
            color: black !important;
        }

        .booking-wrapper .text-danger {
            color: black !important;
        }

        .booking-wrapper .text-warning {
            color: black !important;
        }

        /* Calendar styling */
        .booking-wrapper .calendar {
            border: 2px solid black;
            border-radius: 1rem;
            background-color: white;
        }

        .booking-wrapper .calendar-header {
            background-color: white;
            color: black;
            font-family: var(--font-heading);
            font-weight: 600;
            padding: 1rem;
            border-radius: 0.75rem 0.75rem 0 0;
            border-bottom: 2px solid black;
        }

        .booking-wrapper .calendar-day {
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
            text-align: center;
            font-family: var(--font-primary);
            color: black;
        }

        .booking-wrapper .calendar-day.available {
            background-color: white;
            cursor: pointer;
        }

        .booking-wrapper .calendar-day.available:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .booking-wrapper .calendar-day.booked {
            background-color: rgba(0, 0, 0, 0.1);
            color: rgba(0, 0, 0, 0.5);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .booking-wrapper {
                padding: 1rem 0;
            }

            .booking-wrapper .card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
    {% block booking_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="booking-wrapper">
    <div class="container py-4">
        {% block booking_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block booking_extra_js %}{% endblock %}
{% endblock %}
