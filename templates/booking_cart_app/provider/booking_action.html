{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}
{% if action == 'accept' %}Accept Booking{% else %}Decline Booking{% endif %} - {{ booking.booking_id }}
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header {% if action == 'accept' %}bg-success{% else %}bg-danger{% endif %} text-white">
                    <h5 class="mb-0">
                        {% if action == 'accept' %}
                            <i class="fas fa-check"></i> Accept Booking
                        {% else %}
                            <i class="fas fa-times"></i> Decline Booking
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Booking Information -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Booking Details</h6>
                        <p><strong>Booking ID:</strong> {{ booking.booking_id }}</p>
                        <p><strong>Customer:</strong> {{ booking.customer.email }}</p>
                        {% if booking.customer.customerprofile %}
                        <p><strong>Customer Name:</strong> {{ booking.customer.customerprofile.first_name }} {{ booking.customer.customerprofile.last_name }}</p>
                        {% endif %}
                        <p><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                        <p><strong>Status:</strong> {{ booking.get_status_display }}</p>
                        <p><strong>Total Amount:</strong> ${{ booking.total_amount }}</p>
                        <p><strong>Booking Date:</strong> {{ booking.created_at|date:"F d, Y g:i A" }}</p>
                    </div>

                    <!-- Services in booking -->
                    <h6 class="mt-3 mb-3">Services in this booking:</h6>
                    {% for item in booking.items.all %}
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>{{ item.service_title }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {{ item.scheduled_date|date:"F d, Y" }} at {{ item.scheduled_time|time:"g:i A" }}
                                        ({{ item.duration_minutes }} minutes)
                                    </small>
                                </div>
                                <div class="col-md-4 text-right">
                                    <strong>Qty: {{ item.quantity }}</strong><br>
                                    <strong>${{ item.service_price }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    {% if booking.notes %}
                    <div class="alert alert-secondary mt-3">
                        <h6><i class="fas fa-comment"></i> Customer Notes</h6>
                        <p class="mb-0">{{ booking.notes }}</p>
                    </div>
                    {% endif %}

                    <!-- Action Confirmation -->
                    <div class="alert {% if action == 'accept' %}alert-success{% else %}alert-warning{% endif %} mt-4">
                        <h6>
                            {% if action == 'accept' %}
                                <i class="fas fa-check-circle"></i> Confirm Acceptance
                            {% else %}
                                <i class="fas fa-exclamation-triangle"></i> Confirm Decline
                            {% endif %}
                        </h6>
                        <p class="mb-0">
                            {% if action == 'accept' %}
                                By accepting this booking, you confirm that you can provide the requested services at the scheduled times.
                                The customer will be notified and the booking will be confirmed.
                            {% else %}
                                By declining this booking, you will not be able to provide the requested services.
                                The customer will be notified and may book with another provider.
                            {% endif %}
                        </p>
                    </div>

                    <!-- Action Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="{{ form.action_reason.id_for_label }}">
                                <strong>
                                    {% if action == 'accept' %}
                                        Additional notes (optional):
                                    {% else %}
                                        Reason for declining:
                                    {% endif %}
                                </strong>
                            </label>
                            {{ form.action_reason }}
                            {% if form.action_reason.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.action_reason.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% if action == 'accept' %}
                                    You can add any special instructions or notes for the customer.
                                {% else %}
                                    Please provide a reason for declining. This will help the customer understand your decision.
                                {% endif %}
                            </small>
                        </div>

                        <div class="form-check mt-3">
                            <input 
                                class="form-check-input" 
                                type="checkbox" 
                                id="confirm_action" 
                                required>
                            <label class="form-check-label" for="confirm_action">
                                {% if action == 'accept' %}
                                    I confirm that I want to accept this booking and can provide the services as requested.
                                {% else %}
                                    I confirm that I want to decline this booking.
                                {% endif %}
                            </label>
                        </div>

                        <div class="mt-4 text-center">
                            <button type="submit" class="btn {% if action == 'accept' %}btn-success{% else %}btn-danger{% endif %}">
                                {% if action == 'accept' %}
                                    <i class="fas fa-check"></i> Accept Booking
                                {% else %}
                                    <i class="fas fa-times"></i> Decline Booking
                                {% endif %}
                            </button>
                            <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}" class="btn btn-secondary ml-2">
                                <i class="fas fa-arrow-left"></i> Go Back
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        const action = '{{ action }}';
        const confirmMessage = action === 'accept' 
            ? 'Are you sure you want to accept this booking?' 
            : 'Are you sure you want to decline this booking?';
            
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }
        
        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        if (action === 'accept') {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Accepting...';
        } else {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Declining...';
        }
    });
});
</script>
{% endblock %}
