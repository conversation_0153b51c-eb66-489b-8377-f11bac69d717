{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Service Availability - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>Service Availability Management</h4>
                        <div>
                            <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Bookings
                            </a>
                        </div>
                    </div>
                    <p class="mb-0 text-muted">Manage availability slots for your services</p>
                </div>
                <div class="card-body">
                    <!-- Service Filter -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <select name="service" class="form-control">
                                    <option value="">All Services</option>
                                    {% for service in services %}
                                    <option value="{{ service.id }}" {% if request.GET.service == service.id|stringformat:"s" %}selected{% endif %}>
                                        {{ service.service_title }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="date" class="form-control" value="{{ request.GET.date }}" placeholder="Filter by date">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-outline-secondary">Clear</a>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addAvailabilityModal">
                                    <i class="fas fa-plus"></i> Add Slot
                                </button>
                            </div>
                        </div>
                    </form>

                    {% if availability_slots %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Date</th>
                                    <th>Time Slot</th>
                                    <th>Capacity</th>
                                    <th>Current Bookings</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for slot in availability_slots %}
                                <tr>
                                    <td>
                                        <strong>{{ slot.service.service_title }}</strong>
                                        <br>
                                        <small class="text-muted">{{ slot.service.venue.venue_name }}</small>
                                    </td>
                                    <td>{{ slot.available_date|date:"M d, Y" }}</td>
                                    <td>
                                        {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                        <br>
                                        <small class="text-muted">{{ slot.service.duration_minutes }} min duration</small>
                                    </td>
                                    <td>{{ slot.max_bookings }}</td>
                                    <td>
                                        <span class="badge badge-{% if slot.current_bookings >= slot.max_bookings %}danger{% elif slot.current_bookings > 0 %}warning{% else %}success{% endif %}">
                                            {{ slot.current_bookings }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if slot.is_available %}
                                            {% if slot.current_bookings >= slot.max_bookings %}
                                                <span class="badge badge-danger">Full</span>
                                            {% else %}
                                                <span class="badge badge-success">Available</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge badge-secondary">Unavailable</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editAvailability({{ slot.id }})">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        {% if slot.current_bookings == 0 %}
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAvailability({{ slot.id }})">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Availability pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.service %}&service={{ request.GET.service }}{% endif %}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.service %}&service={{ request.GET.service }}{% endif %}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}">Previous</a>
                            </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.service %}&service={{ request.GET.service }}{% endif %}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.service %}&service={{ request.GET.service }}{% endif %}{% if request.GET.date %}&date={{ request.GET.date }}{% endif %}">Last</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5>No availability slots found</h5>
                        <p class="text-muted">Create availability slots to allow customers to book your services.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addAvailabilityModal">
                            <i class="fas fa-plus"></i> Add First Availability Slot
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Availability Modal -->
<div class="modal fade" id="addAvailabilityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Availability Slot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="post" action="#" id="addAvailabilityForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="service">Service:</label>
                        <select class="form-control" id="service" name="service" required>
                            <option value="">Select a service</option>
                            {% for service in services %}
                            <option value="{{ service.id }}">{{ service.service_title }} - {{ service.venue.venue_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="available_date">Date:</label>
                        <input type="date" class="form-control" id="available_date" name="available_date" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_time">Start Time:</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_time">End Time:</label>
                                <input type="time" class="form-control" id="end_time" name="end_time" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="max_bookings">Maximum Bookings:</label>
                        <input type="number" class="form-control" id="max_bookings" name="max_bookings" min="1" value="1" required>
                        <small class="form-text text-muted">How many customers can book this time slot?</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Availability</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editAvailability(slotId) {
    // This would open an edit modal or redirect to edit page
    alert('Edit functionality would be implemented here for slot ID: ' + slotId);
}

function deleteAvailability(slotId) {
    if (confirm('Are you sure you want to delete this availability slot?')) {
        // This would send a delete request
        alert('Delete functionality would be implemented here for slot ID: ' + slotId);
    }
}

// Set minimum date to today and handle form submission
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('available_date');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
    }

    // Handle form submission
    const form = document.getElementById('addAvailabilityForm');
    const serviceSelect = document.getElementById('service');

    if (form && serviceSelect) {
        form.addEventListener('submit', function(e) {
            const selectedServiceId = serviceSelect.value;
            if (selectedServiceId) {
                // Update form action with selected service ID
                form.action = `/bookings/provider/availability/add/${selectedServiceId}/`;
            } else {
                e.preventDefault();
                alert('Please select a service first.');
            }
        });
    }
});
</script>
{% endblock %}
