{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Availability Management - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Availability Management</h2>
                <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>

            {% if services_with_stats %}
                <div class="row">
                    {% for service_data in services_with_stats %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">{{ service_data.service.service_title }}</h6>
                                <small class="text-muted">{{ service_data.service.venue.venue_name }}</small>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">{{ service_data.service.short_description|truncatewords:15 }}</p>
                                
                                <!-- Availability Stats -->
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="h5 mb-0 text-primary">{{ service_data.total_slots }}</div>
                                            <small class="text-muted">Total Slots</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="h5 mb-0 text-success">{{ service_data.available_slots }}</div>
                                            <small class="text-muted">Available</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="h5 mb-0 text-info">{{ service_data.future_slots }}</div>
                                        <small class="text-muted">Future</small>
                                    </div>
                                </div>

                                <!-- Service Details -->
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-dollar-sign"></i> {{ service_data.service.price_display }} |
                                        <i class="fas fa-clock"></i> {{ service_data.service.duration_display }}
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'booking_cart_app:provider_service_availability' service_data.service.id %}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-calendar-alt"></i> View
                                    </a>
                                    <a href="{% url 'booking_cart_app:provider_add_availability' service_data.service.id %}" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-plus"></i> Add
                                    </a>
                                    <a href="{% url 'booking_cart_app:provider_bulk_availability' service_data.service.id %}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-layer-group"></i> Bulk
                                    </a>
                                    <a href="{% url 'booking_cart_app:provider_availability_calendar' service_data.service.id %}" 
                                       class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-calendar"></i> Calendar
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5>No Services Found</h5>
                    <p class="text-muted">You need to create services before managing availability.</p>
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Services
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive functionality here
    console.log('Availability list loaded');
});
</script>
{% endblock %}
