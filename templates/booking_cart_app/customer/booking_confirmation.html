{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Booking Confirmation - CozyWish{% endblock %}

{% block booking_extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Booking Confirmation Page */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Semantic Colors */
        --cw-success: #059669;
        --cw-info: #0284c7;
        --cw-warning: #d97706;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-success: linear-gradient(135deg, #ffffff 0%, #d1fae5 100%);
    }

    /* Override base styles for confirmation page */
    .booking-wrapper {
        background: var(--cw-accent-light);
        font-family: var(--cw-font-primary);
    }

    /* Success Header */
    .success-header {
        background: var(--cw-gradient-success);
        border: 2px solid var(--cw-success);
        border-radius: 1rem;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: var(--cw-shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .success-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(5, 150, 105, 0.1) 0%, transparent 70%);
        animation: pulse 3s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.1); opacity: 0.8; }
    }

    .success-header .content {
        position: relative;
        z-index: 1;
    }

    .success-header .success-icon {
        font-size: 4rem;
        color: var(--cw-success);
        margin-bottom: 1rem;
        animation: bounce 2s ease-in-out infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .success-header h1 {
        font-family: var(--cw-font-display);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .success-header .subtitle {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-700);
        font-size: 1.2rem;
        margin: 0;
        line-height: 1.6;
    }

    /* Confirmation Cards */
    .confirmation-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .confirmation-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .confirmation-card-header {
        background: var(--cw-gradient-card);
        padding: 1.5rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .confirmation-card-header h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.5rem;
    }

    .confirmation-card-body {
        padding: 2rem;
    }

    /* Booking Details */
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        margin-bottom: 0;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-family: var(--cw-font-primary);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    .detail-value {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-700);
        font-weight: 500;
        text-align: right;
    }

    .detail-value.booking-id {
        font-family: 'Courier New', monospace;
        background: var(--cw-accent-light);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.9rem;
    }

    .detail-value.amount {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-success);
    }

    /* Status Badge */
    .status-badge {
        background: var(--cw-gradient-brand-button);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Service Items */
    .service-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .service-item:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-sm);
    }

    .service-item h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .service-item .service-meta {
        margin-bottom: 1rem;
    }

    .service-item .service-meta p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .service-item .service-meta .fas {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
        width: 1rem;
    }

    .service-item .service-details {
        text-align: right;
    }

    .service-item .service-details p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .service-item .service-details strong {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    /* Services Section Header */
    .services-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .services-header h5 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.25rem;
    }

    .services-header .fas {
        color: var(--cw-brand-primary);
        margin-right: 0.75rem;
        font-size: 1.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .success-header {
            padding: 2rem 1rem;
        }

        .success-header h1 {
            font-size: 2rem;
        }

        .success-header .success-icon {
            font-size: 3rem;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
        }

        .detail-value {
            text-align: left;
            margin-top: 0.25rem;
        }

        .service-item .service-details {
            text-align: left;
            margin-top: 1rem;
        }
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Success Message -->
            <div class="success-header">
                <div class="content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1>Booking Confirmed!</h1>
                    <p class="subtitle">Your spa experience has been successfully booked. Get ready to relax and rejuvenate!</p>
                </div>
            </div>

            <!-- Booking Details Card -->
            <div class="confirmation-card">
                <div class="confirmation-card-header">
                    <h4>
                        <i class="fas fa-calendar-check me-2"></i>
                        Booking Details
                    </h4>
                </div>
                <div class="confirmation-card-body">
                    <!-- Booking Information -->
                    <div class="detail-row">
                        <span class="detail-label">Booking ID:</span>
                        <span class="detail-value booking-id">{{ booking.booking_id }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">
                            <span class="status-badge">{{ booking.get_status_display }}</span>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Total Amount:</span>
                        <span class="detail-value amount">${{ booking.total_amount }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Booking Date:</span>
                        <span class="detail-value">{{ booking.created_at|date:"F d, Y g:i A" }}</span>
                    </div>

                    <!-- Booking Items -->
                    <div class="services-header">
                        <i class="fas fa-spa"></i>
                        <h5>Your Booked Services</h5>
                    </div>

                    {% for item in booking_items %}
                    <div class="service-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>{{ item.service_title }}</h6>
                                <div class="service-meta">
                                    <p>
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ item.service.venue.venue_name }}
                                    </p>
                                    <p>
                                        <i class="fas fa-clock"></i>
                                        {{ item.duration_minutes }} minutes
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4 service-details">
                                <p><strong>Date:</strong> {{ item.scheduled_date|date:"M d, Y" }}</p>
                                <p><strong>Time:</strong> {{ item.scheduled_time|time:"g:i A" }}</p>
                                <p><strong>Price:</strong> ${{ item.service_price }}</p>
                                <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Next Steps -->
            <div class="confirmation-card">
                <div class="confirmation-card-header">
                    <h4>
                        <i class="fas fa-route me-2"></i>
                        What's Next?
                    </h4>
                </div>
                <div class="confirmation-card-body">
                    <div class="next-steps">
                        <div class="step-item">
                            <div class="step-icon email">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="step-content">
                                <h6>Confirmation Email</h6>
                                <p>You will receive a detailed confirmation email with all booking information</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-icon reminder">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="step-content">
                                <h6>Appointment Reminder</h6>
                                <p>We'll send you a reminder 24 hours before your scheduled appointment</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-icon contact">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="step-content">
                                <h6>Provider Contact</h6>
                                <p>The service provider may contact you to confirm specific details</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-icon manage">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="step-content">
                                <h6>Manage Bookings</h6>
                                <p>View and manage all your bookings in your account dashboard</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'booking_cart_app:booking_list' %}" class="btn-cw-primary">
                    <i class="fas fa-list me-2"></i>
                    View My Bookings
                </a>
                <a href="/" class="btn-cw-secondary">
                    <i class="fas fa-home me-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Additional styles for next steps and action buttons */
    .next-steps {
        display: grid;
        gap: 1.5rem;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }

    .step-item:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-sm);
    }

    .step-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        font-size: 1.25rem;
        color: white;
    }

    .step-icon.email {
        background: linear-gradient(135deg, #0284c7, #0369a1);
    }

    .step-icon.reminder {
        background: linear-gradient(135deg, #d97706, #b45309);
    }

    .step-icon.contact {
        background: linear-gradient(135deg, #059669, #047857);
    }

    .step-icon.manage {
        background: linear-gradient(135deg, var(--cw-brand-primary), var(--cw-brand-light));
    }

    .step-content h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .step-content p {
        color: var(--cw-neutral-600);
        margin: 0;
        line-height: 1.5;
        font-size: 0.95rem;
    }

    /* Action Buttons */
    .action-buttons {
        text-align: center;
        margin: 2rem 0;
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1.1rem;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Responsive Design for Action Buttons */
    @media (max-width: 768px) {
        .action-buttons {
            padding: 1.5rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .step-item {
            flex-direction: column;
            text-align: center;
        }

        .step-icon {
            margin-right: 0;
            margin-bottom: 1rem;
            align-self: center;
        }
    }
</style>
{% endblock %}
