{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Checkout - CozyWish{% endblock %}

{% block booking_extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Checkout Page */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Override base styles for checkout page */
    .booking-wrapper {
        background: var(--cw-accent-light);
        font-family: var(--cw-font-primary);
    }

    /* Page Header */
    .checkout-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .checkout-header h1 {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
    }

    .checkout-header .subtitle {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin: 0;
    }

    /* Checkout Cards */
    .checkout-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .checkout-card-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .checkout-card-header h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.5rem;
    }

    .checkout-card-body {
        padding: 1.5rem;
    }

    /* Service Items */
    .service-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .service-item h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .service-item .service-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .service-item .service-details p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .service-item .service-details strong {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .service-item .price-section {
        text-align: right;
    }

    .service-item .price-section p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .service-item .total-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    /* Form Styling */
    .form-section {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-section h5 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-size: 1.25rem;
    }

    .form-label {
        font-family: var(--cw-font-primary);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-control {
        font-family: var(--cw-font-primary);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        background-color: white;
        color: var(--cw-neutral-800);
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background-color: white;
        color: var(--cw-neutral-800);
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    /* Checkbox Styling */
    .form-check {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.25rem;
        background-color: white;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-700);
        font-weight: 500;
        margin-left: 0.5rem;
        line-height: 1.5;
    }

    .form-check-label a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 600;
    }

    .form-check-label a:hover {
        text-decoration: underline;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1.1rem;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-cw-primary:disabled {
        opacity: 0.6;
        transform: none;
        cursor: not-allowed;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Action Buttons Section */
    .action-buttons {
        text-align: center;
        padding-top: 1.5rem;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .checkout-header h1 {
            font-size: 2rem;
        }

        .service-item .price-section {
            text-align: left;
            margin-top: 1rem;
        }

        .action-buttons {
            text-align: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            margin-right: 0;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container">
    <!-- Page Header -->
    <div class="checkout-header">
        <h1>Checkout</h1>
        <p class="subtitle">Review your booking details and complete your reservation</p>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h4>
                        <i class="fas fa-calendar-check me-2"></i>
                        Services to Book
                    </h4>
                </div>
                <div class="checkout-card-body">
                    <form method="post" id="checkout-form">
                        {% csrf_token %}

                        <!-- Cart Items Review -->
                        {% for item in cart.items.all %}
                        <div class="service-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service.service_title }}</h6>
                                    <p class="service-description">{{ item.service.short_description }}</p>
                                    <div class="service-details">
                                        <p><strong>Venue:</strong> {{ item.service.venue.venue_name }}</p>
                                        <p><strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}</p>
                                        <p><strong>Time:</strong> {{ item.selected_time_slot }}</p>
                                    </div>
                                </div>
                                <div class="col-md-4 price-section">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.price_per_item }} each</p>
                                    <p class="total-price">Total: ${{ item.total_price }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Special Notes Section -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-sticky-note me-2"></i>
                                Special Notes
                            </h5>
                            <div class="mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    Additional requests or special instructions (Optional)
                                </label>
                                {{ form.notes }}
                                {% if form.notes.help_text %}
                                <div class="form-text">{{ form.notes.help_text }}</div>
                                {% endif %}
                                {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                <i class="fas fa-shield-alt me-2"></i>
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and
                                <a href="#" target="_blank">Cancellation Policy</a>
                            </label>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="btn-cw-primary" id="submit-btn">
                                <i class="fas fa-check-circle me-2"></i>
                                Complete Booking
                            </button>
                            <a href="{% url 'booking_cart_app:cart_view' %}" class="btn-cw-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Cart
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h4>
                        <i class="fas fa-receipt me-2"></i>
                        Order Summary
                    </h4>
                </div>
                <div class="checkout-card-body">
                    <div class="summary-row">
                        <span>Subtotal:</span>
                        <span>${{ cart.total_price }}</span>
                    </div>

                    {% if discount %}
                    <div class="summary-row discount-row">
                        <span>Discount ({{ discount.code }}):</span>
                        <span>-${{ discount_amount }}</span>
                    </div>
                    {% endif %}

                    <div class="summary-row">
                        <span>Tax:</span>
                        <span>${{ tax_amount|default:"0.00" }}</span>
                    </div>

                    <div class="summary-total">
                        <div class="summary-row total-row">
                            <span>Total:</span>
                            <span>${{ final_total|default:cart.total_price }}</span>
                        </div>
                    </div>

                    <div class="confirmation-note">
                        <i class="fas fa-info-circle me-2"></i>
                        You will receive a confirmation email after booking.
                    </div>
                </div>
            </div>

            <!-- Cancellation Policy -->
            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h4>
                        <i class="fas fa-shield-alt me-2"></i>
                        Cancellation Policy
                    </h4>
                </div>
                <div class="checkout-card-body">
                    <div class="policy-list">
                        <div class="policy-item">
                            <i class="fas fa-check-circle me-2"></i>
                            Free cancellation up to 24 hours before your appointment
                        </div>
                        <div class="policy-item">
                            <i class="fas fa-clock me-2"></i>
                            50% refund for cancellations within 24 hours
                        </div>
                        <div class="policy-item">
                            <i class="fas fa-times-circle me-2"></i>
                            No refund for no-shows
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Additional styles for order summary and policy */
    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
        font-size: 1rem;
    }

    .discount-row {
        color: #059669;
        font-weight: 600;
    }

    .summary-total {
        border-top: 2px solid var(--cw-brand-primary);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .total-row {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    .confirmation-note {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
    }

    .confirmation-note .fas {
        color: var(--cw-brand-primary);
    }

    .policy-list {
        space-y: 1rem;
    }

    .policy-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
        color: var(--cw-neutral-700);
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .policy-item:last-child {
        margin-bottom: 0;
    }

    .policy-item .fas {
        color: var(--cw-brand-primary);
        margin-top: 0.1rem;
        flex-shrink: 0;
    }

    .policy-item .fa-check-circle {
        color: #059669;
    }

    .policy-item .fa-times-circle {
        color: #dc3545;
    }

    .policy-item .fa-clock {
        color: #d97706;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checkout-form');
    const submitBtn = document.getElementById('submit-btn');

    form.addEventListener('submit', function(e) {
        // Disable the submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Processing...';

        // Re-enable button after 5 seconds in case of error
        setTimeout(function() {
            if (submitBtn.disabled) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i>Complete Booking';
            }
        }, 5000);
    });

    // Form validation enhancement
    const termsCheckbox = document.getElementById('terms');
    const form_elements = form.querySelectorAll('input, textarea, select');

    function validateForm() {
        let isValid = true;

        // Check terms checkbox
        if (!termsCheckbox.checked) {
            isValid = false;
        }

        submitBtn.disabled = !isValid;
        return isValid;
    }

    // Add event listeners for real-time validation
    termsCheckbox.addEventListener('change', validateForm);

    // Initial validation
    validateForm();
});
</script>
{% endblock %}
