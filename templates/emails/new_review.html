<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Review Received - CozyWish</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .review-card {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 25px;
            border-radius: 6px;
            margin: 25px 0;
        }
        
        .star-rating {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stars {
            color: #ffc107;
            font-size: 20px;
            margin-right: 10px;
        }
        
        .rating-text {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .customer-info {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        
        .customer-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .review-text {
            font-style: italic;
            color: #495057;
            font-size: 16px;
            line-height: 1.7;
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }
        
        .cta-section {
            text-align: center;
            margin: 35px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }
        
        .tips-section {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .tips-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
        }
        
        .tips-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .tips-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            text-decoration: none;
            font-size: 18px;
        }
        
        .brand-logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        @media (max-width: 600px) {
            .content {
                padding: 25px 20px;
            }
            
            .header {
                padding: 25px 15px;
            }
            
            .review-card {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🌟 New Review Received!</h1>
            <p>Great news! A customer has shared their experience</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello {{ review.venue.service_provider.business_name }},
            </div>
            
            <p>Congratulations! You have received a new review for <strong>{{ review.venue.venue_name }}</strong>. Customer feedback helps build trust and grow your business.</p>
            
            <!-- Review Card -->
            <div class="review-card">
                <div class="star-rating">
                    <div class="stars">
                        {% for i in "12345" %}
                            {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                        {% endfor %}
                    </div>
                    <div class="rating-text">{{ review.rating }}/5 Stars</div>
                </div>
                
                <div class="customer-info">
                    <div class="customer-name">
                        👤 {{ review.customer.get_full_name|default:review.customer.email }}
                    </div>
                    <div style="color: #6c757d; font-size: 14px;">
                        📅 Reviewed on {{ review.created_at|date:"F j, Y \a\t g:i A" }}
                    </div>
                </div>
                
                {% if review.written_review %}
                <div class="review-text">
                    "{{ review.written_review }}"
                </div>
                {% else %}
                <div class="review-text" style="color: #6c757d; font-style: normal;">
                    <em>The customer provided a star rating without written feedback.</em>
                </div>
                {% endif %}
            </div>
            
            <!-- Call to Action -->
            <div class="cta-section">
                <a href="{{ request.scheme }}://{{ request.get_host }}{% url 'review_app:provider_venue_reviews' %}" class="cta-button">
                    📊 View All Reviews & Respond
                </a>
            </div>
            
            <!-- Response Tips -->
            <div class="tips-section">
                <div class="tips-title">
                    💡 Quick Response Tips
                </div>
                <ul class="tips-list">
                    <li>Thank the customer for their feedback</li>
                    <li>Address any specific concerns mentioned</li>
                    <li>Keep your response professional and courteous</li>
                    <li>Invite them to visit again or contact you directly</li>
                    <li>Respond promptly to show you value feedback</li>
                </ul>
            </div>
            
            <p style="margin-top: 30px; color: #6c757d;">
                <strong>Why respond?</strong> Responding to reviews shows professionalism, builds customer relationships, and demonstrates that you value feedback. This can lead to improved customer satisfaction and more bookings.
            </p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="brand-logo">CozyWish</div>
            <div class="footer-text">
                Making every stay memorable through trusted connections
            </div>
            <div class="social-links">
                <a href="#" title="Facebook">📘</a>
                <a href="#" title="Twitter">🐦</a>
                <a href="#" title="Instagram">📷</a>
                <a href="#" title="LinkedIn">💼</a>
            </div>
            <div class="footer-text">
                © 2024 CozyWish. All rights reserved.<br>
                This email was sent to {{ review.venue.service_provider.user.email }} regarding your venue {{ review.venue.venue_name }}.
            </div>
        </div>
    </div>
</body>
</html> 