<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation - CozyWish</title>
    <style>
        /* CozyWish Email Design System */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #262626;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }

        .email-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .email-title {
            font-family: 'Playfair Display', Georgia, serif;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }

        .email-body {
            padding: 40px 30px;
        }

        .payment-card {
            background: #faf5ff;
            border: 1px solid #e9d5ff;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }

        .payment-details {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #7c3aed;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
        }

        .email-footer {
            background: #fef7f0;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #fae1d7;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                💳
            </div>
            <h1 class="email-title">Payment Confirmed</h1>
            <p>Your transaction was successful</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <p><strong>Hello {{ payment.customer.get_full_name|default:payment.customer.first_name }},</strong></p>

            <p>Your payment has been successfully processed! Thank you for your trust in CozyWish.</p>

            <div class="payment-card">
                <h3 style="color: #7c3aed; margin-top: 0;">💰 Payment Details</h3>
                
                <div class="payment-details">
                    <p><strong>Transaction ID:</strong> {{ payment.transaction_id|default:payment.id }}</p>
                    <p><strong>Amount Paid:</strong> ${{ payment.amount }}</p>
                    <p><strong>Payment Method:</strong> {{ payment.payment_method|default:"Card" }}</p>
                    <p><strong>Date:</strong> {{ payment.created_at|date:"F j, Y \a\t g:i A" }}</p>
                    <p><strong>Status:</strong> ✅ Completed</p>
                </div>

                {% if payment.booking %}
                <h4 style="color: #7c3aed;">📋 Related Booking:</h4>
                <div style="background: white; padding: 15px; border-radius: 6px;">
                    <p><strong>Booking:</strong> {{ payment.booking.friendly_id }}</p>
                    <p><strong>Venue:</strong> {{ payment.booking.venue.venue_name }}</p>
                    <p><strong>Date:</strong> {{ payment.booking.booking_date|date:"F j, Y" }}</p>
                </div>
                {% endif %}
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ site_url }}/bookings/" class="cta-button">
                    View My Bookings
                </a>
            </div>

            <div style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <p style="color: #0c4a6e; margin: 0;"><strong>📧 Receipt:</strong> A detailed receipt has been sent to your email address for your records.</p>
            </div>

            <p>If you have any questions about this payment, please don't hesitate to contact our support team.</p>

            <p>Thank you for choosing CozyWish! 🌸</p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p><strong>CozyWish</strong> - Your trusted spa and wellness marketplace</p>
            <p style="font-size: 12px; color: #525252;">
                This email was sent to {{ payment.customer.email }} regarding payment {{ payment.transaction_id|default:payment.id }}
            </p>
        </div>
    </div>
</body>
</html> 