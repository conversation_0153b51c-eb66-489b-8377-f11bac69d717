{% extends 'base.html' %}

{% block title %}Payment Details - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:payment_history' %}">Payment History</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Payment Details</li>
                </ol>
            </nav>
            
            <div class="page-header mb-4">
                <h1 class="h2">Payment Details</h1>
                <p class="text-muted">View detailed information about your payment</p>
            </div>
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Payment Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>Payment Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Payment ID</h6>
                                    <p class="mb-3"><code>{{ payment.payment_id }}</code></p>
                                    
                                    <h6>Amount Paid</h6>
                                    <p class="mb-3 fw-bold fs-5">${{ payment.amount_paid }}</p>
                                    
                                    <h6>Payment Method</h6>
                                    <p class="mb-3">
                                        <span class="badge bg-secondary">{{ payment.get_payment_method_display }}</span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Payment Date</h6>
                                    <p class="mb-3">{{ payment.payment_date|date:"M d, Y H:i" }}</p>
                                    
                                    <h6>Status</h6>
                                    <p class="mb-3">
                                        {% if payment.payment_status == 'succeeded' %}
                                        <span class="badge bg-success fs-6">Succeeded</span>
                                        {% elif payment.payment_status == 'pending' %}
                                        <span class="badge bg-warning fs-6">Pending</span>
                                        {% elif payment.payment_status == 'processing' %}
                                        <span class="badge bg-info fs-6">Processing</span>
                                        {% elif payment.payment_status == 'failed' %}
                                        <span class="badge bg-danger fs-6">Failed</span>
                                        {% elif payment.payment_status == 'refunded' %}
                                        <span class="badge bg-secondary fs-6">Refunded</span>
                                        {% elif payment.payment_status == 'partially_refunded' %}
                                        <span class="badge bg-warning fs-6">Partially Refunded</span>
                                        {% else %}
                                        <span class="badge bg-secondary fs-6">{{ payment.get_payment_status_display }}</span>
                                        {% endif %}
                                    </p>
                                    
                                    {% if payment.completed_date %}
                                    <h6>Completed Date</h6>
                                    <p class="mb-3">{{ payment.completed_date|date:"M d, Y H:i" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if payment.refunded_amount > 0 %}
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h6>Refund Information</h6>
                                    <p class="mb-1"><strong>Refunded Amount:</strong> ${{ payment.refunded_amount }}</p>
                                    <p class="mb-0"><strong>Remaining Amount:</strong> ${{ payment.remaining_refundable_amount }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if payment.failure_reason %}
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h6>Failure Reason</h6>
                                    <div class="alert alert-danger">
                                        {{ payment.failure_reason }}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Booking Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-check me-2"></i>Booking Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Booking ID</h6>
                                    <p class="mb-3">
                                        <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="text-decoration-none">
                                            <code>{{ payment.booking.booking_id }}</code>
                                        </a>
                                    </p>
                                    
                                    <h6>Venue</h6>
                                    <p class="mb-3">{{ payment.booking.venue.venue_name }}</p>

                                    <h6>Location</h6>
                                    <p class="mb-3">{{ payment.booking.venue.city }}, {{ payment.booking.venue.state }}</p>

                                    {% if payment.booking.items.exists %}
                                        <h6>Services</h6>
                                        {% for item in payment.booking.items.all %}
                                            <p class="mb-1">{{ item.service_title }}</p>
                                        {% endfor %}
                                    {% endif %}
                                    
                                    <h6>Booking Date</h6>
                                    <p class="mb-3">{{ payment.booking.booking_date|date:"M d, Y H:i" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Booking Status</h6>
                                    <p class="mb-3">
                                        <span class="badge bg-info">{{ payment.booking.get_status_display }}</span>
                                    </p>
                                    
                                    <h6>Total Price</h6>
                                    <p class="mb-3 fw-bold">${{ payment.booking.total_price }}</p>
                                    
                                    <h6>Service Provider</h6>
                                    <p class="mb-3">{{ payment.provider.email }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Refund Requests -->
                    {% if refund_requests %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-undo me-2"></i>Refund Requests
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for refund_request in refund_requests %}
                            <div class="border rounded p-3 mb-3">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6>{{ refund_request.get_reason_category_display }}</h6>
                                        <p class="mb-2">{{ refund_request.reason_description|truncatechars:100 }}</p>
                                        <small class="text-muted">Requested on {{ refund_request.created_at|date:"M d, Y H:i" }}</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <p class="mb-1 fw-bold">${{ refund_request.requested_amount }}</p>
                                        {% if refund_request.request_status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif refund_request.request_status == 'approved' %}
                                        <span class="badge bg-success">Approved</span>
                                        {% elif refund_request.request_status == 'declined' %}
                                        <span class="badge bg-danger">Declined</span>
                                        {% elif refund_request.request_status == 'processed' %}
                                        <span class="badge bg-info">Processed</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if refund_request.admin_notes %}
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <h6>Admin Notes</h6>
                                        <p class="mb-0">{{ refund_request.admin_notes }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-lg-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if payment.payment_status == 'succeeded' %}
                            <div class="d-grid gap-2">
                                <a href="{% url 'payments_app:payment_receipt' payment_id=payment.payment_id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-download me-2"></i>Download Receipt
                                </a>
                                
                                {% if can_request_refund %}
                                <a href="{% url 'payments_app:refund_request' payment_id=payment.payment_id %}" class="btn btn-outline-warning">
                                    <i class="fas fa-undo me-2"></i>Request Refund
                                </a>
                                {% endif %}
                            </div>
                            {% elif payment.payment_status == 'failed' %}
                            <div class="alert alert-danger">
                                <h6>Payment Failed</h6>
                                <p class="mb-0">This payment has failed. Please contact support for assistance.</p>
                            </div>
                            {% elif payment.payment_status == 'pending' %}
                            <div class="alert alert-warning">
                                <h6>Payment Pending</h6>
                                <p class="mb-0">This payment is still being processed. Please wait for confirmation.</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Support -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-headset me-2"></i>Need Help?
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">If you have any questions about this payment, our support team is here to help.</p>
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="fas fa-headset me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'payments_app:payment_history' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Payment History
                </a>
                <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="btn btn-primary">
                    <i class="fas fa-calendar-check me-2"></i>View Booking Details
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
