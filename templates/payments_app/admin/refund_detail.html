{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Refund Request Detail" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Refund Request Details" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if refund_request %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Refund Request Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Request ID" %}</th>
                                    <td>{{ refund_request.refund_request_id }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Amount Requested" %}</th>
                                    <td>${{ refund_request.requested_amount }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Status" %}</th>
                                    <td>
                                        <span class="badge badge-{% if refund_request.request_status == 'approved' %}success{% elif refund_request.request_status == 'declined' %}danger{% else %}warning{% endif %}">
                                            {{ refund_request.get_request_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Request Date" %}</th>
                                    <td>{{ refund_request.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Reason" %}</th>
                                    <td>{{ refund_request.reason_description|default:"No reason provided" }}</td>
                                </tr>
                                {% if refund_request.admin_notes %}
                                <tr>
                                    <th>{% trans "Admin Notes" %}</th>
                                    <td>{{ refund_request.admin_notes }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Payment Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Payment ID" %}</th>
                                    <td>
                                        <a href="{% url 'payments_app:admin_payment_detail' payment_id=refund_request.payment.payment_id %}">
                                            {{ refund_request.payment.payment_id|truncatechars:8 }}...
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Original Amount" %}</th>
                                    <td>${{ refund_request.payment.amount_paid }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Payment Date" %}</th>
                                    <td>{{ refund_request.payment.payment_date|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Customer" %}</th>
                                    <td>{{ refund_request.payment.customer.get_full_name|default:refund_request.payment.customer.email }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Provider" %}</th>
                                    <td>{{ refund_request.payment.provider.get_full_name|default:refund_request.payment.provider.email }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if refund_request.request_status == 'pending' %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Actions" %}</h5>
                            <div class="btn-group" role="group">
                                <a href="{% url 'payments_app:admin_refund_approve' refund_id=refund_request.refund_request_id %}"
                                   class="btn btn-success">
                                    <i class="fas fa-check"></i> {% trans "Approve Refund" %}
                                </a>
                                <a href="{% url 'payments_app:admin_refund_decline' refund_id=refund_request.refund_request_id %}"
                                   class="btn btn-danger">
                                    <i class="fas fa-times"></i> {% trans "Decline Refund" %}
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <p>{% trans "Refund request not found." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
