{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment Detail" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Payment Details" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'payments_app:admin_payment_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if payment %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Payment Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Payment ID" %}</th>
                                    <td>{{ payment.payment_id }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Amount" %}</th>
                                    <td>${{ payment.amount_paid }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Status" %}</th>
                                    <td>
                                        <span class="badge badge-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% else %}warning{% endif %}">
                                            {{ payment.get_payment_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Payment Date" %}</th>
                                    <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Payment Method" %}</th>
                                    <td>{{ payment.get_payment_method_display }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Customer Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Customer" %}</th>
                                    <td>{{ payment.customer.get_full_name|default:payment.customer.email }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Email" %}</th>
                                    <td>{{ payment.customer.email }}</td>
                                </tr>
                            </table>
                            
                            <h5>{% trans "Provider Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Provider" %}</th>
                                    <td>{{ payment.provider.get_full_name|default:payment.provider.email }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Email" %}</th>
                                    <td>{{ payment.provider.email }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    {% else %}
                    <p>{% trans "Payment not found." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
