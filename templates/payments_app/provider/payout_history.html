{% extends 'base.html' %}

{% block title %}Payout History - Service Provider - CozyWish{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'accounts_app:service_provider_profile' %}">Provider Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:provider_earnings' %}">Earnings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Payout History</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Payout History</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>Earnings Overview
                    </a>
                    <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>Payment History
                    </a>
                </div>
            </div>
            
            {% include 'payments_app/includes/dashboard_skeleton.html' %}
            <div id="dashboard-content" style="display:none;">
            <!-- Payout Summary -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">Total Payouts</h5>
                            <h3 class="text-success">${{ total_payout_amount|floatformat:2 }}</h3>
                            <small class="text-muted">All time</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">Pending Amount</h5>
                            <h3 class="text-warning">${{ pending_amount|floatformat:2 }}</h3>
                            <small class="text-muted">Awaiting transfer</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">Payout Count</h5>
                            <h3 class="text-info">{{ payouts|length }}</h3>
                            <small class="text-muted">Total transfers</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Stripe Integration Notice -->
            <div class="alert alert-info mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="alert-heading">
                            <i class="fab fa-stripe me-2"></i>Stripe Connect Payouts
                        </h5>
                        <p class="mb-0">
                            Your payouts are processed automatically through Stripe Connect. Funds are transferred to your connected bank account daily.
                            For detailed payout information and tax documents, visit your Stripe dashboard.
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ stripe_dashboard_url }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>Open Stripe Dashboard
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Payout History -->
            {% if payouts %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>Payout Records
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Payout ID</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Arrival Date</th>
                                    <th>Method</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payout in payouts %}
                                <tr>
                                    <td>
                                        <code class="text-muted">{{ payout.payout_id }}</code>
                                    </td>
                                    <td>
                                        <strong>${{ payout.amount|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {% if payout.status == 'paid' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Paid
                                            </span>
                                        {% elif payout.status == 'in_transit' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-clock me-1"></i>In Transit
                                            </span>
                                        {% elif payout.status == 'pending' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-hourglass-half me-1"></i>Pending
                                            </span>
                                        {% elif payout.status == 'failed' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Failed
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payout.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ payout.created_date|date:"M d, Y" }}</td>
                                    <td>
                                        {% if payout.status == 'paid' %}
                                            <span class="text-success">{{ payout.arrival_date|date:"M d, Y" }}</span>
                                        {% elif payout.status == 'in_transit' %}
                                            <span class="text-info">{{ payout.arrival_date|date:"M d, Y" }}</span>
                                        {% elif payout.status == 'pending' %}
                                            <span class="text-warning">{{ payout.arrival_date|date:"M d, Y" }}</span>
                                        {% else %}
                                            {{ payout.arrival_date|date:"M d, Y" }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payout.method == 'bank_account' %}
                                            <i class="fas fa-university me-1"></i>Bank Account
                                            <br>
                                            <small class="text-muted">****{{ payout.bank_account_last4 }}</small>
                                        {% else %}
                                            {{ payout.method|title }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ payout.description }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No payouts yet</h5>
                    <p class="text-muted">
                        Once you start receiving payments from customers, your payouts will appear here.
                        Payouts are processed automatically via Stripe.
                    </p>
                    <div class="mt-4">
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-2"></i>Create Your First Venue
                        </a>
                        <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-2"></i>View Earnings Overview
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Payout Information -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>How Payouts Work
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Payouts are processed automatically daily
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Funds arrive in 1-2 business days
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Platform fee is deducted automatically
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    No manual action required
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>Need Help?
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">
                                If you have questions about your payouts or need to update your bank account information, 
                                you can manage everything through your Stripe dashboard.
                            </p>
                            <div class="d-grid gap-2">
                                <a href="{{ stripe_dashboard_url }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-stripe me-2"></i>Stripe Dashboard
                                </a>
                                <a href="mailto:<EMAIL>" class="btn btn-outline-secondary">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            </div><!-- /#dashboard-content -->

            <!-- Navigation Links -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Earnings
                </a>
                <div>
                    <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list me-2"></i>Payment History
                    </a>
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
