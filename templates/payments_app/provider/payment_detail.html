{% extends 'base.html' %}

{% block title %}Payment Details - Service Provider - CozyWish{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'accounts_app:service_provider_profile' %}">Provider Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:provider_payment_history' %}">Payment History</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Payment Details</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Payment Details</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>Back to History
                    </a>
                </div>
            </div>
            
            <!-- Payment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Payment Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Payment ID:</dt>
                                <dd class="col-sm-7">
                                    <code>{{ payment.payment_id }}</code>
                                </dd>
                                
                                <dt class="col-sm-5">Status:</dt>
                                <dd class="col-sm-7">
                                    {% if payment.payment_status == 'succeeded' %}
                                        <span class="badge bg-success">Succeeded</span>
                                    {% elif payment.payment_status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif payment.payment_status == 'processing' %}
                                        <span class="badge bg-info">Processing</span>
                                    {% elif payment.payment_status == 'failed' %}
                                        <span class="badge bg-danger">Failed</span>
                                    {% elif payment.payment_status == 'cancelled' %}
                                        <span class="badge bg-secondary">Cancelled</span>
                                    {% elif payment.payment_status == 'refunded' %}
                                        <span class="badge bg-dark">Refunded</span>
                                    {% elif payment.payment_status == 'partially_refunded' %}
                                        <span class="badge bg-warning">Partially Refunded</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">{{ payment.get_payment_status_display }}</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-5">Amount Paid:</dt>
                                <dd class="col-sm-7">
                                    <strong class="text-success">${{ payment.amount_paid|floatformat:2 }}</strong>
                                </dd>
                                
                                <dt class="col-sm-5">Payment Method:</dt>
                                <dd class="col-sm-7">{{ payment.get_payment_method_display }}</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Payment Date:</dt>
                                <dd class="col-sm-7">{{ payment.payment_date|date:"M d, Y H:i" }}</dd>
                                
                                {% if payment.completed_date %}
                                <dt class="col-sm-5">Completed Date:</dt>
                                <dd class="col-sm-7">{{ payment.completed_date|date:"M d, Y H:i" }}</dd>
                                {% endif %}
                                
                                {% if payment.refunded_amount > 0 %}
                                <dt class="col-sm-5">Refunded Amount:</dt>
                                <dd class="col-sm-7">
                                    <span class="text-danger">${{ payment.refunded_amount|floatformat:2 }}</span>
                                </dd>
                                {% endif %}
                                
                                {% if payment.stripe_payment_intent_id %}
                                <dt class="col-sm-5">Stripe Payment ID:</dt>
                                <dd class="col-sm-7">
                                    <small class="text-muted">{{ payment.stripe_payment_intent_id }}</small>
                                </dd>
                                {% endif %}
                            </dl>
                        </div>
                    </div>
                    
                    {% if payment.failure_reason %}
                    <div class="alert alert-danger mt-3">
                        <strong>Failure Reason:</strong> {{ payment.failure_reason }}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Earnings Breakdown -->
            {% if payment.payment_status == 'succeeded' %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>Earnings Breakdown
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-6">Gross Amount:</dt>
                                <dd class="col-sm-6">${{ payment.amount_paid|floatformat:2 }}</dd>
                                
                                <dt class="col-sm-6">Platform Fee ({{ platform_fee_rate|floatformat:1 }}%):</dt>
                                <dd class="col-sm-6 text-danger">-${{ platform_fee|floatformat:2 }}</dd>
                                
                                <dt class="col-sm-6"><strong>Net Earnings:</strong></dt>
                                <dd class="col-sm-6"><strong class="text-success">${{ net_earnings|floatformat:2 }}</strong></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Payout Information:</strong><br>
                                Your net earnings will be automatically transferred to your bank account via Stripe within 1-2 business days.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Customer Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Name:</dt>
                                <dd class="col-sm-8">
                                    {% if payment.customer.first_name %}
                                        {{ payment.customer.first_name }} {{ payment.customer.last_name }}
                                    {% else %}
                                        <em class="text-muted">Not provided</em>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">{{ payment.customer.email }}</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Customer Since:</dt>
                                <dd class="col-sm-8">{{ payment.customer.date_joined|date:"M Y" }}</dd>
                                
                                <dt class="col-sm-4">Customer ID:</dt>
                                <dd class="col-sm-8">
                                    <small class="text-muted">{{ payment.customer.id }}</small>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Booking Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>Booking Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Booking ID:</dt>
                                <dd class="col-sm-8">
                                    <code>{{ payment.booking.booking_id }}</code>
                                </dd>
                                
                                <dt class="col-sm-4">Venue:</dt>
                                <dd class="col-sm-8">{{ payment.booking.venue.venue_name }}</dd>
                                
                                <dt class="col-sm-4">Service Date:</dt>
                                <dd class="col-sm-8">{{ payment.booking.booking_date|date:"M d, Y" }}</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Service Time:</dt>
                                <dd class="col-sm-8">{{ payment.booking.booking_time|time:"H:i" }}</dd>
                                
                                <dt class="col-sm-4">Duration:</dt>
                                <dd class="col-sm-8">{{ payment.booking.duration }} minutes</dd>
                                
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary">{{ payment.booking.get_status_display }}</span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{% url 'booking_cart_app:provider_booking_detail' booking_slug=payment.booking.slug %}"
                           class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>View Full Booking Details
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Refund Requests -->
            {% if refund_requests %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-undo me-2"></i>Refund Requests
                    </h5>
                </div>
                <div class="card-body">
                    {% for refund in refund_requests %}
                    <div class="border rounded p-3 mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>Refund Request #{{ refund.refund_request_id|truncatechars:8 }}</h6>
                                <p class="mb-2"><strong>Reason:</strong> {{ refund.get_reason_category_display }}</p>
                                <p class="mb-2"><strong>Description:</strong> {{ refund.reason_description|truncatechars:100 }}</p>
                                <p class="mb-0"><strong>Requested Amount:</strong> ${{ refund.requested_amount|floatformat:2 }}</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-2">
                                    {% if refund.request_status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif refund.request_status == 'approved' %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif refund.request_status == 'declined' %}
                                        <span class="badge bg-danger">Declined</span>
                                    {% elif refund.request_status == 'processed' %}
                                        <span class="badge bg-primary">Processed</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ refund.created_at|date:"M d, Y" }}</small>
                            </div>
                        </div>
                        
                        {% if refund.admin_notes %}
                        <div class="mt-2">
                            <small><strong>Admin Notes:</strong> {{ refund.admin_notes }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Navigation Links -->
            <div class="d-flex justify-content-between">
                <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Payment History
                </a>
                <div>
                    <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-chart-line me-2"></i>Earnings Overview
                    </a>
                    <a href="{% url 'booking_cart_app:provider_booking_detail' booking_slug=payment.booking.slug %}"
                       class="btn btn-primary">
                        <i class="fas fa-calendar-check me-2"></i>View Booking
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-2"></i>View Earnings Overview
                        </a>
                        <a href="{% url 'payments_app:provider_payout_history' %}" class="btn btn-outline-success">
                            <i class="fas fa-money-bill-wave me-2"></i>View Payout History
                        </a>
                        <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-outline-info">
                            <i class="fas fa-calendar-alt me-2"></i>Manage Bookings
                        </a>
                        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i>Provider Dashboard
                        </a>
                    </div>
                </div>
            </div>
            
            {% if payment.payment_status == 'succeeded' %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Payout Information</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Payment Successful!</strong><br>
                        Your earnings will be automatically transferred to your connected bank account.
                    </div>
                    <small class="text-muted">
                        Payouts are processed daily via Stripe. Funds typically arrive within 1-2 business days.
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
